
<script setup>
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import { ref } from 'vue';
import Modal from '../components/Modal/Modal.vue';
import DeleteUser from '../components/ModalContent/DeleteModalContent.vue';
import AddApiKey from '../components/ModalContent/AddApiKey.vue';

const openAddAuthModal = ref(false);
const deleteApiModal = ref(false);
const showIndex = ref();
const apis = ref([
  { name: 'sushant',
    gateway: 'rapidapi.com', authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
  { name: 'sushant',
    gateway: 'rapidapi.com',
    authorisation: 'dfhdkjfhdhfjfhdj', createdOn: '01 July, 2023', Expiry: '30 dec, 2024' },
]);

const handleShowIndex = (ind) => {
  if (showIndex.value === ind) {
    showIndex.value = null;
  } else {
    showIndex.value = ind;
  }
};

const handleAddApi = () => {

};
</script>

<template>
    <div class="bg-neutral-900 h-full w-full lg:pl-72 ">

        <div class="w-full h-full p-3 flex flex-col">
            <div
                class="w-full  text-white flex justify-between items-center">
                <div>
                    <p
                        class="text-neutral-500 text-xs sm:text-sm font-normal leading-7">
                        Settings / API Keys</p>
                    <h1
                        class="text-lg sm:text-Base font-semibold leading-7">
                        API Keys</h1>
                </div>
                <div
                    class="w-full sm:w-fit absolute sm:static bottom-3  left-0 flex justify-center items-center">
                    <button
                        @click="() => openAddAuthModal = true"
                        class="w-10/12 flex justify-center items-center sm:w-fit h-10 rounded-full sm:rounded-md
                        bg-[#4572fc] m-0 px-3 text-xs text-white font-semibold leading-6 ">
                        <svg xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            fill="currentColor"
                            class="w-6 h-6 fill-white mr-2">
                            <path fill-rule="evenodd"
                                d="M12 3.75a.75.75 0 01.75.75v6.75h6.75a.75.75 0 010 1.5h-6.75v6.75a.75.75 0 01-1.5
                                0v-6.75H4.5a.75.75 0 010-1.5h6.75V4.5a.75.75 0 01.75-.75z"
                                clip-rule="evenodd" />
                        </svg>
                        Create API Key
                    </button>
                </div>
            </div>
            <div
                class="h-px border-[1px] border-neutral-800 my-1.5">
            </div>

            <!-- Table for api keys -->
            <div style="height: inherit;"
                class="hidden sm:block -mx-4 -my-2 min-h-full sm:-mx-6 lg:-mx-8 h-full">
                <div style="height: inherit; overflow-y: scroll;"
                    class="inline-block min-w-full  align-middle pb-8 sm:px-6 lg:px-8">
                    <table
                        class="min-w-full mb-12 overflow-y-auto ">
                        <thead>
                            <tr>
                                <th scope="col"
                                    class="sticky top-0 z-10 bg-neutral-900 py-1.5 pl-4 pr-3 text-left text-sm font-semibold
                                     text-neutral-500 sm:pl-0">
                                    Name</th>
                                <th scope="col" class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold
                                    text-neutral-500">
                                    API Keys</th>
                                <th scope="col" class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold
                                    text-neutral-500">
                                    API secret

                                </th>
                                <th scope="col" class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold
                                    text-neutral-500">
                                    Created</th>
                                <th scope="col" class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold
                                    text-neutral-500">
                                    Expiry</th>
                                <th scope="col" class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold
                                    text-neutral-500">
                                    <span
                                        class="sr-only">Edit</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody
                            class="divide-y divide-[#56565680]">
                            <tr v-for="(api, ind) in apis"
                                :key="ind">
                                <td
                                    class="whitespace-nowrap py-2.5 h-14 pl-4 pr-3 text-xs font-medium text-white sm:pl-0">
                                    {{ api.name }}</td>
                                <td
                                    class="whitespace-nowrap py-2.5 h-14 pl-3 pr-3 text-xs font-medium text-white sm:pl-0">
                                    {{ api.gateway }}</td>
                                <td class="whitespace-nowrap py-2.5 h-14 pl-2 pr-3 text-xs font-medium text-white sm:pl-0
                                    flex items-center">

                                    <input
                                        v-model="api.authorisation"
                                        style=""
                                        :type="showIndex === ind ? 'text' : 'password'"
                                        id="apiAuth"
                                        name="apiAuth"
                                        class="w-fit border-0  h-10 p-2 rounded-md justify-start items-center inline-flex text-white bg-inherit
                                        placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs"
                                        placeholder="Api"
                                        disabled />
                                    <div
                                        class="h-full flex justify-between items-center w-14 ml-3">
                                        <div class="cursor-pointer "
                                            @click="handleShowIndex(ind)">
                                            <svg v-if="showIndex !== ind"
                                                class="h-6 w-6 fill-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24">
                                                <g
                                                    data-name="Layer 2">
                                                    <g
                                                        data-name="eye">
                                                        <rect />
                                                        <circle
                                                            cx="12"
                                                            cy="12"
                                                            r="1.5" />
                                                        <path
                                                            d="M21.87 11.5c-.64-1.11-4.16-6.68-10.14-6.5-5.53.14-8.73 5-9.6 6.5a1 1 0 0 0 0 1c.63 1.09
                                                            4 6.5 9.89 6.5h.25c5.53-.14 8.74-5 9.6-6.5a1 1 0 0 0 0-1zm-9.87 4a3.5 3.5 0 1 1 3.5-3.5 3.5
                                                            3.5 0 0 1-3.5 3.5z" />
                                                    </g>
                                                </g>
                                            </svg>

                                            <svg v-else
                                                class="h-6 w-6 fill-white"
                                                xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24">
                                                <g
                                                    data-name="Layer 2">
                                                    <g
                                                        data-name="eye-off">
                                                        <rect />
                                                        <circle
                                                            cx="12"
                                                            cy="12"
                                                            r="1.5" />
                                                        <path
                                                            d="M15.29 18.12L14 16.78l-.07-.07-1.27-1.27a4.07 4.07 0 0 1-.61.06A3.5 3.5 0 0 1 8.5 12a4.07
                                                            4.07 0 0 1 .06-.61l-2-2L5 7.87a15.89 15.89 0 0 0-2.87 3.63 1 1 0 0 0 0 1c.63 1.09 4 6.5 9.89
                                                            6.5h.25a9.48 9.48 0 0 0 3.23-.67z" />
                                                        <path
                                                            d="M8.59 5.76l2.8 2.8A4.07 4.07 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 4.07 4.07 0 0
                                                            1-.06.61l2.68 2.68.84.84a15.89 15.89 0 0 0 2.91-3.63 1 1 0 0 0 0-1c-.64-1.11-4.16-6.68-10.14-6.5a9.48
                                                            9.48 0 0 0-3.23.67z" />
                                                        <path
                                                            d="M20.71 19.29L19.41 18l-2-2-9.52-9.53L6.42 5 4.71 3.29a1 1 0 0 0-1.42 1.42L5.53
                                                            7l1.75 1.7 7.31 7.3.07.07L16 17.41l.59.59 2.7 2.71a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" />
                                                    </g>
                                                </g>
                                            </svg>
                                        </div>
                                        <div
                                            class="cursor-pointer ">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                fill="currentColor"
                                                class="w5 h-5 fill-white">
                                                <path
                                                    d="M7.5 3.375c0-1.036.84-1.875 1.875-1.875h.375a3.75 3.75 0 013.75 3.75v1.875C13.5
                                                     8.161 14.34 9 15.375 9h1.875A3.75 3.75 0 0121 12.75v3.375C21 17.16 20.16 18 19.125
                                                     18h-9.75A1.875 1.875 0 017.5 16.125V3.375z" />
                                                <path
                                                    d="M15 5.25a5.23 5.23 0 00-1.279-3.434 9.768 9.768 0 016.963 6.963A5.23 5.23
                                                    0 0017.25 7.5h-1.875A.375.375 0 0115 7.125V5.25zM4.875 6H6v10.125A3.375 3.375
                                                    0 009.375 19.5H16.5v1.125c0 1.035-.84 1.875-1.875 1.875h-9.75A1.875 1.875 0 013
                                                    20.625V7.875C3 6.839 3.84 6 4.875 6z" />
                                            </svg>

                                        </div>

                                    </div>
                                </td>
                                <td
                                    class="whitespace-nowrap py-2.5 h-14 pl-3 pr-3 text-xs font-medium text-white sm:pl-0">
                                    {{ api.createdOn }}</td>
                                <td
                                    class="whitespace-nowrap py-2.5 h-14 pl-3 pr-3 text-xs font-medium text-white sm:pl-0">
                                    {{ api.Expiry }}</td>
                                <td
                                    class="whitespace-nowrap py-3 pl-3 pr-4 text-right text-sm font-medium sm:pr-0 ">

                                    <div class="cursor-pointer"
                                        @click="() => deleteApiModal = true">

                                        <svg xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 24 24"
                                            fill="currentColor"
                                            class="w-5 h-5 fill-red-600">
                                            <path
                                                fillRule="evenodd"
                                                d="M16.5 4.478v.227a48.816 48.816 0 013.878.512.75.75
                                                0 11-.256 1.478l-.209-.035-1.005 13.07a3 3 0 01-2.991 2.77H8.084a3 3
                                                0 01-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 01-.256-1.478A48.567 48.567
                                                 0 017.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 013.369
                                                 0c1.603.051 2.815 1.387 2.815 2.951zm-6.136-1.452a51.196 51.196 0 013.273
                                                 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 00-6 0v-.113c0-.794.609-1.428 1.364-1.452zm-.355
                                                 5.945a.75.75 0 10-1.5.058l.347 9a.75.75 0 101.499-.058l-.346-9zm5.48.058a.75.75
                                                 0 10-1.498-.058l-.347 9a.75.75 0 001.5.058l.345-9z"
                                                clipRule="evenodd" />
                                        </svg>

                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                </div>
            </div>

            <div class="sm:hidden flex-grow overflow-y-auto">
                <div v-for="(api, ind) in apis" :key="ind"
                    class="bg-neutral-800 h-fit mb-3 rounded-lg text-white flex flex-col p-2">
                    <div class="h-40 flex">
                        <div class="flex-grow">
                            <div
                                class="w-full h-fit flex-col justify-start items-start inline-flex
                                bg-inherit py-1.5 sm:mr-8 ">
                                <label
                                    class=" text-xs font-semibold mb-1 text-neutral-500">
                                    Name
                                </label>
                                <p
                                    class="text-white text-sm font-medium leading-normal">
                                    {{ api.name }}</p>

                            </div>
                            <div
                                class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 sm:mr-8 ">
                                <label
                                    class=" text-xs font-semibold mb-1 text-neutral-500">
                                    API Key
                                </label>
                                <p
                                    class="text-white text-sm font-medium leading-normal">
                                    {{ api.gateway }}</p>

                            </div>
                            <div
                                class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 sm:mr-8 ">
                                <label
                                    class=" text-xs font-semibold mb-1 text-neutral-500">
                                    API Secret
                                </label>
                                <p
                                    class="text-white text-sm font-medium leading-normal">
                                    {{ api.authorisation }}</p>

                            </div>
                        </div>
                        <div
                            class="w-8 h-full flex flex-col justify-between items-center">
                            <div
                                class="h-full flex justify-end items-start p-1 relative whitespace-nowrap
                                text-right text-sm font-medium">
                                <Menu as="div"
                                    class="relative w-full inline-block text-left">
                                    <div>
                                        <MenuButton as="div"
                                            class="inline-flex w-fit mr-1.5 rounded-md bg-inherit py-0 text-xs
                                            text-white ring-gray-300 cursor-pointer">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                viewBox="0 0 24 24"
                                                fill="currentColor"
                                                class="w-6 h-6 fill-white">
                                                <path
                                                    fillRule="evenodd"
                                                    d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113
                                                    0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"
                                                    clipRule="evenodd" />
                                            </svg>
                                        </MenuButton>
                                    </div>

                                    <transition
                                        enter-active-class="transition ease-out duration-100"
                                        enter-from-class="transform opacity-0 scale-95"
                                        enter-to-class="transform opacity-100 scale-100"
                                        leave-active-class="transition ease-in duration-75"
                                        leave-from-class="transform opacity-100 scale-100"
                                        leave-to-class="transform opacity-0 scale-95">
                                        <MenuItems
                                            class="absolute -top-2 right-6 z-50 mt-2 w-fit origin-top-right rounded-md
                                            bg-neutral-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                            <div
                                                class="py-2 flex flex-col">
                                                <!-- <MenuItem>
                                                <a href="#" @click="() => { }"
                                                    class="text-gray-300 block px-3 py-1 text-xs hover:text-white">Edit
                                                    API</a>
                                                </MenuItem> -->
                                                <MenuItem>
                                                <a href="#"
                                                    @click="() => deleteApiModal = !deleteApiModal"
                                                    class="text-gray-300 block px-3 py-1 text-xs hover:text-white leading-tight">Delete
                                                    API</a>
                                                </MenuItem>
                                            </div>
                                        </MenuItems>
                                    </transition>
                                </Menu>
                            </div>
                            <div class="mb-3 cursor-pointer"
                                @click="handleShowIndex(ind)">
                                <svg v-if="showIndex !== ind"
                                    class="h-6 w-6 fill-white"
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24">
                                    <g data-name="Layer 2">
                                        <g data-name="eye">
                                            <rect />
                                            <circle cx="12"
                                                cy="12"
                                                r="1.5" />
                                            <path
                                                d="M21.87 11.5c-.64-1.11-4.16-6.68-10.14-6.5-5.53.14-8.73 5-9.6
                                                6.5a1 1 0 0 0 0 1c.63 1.09 4 6.5 9.89 6.5h.25c5.53-.14 8.74-5 9.6-6.5a1
                                                1 0 0 0 0-1zm-9.87 4a3.5 3.5 0 1 1 3.5-3.5 3.5 3.5 0 0 1-3.5 3.5z" />
                                        </g>
                                    </g>
                                </svg>

                                <svg v-else
                                    class="h-6 w-6 fill-white"
                                    xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24">
                                <g data-name="Layer 2">
                                    <g data-name="eye-off">
                                        <rect />
                                        <circle cx="12"
                                            cy="12"
                                            r="1.5" />
                                        <path
                                            d="M15.29 18.12L14 16.78l-.07-.07-1.27-1.27a4.07 4.07 0 0 1-.61.06A3.5 3.5 0 0 1
                                                8.5 12a4.07 4.07 0 0 1 .06-.61l-2-2L5 7.87a15.89 15.89 0 0 0-2.87 3.63 1 1 0 0 0 0
                                                1c.63 1.09 4 6.5 9.89 6.5h.25a9.48 9.48 0 0 0 3.23-.67z" />
                                        <path
                                            d="M8.59 5.76l2.8 2.8A4.07 4.07 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 4.07 4.07 0 0
                                                1-.06.61l2.68 2.68.84.84a15.89 15.89 0 0 0 2.91-3.63 1 1 0 0 0
                                                0-1c-.64-1.11-4.16-6.68-10.14-6.5a9.48 9.48 0 0 0-3.23.67z" />
                                        <path
                                            d="M20.71 19.29L19.41 18l-2-2-9.52-9.53L6.42 5 4.71 3.29a1 1 0 0 0-1.42 1.42L5.53 7l1.75
                                                1.7 7.31 7.3.07.07L16 17.41l.59.59 2.7 2.71a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z" />
                                    </g>
                                </g>
                            </svg>
                        </div>
                    </div>
                </div>
                <div
                    class="h-px border-[1px] border-neutral-700 ">
                </div>
                <div
                    class="flex-grow flex justify-start items-center py-1">
                    <h1
                        class="text-neutral-500 text-sm w-fit">
                        created On : {{ api.createdOn }}
                    </h1>
                </div>

            </div>
        </div>
    </div>

</div>
<Modal :open="deleteApiModal">
    <DeleteUser @closeModal="(e) => deleteApiModal = false"
        :dataName="'API Keys'" />
</Modal>
<Modal :open="openAddAuthModal">
    <AddApiKey @closeModal="(e) => openAddAuthModal = false"
        @AddApi="handleAddApi" />
</Modal></template>

<style scoped>/* width */
::-webkit-scrollbar {
    width: 0.1rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
    display: none;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
    display: none;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
    display: none;
}</style>

<script setup>
import { ArrowLeftOnRectangleIcon } from '@heroicons/vue/20/solid';
import { onMounted } from 'vue';
import { OrganizationStore } from '../store/archiveprojects';
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline';

const organizationStore = OrganizationStore();

/* Methods */

const handleAcceptInvitation = (val) => {
  console.log('Accept');
  console.log(val);
};

const handleDeclineInvitation = (val) => {
  console.log('Decline');
  console.log(val);
};

// LogOut
const handleLogOut = () => {
  window.location = '/login';
};

/* Hooks */
onMounted(() => {
  //    OrganizationStore.GetInvitationList();
});

</script>
<template>
    <div class="h-screen w-full flex flex-col justify-start items-center p-3">

        <div class="border-b-[1px] border-b-gray-700 p-4 flex justify-center items-center gap-2 w-full">
         <!--    <img class="w-10 mb-5" :src="noDataFound" alt=""> -->
         <ExclamationTriangleIcon class="w-7 text-yellow-400"/>
            <h3 class="text-white text-xl font-medium "> No Organization was found for this User ! </h3>

        </div>

        <div class="w-[80%]">

            <div class="flex justify-between items-center">
                <h3 class="text-white text-4xl font-semibold"> Invitation </h3>
                <button class="my-4 text-[#8f8e8e] cursor-pointer flex
                justify-center items-center gap-2 border-[1px] border-[#8f8e8e] p-2 rounded-md "
                @click="handleLogOut"> <ArrowLeftOnRectangleIcon class="w-7"/>  LogOut </button>
            </div>

            <table class="w-full overflow-y-auto ">
                  <thead>
                    <tr>
                      <th scope="col" class="px-3 py-0.5 text-center text-sm font-semibold text-neutral-500">Email ID</th>
                      <th scope="col" class="px-3 py-0.5 text-center text-sm font-semibold text-neutral-500">Phone Number
                      </th>
                      <th scope="col" class="px-3 py-0.5 text-center text-sm font-semibold text-neutral-500">Role
                      </th>

                      <th scope="col" class="px-3 py-0.5 text-center text-sm font-semibold text-neutral-500" >
                          Status
                      </th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-[#56565680]">
                    <tr v-if="organizationStore.invitation === null || organizationStore.invitation.length === 0 ">
                        <td class="whitespace-nowrap px-3 py-3 text-xs text-center text-gray-300"> Sample </td>
                      <td class="whitespace-nowrap px-3 py-3 text-xs text-center text-gray-300"> XXXXXXXXXX </td>
                      <td class="whitespace-nowrap px-3 py-3 text-xs text-center text-gray-300"> editor </td>
                      <td   class="whitespace-nowrap py-3 text-xs text-center text-gray-300 flex justify-center items-center gap-2">
                          <button class="bg-[rgba(0,100,0,0.4)] text-white backdrop-blur-lg px-3 cursor-pointer border-2 border-green-600 py-2 rounded-3xl" @click="handleAcceptInvitation(user)">
                              Accept
                          </button>
                          <button class="bg-[rgba(100,0,0,0.4)] text-white backdrop-blur-lg px-3 cursor-pointer border-2 border-red-600 py-2 rounded-3xl" @click="handleDeclineInvitation(user)">
                              Decline
                          </button>
                      </td>
                         <!--    <td colspan="4" class="text-center whitespace-nowrap px-3 py-3 text-xs text-gray-300"> No Data found ! </td> -->
                    </tr>
                    <tr v-else v-for="user, userId in organizationStore.invitation" :id="userId" :key="userId">
                      <td class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">{{ user.email }}</td>
                      <td class="whitespace-nowrap px-3 py-3 text-xs text-gray-300"> XXXXXXXXXX </td>
                      <td class="whitespace-nowrap px-3 py-3 text-xs text-gray-300"> {{ user.role }} </td>
                      <td   class="whitespace-nowrap px-3 py-3 text-xs text-gray-300 flex justify-start items-start gap-2">
                          <button class="bg-[rgba(0,100,0,0.4)] text-white backdrop-blur-lg px-3 cursor-pointer border-2 border-green-600 py-2 rounded-3xl" @click="handleAcceptInvitation(user)">
                              Accept
                          </button>
                          <button class="bg-[rgba(100,0,0,0.4)] text-white backdrop-blur-lg px-3 cursor-pointer border-2 border-red-600 py-2 rounded-3xl" @click="handleDeclineInvitation(user)">
                              Decline
                          </button>
                      </td>
                    </tr>
                    <div class=" border-0 h-36"></div>
                  </tbody>
            </table>

        </div>

    </div>
</template>

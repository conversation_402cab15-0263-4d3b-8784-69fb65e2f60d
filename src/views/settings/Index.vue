<script setup>
import SideNavBar from '../../components/common/SideNavBar.vue';
import OrganizationSettings from '../../components/settings/Index.vue';
import Navbar from '../../components/common/Navbar.vue';

</script>

<template>
     <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar/>
        <div class="dynamic-viewbox">
        <SideNavBar />
        <div class="dynamic-container">
            <OrganizationSettings></OrganizationSettings>
        </div>
    </div>
</div>
</template>

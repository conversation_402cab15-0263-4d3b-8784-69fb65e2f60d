<script setup>
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
// import SideBar from '../../../components/Projects/SideBar.vue';
import ProjectLandmarks from '../../../components/Projects/landmarks/Index.vue';
import { UserStore } from '../../../store/index';
import SideNavBar from '@/components/common/SideNavBar.vue';
const userStore = UserStore();
</script>

<template>
    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar/>
        <div
            class="h-screen w-full flex justify-start items-start overflow-hidden bg-[#f3f4f6]">
            <!-- <SideBar /> -->
            <SideNavBar/>
            <div v-if="userStore.user_data"
                class="pt-0 bg-transparent h-full overflow-y-auto w-full">
                <ProjectLandmarks />
                <router-view></router-view>
            </div>
        </div>
    </div></template>

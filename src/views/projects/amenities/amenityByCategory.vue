<script setup>
import DesignMenuBar from '@/components/scenes/DesignMenuBar.vue';
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
import ReorderAmenities from '@/components/Projects/amenities/ReorderAmenities.vue';
import { UserStore } from '@/store';

const userStore = UserStore();
</script>

<template>
    <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar/>
        <div
            class="h-full overflow-hidden w-full">
            <div v-if="userStore.user_data"
                class="pt-0 bg-transparent h-full overflow-y-auto w-full flex-1 bg-gray-100 flex flex-col overflow-hidden">
                   <div class="flex h-full w-full overflow-hidden gap-2 border">
                    <DesignMenuBar/>
                    <ReorderAmenities />
                    <router-view></router-view>
                   </div>
                <router-view></router-view>
            </div>
        </div>
    </div>
    </div>
</template>

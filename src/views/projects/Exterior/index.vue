<script setup>
import Exterior from '@/components/Exterior/ExteriorFile.vue';
import { UserStore } from '../../../store/index';
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';
import SideNavBar from '@/components/common/SideNavBar.vue';
const userStore = UserStore();
</script>

<template>
     <div class="w-full h-full overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
         <DatacenterNavBar/>
        <div class="dynamic-viewbox">
        <SideNavBar/>
        <div v-if="userStore.user_data" class="dynamic-container">
            <Exterior/>
            <router-view></router-view>
        </div>
    </div>
</div>
</template>

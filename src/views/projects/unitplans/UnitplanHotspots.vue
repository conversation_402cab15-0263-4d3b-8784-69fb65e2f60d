<script setup>
import Navbar from '../../../components/common/Navbar.vue';
import UnitplanHotspots from '../../../components/Projects/unitplans/UnitplanHotspots.vue';
// import SideBar from '../../../components/Projects/SideBar.vue';
import SideNavBar from '@/components/common/SideNavBar.vue';

</script>

<template>
    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar />
        <div class="dynamic-viewbox">
        <SideNavBar />
        <div v-if="userStore.user_data" class="dynamic-container">
                <UnitplanHotspots />
                <router-view></router-view>
            </div>
        </div>
    </div></template>

<script setup>
import Navbar from '../../components/common/Navbar.vue';
import SideNavBar from '../../components/common/SideNavBar.vue';
import ProjectsView from '../../components/Projects/RootProject.vue';
import { UserStore } from '../../store/index';
const userStore = UserStore();
</script>

<template>
    <div class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar/>
    <div class="dynamic-viewbox">
        <SideNavBar />
        <div v-if="userStore.user_data" class="dynamic-container">
            <ProjectsView/>
            <router-view></router-view>
        </div>
    </div>
</div>
</template>

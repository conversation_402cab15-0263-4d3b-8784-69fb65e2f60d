<script setup>
import { watch, ref } from 'vue';
import { OrganizationStore } from '../../store/archiveprojects';
import { useRoute } from 'vue-router';

const route = useRoute();
const organizationStore = OrganizationStore();
const hotspotState = ref(true);
const projectID = route.params.projectId;
const selectedOption = ref('scene');
const hotspotTitle = ref('');
const hotspotPitch = ref('');
const hotspotYaw = ref('');
const tab = ref('add');
const hotspotTargetYaw = ref('');
const hotspotTargetPitch = ref('');
const hotspots = ref([]);
const clickedHotspot = ref([]);
const pitchVal = ref('');

var hotspotClickHandler = function (e) {
  const value = e.target.textContent;

  clickedHotspot.value = hotspots.value.hotspots.filter(
    (i) => i.text === value,
  )[0];
  pitchVal.value = clickedHotspot.value.pitch;
  console.log(hotspots.value.hotspots.filter((i) => i.text === value)[0]);
};
function transformHotspots (hotspots) {
  return hotspots.map((hotspot) => {
    return {
      _id: hotspot._id,
      pitch: hotspot.pitch,
      yaw: hotspot.yaw,
      type: hotspot.hotspot_type,
      text: hotspot.text,
      targetYaw: hotspot.target_yaw,
      targetPitch: hotspot.target_pitch,
      tourId: hotspot.tour_id,
      sceneId: hotspot.scene_id,
      clickHandlerFunc: hotspotClickHandler,
    };
  });
}

function extractScenesAndHotspots (data) {
  const result = {
    hotspots: [],
  };

  for (const sceneId in data.scenes) {
    const scene = data.scenes[sceneId];

    for (const hotspotId in scene.hot_spots) {
      result.hotspots.push(scene.hot_spots[hotspotId]);
    }
  }

  result.hotspots = transformHotspots(result.hotspots);

  return result;
}
const updateHotspotHandler = () => {
  const data = {
    project_id: projectID,
    tour_id: clickedHotspot.value.tourId,
    scene_id: clickedHotspot.value.sceneId,
    hotspot_id: clickedHotspot.value._id,
    pitch: pitchVal.value,
  };
  organizationStore
    .updateHotspot(data)
    .then((res) => {
      hotspots.value = extractScenesAndHotspots(res);

      hotspotState.value = !hotspotState.value;
    })
    .catch((err) => console.log(err));
};
const hotspotHandler = (scenePreview) => {
  const data = {
    pitch: hotspotPitch.value,
    tour_id: scenePreview.tour_id,
    project_id: projectID,
    scene_id: scenePreview._id,
    yaw: hotspotYaw.value,
    hotspot_type: selectedOption.value,
    text: hotspotTitle.value,
    target_pitch: hotspotTargetPitch.value,
    target_yaw: hotspotTargetYaw.value,
  };
  organizationStore
    .getHotspot(data)
    .then((res) => {
      hotspots.value = extractScenesAndHotspots(res);

      hotspotState.value = !hotspotState.value;
    })
    .catch((err) => console.log(err));
};
const scenePreview = ref([]);
let pannellumViewer = null;

function updatePanorama (scenePreview) {
  console.log(hotspots.value.hotspots);
  const panoramaContainer = document.getElementById('panoramaContainer');
  if (panoramaContainer) {
    if (pannellumViewer) {
      pannellumViewer.destroy();
      pannellumViewer = null;
    }
    pannellumViewer = window.pannellum.viewer('panoramaContainer', {
      type: scenePreview.scene_type,
      panorama: scenePreview.panorama,
      autoLoad: true,
      compass: false,
      hotSpots: hotspots.value.hotspots,
    });
  } else {
    console.error('Panorama element not found.');
  }
}

watch(
  () => organizationStore.scenePreview,
  (newVal) => {
    updatePanorama(newVal);
    scenePreview.value = newVal;

    const newArr = transformHotspots(Object.values(newVal.hot_spots));
    const newHotspot = {
      hotspots: newArr,
    };
    hotspots.value = newHotspot;
  },
);

watch(hotspots, () => {
  updatePanorama(scenePreview.value);
});

</script>

<template>
  <div class="relative w-full h-full">
    <div
      class="fixed z-[20] right-1 top-64 rounded-md flex flex-col items-center"
    >
      <button
        v-if="hotspotState"
        @click="() => (hotspotState = !hotspotState)"
        class="py-2 px-3 rounded-lg w-fit bg-buttonPrimary text-white mt-1"
      >
        Hotspot
      </button>
      <div class="flex-col" v-else>
        <ul class="flex gap-1">
          <li
            class="p-2 bg-propvrDetailInfoBlack text-white cursor-pointer hover:opacity-75"
            @click="tab = 'add'"
          >
            Add Hotspot
          </li>
          <li
            class="p-2 bg-propvrDetailInfoBlack text-white cursor-pointer hover:opacity-75"
            @click="tab = 'update'"
          >
            Update Hotspot
          </li>
          <li
            class="p-2 bg-propvrDetailInfoBlack text-white cursor-pointer hover:opacity-75"
          >
            Delete Hotspot
          </li>
        </ul>
        <div v-if="tab === 'add'">
          <ul
            class="flex flex-col gap-3 mt-3 p-3 bg-propvrBlack"
            v-if="!hotspotState"
          >
            <li class="flex justify-between">
              <label for="hotspotTitle">Title</label
              ><input
                name="hotspotTitle"
                id="hotspotTitle"
                v-model="hotspotTitle"
                class="rounded-md"
                type="text"
              />
            </li>
            <li class="flex justify-between">
              <label for="pitch">Pitch</label
              ><input
                name="pitch"
                id="pitch"
                v-model="hotspotPitch"
                class="rounded-md"
                type="number"
              />
            </li>
            <li class="flex justify-between">
              <label for="yaw">Yaw</label
              ><input
                name="yaw"
                id="yaw"
                v-model="hotspotYaw"
                class="rounded-md"
                type="number"
              />
            </li>
            <li class="flex justify-between">
              <label for="targetYaw">Target yaw</label
              ><input
                name="targetYaw"
                id="targetYaw"
                v-model="hotspotTargetYaw"
                class="rounded-md"
                type="number"
              />
            </li>
            <li class="flex justify-between">
              <label for="targetPitch">Target pitch</label
              ><input
                name="targetPitch"
                id="targetPitch"
                v-model="hotspotTargetPitch"
                class="rounded-md"
                type="number"
              />
            </li>
            <li class="flex justify-between">
              <label for="selectOption">Hotspot type:</label>
              <select
                class="rounded-md"
                v-model="selectedOption"
                id="selectOption"
              >
                <option value="scene">scene</option>
                <option value="info">info</option>
              </select>
            </li>
          </ul>
          <button
            @click="hotspotHandler(organizationStore.scenePreview)"
            class="py-2 px-3 rounded-lg w-fit bg-buttonPrimary text-white mt-1"
          >
            Add Hotspot
          </button>
        </div>
        <div v-if="tab === 'update'">
          <ul
            class="flex flex-col gap-3 mt-3 p-3 bg-propvrBlack"
            v-if="!hotspotState"
          >
            <li class="flex justify-between">
              <label for="hotspotTitle">Title</label
              ><input
                name="hotspotTitle"
                id="hotspotTitle"
                v-model="clickedHotspot.text"
                class="rounded-md text-white"
                type="text"
                disabled
              />
            </li>
            <li class="flex justify-between">
              <label for="pitch">Pitch</label
              ><input
                name="pitch"
                id="pitch"
                v-model="pitchVal"
                class="rounded-md"
                type="number"
              />
            </li>
          </ul>
          <button
            @click="updateHotspotHandler"
            class="py-2 px-3 rounded-lg w-fit bg-buttonPrimary text-white mt-1"
          >
            Update Hotspot
          </button>
        </div>
      </div>
    </div>
    <div id="panoramaContainer" class="panorama-container"></div>
  </div>
</template>
<style scoped>
.panorama-container {
  width: 100%;
  height: 100%;
}
</style>

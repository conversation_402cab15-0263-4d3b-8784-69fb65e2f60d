<script setup>
import { Org_Store } from '@/store/organization';
import Navbar from '../../components/common/Navbar.vue';
// import SideBar from '../../components/scenes/SceneLeftSideBar.vue';
import SceneById from '../../components/scenes/SceneById.vue';
import MasterSceneMenuBar from '@/components/scenes/MasterSceneMenuBar.vue';
import { UserStore } from '../../store/index';
const userStore = UserStore();
const organizationStore = Org_Store();
organizationStore.RefreshMasterScenes();
</script>

<template>

    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <Navbar />
        <div
            class="h-full overflow-hidden w-full">

            <div v-if="userStore.user_data"
                class="pt-0 bg-transparent h-full overflow-y-auto w-full flex-1 bg-gray-100 flex flex-col overflow-hidden">
                   <div class="flex justify-evenly h-full w-full overflow-hidden gap-2 border relative z-10">
                   <MasterSceneMenuBar/>
                   <SceneById />
                   <router-view></router-view>
                   </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import DatacenterNavBar from '@/components/common/DatacenterNavBar.vue';

import Gsplat from '../../components/scenes/Gsplat/Gsplat.vue';
import { UserStore } from '../../store/index';
import { Org_Store } from '../../store/organization';
import SideNavBar from '@/components/common/SideNavBar.vue';

const userStore = UserStore();
const organizationStore = Org_Store();

// Inialization
organizationStore.RefreshMasterScenes();

</script>

<template>
    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar />
        <div v-if="organizationStore.masterScenes !== null"
            class="dynamic-viewbox">
            <SideNavBar />
            <div v-if="userStore.user_data"
                class="dynamic-container">
                <Gsplat/>
                <router-view ></router-view>
            </div>
        </div>
    </div>

    <div
        class="w-full h-screen overflow-hidden relative bg-bg-1000 dark:bg-bg-50 flex flex-col ">
        <DatacenterNavBar />
        <div
            class="dynamic-viewbox">
            <!-- <SideBar /> -->
            <div v-if="userStore.user_data"
                class="dynamic-container">
                <Gsplat/>
                <router-view></router-view>
            </div>
        </div>
    </div>
</template>

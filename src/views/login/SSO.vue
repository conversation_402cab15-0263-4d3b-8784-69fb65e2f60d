<script setup>
import { ref } from 'vue';
import { UserStore } from '../../store';
import { deleteCookie } from '../../helpers/domhelper';
import { useRouter } from 'vue-router';
import LoaderComp from '../../components/common/LoaderComp.vue';

const router = useRouter();
const Store = UserStore();
const loader = ref(true);

deleteCookie('organization');
deleteCookie('accessToken');
deleteCookie('refreshToken');

Store.SSO().then(() => {
  Store.GetAuth().then(() => {
    loader.value = false;
    router.push('/sessions');
  });
});
</script>

<template>
  <LoaderComp v-if="loader" class="z-[9999]"/>
</template>

<style scoped>

</style>

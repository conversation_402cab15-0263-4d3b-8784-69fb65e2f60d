<script setup>
import { ref, defineEmits, nextTick} from 'vue';
import SearchImageComponent from '../../components/UIElements/SearchImageComponent.vue';
const emit = defineEmits(['closeInviteCard']);
const link = ref('https://meet.google.com/pgw-pdww-cic');
const clone = ref(null);
const showCard = ref(true);
const isClicked = ref(false);
const svgFill=ref('#0F0F0F');
const bgColor = ref('bg-bg-900');

async function copyLink () {
  await nextTick(); // Wait for the next DOM update cycle

  const element = clone.value;
  if (element) {
    element.select();
    element.setSelectionRange(0, 99999);

    try {
      navigator.clipboard.writeText(link.value);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  } else {
    console.error('Clone input element not found.');
  }
}

const bgToggler= () => {
  isClicked.value = !isClicked.value;
  console.log(isClicked.value);
  // SvgFill.value = isClicked ? '#ffffff' : '#0F0F0F';
  // BgColor.value = isClicked ? 'bg-bg-default' : 'bg-bg-900'
  if (isClicked.value){
    svgFill.value = '#ffffff';
    bgColor.value = 'bg-bg-default';
  }
  if (!isClicked.value){
    console.log('enteres');
    svgFill.value = '#0F0F0F';
    bgColor.value = 'bg-bg-900';
  }
};

const handleClose = () => {
  showCard.value = false;
  emit('closeInviteCard');
};

</script>

<template>
    <div ref="showCard" class="flex justify-center items-center">
        <div class="w-[450px] min-h-[350px] rounded-2xl p-[20px] bg-bg-1000 flex flex-col gap-[20px]  md:w-[600px] ">
            <div class="mb-2 flex justify-between items-center">
                <h1 class="text-base text-black font-semibold">
                Invite participant</h1>
                <button
                class="p-1 rounded cursor-pointer hover:bg-bg-900"
                @click="handleClose()">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24" strokeWidth="{1.5}"
                    stroke="currentColor" class="w-6 h-6">
                    <path strokeLinecap="round" strokeLinejoin="round"
                    d="M6 18 18 6M6 6l12 12" />
                </svg>
                </button>
            </div>
            <div class="flex flex-col">

                <div>
                <SearchImageComponent/>
                </div>

                <div class="flex flex-col gap-[12px] mt-[250px]">
                    <p class="text-lg font-semibold">Meeting link <strong>*</strong></p>
                    <div class="flex gap-[24px]">
                            <div class="w-[488px] h-[48px] gap-1 p-[13px] bg-bg-950 rounded-lg flex justify-baseline items-center">
                                <div class="w-[24px] h-[24px]">
                                    <svg width="20" height="20" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.8877 18.0738C14.0438 18.23 14.1315 18.4417 14.1315 18.6625C14.1315 18.8834 14.0438 19.0951 13.8877 19.2513L12.8528 20.2925C11.7584 21.3863 10.2742 22.0005 8.72688 22C7.17956 21.9995 5.6958 21.3844 4.60203 20.2899C3.50825 19.1954 2.89404 17.7113 2.89453 16.164C2.89502 14.6166 3.51016 13.1329 4.60463 12.0391L7.11594 9.5278C8.1672 8.4757 9.58079 7.86511 11.0674 7.82098C12.5541 7.77685 14.0014 8.30252 15.1132 9.29041C15.1952 9.36315 15.2622 9.45133 15.3101 9.54993C15.3581 9.64853 15.3861 9.75561 15.3927 9.86505C15.3993 9.97449 15.3842 10.0842 15.3484 10.1878C15.3126 10.2914 15.2568 10.387 15.184 10.469C15.1113 10.5511 15.0231 10.618 14.9245 10.6659C14.8259 10.7139 14.7188 10.7419 14.6094 10.7485C14.4999 10.7551 14.3903 10.74 14.2866 10.7042C14.183 10.6684 14.0874 10.6126 14.0054 10.5398C13.2113 9.83464 12.1778 9.45946 11.1163 9.491C10.0547 9.52255 9.04536 9.95844 8.29455 10.7095L5.78324 13.2177C5.00205 13.9989 4.56318 15.0584 4.56318 16.1632C4.56318 17.268 5.00205 18.3275 5.78324 19.1087C6.56443 19.8899 7.62395 20.3288 8.72872 20.3288C9.8335 20.3288 10.893 19.8899 11.6742 19.1087L12.7091 18.0738C12.7865 17.9963 12.8784 17.9349 12.9795 17.893C13.0806 17.851 13.189 17.8295 13.2984 17.8295C13.4079 17.8295 13.5163 17.851 13.6174 17.893C13.7185 17.9349 13.8104 17.9963 13.8877 18.0738ZM21.1843 3.70554C20.0897 2.61337 18.6065 2 17.0602 2C15.5139 2 14.0307 2.61337 12.9361 3.70554L11.9012 4.74151C11.745 4.89781 11.6574 5.10973 11.6575 5.33067C11.6576 5.5516 11.7454 5.76345 11.9017 5.9196C12.058 6.07576 12.2699 6.16343 12.4909 6.16333C12.7118 6.16324 12.9236 6.07538 13.0798 5.91908L14.1147 4.88415C14.8959 4.10296 15.9554 3.6641 17.0602 3.6641C18.165 3.6641 19.2245 4.10296 20.0057 4.88415C20.7869 5.66535 21.2258 6.72487 21.2258 7.82964C21.2258 8.93441 20.7869 9.99393 20.0057 10.7751L17.4944 13.2885C16.7432 14.0392 15.7336 14.4745 14.672 14.5055C13.6105 14.5364 12.5772 14.1607 11.7835 13.4551C11.7015 13.3824 11.6059 13.3265 11.5023 13.2907C11.3987 13.2549 11.289 13.2398 11.1796 13.2464C10.9585 13.2597 10.7518 13.3602 10.6049 13.5259C10.5322 13.6079 10.4763 13.7035 10.4405 13.8071C10.4047 13.9108 10.3896 14.0204 10.3962 14.1299C10.4095 14.3509 10.51 14.5576 10.6757 14.7045C11.7873 15.6927 13.2344 16.2187 14.7211 16.175C16.2077 16.1312 17.6215 15.521 18.673 14.4692L21.1843 11.9579C22.2771 10.8632 22.8909 9.37958 22.8909 7.83276C22.8909 6.28595 22.2771 4.80234 21.1843 3.70763V3.70554Z" fill="black"/>
                                </svg>
                                </div>
                                <input v-on:focus="$event.target.select()"
                                ref="clone"  :value="link" readonly
                                class="w-full bg-bg-950" />
                            </div>
                            <div @click="copyLink(), bgToggler()"  :class = "bgColor"
                            class="w-[48px] h-[48px]  bg-bg-900 rounded-lg flex justify-center items-center">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M5.81859 5.48851C5.85776 5.4884 5.89692 5.48828 5.93727 5.48816C6.06843 5.48782 6.1996 5.48776 6.33076 5.48771C6.42484 5.48753 6.51892 5.48733 6.613 5.48712C6.86857 5.48661 7.12414 5.48636 7.37971 5.48618C7.53945 5.48608 7.6992 5.48591 7.85894 5.48574C8.30169 5.48526 8.74444 5.48486 9.18719 5.48473C9.2155 5.48472 9.24382 5.48471 9.27299 5.4847C9.31556 5.48469 9.31556 5.48469 9.35899 5.48468C9.41649 5.48466 9.47399 5.48464 9.53149 5.48462C9.56002 5.48462 9.58854 5.48461 9.61793 5.4846C10.0797 5.48444 10.5416 5.48376 11.0034 5.48286C11.4781 5.48193 11.9529 5.48145 12.4276 5.4814C12.694 5.48137 12.9603 5.48114 13.2266 5.48044C13.4772 5.47977 13.7278 5.47971 13.9785 5.48005C14.0702 5.48008 14.162 5.47991 14.2538 5.47953C15.4976 5.47461 15.4976 5.47461 15.9946 5.92267C16.2692 6.20957 16.4579 6.57226 16.4588 6.97274C16.459 7.04594 16.4591 7.04594 16.4594 7.12062C16.4594 7.17507 16.4594 7.22953 16.4594 7.28398C16.4596 7.3416 16.4597 7.39922 16.4599 7.45684C16.4604 7.61558 16.4606 7.77431 16.4608 7.93305C16.461 8.10411 16.4615 8.27517 16.4619 8.44622C16.4628 8.85959 16.4634 9.27297 16.4639 9.68634C16.4641 9.8031 16.4642 9.91986 16.4644 10.0366C16.4654 10.7617 16.4662 11.4868 16.4667 12.2119C16.4668 12.3799 16.4669 12.5478 16.4671 12.7158C16.4671 12.7576 16.4671 12.7993 16.4671 12.8424C16.4676 13.5185 16.4689 14.1945 16.4706 14.8706C16.4722 15.5648 16.4731 16.259 16.4733 16.9531C16.4735 17.3428 16.4739 17.7326 16.4752 18.1223C16.4763 18.4543 16.4767 18.7863 16.4763 19.1183C16.4761 19.2876 16.4762 19.4569 16.4772 19.6262C16.478 19.7814 16.478 19.9366 16.4773 20.0918C16.4772 20.1478 16.4774 20.2037 16.478 20.2596C16.4828 20.7502 16.3689 21.181 16.0249 21.5447C15.5159 22.0262 14.9393 22.0014 14.281 21.9984C14.183 21.9987 14.0851 21.999 13.9871 21.9994C13.7495 22.0002 13.512 22.0001 13.2744 21.9995C13.0811 21.9991 12.8879 21.999 12.6947 21.9993C12.6671 21.9993 12.6395 21.9993 12.6111 21.9993C12.555 21.9994 12.4989 21.9995 12.4429 21.9995C11.9182 22.0001 11.3936 21.9995 10.8689 21.9984C10.4195 21.9975 9.97016 21.9977 9.52078 21.9986C8.99789 21.9996 8.475 22 7.95211 21.9994C7.89626 21.9994 7.84042 21.9993 7.78457 21.9993C7.7571 21.9992 7.72963 21.9992 7.70133 21.9992C7.50851 21.999 7.3157 21.9993 7.12288 21.9997C6.86297 22.0003 6.60308 21.9999 6.34316 21.9988C6.248 21.9985 6.15283 21.9986 6.05766 21.999C5.44336 22.0016 4.92565 21.9905 4.45437 21.5447C4.08506 21.1543 3.99665 20.7005 4.00136 20.1795C4.00116 20.1215 4.00088 20.0635 4.00052 20.0055C3.99979 19.8474 4.00026 19.6893 4.00094 19.5312C4.00146 19.3603 4.00086 19.1893 4.0004 19.0183C3.99969 18.6839 4.00001 18.3495 4.00071 18.015C4.00126 17.7432 4.00141 17.4713 4.00128 17.1995C4.00126 17.1607 4.00124 17.122 4.00123 17.0821C4.00119 17.0034 4.00114 16.9246 4.0011 16.8459C4.00077 16.1505 4.00142 15.455 4.00258 14.7596C4.0037 14.0852 4.00398 13.4108 4.00337 12.7364C4.00272 12.0014 4.00256 11.2664 4.00325 10.5314C4.00332 10.453 4.00339 10.3746 4.00346 10.2962C4.0035 10.2576 4.00353 10.2191 4.00357 10.1793C4.00379 9.90784 4.00366 9.63637 4.0034 9.36489C4.0031 9.03398 4.00336 8.70309 4.00434 8.37219C4.00483 8.20344 4.00507 8.03471 4.00463 7.86597C4.00424 7.71135 4.00457 7.55675 4.00547 7.40214C4.00567 7.34636 4.00562 7.29058 4.00526 7.2348C4.00226 6.71397 4.13283 6.31533 4.48328 5.9248C4.8584 5.56447 5.31938 5.48805 5.81859 5.48851ZM5.2384 6.79907C5.14403 6.9249 5.15067 7.04914 5.15049 7.19995C5.15034 7.24729 5.15019 7.29463 5.15004 7.3434C5.15007 7.39621 5.1501 7.44901 5.15014 7.50182C5.15004 7.5577 5.14992 7.61359 5.14979 7.66947C5.14947 7.8234 5.1494 7.97734 5.14938 8.13127C5.1493 8.29715 5.14901 8.46302 5.14875 8.62889C5.14822 8.99175 5.14798 9.3546 5.14781 9.71745C5.1477 9.9439 5.14754 10.1704 5.14736 10.3968C5.14689 11.0235 5.14649 11.6502 5.14636 12.2769C5.14635 12.317 5.14634 12.3571 5.14633 12.3985C5.14632 12.4387 5.14631 12.4789 5.1463 12.5203C5.14629 12.6018 5.14627 12.6833 5.14625 12.7648C5.14624 12.8053 5.14623 12.8457 5.14623 12.8873C5.14607 13.5427 5.14539 14.1981 5.14448 14.8534C5.14356 15.5259 5.14308 16.1983 5.14303 16.8708C5.14299 17.2485 5.14277 17.6262 5.14206 18.004C5.14146 18.3255 5.14126 18.6471 5.14159 18.9687C5.14174 19.1328 5.1417 19.297 5.14115 19.4611C5.14066 19.6113 5.1407 19.7615 5.14118 19.9117C5.14125 19.9661 5.14113 20.0205 5.14079 20.0749C5.14036 20.1487 5.14072 20.2225 5.14112 20.2964C5.1411 20.3374 5.14107 20.3785 5.14105 20.4208C5.15797 20.5497 5.19598 20.6221 5.27249 20.7265C5.47142 20.8651 5.6589 20.8599 5.89216 20.8583C5.9502 20.8585 5.9502 20.8585 6.00942 20.8588C6.13898 20.8593 6.26852 20.8589 6.39808 20.8585C6.491 20.8587 6.58393 20.8589 6.67685 20.8592C6.90298 20.8596 7.12911 20.8596 7.35524 20.8592C7.53905 20.859 7.72285 20.8589 7.90666 20.8591C7.93283 20.8591 7.95901 20.8591 7.98598 20.8591C8.03915 20.8591 8.09233 20.8592 8.1455 20.8592C8.64396 20.8596 9.14242 20.8592 9.64088 20.8585C10.0684 20.858 10.496 20.8581 10.9236 20.8586C11.4203 20.8593 11.917 20.8595 12.4137 20.8592C12.4666 20.8591 12.5196 20.8591 12.5726 20.8591C12.5987 20.859 12.6247 20.859 12.6516 20.859C12.8351 20.8589 13.0187 20.8591 13.2022 20.8593C13.4493 20.8597 13.6965 20.8594 13.9436 20.8588C14.0343 20.8586 14.1251 20.8587 14.2158 20.8589C14.3396 20.8592 14.4633 20.8588 14.5871 20.8583C14.6231 20.8585 14.6591 20.8588 14.6962 20.859C14.8933 20.8572 15.0413 20.8458 15.2068 20.7265C15.3043 20.5638 15.3383 20.4429 15.3383 20.2542C15.3386 20.2069 15.339 20.1596 15.3393 20.1109C15.339 20.0588 15.3387 20.0067 15.3385 19.9545C15.3386 19.8989 15.3388 19.8433 15.339 19.7876C15.3395 19.6349 15.3393 19.4822 15.3389 19.3295C15.3386 19.1647 15.3391 19 15.3394 18.8352C15.3399 18.5125 15.3398 18.1898 15.3394 17.8672C15.3392 17.6049 15.3391 17.3427 15.3393 17.0804C15.3393 17.0431 15.3393 17.0058 15.3393 16.9674C15.3394 16.8916 15.3394 16.8158 15.3394 16.74C15.3398 16.0291 15.3394 15.3183 15.3388 14.6074C15.3382 13.9973 15.3383 13.3872 15.3389 12.7772C15.3395 12.0689 15.3397 11.3606 15.3394 10.6522C15.3393 10.5767 15.3393 10.5012 15.3393 10.4257C15.3392 10.37 15.3392 10.37 15.3392 10.3131C15.3391 10.0511 15.3393 9.7892 15.3396 9.52725C15.3399 9.20806 15.3398 8.88887 15.3392 8.56968C15.3389 8.4068 15.3388 8.24392 15.3391 8.08104C15.3395 7.93195 15.3393 7.78288 15.3387 7.63379C15.3385 7.57984 15.3386 7.52589 15.3389 7.47194C15.3392 7.39862 15.3388 7.32529 15.3383 7.25197C15.3383 7.21123 15.3383 7.1705 15.3383 7.12854C15.3167 6.95859 15.2568 6.8264 15.1313 6.70898C15.0009 6.63391 14.8801 6.6189 14.7313 6.61864C14.6824 6.61842 14.6824 6.61842 14.6326 6.61819C14.597 6.61822 14.5614 6.61826 14.5247 6.61829C14.4867 6.61818 14.4488 6.61806 14.4097 6.61794C14.2821 6.6176 14.1546 6.61754 14.027 6.61749C13.9357 6.61731 13.8444 6.61711 13.7531 6.6169C13.5048 6.61639 13.2564 6.61614 13.0081 6.61596C12.853 6.61585 12.698 6.61569 12.5429 6.61552C12.1134 6.61504 11.6839 6.61464 11.2544 6.61451C11.227 6.6145 11.1995 6.61449 11.1712 6.61448C11.1436 6.61447 11.1161 6.61447 11.0877 6.61446C11.0319 6.61444 10.9761 6.61442 10.9203 6.6144C10.8926 6.6144 10.8649 6.61439 10.8364 6.61438C10.3878 6.61422 9.93915 6.61354 9.49054 6.61264C9.02983 6.61171 8.56913 6.61123 8.10842 6.61118C7.8498 6.61115 7.59119 6.61091 7.33257 6.61022C7.08932 6.60957 6.84609 6.60949 6.60284 6.60983C6.51356 6.60986 6.42428 6.60969 6.335 6.60931C6.21312 6.60881 6.09126 6.60904 5.96937 6.60945C5.91638 6.60898 5.91638 6.60898 5.86232 6.6085C5.62176 6.61037 5.43185 6.64431 5.2384 6.79907Z"
                                :fill="svgFill"/>
                                <path d="M9.74423 2.00162C9.84218 2.00135 9.94013 2.00101 10.0381 2.00058C10.2757 1.99979 10.5132 1.9999 10.7508 2.00047C10.9441 2.00091 11.1373 2.00097 11.3305 2.00076C11.3581 2.00073 11.3857 2.0007 11.4141 2.00067C11.4702 2.0006 11.5263 2.00054 11.5823 2.00048C12.107 1.99992 12.6316 2.00056 13.1563 2.00161C13.6057 2.00248 14.055 2.00233 14.5044 2.00143C15.0273 2.00038 15.5502 1.99998 16.0731 2.00057C16.1289 2.00063 16.1848 2.0007 16.2406 2.00076C16.2818 2.0008 16.2818 2.0008 16.3239 2.00085C16.5167 2.00102 16.7095 2.00073 16.9023 2.00027C17.1622 1.99967 17.4221 2.0001 17.682 2.00123C17.7772 2.00148 17.8724 2.00141 17.9675 2.00098C18.5818 1.99846 19.0996 2.00949 19.5708 2.45536C19.9401 2.84572 20.0286 3.29952 20.0238 3.82055C20.024 3.87853 20.0243 3.93651 20.0247 3.99448C20.0254 4.15259 20.025 4.31067 20.0243 4.46877C20.0237 4.63974 20.0244 4.81071 20.0248 4.98168C20.0255 5.31611 20.0252 5.65053 20.0245 5.98497C20.0239 6.25682 20.0238 6.52867 20.0239 6.80052C20.0239 6.83927 20.024 6.87802 20.024 6.91794C20.024 6.99666 20.0241 7.07539 20.0241 7.15411C20.0244 7.84956 20.0238 8.54501 20.0226 9.24045C20.0215 9.91484 20.0212 10.5892 20.0218 11.2636C20.0225 11.9986 20.0226 12.7336 20.022 13.4686C20.0219 13.547 20.0218 13.6254 20.0217 13.7038C20.0217 13.7424 20.0217 13.7809 20.0216 13.8207C20.0214 14.0922 20.0215 14.3637 20.0218 14.6351C20.0221 14.966 20.0218 15.2969 20.0209 15.6278C20.0204 15.7966 20.0201 15.9653 20.0206 16.1341C20.021 16.2887 20.0206 16.4433 20.0197 16.5979C20.0195 16.6537 20.0196 16.7094 20.0199 16.7652C20.023 17.286 19.8924 17.6847 19.5419 18.0752C19.2255 18.3792 18.7964 18.5219 18.3613 18.5144C18.1853 18.495 18.0773 18.4178 17.9614 18.2893C17.8404 18.1145 17.832 17.9752 17.8567 17.7658C17.924 17.5746 18.0278 17.4673 18.2073 17.3763C18.3257 17.3468 18.4441 17.325 18.5642 17.3037C18.6802 17.2572 18.7327 17.2149 18.7985 17.1092C18.8741 16.9124 18.8516 16.6921 18.8499 16.4841C18.85 16.4282 18.8502 16.3722 18.8505 16.3162C18.851 16.1625 18.8505 16.0089 18.8499 15.8552C18.8494 15.6894 18.8497 15.5236 18.85 15.3578C18.8504 15.0331 18.85 14.7084 18.8492 14.3837C18.8484 14.0056 18.8484 13.6276 18.8485 13.2496C18.8485 12.5752 18.8478 11.9008 18.8467 11.2264C18.8456 10.5717 18.845 9.91709 18.8451 9.26245C18.8451 9.22213 18.8451 9.1818 18.8451 9.14026C18.8451 9.07988 18.8451 9.07988 18.8451 9.01828C18.8451 8.27328 18.8447 7.52829 18.844 6.7833C18.8438 6.5197 18.8437 6.25609 18.8436 5.99249C18.8436 5.67134 18.8432 5.3502 18.8425 5.02905C18.8422 4.86515 18.8419 4.70125 18.842 4.53735C18.8421 4.38738 18.8418 4.23741 18.8413 4.08744C18.8411 4.03312 18.8411 3.9788 18.8413 3.92449C18.8414 3.85076 18.8411 3.77704 18.8407 3.70331C18.8406 3.64184 18.8406 3.64184 18.8405 3.57912C18.8301 3.46215 18.8054 3.37813 18.7527 3.27348C18.5802 3.16207 18.3717 3.18472 18.1735 3.18567C18.1345 3.18555 18.0955 3.18542 18.0553 3.1853C17.9244 3.18499 17.7936 3.18523 17.6627 3.18548C17.569 3.18537 17.4752 3.18523 17.3815 3.18507C17.1532 3.18475 16.9249 3.18479 16.6966 3.18502C16.5111 3.1852 16.3256 3.18522 16.14 3.18513C16.1136 3.18512 16.0872 3.18511 16.06 3.1851C16.0064 3.18507 15.9527 3.18505 15.8991 3.18502C15.396 3.1848 14.893 3.18506 14.39 3.18547C13.9584 3.18582 13.5268 3.18576 13.0952 3.1854C12.5939 3.18499 12.0927 3.18482 11.5915 3.18506C11.538 3.18509 11.4846 3.18511 11.4311 3.18513C11.4048 3.18515 11.3785 3.18516 11.3514 3.18517C11.1661 3.18524 10.9808 3.18512 10.7955 3.18494C10.5461 3.1847 10.2967 3.18487 10.0473 3.18532C9.95564 3.18543 9.864 3.1854 9.77236 3.18522C9.64747 3.18501 9.52259 3.18528 9.3977 3.18567C9.36127 3.18549 9.32483 3.18532 9.28729 3.18514C9.04379 3.18642 9.04379 3.18642 8.81845 3.27348C8.76312 3.36666 8.74303 3.45301 8.72562 3.55942C8.68657 3.79551 8.64286 3.9249 8.46783 4.09159C8.30198 4.19076 8.15014 4.19529 7.96137 4.16951C7.77159 4.10212 7.65845 4.00031 7.57179 3.81889C7.50775 3.36153 7.61489 2.97523 7.87615 2.59658C8.37303 1.98901 9.02117 1.99834 9.74423 2.00162Z"
                                :fill="svgFill"/>
                                </svg>
                            </div>
                    </div>
                </div>

                <div class="mt-[30px] sm:mt-4 flex flex-col justify-center items-center gap-5">
                    <button type="submit"
                        class="inline-flex justify-center items-center gap-2 rounded bg-bg-100 w-full h-10  text-base font-medium  text-txt-1000 shadow-sm hover:bg-bg-150" @click="emit(handleSchedule)">
                        Schedule & send back
                    </button>
                    <button type="button"
                        class="inline-flex justify-center items-center gap-2 rounded bg-bg-850 w-full h-10 text-base font-medium text-txt-100 shadow-sm hover:bg-bg-900" @click="handleClose()">
                    back
                    </button>
            </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.no-scrollbar::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
}

.no-scrollbar {
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
}
</style>

import type { leadAnalyticsQuery } from '@/types/leads';
import { GetRequestWithHeadersAndParms, PostRequest } from '../../helpers/apihelper';
import type { EventDetailsParams, EventDetailsRow, ReportRow } from '@/types/analyticsModel';
import { analytics_store } from '@/store/analytics';
const api_url = import.meta.env.VITE_API_URL;
const Store = analytics_store();
export async function GetSessionAnalytics (params: leadAnalyticsQuery, page:number, limit:number): Promise<object> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeadersAndParms({
      url: `${api_url}/session/GetAnalytics?page=${page}&limit=${limit}`, params: params,
    }).then((res) => {
      Store.analyticsData = res as object;
      resolve(res as object);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function GetSessionGoogleAnalytics (startDate:string, endDate:string, orgId:string, projectId:string):Promise<ReportRow[]> {
  const url = `${api_url}/analytics/getReports`;
  const body = {
    startDate,
    endDate,
    orgId,
    projectId,
  };

  try {
    const response = await PostRequest({ url, body });
    if (response) {
      return response as ReportRow[];
    }
    throw new Error('No response from the API');

  } catch (error) {
    console.error('Error fetching Google Analytics data:', error);
    throw error;
  }
}

export async function GetEventDetails ({startDate, endDate, orgId, projectId, eventName, customFields}:EventDetailsParams):Promise<EventDetailsRow[]> {
  const url = `${api_url}/analytics/getEventReport`;
  const body = {
    startDate,
    endDate,
    orgId,
    projectId,
    eventName,
    customFields,
  };

  try {
    const response = await PostRequest({ url, body });
    if (response) {
      return response as EventDetailsRow[];
    }
    throw new Error('No response from the API');

  } catch (error) {
    console.error('Error fetching event details:', error);
    throw error;
  }
}

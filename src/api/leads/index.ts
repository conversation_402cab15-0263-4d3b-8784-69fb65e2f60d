import type { createLeadInput, Leads, updateLeadInput } from '@/types/leads';
import { PostRequestWithHeaders } from '../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function GetLeads (params:string):Promise<object>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/lead/GetLeads${params ? params: '' }`}).then((res) => {
      resolve(res as object);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function CreateLead (values:createLeadInput):Promise<Leads | void>{

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/lead/CreateLead`, body: {
      ...values,
    }}).then((res) => {
      resolve(res as Leads);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function EditLead (values:updateLeadInput):Promise<Leads>{

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/lead/UpdateLead`, body: {
      ...values,
    }}).then((res) => {
      resolve(res as Leads);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function DeleteLead (lead_id:string) :Promise<Leads | void>{

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/lead/DeleteLead/${lead_id}`}).then((res) => {
      resolve(res as Leads);
    }).catch((error) => {
      reject(error);
    });
  });
}

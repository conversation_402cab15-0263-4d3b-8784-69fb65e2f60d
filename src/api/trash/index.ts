import { GetRequestWithHeaders, PostRequestWithHeaders } from "@/helpers/apihelper";
const api_url = import.meta.env.VITE_API_URL;

export async function GetAllTrash (project_id?: string, organization_id?:string, type?: string, page?: number) {
  return new Promise((resolve, reject) => {
    const url = project_id && type
      ? `${api_url}/trash/getAllTrash?project_id=${project_id}&type=${type}&page=${page}` :
      organization_id && type ? `${api_url}/trash/getAllTrash?organization_id=${organization_id}&type=${type}&page=${page}`
        : `${api_url}/trash/getAllTrash`;
    GetRequestWithHeaders({ url }).then((res) => {
      resolve(res);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function RestoreTrash (payload: object, project_id: string, entity: string, action: string){
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/${entity}/${action}/${project_id}`, body: {...payload}}).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function DeleteTrash (trash_id: string[]){
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/trash/deleteTrash`, body: {trash_id: trash_id}}).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

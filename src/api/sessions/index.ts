import type { createSessionInput, GetAnalyticsType, GetAvailableSloteType, SessionControl, Session, sessionAnalyticsQuery, CheckAvailableSessionSlotsType, CheckAvailableSessionSlotsResponse, FetchSessionSlotsType, FetchSessionSlotsResponse, CheckInstanceAvailability } from '@/types/session';
import { GetRequestWithHeadersAndParms, PostRequestWithHeaders} from '../../helpers/apihelper';
import { getCookie } from '../../helpers/domhelper';
import { objectToQueryString } from '../../helpers/helpers';

const api_url = import.meta.env.VITE_API_URL;

export async function GetAnalytics (values:GetAnalyticsType): Promise<object | void> {
  return new Promise((resolve, reject) => {
    const organizationId = getCookie('organization');

    GetRequestWithHeadersAndParms({url: `${api_url}/session/GetAnalytics`, params: {
      organization_id: organizationId,
      ...(values !== null && values!== undefined && values.userId && {user_id: values.userId}),
      ...(values !== null && values!== undefined && values.projectId && {project_id: values.projectId}),
      ...(values !== null && values!== undefined && values.startDate && {start_date: values.startDate}),
      ...(values !== null && values!== undefined && values.endDate && {end_date: values.endDate}),
      ...(values !== null && values!== undefined && values.type && {type: values.type}),
      ...(values !== null && values!== undefined && values.source && {source: values.source}),
      ...(values !== null && values!== undefined && values.source && {tag: values.tag}),
    }}).then((res) => {
      resolve(res as object);
    }).catch((error) => {
      reject(error);
    });

  });

}

export async function GetSessions (type:sessionAnalyticsQuery){
  const params = objectToQueryString(type);
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/session/GetSessions`+params })
      .then((res) => {
        resolve(res);
      }).catch((error) => {
        reject(error);
      });
  });

}
export async function CheckAvailableSessionSlots (DTO:CheckAvailableSessionSlotsType): Promise<CheckAvailableSessionSlotsResponse >{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/session/CheckAvailableSessionSlots`, body: {
      ...DTO,
    }}).then((res) => {
      resolve(res as CheckAvailableSessionSlotsResponse);
    }).catch((error) => {
      reject(error);
    });
  });

}
export async function getListOfSessionSlots (DTO:FetchSessionSlotsType): Promise<FetchSessionSlotsResponse[] >{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/session/getListOfSessionSlots`, body: {
      ...DTO,
    }}).then((res) => {
      resolve(res as FetchSessionSlotsResponse[]);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function createSession (DTO:createSessionInput): Promise<Session >{

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/session/createSession`, body: {
      ...DTO,
    }}).then((res) => {
      resolve(res as Session);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function GetAvailableSlots (values:GetAvailableSloteType): Promise<object[] | null>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/session/GetAvailableSlots`, body: {
      project_id: values.project_id,
      date: new Date(values.date),
      zone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    }}).then((res) => {
      resolve(res as object[] | null);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function RejoinSession (values:SessionControl): Promise<Session | void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/session/RejoinSession`, body: {
      session_id: values.session_id,
    }}).then((res) => {
      resolve(res as Session);
    }).catch((error) => {
      reject(error);
    });
  });

}

export  async function CancelSession (values:SessionControl): Promise<Session | void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/session/CancelSession`, body: {
      session_id: values.session_id,
    }}).then((res) => {
      resolve(res as Session);
    }).catch((error) => {
      reject(error);
    });
  });

}

export  async function EndSession (values:SessionControl): Promise<Session | void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/session/EndSession`, body: {
      session_id: values.session_id,
    }}).then((res) => {
      resolve(res as Session);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function CheckInstanceAvailabilityApi (values: CheckInstanceAvailability): Promise<Session | void> {
  try {
    const response = await PostRequestWithHeaders({
      url: `${api_url}/session/CheckInstanceAvailability`,
      body: values,
    });
    return response as Session;
  } catch (error) {
    console.error("Error in CheckInstanceAvailabilityApi:", error);
    throw error;
  }
}

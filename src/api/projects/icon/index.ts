import { PostRequestWithHeaders, GetRequestWithHeaders } from "../../../helpers/apihelper";
import type {iconLibrary, allIcons} from '@/types/iconLibrary';
const api_url = import.meta.env.VITE_API_URL;

export async function createIcon (data: iconLibrary):Promise<iconLibrary> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/icon/createIcon`,
      body: data,
    })
      .then((res) => {
        resolve(res as iconLibrary);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function getIcons ():Promise<allIcons> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({
      url: `${api_url}/icon/getIcon`,
    })
      .then((res) => {
        resolve(res as allIcons);
      })
      .catch((err: unknown) => {
        reject(err);
      });
  });
}

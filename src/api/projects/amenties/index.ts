import type { Amenity, bulkUpdateType, CreateAmenityInput, DeleteMedia, moveAmenityToTrash, UpdateAmenityInput } from '@/types/amenity';
import { GetRequestWithHeaders, GetRequestWithHeadersAndParms, PostRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function getListOfAmenities (project_id :string, category :string | void) :Promise<Record<string, Amenity> | null>  {

  return new Promise((resolve, reject) => {
    GetRequestWithHeadersAndParms({
      url: `${api_url}/amenity/GetAmenities`, params: {
        project_id: project_id,
        category: category,
      },
    }).then((res) => {
      resolve(res as Record<string, Amenity>);
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function amenityDataSyncUp (projectId :string) : Promise<string> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/amenity/syncUpAmenityData/${projectId}` }).then((res) => {
      resolve(res as string);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function CreateMedia (data :FormData) : Promise<void> {

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/amenity/CreateMedia`, body: data }).then(() => {
      resolve();
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function UpdateMedia (data :FormData) : Promise<void> {

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/amenity/UpdateMedia`, body: data }).then(() => {
      resolve();
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function CreateAmenity (data :CreateAmenityInput) : Promise<Amenity> {

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/amenity/CreateAmenity`, body: data,
    }).then((res) => {
      resolve(res as Amenity);
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function DeleteMedia (data: DeleteMedia ) : Promise<Amenity | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/amenity/DeleteMedia`, body: {
        ...data,
      },
    }).then((res) => {
      resolve(res as Amenity);
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function UpdateAmenity (data : UpdateAmenityInput)  : Promise <Amenity>{

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/amenity/UpdateAmenity`, body: data,
    }).then((res) => {
      resolve(res as Amenity);
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function DeleteAmenity (data :object) : Promise<void>{

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/amenity/DeleteAmenity`, body: {
        ...data,
      },
    }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function moveAmenityToTrash (data :moveAmenityToTrash, project_id :string) :Promise<void> {

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/amenity/moveToTrash/${project_id}`, body: {
        ...data,
      },
    }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function getCategories (projectId :string) :Promise<string[] | null>  {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/amenity/getCategories?project_id=${projectId}` }).then((res) => {
      resolve(res as string[]);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function getAmenityCategories (projectId :string, category_id :string) :Promise<string[] | null> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({
      url: `${api_url}/amenity/getCategories?project_id=${projectId}&category=${category_id}`,
    })
      .then((res) => {
        resolve(res as string[]);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export async function updateBulkAmenity (data :bulkUpdateType) : Promise<string | null> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/amenity/updateBulkAmenity`,
      body: data,
    })
      .then((res) => {
        resolve(res as string);
      })
      .catch((error) => {
        reject(error);
      });
  });
}

export async function updateAmenitySettings (data:bulkUpdateType):Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/project/updateAmenitySettings`, body: data }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });

  });
}

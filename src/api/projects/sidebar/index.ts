import type { createSidebarType, sidebarTrashType, sidebarType, updatesidebarQuery, updateSidebarType } from '@/types/sidebar';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function getListofSidebarOptions (project_id:string):Promise <Record<string, sidebarType>> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/sidebar/getOptions/${project_id}`}).then((res) => {
      resolve(res as Record<string, sidebarType>);
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function createSidebarOption (data :createSidebarType) : Promise<sidebarType | void>  {
  return new Promise((resolve, reject) => {

    PostRequestWithHeaders({ url: `${api_url}/sidebar/createOptions`, body: data }).then((res) => {
      resolve(res as  sidebarType);
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function editSidebarOption (data :updatesidebarQuery) : Promise<sidebarType | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/sidebar/updateOptions`, body: data }).then((res) => {
      resolve(res as sidebarType);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function moveSidebarToTrash (data: sidebarTrashType, project_id : string) : Promise < sidebarType | void > {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/sidebar/moveToTrash/${project_id}`, body: data }).then((res) => {
      resolve(res as sidebarType);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateBulkOptions (data : updateSidebarType) : Promise<string |void> {
  return new Promise((resolve, reject) => {

    PostRequestWithHeaders({ url: `${api_url}/sidebar/updateBulkOptions`, body: data }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function deleteSidebarOption (data : object) : Promise<string | void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/sidebar/deleteOption`, body: data }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });

  });
}

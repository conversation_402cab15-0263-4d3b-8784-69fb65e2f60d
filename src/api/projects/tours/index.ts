import type { createSceneData, createVirtualTourData, DeleteVirtualTour, Group, Image, Link, ListSceneInTourType, SubGroup, updateVirtualTourData, VirtualTour, Label } from '@/types/virtualTour';
import {
  PostRequestWithHeaders, GetRequestWithHeaders,
} from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function CreateVirtualTour (data:createVirtualTourData):Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/CreateVirtualTour`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function DeleteVirtualTour (data:DeleteVirtualTour):Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/deleteTour`,
      body: data,
    })
      .then(() => {
        resolve();
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function UpdateVirtualTour (data:updateVirtualTourData):Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateTour`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function AddTourLabel (data: Label): Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/AddTourLabel`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function UpdateTourLabel (data: Label): Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/UpdateTourLabel`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function DeleteTourLabel (data: Label): Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/DeleteTourLabel`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function addImageToTour (data: Image): Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/addImageToTour`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function updateTourImage (data: Image): Promise<VirtualTour | void> {
  return new Promise((resolve) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateTourImage`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      });
  });
}

export async function deleteImageFromTour (data: Image): Promise<VirtualTour | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/deleteImageFromTour`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function duplicateTourImage (data: Image): Promise<VirtualTour | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/duplicateTourImage`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function addLink (data: Link ): Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/addLink`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function updateLink (data: Link ): Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateLink`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function deleteLink (data: Link ): Promise<VirtualTour |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/deleteLink`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function addGroupToTour (data: Group ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/addGroupToTour`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function updateGoupIdImg (data: Image ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateGoupIdImg`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function updateTourGroup (data: Group ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateTourGroup`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function updateGroupOrder (data: Group ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateGroupOrder`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function deleteTourGroup (data: Group ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/deleteTourGroup`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function addSubGroup (data: SubGroup ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/addSubGroup`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function updateSubGroupIdImg (data: Image ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateSubGroupIdImg`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function updateSubGroup (data: SubGroup ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateSubGroup`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function updateSubGroupOrder (data: SubGroup ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/updateSubGroupOrder`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function deleteSubGroup (data: SubGroup ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/deleteSubGroup`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function moveToUnCategory (data: SubGroup ): Promise<VirtualTour> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/moveToUnCategory`,
      body: data,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function GetVirtualTourList (project_id:string):Promise<Record<string, VirtualTour> | null> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({
      url: `${api_url}/virtualTour/getAllTour/${project_id}`,
    })
      .then((res) => {
        resolve(res as Record<string, VirtualTour>);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function getTourById (project_id:string, tour_id:string): Promise<Record<string, VirtualTour> | void> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({
      url: `${api_url}/virtualTour/getTourById/${project_id}/${tour_id}`,
    })
      .then((res) => {
        resolve(res as Record<string, VirtualTour>);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function GetStructuredTourById (project_id:string, tour_id:string): Promise<Record<string, VirtualTour> | void> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({
      url: `${api_url}/virtualTour/getStructuredTourById/${project_id}/${tour_id}`,
    })
      .then((res) => {
        resolve(res as Record<string, VirtualTour>);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function AddSceneToTour (formdata :createSceneData) : Promise<VirtualTour | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/AddSceneToTour`,
      body: formdata,
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

export async function ListSceneInTour (data : ListSceneInTourType) : Promise<Record<string, VirtualTour> | null> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/ListScenesInTour`,
      body: data,
    })
      .then((res) => {
        resolve(res as Record<string, VirtualTour>);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

// CreateVirtualTour(data) {
//     Return new Promise((resolve, reject) => {
//       Const current_organization = getCookie("organization");
//       Console.log("data", data);
//       If (current_organization) {
//         PostRequestWithHeaders({
//           Url: `${api_url}/virtualTour/CreateVirtualTour`,
//           Body: data,
//         })
//           .then((res) => {
//             Resolve(res);
//           })
//           .catch((err) => {
//             Reject(err);
//           });
//       }
//     });
//   },

export async function ConvertExternalTour (tour_id: string, project_id: string, link: string): Promise<VirtualTour | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/virtualTour/convertExternalTour`,
      body: {
        tour_id: tour_id,
        project_id: project_id,
        link: link,
      },
    })
      .then((res) => {
        resolve(res as VirtualTour);
      })
      .catch((err) => {
        reject(err);
      });
  });
}

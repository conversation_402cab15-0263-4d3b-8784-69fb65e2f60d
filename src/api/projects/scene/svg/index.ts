import type { projectSVG, updatePayload } from '@/types/projectSVG';
import { PostRequestWithHeaders } from '../../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function createSVG (data: object): Promise<projectSVG> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectSVG/createSVG`, body: data }).then((res) => {
      resolve(res as projectSVG);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function updateLayers (payload: updatePayload) {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectSVG/updateLayers`, body: payload }).then((res) => {
      resolve(res as object);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}
export async function updateLayersVideoTag (payload:updatePayload) {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectSVG/updateLayersVideoTag`, body: payload }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function createLayers (payload: updatePayload): Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectSVG/createLayers`, body: payload }).then((res) => {
      resolve(res as object);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

/* UpdatesvgLayer */
export async function updatesvgLayer (payload: updatePayload): Promise<object> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectSVG/updatesvgLayer`, body: payload }).then((res) => {
      resolve(res as object);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function moveSvgToTrash (payload: object, project_id: string): Promise<projectSVG> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectSVG/moveToTrash/${project_id}`, body: payload }).then((res) => {
      resolve(res as projectSVG);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function deleteLayer (payload: object): Promise<string>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectSVG/deleteLayer`, body: payload }).then((res) => {
      resolve(res as string);
    }).catch((err) => {
      reject(err);
    });
  });
}

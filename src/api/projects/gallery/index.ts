import type { bulkUpdateType, MoveGalleryItemToTrash } from '@/types/amenity';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';
import type { createGalleryItem, galleryItem, updateGalleryItemType } from '@/types/gallery';

const api_url = import.meta.env.VITE_API_URL;

export async function getListofGalleryItems (projectId :string) : Promise<Record<string, galleryItem>> {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/gallery/getGallery?project_id=${projectId}` }).then((res) => {
      resolve(res as Record<string, galleryItem>);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function galleryDataSyncUp (projectId :string) : Promise<string> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/gallery/syncUpGalleryData/${projectId}` }).then((res) => {
      resolve(res as string);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function getListofGalleryItemsByCategory (projectId :string, category_id :string) : Promise<Record<string, galleryItem> | void> {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/gallery/getGallery?project_id=${projectId}&category=${category_id}` }).then((res) => {
      resolve(res as Record<string, galleryItem>);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function addGalleryItem (values : createGalleryItem) : Promise<galleryItem | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/gallery/creategalleryitem`, body: values,
    }).then((res) => {
      resolve(res as galleryItem);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function updateGalleryItem (values : updateGalleryItemType) : Promise<galleryItem | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/gallery/updateGalleryItem`, body: values,
    }).then((res) => {
      resolve(res as galleryItem);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function deleteGalleryItem (values : object) :Promise<galleryItem |void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/gallery/deleteGalleryItem`, body: values,
    }).then((res) => {
      resolve(res as galleryItem);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function moveGalleryItemToTrash (values :MoveGalleryItemToTrash, project_id : string) : Promise<galleryItem |void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/gallery/moveToTrash/${project_id}`, body: values,
    }).then((res) => {
      resolve(res as galleryItem);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function getCategories (projectId :string) :Promise<string[] | null> {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/gallery/getCategories?project_id=${projectId}` }).then((res) => {
      resolve(res as string[]);
    }).catch((error) => {
      reject(error);
    });
  });

}

export async function updateBulkGalleryItems (data : bulkUpdateType) : Promise<string> {
  return new Promise((resolve, reject) => {

    PostRequestWithHeaders({ url: `${api_url}/gallery/updateBulkGalleryItems`, body: data }).then((res) => {
      resolve(res as string);
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function updateGallerySettings (data : bulkUpdateType): Promise<string | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/project/updateGallerySettings`, body: data }).then((res) => {
      resolve(res as string);
    }).catch((err) => {
      reject(err);
    });

  });
}

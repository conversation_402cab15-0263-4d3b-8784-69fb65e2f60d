import type {createProjectLandmark, getAllRoutesInput, getRoutesInput, projectLandmark, projectLandmarkTrashType, saveRoutes, updateProjectLandmark, updateProjectLandmarkFiles } from '@/types/projectLandmark';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function createLandmark (data : createProjectLandmark) : Promise<projectLandmark | void > {
  return new Promise((resolve, reject) => {

    PostRequestWithHeaders({ url: `${api_url}/projectLandmark/createLandmark`, body: data }).then((res) => {
      resolve(res as projectLandmark);
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function updateLandmark (data :updateProjectLandmark): Promise<projectLandmark | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectLandmark/updateLandmark`, body: data }).then((res) => {
      resolve(res as projectLandmark);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateLandmarkFiles (data: updateProjectLandmarkFiles) : Promise <void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectLandmark/updateLandmarkFiles`, body: data }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function moveLandmarkToTrash (values:projectLandmarkTrashType, project_id :string ):Promise <void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectLandmark/moveToTrash/${project_id}`, body: values }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function getListofLandmark (projectId : string) : Promise <Record<string, projectLandmark>> {
  return new Promise((resolve, reject) => {

    GetRequestWithHeaders({ url: `${api_url}/projectLandmark/getListofLandmark/${projectId}` }).then((res) => {
      resolve(res as Record<string, projectLandmark> );
    }).catch((err) => {
      reject(err);
    });

  });
}

export async function saveRoutes (data : saveRoutes) :  Promise<string> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectLandmark/saveRoutes`, body: data}).then((res) => {
      resolve(res as string);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function getRoute (data : getRoutesInput) : Promise <void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectLandmark/getRoutes`, body: data}).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function fetchNearbyLandmarks (data : getAllRoutesInput) : Promise <boolean | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/projectLandmark/fetchNearbyLandmarks`, body: data}).then((res) => {
      resolve(res as boolean);
    }).catch((err) => {
      reject(err);
    });
  });
}

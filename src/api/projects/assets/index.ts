import type { assets, AssetsObject, createAssets, deleteAsset, updateAssets, updateSettingsFile, updateSettingsFileResponse } from '@/types/asset';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;
export async function getListofAssets (projectId: string): Promise<AssetsObject> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/assets/getAssetsList/${projectId}` }).then((res) => {
      resolve(res as AssetsObject);
    }).catch((error: unknown) => {
      reject(error);
    });
  });
}

export async function updateSettingsFile (values:updateSettingsFile):Promise<updateSettingsFileResponse> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/project/uploadSettingFiles`, body: values,
    }).then((res) => {
      resolve(res as updateSettingsFileResponse);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function createAsset (values:createAssets) : Promise<assets> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/assets/createAsset`, body: values,
    }).then((res) => {
      resolve(res as assets);
    }).catch((error:unknown) => {
      reject(error);
    });
  });
}

export async function updateAsset (values:updateAssets):Promise<assets> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/assets/updateAsset`, body: values,
    }).then((res) => {
      resolve(res as assets);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function deleteAsset (values:deleteAsset): Promise<assets | void>  {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/assets/deleteAsset`, body: values,
    }).then((res) => {
      resolve(res as assets);
    }).catch((error) => {
      reject(error);
    });
  });
}

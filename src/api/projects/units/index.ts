import type { createUnits, Units, unitTrashType, updateUnits } from '@/types/units';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function CreateUnit (data : createUnits): Promise<Units | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unit/CreateUnit`, body: data }).then((res) => {
      resolve(res as Units);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function AddBulkUnits (data:object[]):Promise<Units | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unit/BulkCreateUnit`, body: data }).then((res) => {
      resolve(res as Units);
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function updateUnit (payload : updateUnits, unit_id:string): Promise<Units | void> {

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unit/updateUnit/${unit_id}`, body: payload }).then((res) => {
      resolve(res as Units);
    }).catch((err) => {
      reject(err);
    });
  });

}

export async function deleteUnit (project_id:string, unit_id:string) :Promise<Units | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unit/deleteUnit/${unit_id}`, body: { project_id: project_id } }).then((res) => {
      resolve(res as Units);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function moveUnitToTrash (values : unitTrashType, project_id:string):Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/unit/moveToTrash/${project_id}`, body: values}).then(() => {
      resolve( );
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function getListofUnits (project_id:string) :Promise <Record<string, Units>> {

  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/unit/getListofUnits/${project_id}` }).then((res) => {
      resolve(res as Record<string, Units>);
    }).catch((err) => {
      reject(err);
    });
  });

}

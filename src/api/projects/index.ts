import type { createProjectInput, editProjectInput, MoveProjectToTrash, Project } from '@/types/projects';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../helpers/apihelper';

const api_url = import.meta.env.VITE_API_URL;

export async function CreateProject (payload: createProjectInput) : Promise<Record<string, Project>|void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/project/CreateProject`, body: payload}).then((res) => {
      resolve(res as Record<string, Project>);
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function EditProject (payload: editProjectInput) : Promise<Record<string, Project>|void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/project/EditProject`, body: payload}).then((res) => {
      resolve(res as Record<string, Project>);
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function GetProjectById (projectId: string) : Promise<Record<string, Project>|void> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/project/GetProject/${projectId}`}).then((res) => {
      resolve(res as Record<string, Project>);
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function MoveProjectToTrash (project_id: string, values:MoveProjectToTrash) : Promise<string | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/project/moveToTrash/${project_id}`, body: values,
    }).then((res) => {
      resolve(res as string);
    }).catch((error) => {
      reject(error);
    });
  });
}
export async function RestoreProject (trash_id: string) : Promise<string | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/project/restoreProject`, body: {trash_id: trash_id},
    }).then((res) => {
      resolve(res as string);
    }).catch((error) => {
      reject(error);
    });
  });
}

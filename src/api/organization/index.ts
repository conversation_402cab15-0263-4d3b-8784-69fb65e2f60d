import type { Organization, Role, UpdateOrganizationInput, UserRole, User, unique_org_id } from './../../types/organization';
import { GetRequestWithHeaders, PostRequestWithHeaders } from '../../helpers/apihelper';
import { getCookie } from '../../helpers/domhelper';
import type { Users } from '@/types/user';
import type { Project } from '@/types/projects';
import axios from 'axios';
const api_url = import.meta.env.VITE_API_URL;

export async function CreateOrganization (payload: FormData): Promise<Organization> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/org/CreateOrganization`,
      body: payload,
    }).then((res) => {
      resolve(res as Organization);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function GetOrganization ():Promise<Organization>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/org/GetOrganization`, body: {},
    }).then((res) => {
      resolve(res as Organization);
    }).catch((error: unknown) => {
      reject(error);
    });
  });
}

export async function UpdateOrganization (formData: UpdateOrganizationInput): Promise<Organization>{

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/org/UpdateOrganization`, body: formData}).then((res) => {
      resolve(res as Organization);
    }).catch((error) => {
      reject(error);
    });
  });

}
export async function Checkunique_org_id (unique_org_id: string): Promise<unique_org_id> {
  try {
    const response = await axios.get(`${api_url}/org/${unique_org_id}/checkUserIDExistence`);

    return response.data;
  } catch (error) {

    console.error("Error checking unique_org_id:", error);
    throw error;
  }
}

export async function GetRolesFromUser ():Promise<Role|null>{
  const current_organization = getCookie('organization');

  return new Promise((resolve, reject) =>  {
    PostRequestWithHeaders({ url: `${api_url}/org/GetRolesFromUser`, body: {
      organization_id: current_organization,
    }}).then((res) => {
      resolve(res as Role);
    }).catch((error:unknown) => {
      reject(error);
    });
  });

}

export async function ListUsersInOrganization (filterQuery:string[]):Promise<Users[]> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/org/ListUsersInOrganization`, body: {filter: filterQuery}}).then((res) => {
      resolve(res as Users[]);
    }).catch((error: unknown) => {
      reject(error);
    });
  });
}

export async function AssignRoleToUser (user_id: string, role: UserRole):Promise<boolean> {
  const current_organization = getCookie('organization');
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/org/AssignRoleToUser`, body: {
      organization_id: current_organization,
      user_id: user_id,
      role: role,
    }}).then((res) => {
      resolve(res as boolean);
    }).catch((error : unknown) => {
      reject(error);
    });
  });

}

export async function AddUserToOrganization (values: User):Promise<object>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/org/AddUserToOrganization`, body: {
      ...values,
    }}).then((res) => {
      resolve(res as object);
    }).catch((error: unknown) => {
      reject(error);
    });
  });

}

export async function RemoveUserFromOrganization (email: string):Promise<boolean>{

  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/org/RemoveUserFromOrganization`, body: {
      email: email,
    }}).then((res) => {
      resolve(res as boolean);
    }).catch((error: unknown) => {
      reject(error);
    });
  });

}

export async function ListProjectsFromOrganization (): Promise<Record<string, Project>> {
  return new Promise ((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/org/ListProjectsFromOrganization`}).then((res) => {
      resolve(res as Record<string, Project>);
    }).catch((error: unknown) => {
      reject(error);
    });
  });
}
export async function FilterProjectsFromOrganization (filters:object, page:number): Promise<Record<string, Project>> {
  return new Promise ((resolve, reject) => {
    PostRequestWithHeaders({url: `${api_url}/org/FilterProjectsFromOrganization`,
      body: {
        filters: filters,
        page: page,
      },
    })
      .then((res) => {
        resolve(res as Record<string, Project>);
      }).catch((error: unknown) => {
        reject(error);
      });
  });
}

export async function GetNotifications (user_id: string, viewed?: boolean): Promise<Notification[]> {
  return new Promise((resolve, reject) => {
    let url = `${api_url}/notification/getNotifications?user_id=${user_id}`;

    if (viewed === false) {
      url += '&viewed=false';
    }

    GetRequestWithHeaders({ url: url })
      .then((res) => {
        resolve(res as Notification[]);
      })
      .catch((error: unknown) => {
        reject(error);
      });
  });
}

export async function UpdateNotifications (id: string, payload: object): Promise<Notification> {
  return new Promise((resolve, reject) => {
    const url = `${api_url}/notification/updateNotifications/${id}`;

    PostRequestWithHeaders({ url: url, body: payload })
      .then((res) => {
        resolve(res as Notification);
      })
      .catch((error: unknown) => {
        reject(error);
      });
  });
}

/* export async function  getListOfMasterScenes ():Promise<Record<string, masterScene>> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/masterScene/getAllScenes` }).then((res) => {
      resolve(res as Record<string, masterScene>);
    }).catch((err) => {
      reject(err);
    });
  });
} */

/* export async function createScene (data: object) {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/createScene`, body: data }).then((res) => {
      resolve(res);
    }).catch((err) => {
      reject(err);
    });
  });
}
 */

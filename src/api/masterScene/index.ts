import { PostRequestWithHeaders, GetRequestWithHeaders } from '../../helpers/apihelper';
import type { convertDeepZoom, coordinate, deleteCoordinate, masterScene, transformedMasterScene, updateCoordinate, updateMasterScene, updateBulkSceneType } from '@/types/masterScene';
import type {  createSceneInputType } from '@/types/projectScene';

const api_url = import.meta.env.VITE_API_URL;

export async function getScene (id:string): Promise<transformedMasterScene> {
  return new Promise((resolve, reject) => {
    const sceneId = id;
    GetRequestWithHeaders({ url: `${api_url}/masterScene/getScene/${sceneId}` }).then((res) => {
      resolve(res as transformedMasterScene);
    }).catch((error) => {
      reject(error);
    });
  });
}

export async function  getAllScenes (): Promise<transformedMasterScene | null> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/masterScene/getAllScenes` }).then((res) => {
      resolve(res as transformedMasterScene | null);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function moveMasterSceneToTrash (payload: object): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/moveToTrash`, body: payload }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}
export async function restoreMasterScenes (trash_id:string, scene_id:string): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/restoreScenes`, body: {trash_Id: trash_id, scene_Id: scene_id} }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function convertMasterSceneDeepZoom (payload: convertDeepZoom): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/convertSceneType`, body: payload }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function createMasterScene (payload: createSceneInputType): Promise<masterScene> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/createScene`, body: payload }).then((res) => {
      resolve(res as masterScene);
    }).catch((err: unknown) => {
      reject(err);
    });
  });
}

export async function createCoordinates (data:coordinate): Promise<masterScene | void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/createCoordinates`, body: data}).then((res) => {
      resolve(res as masterScene);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function deleteCoordinate (data:deleteCoordinate): Promise<masterScene | void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/deleteCoordinate`, body: data}).then((res) => {
      resolve(res as masterScene);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateCoordinate (data:updateCoordinate): Promise<masterScene | void>{
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/updateCoordinate`, body: data},
    ).then((res) => {
      resolve(res as masterScene);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateSceneFiles (payload: updateMasterScene): Promise<masterScene> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/updateSceneFiles`, body: payload }).then((res) => {
      resolve(res as masterScene);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateMasterScene (data:updateMasterScene):Promise<masterScene> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({
      url: `${api_url}/masterScene/updateScene`,
      body: data,
    }).then((res) => {
      resolve(res as masterScene);
    }).catch((err) => {
      reject(err);
      console.log(err);
    });
  });
}

export async function getAllScenesFrames (parent_scene_id: string): Promise<transformedMasterScene | null> {
  return new Promise((resolve, reject) => {
    GetRequestWithHeaders({ url: `${api_url}/masterScene/getAllScenes?type=rotatable_image_frame&parent=${parent_scene_id}` }).then((res) => {
      resolve(res as transformedMasterScene);
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function updateBulkSceneFrames (payload: updateBulkSceneType): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/updateBulkSceneFrames`, body: payload }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

export async function convertDeepZoom (payload: convertDeepZoom): Promise<void> {
  return new Promise((resolve, reject) => {
    PostRequestWithHeaders({ url: `${api_url}/masterScene/convertSceneType`, body: payload }).then(() => {
      resolve();
    }).catch((err) => {
      reject(err);
    });
  });
}

import { defineStore } from 'pinia';
import { getListofLandmark } from '../api/projects/landmarks/index';
import { getListofScenes } from '../api/projects/scene/index';
import { getListOfBuildings } from '../api/projects/buildings/index';
import { getListOfCommunities } from '../api/projects/communities/index';
import { GetVirtualTourList } from '../api/projects/tours';
import { getListOfAmenities } from '../api/projects/amenties';
import { getListofUnits } from '../api/projects/units';
import { getListofUnitplan } from '../api/projects/unitplans';
import { getListofGalleryItems } from '../api/projects/gallery';
import { getListofSidebarOptions } from '../api/projects/sidebar';
import { getListofAssets } from '../api/projects/assets';
import { GetProject } from '../api/projects/settings/index';

import type { projectLandmark } from '@/types/projectLandmark';
import type { transformedProjectScene } from '@/types/projectScene';
import type { Building } from '@/types/building';
import type { community } from '@/types/community';
import type { VirtualTour } from '@/types/virtualTour';
import type { Amenity } from '@/types/amenity';
import type { Units } from '@/types/units';
import type { unitplanType } from '@/types/unitplan';
import type { galleryItem } from '@/types/gallery';
import type { sidebarType } from '@/types/sidebar';
import type { Project } from '@/types/projects';
import type { AssetsObject } from '@/types/asset';

interface ProjectState {
  units: Record<string, Units> | null;
  unitplans: Record<string, unitplanType> | null;
  scenes: Record<string, transformedProjectScene> | null;
  settings: Project | null;
  landmarks: Record<string, projectLandmark> | null;
  communities: Record<string, community> | null;
  amenities: Record<string, Amenity> | null;
  buildings: Record<string, Building> | null;
  virtualtours: Record<string, VirtualTour> | null;
  tourImages: Record<string, any>;
  sidebarOptions: Record<string, sidebarType> | null;
  galleryItems: Record<string, galleryItem> | null;
  assets: AssetsObject | null;
}

interface sidebarOrder extends sidebarType {
  order: number;
}

export const ProjectStore = defineStore({
  id: 'project_level',
  state: (): ProjectState => ({
    units: null,
    unitplans: null,
    scenes: null,
    settings: null,
    landmarks: null,
    communities: null,
    amenities: null,
    buildings: null,
    virtualtours: null,
    tourImages: {},
    sidebarOptions: null,
    galleryItems: null,
    assets: null,
  }),
  actions: {
    // Landmarks
    SyncMultipleLandmarks (landmarks: Record<string, projectLandmark>): void {
      this.landmarks = { ...this.landmarks, ...landmarks };
    },
    RefreshLandmarks (project_id: string): void {
      if (!this.landmarks) {
        getListofLandmark(project_id).then((landmarks) => {
          this.landmarks = landmarks;
        });
      }
    },
    ForceRefreshLandmarks (project_id: string): void {
      getListofLandmark(project_id).then((landmarks) => {
        this.landmarks = landmarks;
      });
    },

    // Scenes
    SyncMultipleScenes (scenes: Record<string, transformedProjectScene>): void {
      this.scenes = { ...this.scenes, ...scenes };
    },
    RefreshScenes (project_id: string): void {
      if (!this.scenes) {
        getListofScenes(project_id).then((scenes) => {
          this.scenes = scenes;
        });
      }
    },
    ForceRefreshScenes (project_id: string): void {
      getListofScenes(project_id).then((scenes) => {
        this.scenes = scenes;
      });
    },

    // Buildings
    SyncMultipleBuildings (buildings: Record<string, Building>): void {
      this.buildings = { ...this.buildings, ...buildings };
    },
    RefreshBuildings (project_id: string): void {
      if (!this.buildings) {
        getListOfBuildings(project_id).then((buildings) => {
          this.buildings = buildings;
        });
      }
    },
    ForceRefreshBuildings (project_id: string): void {
      getListOfBuildings(project_id).then((buildings) => {
        this.buildings = buildings;
      });
    },

    // Communities
    SyncMultipleCommunities (communities: Record<string, community>): void {
      this.communities = { ...this.communities, ...communities };
    },
    RefreshCommunities (project_id: string): void {
      if (!this.communities) {
        getListOfCommunities(project_id).then((communities) => {
          this.communities = communities;
        });
      }
    },
    ForceRefreshCommunities (project_id: string): void {
      getListOfCommunities(project_id).then((communities) => {
        this.communities = communities;
      });
    },

    // Virtual Tours
    SyncMultipleVirtualTours (virtualtours: Record<string, VirtualTour>): void {
      this.virtualtours = { ...this.virtualtours, ...virtualtours };
    },
    RefreshVirtualTours (project_id: string): void {
      if (!this.virtualtours) {
        GetVirtualTourList(project_id).then((virtualtours) => {
          this.virtualtours = virtualtours;
        });
      }
    },
    ForceRefreshVirtualTours (project_id: string): void {
      GetVirtualTourList(project_id).then((virtualtours) => {
        this.virtualtours = virtualtours;
      });
    },

    // Amenities
    SyncMultipleAmenities (amenities: Record<string, Amenity>): void {
      this.amenities = { ...this.amenities, ...amenities };
    },
    RefreshAmenities (project_id: string): void {
      if (!this.amenities) {
        getListOfAmenities(project_id).then((amenities) => {
          this.amenities = amenities;
        });
      }
    },
    ForceRefreshAmenities (project_id: string): void {
      getListOfAmenities(project_id).then((amenities) => {
        this.amenities = amenities;
      });
    },

    // Units
    SyncMultipleUnits (units: Record<string, Units>): void {
      this.units = { ...this.units, ...units };
    },
    RefreshUnits (project_id: string): void {
      if (!this.units) {
        getListofUnits(project_id).then((units) => {
          this.units = units;
        });
      }
    },
    ForceRefreshUnits (project_id: string): void {
      getListofUnits(project_id).then((units) => {
        this.units = units;
      });
    },

    // Unit Plans
    SyncMultipleUnitplans (unitplans: Record<string, unitplanType>): void {
      this.unitplans = { ...this.unitplans, ...unitplans };
    },
    RefreshUnitplans (project_id: string): void {
      if (!this.unitplans) {
        getListofUnitplan(project_id).then((unitplans) => {
          this.unitplans = unitplans;
        });
      }
    },
    ForceRefreshUnitplans (project_id: string): void {
      getListofUnitplan(project_id).then((unitplans) => {
        this.unitplans = unitplans;
      });
    },

    // Gallery Items
    SyncMultipleGalleryItems (galleryItems: Record<string, galleryItem>): void {
      this.galleryItems = { ...this.galleryItems, ...galleryItems };
    },
    RefreshGalleryItems (project_id: string): void {
      if (!this.galleryItems) {
        getListofGalleryItems(project_id).then((galleryItems) => {
          this.galleryItems = galleryItems;
        });
      }
    },
    ForceRefreshGalleryItems (project_id: string): void {
      getListofGalleryItems(project_id).then((galleryItems) => {
        this.galleryItems = galleryItems;
      });
    },

    // Assets
    SyncMultipleAssets (assets: AssetsObject): void {
      this.assets = { ...this.assets, ...assets };
    },
    RefreshAssets (project_id: string): void {
      if (!this.assets) {
        getListofAssets(project_id).then((assets) => {
          if (assets?.assets?.total > 0) {
            this.assets = assets;
          }
        });
      }
    },
    ForceRefreshAssets (project_id: string): void {
      getListofAssets(project_id).then((assets) => {
        if (assets?.assets?.total > 0) {
          this.assets = assets;
        }
      });
    },

    // Settings
    SyncMultipleSettings (settings: Project): void {
      this.settings = { ...this.settings, ...settings };
    },
    RefreshSettings (project_id: string) : void {
      if (!this.settings) {
        GetProject(project_id).then((settings) => {
          this.settings = settings;
        });
      }
    },
    ForceRefreshSettings (project_id: string): void {
      GetProject(project_id).then((settings) => {
        this.settings = settings;
      });
    },

    // Sidebar options
    SyncMultipleSidebarOptions (sidebarOptions: Record<string, sidebarType>): void {
      const sortOptions: Record<string, sidebarType> = { ...this.sidebarOptions, ...sidebarOptions };
      const sortedSidebarOptions: Record<string, sidebarType> = {}; // Updated variable name

      if (Object.keys(sortOptions).length) { // Added .length to check if keys are present
        Object.values(sortOptions)
          .sort((a, b) => (a as sidebarOrder).order - (b as sidebarOrder).order)
          .forEach((item) => {
            sortedSidebarOptions[item._id] = item;
          });
      }

      this.sidebarOptions = sortedSidebarOptions; // Updated to assign the correct variable
    },

    RefreshSidebarOptions (project_id: string): void {
      if (!this.sidebarOptions) {
        getListofSidebarOptions(project_id).then((sidebarOptions) => {
          this.sidebarOptions = sidebarOptions;
        });
      }
    },
    ForceRefreshSidebarOptions (project_id: string): void {
      getListofSidebarOptions(project_id).then((sidebarOptions) => {
        this.sidebarOptions = sidebarOptions;
      });
    },
  },
});

import { defineStore } from 'pinia';
import { setTheme } from '../helpers/themehelper';

interface Toaster {
  show: boolean;
  state: string | null;
  message: string | null;
}
interface ErrorModal {
  show: boolean;
  message: string | null;
}

interface UIState {
  toggleTheme: string;
  toaster: Toaster;
  errorModal: ErrorModal;
  loader: boolean;
  toastTimeout?: number;
  disableToaster?:boolean;
  disableAddSessionIcon?:boolean;
}

const initialRenderTheme = () => {
  // If (sessionStorage.getItem('theme') ){
  //   If (sessionStorage.getItem('theme') === 'dark' ){

  //     SetTheme('dark');
  //     Return 'dark';

  //   }

  //   SetTheme('light');
  //   Return 'light';

  // }
  // Default theme
  return 'light';

};

export const uiOperations = defineStore({
  id: 'uiOperation',
  state: ():UIState => ({
    toggleTheme: initialRenderTheme(),
    toaster: {
      show: false,
      state: null, // Error | success | warning
      message: null,
    },
    errorModal: {
      show: false,
      message: null,
    },
    loader: false,
    toastTimeout: undefined,
    disableToaster: false,
    disableAddSessionIcon: false,

  }),
  actions: {
    handleToggleTheme (theme:string){
      setTheme(theme as 'light' | 'dark');
      this.toggleTheme = theme;
      sessionStorage.setItem('theme', theme);
    },
    handleLoader (bool:boolean){
      this.loader = bool;
    },
    showToast (message:string, state = 'success', duration = 2000) {
      if (this.toastTimeout) {
        window.clearTimeout(this.toastTimeout);
      }
      this.toaster = {
        show: true,
        message,
        state,
      };
      this.toastTimeout = window.setTimeout(() => {
        this.toaster.show = false;
      }, duration);
    },
    handleApiErrorMessage (message:string, state = 'error') {
      this.showToast(message, state);
    },
    handleSuccessMessage (message:string) {
      this.showToast(message, 'success');
    },

  },
});

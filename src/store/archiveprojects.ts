import { defineStore } from 'pinia';
import {
  PostRequestWithHeaders,
  PostRequest,
} from '../helpers/apihelper';
import { getCookie } from '../helpers/domhelper';
import { uiOperations } from './uiOperations';
import { notAuthorized } from '../enum';
import { UserStore } from './index';
import type { Organization } from '@/types/organization';
import type { GetAvailableSloteType, Session } from '@/types/session';
import type { createHotspotData, UpdateHotSpots, VirtualTour } from '@/types/virtualTour';
import type { Users } from '@/types/user';
import type { Project } from '@/types/projects';
import type { Leads } from '@/types/leads';

interface SessionDTO {
  name: string | null;
  email: string | null;
  timeSlot: string | null;
  phonenumber: string | null;
  description: string | null;
}

const uiStore = uiOperations();
const userStore = UserStore();

const api_url = import.meta.env.VITE_API_URL;

interface OrgState {
  organization_data: Organization |null;
  projects: Record<string, Project> | null;
  users:  Users[] | null;
  invitation: null;
  scenePreview:null | VirtualTour;
}

export const OrganizationStore = defineStore({
  id: 'project',
  state: ():OrgState => ({
    organization_data: null,
    projects: null,
    users: null,
    invitation: null,
    scenePreview: null,
  }),
  actions: {

    // ok
    GetOrganizationDetails ():void {
      PostRequestWithHeaders({
        url: `${api_url}/org/GetOrganization`,
      })
        .then((organization_data) => {
          this.organization_data = organization_data as Organization;
        })
        .catch((error) => {
          if (userStore.verifyAuth()) {
            // Router.push('/login');
          } else {
            if (error.message) {
              uiStore.handleApiErrorMessage(error.message); //
            } else {
              if (notAuthorized.toLowerCase() !== error.error.toLowerCase()) {
                uiStore.handleApiErrorMessage(error.error);
              }
            }
          }
        });
    },

    // ok
    async UpdateOrganizationDetails (name:string, address:string, foundDate:Date): Promise<Organization | object> {
      if (name && address && foundDate) {
        return new Promise((resolve, reject) => {
          PostRequestWithHeaders({
            url: `${api_url}/org/UpdateOrganization`,
            body: {
              founding_date: foundDate,
              address: address,
              name: name,
            },
          })
            .then((res) => {
              console.log('Res');
              this.GetOrganizationDetails();
              resolve(res as Organization);
            })
            .catch((error) => {
              reject(error);
            });
        });
      }
      return { status: 0, error: 'No data provided' };

    },

    // ok
    async createSession (DTO:SessionDTO, sessionType:string, project_id:string): Promise<Session | object> {
      if (project_id && sessionType) {
        return new Promise((resolve, reject) => {
          PostRequestWithHeaders({
            url: `${api_url}/session/createSession`,
            body: {
              project_id: project_id,
              type: 'default',
              source: 'dashboard',
              is_scheduled:
                sessionType.toLowerCase() === 'create' ? false : true,
              ...(sessionType.toLowerCase() === 'schedule' && {
                schedule_time: DTO.timeSlot,
              }),
              description: `${DTO.description ? DTO.description : ''}`,
            },
          }).then((res) => {
            console.log('Res CreateSession');
            const session = res as Session;
            this.CreateLead(DTO, project_id, session._id)
              .then(() => {
                resolve(session);
              })
              .catch((error) => {
                reject(error);
              });
          });
        });
      }
      return { status: 0, error: 'No data provided' };

    },

    // ok
    async CreateLead (DTO:SessionDTO, project_id:string, session_id:string):Promise<Leads | object> {
      if (DTO && project_id && session_id) {
        return new Promise((resolve, reject) => {
          PostRequest({
            url: `${api_url}/lead/CreateLead`,
            body: {
              name: DTO.name,
              email: DTO.email,
              phone_number: DTO.phonenumber ? DTO.phonenumber : '',
              project_id: project_id,
              session_id: session_id,
              type: 'project',
              source: 'sales_session',
              status: 'new',
            },
          })
            .then((res) => {
              console.log('Res CreateLead');
              resolve(res as Leads);
            })
            .catch((error) => {
              reject(error);
            });
        });
      }
      return { status: 0, error: 'No data provided' };

    },

    // ok
    async GetProjects (): Promise<Record<string, Project>> {
      uiStore.handleLoader(true);
      return new Promise((resolve) => {
        PostRequestWithHeaders({
          url: `${api_url}/org/ListProjectsFromOrganization`,
        })
          .then((projects) => {
            this.projects = projects as Record<string, Project>;
            uiStore.handleLoader(false);
            resolve(projects as Record<string, Project>);
          })
          .catch((error) => {
            if (userStore.verifyAuth()) {
              // Router.push('/login');
            } else {
              if (error.message) {
                uiStore.handleApiErrorMessage(error.message);
              } else {
                if (notAuthorized.toLowerCase() !== error.error.toLowerCase()) {
                  uiStore.handleApiErrorMessage(error.error);
                }
              }
            }
          });
      });
    },

    // ok
    async GetAvailableSlots (values:GetAvailableSloteType): Promise<object[] | object> {
      uiStore.handleLoader(true);
      if (values.project_id && values.date) {
        return new Promise((resolve, reject) => {
          PostRequestWithHeaders({
            url: `${api_url}/session/GetAvailableSlots`,
            body: {
              project_id: values.project_id,
              date: values.date,
            },
          })
            .then((res) => {
              uiStore.handleLoader(false);
              resolve(res as object[]);
            })
            .catch((error) => {
              uiStore.handleLoader(false);
              reject(error);
              if (userStore.verifyAuth()) {
                // Router.push('/login');
              } else {
                if (error.message) {
                  uiStore.handleApiErrorMessage(error.message);
                } else {
                  if (
                    notAuthorized.toLowerCase() !== error.error.toLowerCase()
                  ) {
                    uiStore.handleApiErrorMessage(error.error);
                  }
                }
              }
            });
        });
      }
      return { status: 0, error: 'No data provided' };

    },

    // OK
    GetUsers ():void {
      PostRequestWithHeaders({ url: `${api_url}/org/ListUsersInOrganization` })
        .then((users) => {
          console.log('List of User in Org' + users);
          this.users = users as Users[];
        })
        .catch((error) => {
          if (userStore.verifyAuth()) {
            // Router.push('/login');
          } else {
            if (error.message) {
              uiStore.handleApiErrorMessage(error.message);
            } else {
              if (notAuthorized.toLowerCase() !== error.error.toLowerCase()) {
                uiStore.handleApiErrorMessage(error.error);
              }
            }
          }
        });
    },
    // ok
    selectScenePreview (img:VirtualTour) {
      this.scenePreview = img;
    },

    // ok
    getHotspot (data:createHotspotData) {
      console.log(data);
      return new Promise((resolve, reject) => {
        const current_organization = getCookie('organization');

        if (current_organization) {
          PostRequestWithHeaders({
            url: `${api_url}/virtualTour/AddHotspotToScene`,
            body: data,
          })
            .then((res) => {
              console.log('response', res);
              resolve(res);
            })
            .catch((err) => {
              reject(err);
            });
        }
      });
    },

    // ok
    updateHotspot (data:UpdateHotSpots): Promise<VirtualTour > {
      return new Promise((resolve, reject) => {
        const current_organization = getCookie('organization');

        if (current_organization) {
          PostRequestWithHeaders({
            url: `${api_url}/virtualTour/UpdateHotspot`,
            body: data,
          })
            .then((res) => {
              console.log('response', res);
              resolve(res as VirtualTour);
            })
            .catch((err) => {
              reject(err);
            });
        }
      });
    },
  },
});

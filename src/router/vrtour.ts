import type { RouteRecordRaw } from "vue-router";
export default [
  {
    path: '/projects/:project_id/tours/:tour_id/customtour',
    name: 'customtour',
    component: () => import('../views/vrtourcreator/index.vue'),
    children: [
      {
        path: 'addtour',
        name: 'Add_tour',
        components: {
          addMedia: () =>
            import('../components/tourcreator/AddMediaComponent.vue'),
        },
      },
      {
        path: 'replace_image/:image_id',
        name: 'Replace_image',
        components: {
          addMedia: () =>
            import('../components/tourcreator/ReplaceImageComponent.vue'),
        },
      },
      {
        path: 'delete/:image_id',
        name: 'Delete_tour_custom',
        components: {
          addMedia: () =>
            import('../components/tourcreator/DeletePromptComponent.vue'),
        },
      },
      {
        path: 'edit_title/:image_id',
        name: 'Edit_Title',
        components: {
          addMedia: () =>
            import('../components/tourcreator/EditTitlePromptComponent.vue'),
        },
      },

      {
        path: 'addtour/delete',
        name: 'Delete_tour',
        components: {
          addMedia: () =>
            import('../components/tourcreator/DeletePromptComponent.vue'),
        },
      },

      {
        path: ':image_id',
        name: 'image',
        components:
           () =>
             import('../views/vrtourcreator/index.vue'),
      },
    ],
  },
/*   {
    path: '/mle',
    name: 'mle',
    component:
    () => import('../components/tourcreator/MLEviewer.vue'),
  }, */
] as Array<RouteRecordRaw>;

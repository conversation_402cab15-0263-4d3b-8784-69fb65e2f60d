import type { RouteRecordRaw } from "vue-router";
export default [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/login/Index.vue'),
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/login/Index.vue'),
  },
  {
    path: '/sso',
    name: 'sso',
    component: () => import('../views/login/SSO.vue'),
  },

  {
    path: '/settings',
    name: 'settings',
    component: () => import('../views/settings/Index.vue'),
  },

  {
    path: '/reset-password',
    name: 'reset-password',
    component: () => import('../components/login/resetPassword.vue'),
  },
] as Array<RouteRecordRaw>;

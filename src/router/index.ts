import { createRouter, createWebHistory } from 'vue-router';
import ProjectsRoutes from './projects';
import LeadsRoutes from './leads';
import SessionsRoutes from './sessions';
import UsersRoutes from './users';
import MasterSceneRoutes from './masterScenes';
import RootRoutes from './root';
import VRTourRoutes from './vrtour';
import type { RouteRecordRaw } from "vue-router";
import Translate from './translate';
import Trash from './trash';
import Notification from './notification';
import SchedulesRoutes from "./schedules";

// Import OrganizationSettingsRoutes from "./settings.ts";
import AnalyticsRoutes from './analytics';
// import meetingsRoutes from './meetings';

const routes : Array<RouteRecordRaw> = [
  ...ProjectsRoutes,
  ...LeadsRoutes,
  ...SessionsRoutes,
  ...UsersRoutes,
  ...RootRoutes,
  ...MasterSceneRoutes,
  ...VRTourRoutes,
  ...Translate,
  ...Trash,
  ...Notification,
  ...SchedulesRoutes,
  // ...OrganizationSettingsRoutes,
  ...SchedulesRoutes,
  ...AnalyticsRoutes,
  //   {
  //     Path: "/dashboard",
  //     Name: "dashboard",
  //     Component:() => import("../views/Layout/MainLayout.vue"),
  //     Children:[
  //       {
  //           Path:"projects",
  //           Name:"projects",
  //           Component:() => import("../views/Layout/ContentView.vue"),
  //           Children:[
  //                 {
  //                     Path:"",
  //                     Name:"projects",
  //                     Component:() => import("../views/projects/ProjectDashboard.vue"),
  //                 },
  //           ]
  //       },
  //       {
  //         Path:"leads",
  //         Name:"leads",
  //         Component:() => import("../views/Layout/ContentView.vue"),
  //         Children:[
  //               {
  //                   Path:"",
  //                   Name:"leads",
  //                   Component:() => import("../views/Leads/Leads.vue"),
  //               },
  //         ]
  //     },

  //     ]
  //   },

  //   {
  //     Path: "/temp",
  //     Name: "temp",
  //     Component: () => import("../views/Temp.vue"),
  //   },
  //   {
  //     Path: "/noOrganization",
  //     Name: "No Organization",
  //     Component: () => import("../views/NoOrganizationFound.vue"),
  //   },
  //   {
  //     Path: "/users",
  //     Name: "User Management",
  //     Components: { default: () => import("../views/UserManagementView.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  //   {
  //     Path: "/masterscenes",
  //     Name: "master Scenes",
  //     Components: { default: () => import("../views/MasterScenes.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  //   {
  //     Path: "/landmarks",
  //     Name: "master landmarks",
  //     Components: { default: () => import("../views/MasterLandmarksView.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  //   {
  //     Path: "/masterscenes/:sceneId",
  //     Name: "Master Scene By Id",
  //     Components: { default: () => import("../views/MasterSceneById.vue"), sidebar: () => import("../components/MasterSceneSidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  //   {
  //     Path: "/mastersvgs/:sceneId",
  //     Name: "Master Svgs",
  //     Components: { default: () => import("../views/MasterSvgs.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  //   {
  //     Path: "/tours",
  //     Name: "Tours",
  //     Components: { default: () => import("../views/Tours.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  // //   {
  // //     path: "/projects",
  // //     name: "Project page",
  // //     components: { default: () => import("../views/MyProject.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  // //   },
  //   {
  //     Path: "/projects/:projectId",
  //     Name: "projectbyid",
  //     Component: () => import("../App.vue"),
  //     Children: [
  //       {
  //         Path: 'unitplans',
  //         Name: 'projectbyid.unitplans',
  //         Components: { default: () => import("../views/ProjectSubViews/UnitplansView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'session',
  //         Name: 'projectbyid.session',
  //         Components: { default: () => import("../views/ProjectSubViews/sessionViewComp.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'unitplans/:unitplan_id',
  //         Name: 'projectbyid.unitplanbyid',
  //         Components: { default: () => import("../views/ProjectSubViews/UnitplanByIdView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },

  //       {
  //         Path: 'units',
  //         Name: 'projectbyid.units',
  //         Components: { default: () => import("../views/ProjectSubViews/UnitsView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'landmarks',
  //         Name: 'projectbyid.landmarks',
  //         Components: { default: () => import("../views/ProjectSubViews/LandmarksView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'scenes',
  //         Name: 'projectbyid.scenes',
  //         Components: { default: () => import("../views/ProjectSubViews/ScenesView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: "scenes/:sceneId",
  //         Name: "projectbyid.sceneById",
  //         Components: { default: () => import("../views/ProjectSubViews/ProjectSceneById.vue"), sidebar: () => import("../components/ProjectSceneSidebar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'buildings',
  //         Name: 'projectbyid.buildings',
  //         Components: { default: () => import("../views/ProjectSubViews/BuildingsView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'tours',
  //         Name: 'projectbyid.tours',
  //         Components: { default: () => import("../views/ProjectSubViews/ToursView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'projectSettings',
  //         Name: 'projectbyid.projectSettings',
  //         Components: { default: () => import("../views/ProjectSubViews/ProjectSettings.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'amenities',
  //         Name: 'projectbyid.amenities',
  //         Components: { default: () => import("../views/ProjectSubViews/Amenities.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //       {
  //         Path: 'communities',
  //         Name: 'projectbyid.communities',
  //         Components: { default: () => import("../views/ProjectSubViews/Communities.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
  //       },
  //     ]
  //   },
  //   {
  //     Path: "/analytics",
  //     Name: "analytics",
  //     Components: {
  //       Default: () => import("../views/AnalyticsSubViews/SessionAnalytics.vue"),
  //       Sidebar: () => import("../components/Sidebar.vue"),
  //       Header: () => import("../components/Header.vue")
  //     }
  //   },
  //   {
  //     Path: "/apis",
  //     Name: "api keys",
  //     Components: { default: () => import("../views/ApiKeysView.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  //   {
  //     Path: "/info",
  //     Name: "Organisation Info",
  //     Components: { default: () => import("../views/OrganisationInfoView.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  //   {
  //     Path: "/myschedule",
  //     Name: "Myschedule",
  //     Components: { default: () => import("../views/MySchedule.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },
  //   {
  //     Path: "/myleads",
  //     Name: "myleads",
  //     Components: { default: () => import("../views/MyLeads.vue"), sidebar: () => import("../components/Sidebar.vue"), header: () => import("../components/Header.vue") }
  //   },

  //   {
  //     Path: "/",
  //     Name: "Viewer",
  //     Component: () => import("../components/MainViewer/viewer.vue"),
  //   }
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;

/*

  {
    path: "/dashboard",
    name: "dashboard",
    component: () => import("../views/Dashboard/Dashboard.vue"),
    children:[
      {
        path: 'rieds',
        name: 'rieds',
        components:{default: () => import("../components/Projects/ProjectDashboard.vue")},
        children:[
              {
                path: ":projectId",
                name: "projects/Id",
                components: { default: () => import("../views/ProjectSubViews/ProjectSceneById.vue"), sidebar: () => import("../components/ProjectSceneSidebar.vue"), header: () => import("../components/Header.vue") },
                children:[
                      {
                        path: 'unitplans',
                        name: 'unitplans',
                        components: { default: () => import("../views/ProjectSubViews/UnitplansView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") }
                      }
                ]
              },
        ]
      },
      {
        path: 'leads',
        name: 'leads',
        components: { default: () => import("../views/ProjectSubViews/UnitplansView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") },
        children:[
        {
          path: ":leadId",
          name: "leads/leadId",
          components: { default: () => import("../views/ProjectSubViews/ProjectSceneById.vue"), sidebar: () => import("../components/ProjectSceneSidebar.vue"), header: () => import("../components/Header.vue") },
          children:[
                {
                  path: 'sample',
                  name: 'sample',
                  components: {default: () => import("../views/ProjectSubViews/UnitplansView.vue")}
                }
          ]
        },
      ]
      },
      {
        path: 'schedules',
        name:'schedules',
        components: { default: () => import("../views/ProjectSubViews/UnitplansView.vue"), sidebar: () => import("../components/myProject/ProjectSideBar.vue"), header: () => import("../components/Header.vue") },
        children:[
          {
            path: ":schedulesId",
           name: "schedules/schedulesId",
          components: { default: () => import("../views/ProjectSubViews/ProjectSceneById.vue"), sidebar: () => import("../components/ProjectSceneSidebar.vue"), header: () => import("../components/Header.vue") },
          children:[
                {
                  path: 'sample',
                  name: 'sample',
                  components: {default: () => import("../views/ProjectSubViews/UnitplansView.vue")}
                }
          ]
          }
        ]

      }
    ]
  }

*/

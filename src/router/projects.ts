
import type { RouteRecordRaw } from "vue-router";

export default [
  {
    path: '/reOrder',
    name: 'reOrder',
    component: () => import('../views/testReOrder/NestedReOrderTest.vue'),
  },
  {
    path: '/reOrder-scene',
    name: 'reOrderScene',
    component: () => import('../views/testReOrder/ScenesReOrderTest.vue'),
  },
  {
    path: '/reOrder-floors',
    name: 'reOrderFloors',
    component: () => import('../views/testReOrder/FloorReOrderTest.vue'),
  },
  {
    path: '/projects',
    name: 'projects',
    component: () => import('../views/projects/Index.vue'),  // ../views/projects/Index.vue
    children: [
      {
        path: 'add',
        name: 'add_projects',
        component: () =>
          import('../components/Projects/AddProjectForm.vue'),
      },
      {
        path: 'add/organization',
        name: 'add_organization',
        component: () =>
          import('../components/Organizations/AddOrganizationForm.vue'),
      },
      {
        path: 'edit/:project_id',
        name: 'edit_projects',
        component: () =>
          import('../components/Projects/EditProjectForm.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id',
    name: 'project',
    component: () => import('../views/projects/ProjectDetail.vue'),
  },

  {
    path: '/projects/:project_id/units',
    name: 'project_units',
    component: () => import('../views/projects/units/Index.vue'),
    // children: [
    //   {
    //     path: 'create',
    //     name: 'project_units_create',
    //     component: () =>
    //       import('../components/Projects/units/AddUnitModal.vue'),
    //   },
    //   {
    //     path: ':unit_id/edit',
    //     name: 'project_units_edit',
    //     component: () =>
    //       import('../components/Projects/units/updateUnitModal.vue'),
    //   },
    // ],
  },
  {
    path: '/projects/:project_id/sessions',
    name: 'project_sessions',
    component: () => import('../views/projects/sessions/Index.vue'),
  },
  {
    path: '/projects/:project_id/landmarks',
    name: 'project_landmarks',
    component: () => import('../views/projects/landmarks/Index.vue'),
    children: [
      {
        path: 'create',
        name: 'project_landmarks_create',
        component: () =>
          import('../components/Projects/landmarks/CreateLandmark.vue'),
      },
      {
        path: 'createmultiple',
        name: 'project_landmarks_createmultiple',
        component: () =>
          import('../components/Projects/landmarks/CreateMultipleLandmark.vue'),
      },
      {
        path: ':landmark_id/edit',
        name: 'project_landmarks_edit',
        component: () =>
          import('../components/Projects/landmarks/EditLandmark.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/design',
    name: 'project_design',
    component: () => import('../views/projects/scenes/SceneById.vue'),
    children: [
    ],
  },

  {
    path: '/projects/:project_id/design/scenes',
    name: 'project_design_scenes',
    component: () => import('../views/projects/scenes/SceneById.vue'),
    children: [
      {
        path: 'create',
        name: 'project_scenes_create',
        component: () =>
          import('../components/scenes/CreateProjectScene.vue'),
      },
      {
        path: ':scene_id/convertdeepzoom',
        name: 'project_scenes_deepzoomconversion',
        component: () =>
          import('../components/scenes/ConvertToDeepzoom.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/design/scenes/:scene_id',
    name: 'design_scene',
    component: () => import('../views/projects/scenes/SceneById.vue'),
    children: [
      {
        path: 'createsvg',
        name: 'project_scene_create_svg',
        component: () => import('../components/scenes/AddSvg.vue'),
      },
      {
        path: 'edit',
        name: 'project_scene_edit',
        component: () =>
          import('../components/scenes/EditSceneSettingModal.vue'),
      },
      {
        path: 'createframe',
        name: 'project_scenes_create_frame',
        component: () =>
          import('../components/scenes/CreateImageFrame.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/design/scenes/:scene_id/layers',
    name: 'design_layers',
    component: () => import('../views/projects/scenes/SceneById.vue'),
  },
  {
    path: '/projects/:project_id/design/scenes/:scene_id/icons',
    name: 'design_icons',
    component: () => import('../views/projects/scenes/SceneById.vue'),
  },
  {
    path: '/projects/:project_id/design/scenes/:scene_id/gsplat',
    name: 'design_scenes_gsplat',
    component: () => import('../views/projects/scenes/Gsplat.vue'),
    children: [
      {
        path: 'edit',
        name: 'project_scenes_gsplat_edit',
        component: () =>
          import('../components/scenes/EditSceneSettingModal.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/design/scenes/:scene_id/gsplat/layers',
    name: 'design_scenes_gsplat_layers',
    component: () => import('../views/projects/scenes/Gsplat.vue'),
  },
  {
    path: '/projects/:project_id/design/scenes/:scene_id/deepzoom',
    name: 'design_scenes_deepzoom',
    component: () => import('../views/projects/scenes/Deepzoom.vue'),
    children: [
      {
        path: 'createlayers',
        name: 'project_scene_create_layers',
        component: () => import('../components/scenes/CreateLayers.vue'),
      },
      {
        path: 'edit',
        name: 'project_scene_deepzoom_edit',
        component: () =>
          import('../components/scenes/EditSceneSettingModal.vue'),
      },
      {
        path: 'createicons',
        name: 'project_scene_create_icons',
        component: () => import('../components/scenes/CreateIcons.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/scenes',
    name: 'project_scenes',
    component: () => import('../views/projects/scenes/Index.vue'),
    children: [
      {
        path: 'create',
        name: 'project_scenes_create',
        component: () =>
          import('../components/scenes/CreateProjectScene.vue'),
      },
      {
        path: ':scene_id/convertdeepzoom',
        name: 'project_scenes_deepzoomconversion',
        component: () =>
          import('../components/scenes/ConvertToDeepzoom.vue'),
      },
    ],
  },
  // {
  //   path: '/projects/:project_id/scenes/:scene_id',
  //   name: 'project_scene',
  //   component: () => import('../views/projects/scenes/SceneById.vue'),
  //   children: [
  //     {
  //       path: 'createsvg',
  //       name: 'project_scene_create_svg',
  //       component: () => import('../components/scenes/AddSvg.vue'),
  //     },
  //     {
  //       path: 'edit',
  //       name: 'project_scene_edit',
  //       component: () =>
  //         import('../components/scenes/EditSceneSettingModal.vue'),
  //     },
  //     {
  //       path: 'createframe',
  //       name: 'project_scenes_create_frame',
  //       component: () =>
  //         import('../components/scenes/CreateImageFrame.vue'),
  //     },
  //   ],
  // },
  // {
  //   path: '/projects/:project_id/scenes/:scene_id/gsplat',
  //   name: 'project_scenes_gsplat',
  //   component: () => import('../views/projects/scenes/Gsplat.vue'),
  //   children: [
  //     {
  //       path: 'edit',
  //       name: 'project_scenes_gsplat_edit',
  //       component: () =>
  //         import('../components/scenes/EditSceneSettingModal.vue'),
  //     },
  //   ],
  // },
  // {
  //   path: '/projects/:project_id/scenes/:scene_id/deepzoom',
  //   name: 'project_scenes_deepzoom',
  //   component: () => import('../views/projects/scenes/Deepzoom.vue'),
  //   children: [
  //     {
  //       path: 'createlayers',
  //       name: 'project_scene_create_layers',
  //       component: () => import('../components/scenes/CreateLayers.vue'),
  //     },
  //     {
  //       path: 'edit',
  //       name: 'project_scene_deepzoom_edit',
  //       component: () =>
  //         import('../components/scenes/EditSceneSettingModal.vue'),
  //     },
  //     {
  //       path: 'createicons',
  //       name: 'project_scene_create_icons',
  //       component: () => import('../components/scenes/CreateIcons.vue'),
  //     },
  //   ],
  // },
  {
    path: '/projects/:project_id/design/tours',
    name: 'project_design_tours',
    component: () => import('../views/projects/tours/Index.vue'),
  },
  {
    path: '/projects/:project_id/design/tours/:tour_id',
    name: 'project_designtours_Tourview',
    component: () => import('../views/projects/tours/TourView.vue'),
    children: [
      /*       {
        path: 'edit',
        name: 'project_tourscene_edit',
        components: {
          modal: () => import('../components/Projects/tours/EditTourModal.vue'),
        },
      }, */
    ],
  },
  /*  {
    path: '/projects/:project_id/tours/:tour_id',
    name: 'design_tours_Tourview',
    component: () => import('../views/projects/tours/TourView.vue'),
  }, */

  {
    path: '/projects/:project_id/communities',
    name: 'project_communities',
    component: () => import('../views/projects/communities/Index.vue'),
    children: [
      {
        path: 'create',
        name: 'project_communities_create',
        component: () =>
          import('../components/Projects/communities/CreateCommunityModal.vue'),
      },
      {
        path: ':community_id/edit',
        name: 'project_communities_edit',
        component: () =>
          import('../components/Projects/communities/EditCommunityModal.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/buildings',
    name: 'project_buildings',
    component: () => import('../views/projects/buildings/Index.vue'),
    children: [
      {
        path: 'create',
        name: 'project_buildings_create',
        component: () =>
          import('../components/Projects/buildings/CreateBuildingModal.vue'),
      },
      {
        path: ':building_id/edit',
        name: 'project_buildings_edit',
        component: () =>
          import('../components/Projects/buildings/EditBuildingModal.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/unitplans',
    name: 'project_unitplans',
    component: () => import('../views/projects/unitplans/Index.vue'),
  },
  {
    path: '/projects/:project_id/unitplans/:unitplan_id/createfloorplan_link',
    name: 'project_unitplan_createfloorplan_link',
    component: () => import('../views/projects/unitplans/UnitplanHotspots.vue'),

  },
  {
    path: '/projects/:project_id/design/unitplan',
    name: 'project_design_unitplan',
    component: () => import('../views/projects/unitplans/unitplanbyId.vue'),
  },
  {
    path: '/projects/:project_id/design/unitplan/:unitplan_id',
    name: 'design_unitplan',
    component: () => import('../views/projects/unitplans/unitplanbyId.vue'),
  },
  {
    path: '/projects/:project_id/settings',
    name: 'project_settings',
    component: () => import('../views/projects/settings/Index.vue'),
  }, {
    path: '/projects/:project_id/analytics',
    name: 'Analytics',
    component: () => import('../views/projects/googleAnalytics/index.vue'),
  },
  {
    path: '/projects/:project_id/gallery',
    name: 'project_gallery',
    component: () => import('../views/projects/gallery/Index.vue'),
    children: [
      {
        path: 'create',
        name: 'project_gallery_create',
        component: () =>
          import('../components/Projects/gallery/CreateModal.vue'),
      },
      {
        path: 'createmultiple',
        name: 'project_gallery_create_multiple',
        component: () =>
          import('../components/Projects/gallery/CreateMultipleGallery.vue'),
      },
      {
        path: 'categoryreorder',
        name: 'project_gallery_categoryreorder',
        component: () =>
          import('../components/Projects/gallery/ReOrderCategoryModal.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/gallery/:category_id',
    name: 'gallery_by_category',
    component: () => import('../views/projects/gallery/galleryByCategory.vue'),
    children: [
      {
        path: ':gallery_item_id/edit',
        name: 'gallery_by_category_edit',
        component: () => import('../components/Projects/gallery/EditModal.vue'),
      },
      {
        path: 'reorder',
        name: 'gallery_by_category_reorder',
        component: () =>
          import('../components/Projects/gallery/ReOrderGalleryModal.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/amenities/:category_id',
    name: 'amenities_by_category',
    component: () =>
      import('../views/projects/amenities/amenityByCategory.vue'),
    children: [
      {
        path: 'reorder',
        name: 'amenity_by_category_reorder',
        component: () =>
          import('../components/Projects/amenities/ReorderAmenityModal.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/amenities',
    name: 'project_amenities',
    component: () => import('../views/projects/amenities/Index.vue'),
    children: [
      {
        path: 'create',
        name: 'project_amenities_create',
        components: {
          modal: () =>
            import('../components/Projects/amenities/CreateAmenities.vue'),
        },
      },
      {
        path: 'amenitycategoryreorder',
        name: 'project_amenities_reorder',
        components: {
          modal: () =>
            import('../components/Projects/amenities/ReorderAmenityCategoryModal.vue'),
        },
      },
      {
        path: ':id/edit',
        name: 'project_amenities_edit',
        meta: {
          is_history: true,
        },
        components: {
          modal: () =>
            import('../components/Projects/amenities/EditAmenities.vue'),
        },
      },
      {
        path: ':id/delete',
        name: 'project_amenities_delete',
        meta: {
          is_history: true,
        },
        components: {
          modal: () =>
            import('../components/Projects/amenities/DeleteAmenities.vue'),
        },
      },
    ],
  },
  {
    path: '/projects/:project_id/sidebar',
    name: 'project_sidebar',
    component: () => import('../views/projects/sidebar/Index.vue'),
    children: [
      // {
      //   path: 'create',
      //   name: 'project_sidebar_create',
      //   component: () =>
      //     import('../components/Projects/sidebar/CreateSidebarModal.vue'),
      // },
      {
        path: ':option_id/edit',
        name: 'project_sidebar_edit',
        component: () =>
          import('../components/Projects/sidebar/EditSidebarModal.vue'),
      },
      {
        path: 'reorder',
        name: 'project_sidebar_reorder',
        component: () =>
          import('../components/Projects/sidebar/ReOrderModal.vue'),
      },
    ],
  },
  {
    path: '/projects/:project_id/assets',
    name: 'project_assets',
    component: () => import('../views/projects/assets/Index.vue'),
  },
  {
    path: '/projects/:project_id/about',
    name: 'project_about',
    component: () => import('../views/projects/about/index.vue'),
  },
  {
    path: '/projects/:project_id/exterior',
    name: 'exterior',
    component: () => import('../views/projects/Exterior/index.vue'),
  },
  {
    path: '/projects/:project_id/design/galleryCategory',
    name: 'galleryCAtegory',
    component: () => import('../views/projects/gallery/galleryByCategory.vue'),
  },
  {
    path: '/projects/:project_id/design/amenityCategory',
    name: 'amenityCategory',
    component: () => import('../views/projects/amenities/amenityByCategory.vue'),
  },
] as Array<RouteRecordRaw>;

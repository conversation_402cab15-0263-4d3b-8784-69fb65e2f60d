import * as yup from 'yup';
import { fileValidation, imageSizeValidation, RegXEmailPattern, onlyNumber, urlRegEx } from '../../helpers/validationSchemaHelpers';

export const createOrganizationSchema = yup.object({
  name: yup.string().required('Organization name is required'),
  founding_date: yup.string().required('Founding date is required'),
  contact_email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required('Contact email is required'),
  phone_number: yup.string().matches(onlyNumber, 'Invalid Phone Number').min(10, 'Phone number must be at least 10 digits').max(15, 'Phone number must be at most 15 digits').required('Phone number is required'),
  address: yup.string().required('Address is required'),
  website: yup.string().matches(urlRegEx, 'Invalid website URL').required('Website is required'),
  max_users: yup.string().matches(onlyNumber, 'Only Numbers').required('Maximum users is required'),
  thumbnail: yup.mixed().required('Organization logo is required').test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
    .test('file', 'Image size is more than 2 mb', (value) => imageSizeValidation(value as File)),
});

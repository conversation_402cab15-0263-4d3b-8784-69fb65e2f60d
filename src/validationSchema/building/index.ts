import { fileValidation, imageSizeValidation } from '@/helpers/validationSchemaHelpers';
import * as yup from 'yup';

export const buildingSchema = yup.object({
  name: yup.string().required(),
  type: yup.string().required(),
  total_floors: yup.number().required().typeError('Value must be a number'),
  community_id: yup.string().when('type', {
    is: (val:string) => val === 'villa',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  floorsnew: yup.array().nullable().of(
    yup.lazy((item) =>
      (typeof item === 'object'
        ? yup.object({ name: yup.string().required() })
        : yup.string().required()),
    ),
  ),
  file: yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
    .test('file', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
});

export const CommunitySchema = yup.object({
  name: yup.string().required(),
  category: yup.string().required(),
  file: yup.mixed().required().test('is-valid-type', 'Not a valid image type', (value) => fileValidation(value as File, 'image'))
    .test('file', 'Image size is more than 2 mb', (value) => imageSizeValidation(value  as File)),
});

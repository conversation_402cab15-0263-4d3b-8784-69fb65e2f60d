import { sidebar_icons } from '@/helpers/icons';
import * as yup from 'yup';

const sidebarNames = Object.values(sidebar_icons).map((elem) => elem.name);
export const sideBarSchema = yup.object({
  type: yup.string().required(),
  icon_id: yup.string().when('name', {
    is: (val:string) => sidebarNames.includes(val),
    then: () => yup.string().nullable(),
    otherwise: () => yup.string().required(),
  }),
  name: yup.object().required(),
  scene_id: yup.string().when('type', {
    is: (val:string) => val === 'projectscene' || val === 'masterscene',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  link: yup.string().when('type', {
    is: (val:string) => val === 'custom',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
});

export const editSideBarSchema = yup.object({
  type: yup.string(),
  icon_id: yup.string().when('name', {
    is: (val:string) => sidebarNames.includes(val),
    then: () => yup.string().nullable(),
    otherwise: () => yup.string().required(),
  }),
  name: yup.object().required(),
  scene_id: yup.string().when('type', {
    is: (val:string) => val === 'projectscene' || val === 'masterscene',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable().transform((value, original) => (original === '' ? null : value)),
  }),
  link: yup.string().when('type', {
    is: (val:string) => val === 'custom',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  // link_or_scene: yup.string().nullable().test(
  //   'scene-or-link',
  //   'Either scene_id or link is required',
  //   function () {
  //     const { type, scene_id, link } = this.parent;
  //     // Check if type is 'custom' and both scene_id and link are missing
  //     if (type === 'custom' && !scene_id && !link) {
  //       return false; // Trigger validation error
  //     }
  //     return true; // Validation passes
  //   },
  // ),
});

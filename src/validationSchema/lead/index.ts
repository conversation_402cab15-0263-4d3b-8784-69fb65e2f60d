import * as yup from 'yup';
import { only<PERSON><PERSON>ber, RegXEmailPattern } from '../../helpers/validationSchemaHelpers';
// AddLeads
export const addLeadsSchema = yup.object({
  name: yup.string().required(),
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required(),
  "phone number": yup.string().matches(onlyN<PERSON>ber, 'Invalid Phone Number').max(10),
  project: yup.object().nullable(),
  unit: yup.object().nullable(),
  status: yup.string().nullable(),
  lead_product_interest: yup.string().nullable(),
  lead_industry_type: yup.string().nullable(),
});

export const editLeadSchema = yup.object({
  name: yup.string(),
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email'),
  phone_number: yup.string().matches(only<PERSON><PERSON><PERSON>, 'Invalid Phone Number').max(10),
  project_id: yup.object().nullable(),
  unit_id: yup.object().nullable(),
  lead_status: yup.string().nullable(),
  lead_product_interest: yup.string().nullable(),
  lead_industry_type: yup.string().nullable(),
});

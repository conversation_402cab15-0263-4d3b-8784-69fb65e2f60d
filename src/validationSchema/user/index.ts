import * as yup from 'yup';
import { RegXEmailPattern } from '../../helpers/validationSchemaHelpers';

// Sign In
export const signInSchema = yup.object({
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required("Email ID is required"),
  password: yup.string().required(),
});

// Sign Up
export const nameSchema = yup.object({
  first_name: yup.string().required("First name is required"),
  last_name: yup.string().required("Last name is required"),
});

export const emailSchema = yup.object({
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required("Email ID is a required"),
});

export const roleSchema = yup.object({
  organizationRole: yup.string().required('Organization Role is a required'),
});

export const passwordSchema = yup.object({
  password: yup.string().min(8, 'Password must be at least 8 characters').required('Password is a required'),
  confirmPassword: yup.string()
    .oneOf([yup.ref('password')], 'Passwords must match')
    .required('Confirm password is a required'),
});

export const addUserInOrganization = yup.object({
  email: yup.string().matches(RegXEmailPattern, 'Invalid Email').required(),
  role: yup.string().required(),
});
export const editUserRoleInOrganization = yup.object({
  role: yup.string().required(),
});

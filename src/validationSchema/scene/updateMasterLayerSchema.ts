import { categoryItems } from '@/helpers/constants';
import { ref } from 'vue';
import * as yup from 'yup';

const imageValidation = ['image/jpeg', 'image/jpg', 'image/webp'];
const iconFileValidation = ['image/png', 'image/svg', 'image/svg+xml'];
const videoValidation = ['video/mp4'];
const zipValidation = ['application/x-zip-compressed', 'application/zip'];
const pdfValidation = ['application/pdf'];

type ValidationKeys = 'image' | 'pdf' | 'zip' | 'video' | 'icon';
const fileValidation = (fileName : File, validateTo: string) => { // ValidateTo => image, pdf, zip, video, icon, 3d
  if (fileName === null){
    return true;
  }
  if (fileName !== undefined) {
    if (fileName) {
      const validationMap = {
        image: imageValidation,
        pdf: pdfValidation,
        zip: zipValidation,
        video: videoValidation,
        icon: iconFileValidation,
      };
      return validationMap[validateTo as ValidationKeys]?.includes(fileName.type) || false;
    }
    return false;
  }
  return true; // While field is not required then it will not create any error message
};

const svgFormats = ['image/svg+xml'];
const jsonFormats = ['application/json'];

function isValidFileType (fileName:File|Blob, type:string) {
  if (fileName !== undefined){
    if (type !== 'deepzoom'){
      // Gsplat
      if (fileName && svgFormats.includes(fileName.type)) {
        return true;
      }
    } else {
      // Deep zoom
      if (fileName && [...jsonFormats, ...svgFormats].includes(fileName.type)) {
        return true;
      }
    }
    return false;

  }
  return true;
}

const videoSizeValidation = (fileName : File) => {
  console.log("-----------file in validation(videoSizeValidation)", fileName);
  console.error("------------file in validation(videoSizeValidation)", fileName);
  console.debug("-------------file in validation(videoSizeValidation)", fileName);

  if (!fileName){
    return true;
  }
  const MAX_VIDEO_SIZE = 40 * 1024 * 1024; // 40 MB in bytes
  if (fileName !== undefined) {
    console.log(fileName.size);
    return fileName.size <= MAX_VIDEO_SIZE;
  }
  return true;
};

export const validationSchema = yup.object({
  type: yup.string().required(),
  /*   landmark_id: yup.string().when('type', {
    is: (val:string) => val === 'landmark',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }), */
  /*   route_id: yup.string().when('type', {
    is: (val:string) => val === 'landmark',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }), */
  name: yup.string().optional().nullable(),
  project_id: yup.string().when('type', {
    is: (val:string) => val === 'project',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  scene_id: yup.string().when('type', {
    is: (val:string) => val === 'pin' || val==='scene',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  image_id: yup.string().when('type', {
    is: (val:string) => val === 'image',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  video_tag: yup.mixed()
    .test('is-valid-type', 'Not a valid Video File', (value) => fileValidation(value as File, 'video'))
    .test('file', 'Video size is more than 40 mb', (value) => videoSizeValidation(value as File))
    .optional().nullable(),
  outputVideoTag: yup.string().optional(),
});

export const deepzoomUpdateLayersValid = yup.object({
  type: yup.string().optional().nullable(),
  /*  landmark_id: yup.string().when('type', {
    is: (val:string) => val === 'landmark',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  route_id: yup.string().when('type', {
    is: (val:string) => val === 'landmark',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }), */

  project_id: yup.string().when('type', {
    is: (val:string) => val === 'project',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  scene_id: yup.string().when('type', {
    is: (val:string) => val === 'pin' || val==='scene',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  image_id: yup.string().when('type', {
    is: (val:string) => val === 'image',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  name: yup.string().optional().nullable(),
  x: yup.number().required(),
  y: yup.number().required(),
  minandmax: yup.array().required(),
  zindex: yup.array().required(),
  placement: yup.string().when('reSize', {
    is: (val:boolean) => val === true,
    then: () => yup.string().nullable(),
    otherwise: () => yup.string().required(),
  }),
  reSize: yup.boolean(),
});

// isPlane ? yup.string().nullable() :
export const gsplatFieldValidatteSchema = (type:string, isPlane:boolean) => {
  console.log(type);
  return yup.object({
    name: yup.string().required(),
    type: yup.string().required(),
    /*     landmark_id: yup.string().when('type', {
      is: (val:string) => val === 'landmark',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    route_id: yup.string().when('type', {
      is: (val:string) => val === 'landmark',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }), */
    project_id: yup.string().when('type', {
      is: (val:string) => val === 'project',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    scene_id: yup.string().when('type', {
      is: (val:string) => val === 'pin' || val==='scene',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    image_id: yup.string().when('type', {
      is: (val:string) => val === 'image',
      then: () => yup.string().required(),
      otherwise: () => yup.string().nullable(),
    }),
    xposition: yup.number().required(),
    yposition: yup.number().required(),
    zposition: yup.number().required(),
    svgFile: type === 'add' && !isPlane ? yup.mixed().required().test('svgFile', 'Not a valid svg', (value) => isValidFileType(value as File|Blob, 'gsplat')) : yup.mixed().nullable().test('svgFile', 'Not a valid svg', (value) => isValidFileType(value as File|Blob, 'gsplat')),
  });
};

const formSchema = {
  'type': {
    'name': 'type',
    'label': 'Type',
    'type': 'dropdown',
    'ref': ref(),
    'as': 'select',
    'options': {
    /*   'landmark': {
        'value': 'landmark',
        'toShow': [{ 'field': 'landmark_id', 'optional': false }, { 'field': 'route_id', 'optional': false },{'field': 'video_tag', 'optional': false}],
      }, */
      'scene': {
        'value': 'scene',
        'toShow': [{ 'field': 'scene_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'project': {
        'value': 'project',
        'toShow': [{ 'field': 'project_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'pin': {
        'value': 'pin',
        'toShow': [{ 'field': 'scene_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'image': {
        'value': 'image',
        'toShow': [{ 'field': 'image_id', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'route': {
        'value': 'route',
        'toShow': [],
      },
      'radius': {
        'value': 'radius',
        'toShow': [],
      },
      'label': {
        'value': 'label',
        'toShow': [{ 'field': 'title', 'optional': false }, { 'field': 'category', 'optional': false }, {'field': 'video_tag', 'optional': false}],
      },
      'unavailable': {
        'value': 'unavailable',
        'toShow': [],
      },
      'static': {
        'value': 'static',
        'toShow': [],
      },
      'plane': {
        'value': 'plane',
        'toShow': [],
      },
      'none': {
        'value': 'none',
        'toShow': [],
      },
      'zoom_target': {
        'value': 'zoom_target',
        'toShow': [],
      },

    },
    'defaultValue': '',
    'static': true,
  },
  'iconType': {
    'name': 'icontype',
    'label': 'Type',
    'type': 'dropdown',
    'ref': ref('scene'),
    'as': 'select',
    'options': {
      'svg': {
        'value': 'svg',
        'toShow': [{ 'field': 'scene_id', 'optional': false }],
      },
    },
    'defaultValue': 'landmark',
    'static': true,
  },
  'project_id': {
    'name': 'project_id',
    'label': 'project_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'landmark_id': {
    'name': 'landmark_id',
    'label': 'landmark_id',
    'type': 'dropdown',
    'ref': ref(),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'scene_id': {
    'name': 'scene_id',
    'label': 'scene_id',
    'type': 'dropdown',
    'ref': ref(),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'route_id': {
    'name': 'route_id',
    'label': 'route_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,

    'static': false,
  },
  'image_id': {
    'name': 'image_id',
    'label': 'image_id',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': [],
    'defaultValue': null,
    'static': false,
  },
  'title': {
    'name': 'title',
    'label': 'title',
    'type': 'text',
    'ref': ref(null),
    'as': 'input',
    'options': [],
    'defaultValue': null,
    'static': false,
  },
  'labelName': {
    'name': 'name',
    'label': 'name',
    'type': 'text',
    'ref': ref(null),
    'as': 'input',
    'options': [],
    'defaultValue': null,
    'static': false,
  },
  'category': {
    'name': 'category',
    'label': 'category',
    'type': 'dropdown',
    'ref': ref(null),
    'as': 'select',
    'options': categoryItems,
    'defaultValue': null,
    'static': false,
  },
  'outputVideoTag': {
    'name': 'outputVideoTag',
    'label': 'outputVideoTag',
    'id': 'outputVideoTag',
    'type': 'text',
    'outputRef': ref(''),
  },
  'video_tag': {
    'name': 'video_tag',
    'label': 'video_tag',
    'id': 'video_tag',
    'type': 'file',
    'ref': ref(null),
  },
};

export default formSchema;

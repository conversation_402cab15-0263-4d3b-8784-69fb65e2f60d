import * as yup from 'yup';

// Create & Edit
export const tourSchema = yup.object({
  name: yup.string().required(),
  type: yup.string().required(),
  description: yup.string().required(),
  category: yup.string().required(),
  unitplan_id: yup.string().nullable(),
  link: yup.string().when('type', {
    is: (val:string) => val === 'external',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  space_id: yup.string().when('type', {
    is: (val:string) => val === 'matterport',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  model: yup.mixed().when('type', {
    is: (val: string) => val === 'MLE',
    then: () =>
      yup.mixed().required('Model file is required')
        .test('fileType', 'Model file must be a .glb file', (value) => {
          if (!value || typeof value === 'string') {
            return true;
          }
          return value instanceof File && value.name.toLowerCase().endsWith('.glb');
        })
        .test('fileSize', 'Model file must be smaller than 10MB', (value) => {
          if (!value || typeof value === 'string') {
            return true;
          }
          return value instanceof File && value.size <= 10 * 1024 * 1024;
        }),
    otherwise: () => yup.mixed().nullable(),
  }),
  camera: yup.mixed().when('type', {
    is: (val: string) => val === 'MLE',
    then: () =>
      yup.mixed().required('Camera file is required')
        .test('fileType', 'Camera file must be a .gltf file', (value) => {
          if (!value || typeof value === 'string') {
            return true;
          }
          return value instanceof File && value.name.toLowerCase().endsWith('.gltf');
        })
        .test('fileSize', 'Camera file must be smaller than 10MB', (value) => {
          if (!value || typeof value === 'string') {
            return true;
          }
          return value instanceof File && value.size <= 10 * 1024 * 1024;
        }),
    otherwise: () => yup.mixed().nullable(),
  }),
});

export const editTourSchema = yup.object({
  name: yup.string().required(),
  type: yup.string().required(),
  description: yup.string().required(),
  category: yup.string().required(),
  unitplan_id: yup.string().nullable(),
  link: yup.string().when('type', {
    is: (val:string) => val === 'external',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  space_id: yup.string().when('type', {
    is: (val:string) => val === 'matterport',
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  model: yup.mixed().when('type', {
    is: (val: string) => val === 'MLE',
    then: () =>
      yup.mixed().required('Model file is required')
        .test('fileType', 'Model file must be a .glb file', (value) => {
          if (!value || typeof value === 'string') {
            return true;
          } // Allow existing URLs
          return value instanceof File && value.name.toLowerCase().endsWith('.glb');
        })
        .test('fileSize', 'Model file must be smaller than 10MB', (value) => {
          if (!value || typeof value === 'string') {
            return true;
          } // Allow existing URLs
          return value instanceof File && value.size <= 10 * 1024 * 1024;
        }),
    otherwise: () => yup.mixed().nullable(),
  }),
  camera: yup.mixed().when('type', {
    is: (val: string) => val === 'MLE',
    then: () =>
      yup.mixed().required('Camera file is required')
        .test('fileType', 'Camera file must be a .gltf file', (value) => {
          if (!value || typeof value === 'string') {
            return true;
          } // Allow existing URLs
          return value instanceof File && value.name.toLowerCase().endsWith('.gltf');
        })
        .test('fileSize', 'Camera file must be smaller than 10MB', (value) => {
          if (!value || typeof value === 'string') {
            return true;
          } // Allow existing URLs
          return value instanceof File && value.size <= 10 * 1024 * 1024;
        }),
    otherwise: () => yup.mixed().nullable(),
  }),
});

// Groups (Create & Edit)
export const tourGroupSchema = yup.object({
  name: yup.string().required(),
  icon: yup.string().nullable(),
});

// SubGroups (Create & Edit)
export const tourSubGroupSchema = yup.object({
  name: yup.string().required(),
  icon: yup.string().nullable(),
});

// Hotspots
export const tourHotspotSchema = yup.object({
  text: yup.string().required(),
  x: yup.string().required(),
  y: yup.string().required(),
  z: yup.string().required(),
  destination_img_id: yup.object().required(),
});

// Labels
export const tourLabelSchema = yup.object({
  name: yup.string().required(),
  camera_name: yup.string().required(),
  camera_position_x: yup.number().required(),
  camera_position_y: yup.number().required(),
  camera_position_z: yup.number().required(),
  controls_target_x: yup.number().required(),
  controls_target_y: yup.number().required(),
  controls_target_z: yup.number().required(),
});

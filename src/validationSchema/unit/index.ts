import * as yup from 'yup';
import { decimalRegex } from '../../helpers/validationSchemaHelpers';

export const createUnitSchema = yup.object({
  name: yup.string().required(),
  unitplan_id: yup.string().required(),
  status: yup.string().required(),
  building_id: yup.string().optional().nullable(),
  floor_id: yup.string().when('building_id', {
    is: (val:string) => val,
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  community_id: yup.string().optional(),
  measurement: yup.string().matches(decimalRegex, 'Invalid Number.').nullable(),
  measurement_type: yup.string().when('measurement', {
    is: (val:string) => val,
    then: () => yup.string().required(),
    otherwise: () => yup.string(),
  }),
  balcony_measurement: yup.string().matches(decimalRegex, 'Invalid Number.').nullable().optional(),
  balcony_measurement_type: yup.string().when('balcony_measurement', {
    is: (val:string) => val,
    then: () => yup.string().required(),
    otherwise: () => yup.string(),
  }),
  suite_area: yup
    .string()
    .nullable()
    .test('is-decimal', 'Invalid Number.', (value) => {
      if (!value) {
        return true;
      } // allow empty/null
      return decimalRegex.test(value);
    })
    .optional(),
  suite_area_type: yup.string().optional(),
  tour_id: yup.string().optional(),
  price: yup.number().typeError('Value must be a number').nullable()
    .transform((value, originalValue) => {
      return originalValue === '' ? null : value;
    }),
  currency: yup.string().when('price', {
    is: (val:string) => val,
    then: () => yup.string().required(),
    otherwise: () => yup.string(),
  }),
  max_price: yup.mixed().nullable().optional().test('is-number', 'max_price must be a valid number', function (value) {
    if (value === null || value === undefined || value === '') {
      return true; // Allow null, undefined, or empty string
    }
    return !isNaN(Number(value));
  }),
  cta_link: yup.string().optional().nullable(),
  bedroom: yup.string().optional(),
  // .test('cta_link', 'Not a valid url', (value) => isUrl(value as string)),
});

export const updateUnitSchema = yup.object({
  name: yup.string().required(),
  unitplan_id: yup.string().required(),
  status: yup.string().required(),
  building_id: yup.string().optional().nullable(),
  floor_id: yup.string().when('building_id', {
    is: (val:string) => val,
    then: () => yup.string().required(),
    otherwise: () => yup.string().nullable(),
  }),
  community_id: yup.string().optional().nullable(),
  measurement: yup.string().matches(decimalRegex, 'Invalid Number.').required(),
  measurement_type: yup.string().required(),
  balcony_measurement: yup.string().optional().matches(decimalRegex, 'Invalid Number.').optional(),
  balcony_measurement_type: yup.string().required(),
  suite_area: yup
    .string()
    .nullable()
    .test('is-decimal', 'Invalid Number.', (value) => {
      if (!value) {
        return true;
      } // allow empty/null
      return decimalRegex.test(value);
    })
    .optional(),
  suite_area_type: yup.string().optional(),
  tour_id: yup.string().optional(),
  price: yup.number().required().typeError('value must be a number'),
  currency: yup.string().required(),
  cta_link: yup.string().optional().nullable(),
  // .test('cta_link', 'Not a valid url', (value) => isUrl(value as string)),
});

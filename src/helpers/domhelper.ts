import moment from 'moment-timezone';
interface cookieType {
  cname: string;
  cdate: Date;
  extendDate?: Date;
  length:number
}
export function getCookie (cname: string): cookieType | string {
  const name = cname + '=';
  const decodedCookie = decodeURIComponent(document.cookie);
  const ca = decodedCookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return '';
}
export function setCookie (cname: string, cvalue: string, exdays:number): void {
  const d = new Date();
  d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
  const expires = 'expires=' + d.toUTCString();
  document.cookie = cname + '=' + cvalue + ';' + expires + ';path=/';
}

export function deleteCookie (c: cookieType): void {
  const name=c;
  // Console.log("deleting"+name);
  // Document.cookie = name + '="" + ';-1; path=/';
  document.cookie =  name + '=null;' + 'expires=Thu, 01 Jan 1970 00:00:01 GMT;'+';path=/';
}

export function capitalize (s: string) {
  return s[0].toUpperCase() + s.slice(1);
}

// Convert datetime to time locale String
export const extractTheTimeStringFromDateTime = (value: Date): string => {
  // @parms - Value should be new Date() Object.
  const time = value.toLocaleTimeString().split(' ');
  return `${time[0].slice(0, -3)} ${time[1]} `;

};

// Formater of Date (DD-MM-YY)
export const formatTheDateFromISOString = (value: string): string => {
  // @parms -  Value should be ISOString() - like 'T' format " 2023-12-06T06:46:16.416Z " i.e actually ISOString() format.
  return value.split('T')[0].split('-').reverse().join('-');
};

// Remove the object props with null and undefined .
export const removeUndefinedAndNullInObject = (obj: Record<string, any>):Record<string, any> => {
  // @parms - Object
  const newObject: Record<string, any> = {};
  Object.keys(obj).forEach((item: string) => {
    if (item === 'showLabel'){
      newObject[item] = obj[item];
    } else {
      if (obj[item]){
        newObject[item] = obj[item];
      }
    }

  });
  return newObject;
};

export function formatDate (timestamp: number): Date | string {
  const date = new Date(timestamp);
  const today = new Date();
  // Check if the date is the same as today
  if (
    date.getDate() === today.getDate() &&
      date.getMonth() === today.getMonth() &&
      date.getFullYear() === today.getFullYear()
  ) {
    return 'Today';
  }

  // Format date in DD/MM/YYYY
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  const dateStr = `${day}/${month}/${year}`;

  return dateStr;
}

export function formatTime (timestamp: number): string {
  const date = new Date(timestamp);

  // Format time in HH:MM AM/PM
  const hours = date.getHours() % 12 || 12; // Convert to 12-hour format
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const ampm = date.getHours() >= 12 ? 'PM' : 'AM';
  const time = `${hours}:${minutes} ${ampm}`;

  return time;
}

export function formatTimeTimezone (timestamp: number, timezone: string): string {
  // Create a moment object with the timestamp and timezone
  const date = moment.tz(timestamp, timezone);

  // Format time in HH:MM AM/PM
  const time = date.format('h:mm A');

  return time;
}
// Object to array

export const objectToArray = (obj: Record<string, any>): any[] => {

  return Object.values(obj);

};

import axios, { type AxiosRequestConfig } from 'axios';
import { getCookie } from './domhelper';
interface GetRequestWithHeadersProps {
  url: string;
  params?: object;
  body?: object;
  headers?: Record<string, string>;
}
export async function GetRequestWithHeaders ({ url }: GetRequestWithHeadersProps) {
  return new Promise((resolve, reject) => {
    const config:AxiosRequestConfig = {
      method: 'get',
      url,
      headers: {
        'accesstoken': String(getCookie('accessToken')) || undefined,
        'organization': String(getCookie('organization')) || undefined,
      },
    };

    axios(config)
      .then(function (response) {

        if (response.data.status === 1) {
          console.log("res,", response);
          resolve(response.data.data);
        } else {
          reject(response.data);
        }
      })
      .catch(function (error) {
        reject(error);
      });
  });

}

export async function GetRequestWithHeadersAndParms ({ url, params}: GetRequestWithHeadersProps) {

  return new Promise((resolve, reject) => {
    const config:AxiosRequestConfig = {
      method: 'get',
      url,
      headers: {
        'accesstoken': String(getCookie('accessToken')) || undefined,
        'organization': String(getCookie('organization')) || undefined,
      },
      params,
    };

    axios(config)
      .then(function (response) {

        if (response.data.status === 1) {
          resolve(response.data.data);
        } else {
          reject(response.data);
        }
      })
      .catch(function (error) {

        reject(error);
      });
  });

}

export async function PostRequestWithHeaders ({ url, body }: GetRequestWithHeadersProps) {

  return new Promise((resolve, reject) => {
    const config:AxiosRequestConfig = {
      method: 'post',
      url,
      headers: {
        'accesstoken': String(getCookie('accessToken')) || undefined,
        'organization': String(getCookie('organization')) || undefined,
      },
      data: body,
    };

    axios(config)
      .then(function (response) {
        if (response.data.status === 1) {
          console.log("data", response);
          resolve(response.data.data);
        } else {
          reject(response.data);
        }
      })
      .catch(function (error) {
        reject(error);
      });
  });

}

export async function PostRequest ({url, body}: GetRequestWithHeadersProps) {

  return new Promise((resolve, reject) => {
    const config:AxiosRequestConfig = {
      method: 'post',
      url,
      headers: {
        'accesstoken': String(getCookie('accessToken')) || undefined,
      },
      data: body,
    };

    axios(config)
      .then(function (response) {

        if (response.data.status===1){
          resolve(response.data.data);
        } else {
          reject(response.data);
        }

      })
      .catch(function (error) {

        reject(error);
      });
  });
}

export async function PutRequestWithHeaders ({ url, body }: GetRequestWithHeadersProps ) {
  return new Promise((resolve, reject) => {
    // Determine the Content-Type based on the body type
    const isFormData = body instanceof FormData;
    const contentType = isFormData ? 'multipart/form-data' : 'application/json';

    const config: AxiosRequestConfig = {
      method: 'put',
      url,
      headers: {
        'accesstoken': String(getCookie('accessToken')) || undefined,
        'organization': String(getCookie('organization')) || undefined,
        'Content-Type': contentType, // Set dynamically based on body type
      },
      data: body,
    };

    axios(config)
      .then(function (response) {
        if (response.data.status === 1) {
          console.log('PUT Response:', response);
          resolve(response.data.data);
        } else {
          // Reject with the server's error message
          reject(response.data.message || 'Request failed');
        }
      })
      .catch(function (error) {
        // Log detailed error information
        console.error('PUT Error:', error.response?.data || error.message);
        reject(error.response?.data || error.message);
      });
  });
}

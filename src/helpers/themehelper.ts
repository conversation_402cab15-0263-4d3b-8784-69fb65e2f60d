const FONTS_API_KEY = import.meta.env.VITE_FONTS_API_KEY;

interface FontItem {
  category: string;
  family: string;
  files: {
    [key: string]: string; // Keys like 'regular' or others map to font URLs
  };
  kind: string;
  lastModified: string;
  menu: string;
  subsets: string[];
  variants: string[];
  version: string;
  items: []
}

interface FONTS {
  items: FontItem[]
}
export const setTheme = (theme: 'light' | 'dark'): void => {
  const htmlElement = document.querySelector('html');

  if (htmlElement) {
    if (theme === 'light') {
      htmlElement.classList.remove('dark');
    } else {
      htmlElement.classList.add('dark');
    }
  } else {
    console.error('HTML element not found');
  }
};

export async  function fetchGoogleFonts ():Promise< string[] | void> {
  return new Promise((resolve, reject) => {
    const url = `https://www.googleapis.com/webfonts/v1/webfonts?key=${FONTS_API_KEY}`;

    fetch(url)
      .then((response) => {
        if (!response.ok) {
          reject(new Error('Error fetching Fonts'));
        }
        return response.json();
      })
      .then((fontsData : FONTS) => {
        const fontFamily = fontsData.items.filter((items) => {
          return items.variants && items.variants.length >= 9;
        }).map((item) => {
          return item.family;
        });
        resolve(fontFamily);
      })
      .catch((error) => {
        console.error('Error fetching Google Fonts:', error);
        reject(error);
      });
  });
}

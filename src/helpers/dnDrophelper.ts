
/* Apply Drag */
interface item {
  category: string;
  id: string;
  name: string;
  order: number;
}
interface dragresultType  {
  addedIndex: number | null;
  element: string;
  payload: item;
  removedIndex: number | null;
}
export const applyDrag = (arr: item[], dragResult: dragresultType ): item[] => {
  const { removedIndex, addedIndex, payload } = dragResult;

  if (removedIndex === null && addedIndex === null) {
    return arr;
  }

  const result = [...arr];
  let itemToAdd= payload;
  if (removedIndex !== null) {
    itemToAdd = result.splice(removedIndex, 1)[0];
  }
  console.log(itemToAdd);
  if (addedIndex !== null) {
    result.splice(addedIndex, 0, itemToAdd);
  }

  return result;
};

export const countryCity = {
  India: {
    name: 'India',
    cities: ['Bangalore', 'Mumbai', 'Delhi', 'Chennai', 'Kolkata', 'Hyderabad', 'Pune', 'Jaipur', 'Ahmedabad', 'Surat'],
  },
  England: {
    name: 'England',
    cities: ['London', 'Manchester', 'Birmingham', 'Liverpool', 'Leeds', 'Sheffield', 'Bristol', 'Newcastle upon Tyne', 'Nottingham', 'Leicester'],
  },
  Qatar: {
    name: 'Qatar',
    cities: ['Doha', 'Al Wakrah', 'Al Khor', 'Al Rayyan', 'Umm Salal', 'Al Daayen', 'Madinat ash Shamal', 'Al Shamal', 'Al Ghuwariyah', 'Dukhan'],
  },
  UAE: {
    name: 'UAE',
    cities: ['Dubai', 'Abu Dhabi', 'Sharjah', 'Al Ain', 'Ajman', 'Ras Al Khaimah', 'Fujairah', 'Umm Al Quwain'],
  },
  Saudi: {
    name: 'Saudi',
    cities: ['Riyadh', 'Jeddah', 'Mecca', 'Medina', 'Dammam', 'Ta\'if', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON> Mushait', '<PERSON><PERSON>'],
  },
  Yemen: {
    name: 'Yemen',
    cities: ['Sana\'a', 'Aden', 'Taiz', 'Al Hudaydah', 'Ibb', 'Dhamar', 'Al Mukalla', 'Zinjibar', 'Saywun', 'Sadah'],
  },
  USA: {
    name: 'USA',
    cities: ['New York City', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia', 'San Antonio', 'San Diego', 'Dallas', 'San Jose'],
  },
  Canada: {
    name: 'Canada',
    cities: ['Toronto', 'Vancouver', 'Montreal', 'Calgary', 'Edmonton', 'Ottawa', 'Winnipeg', 'Quebec City', 'Hamilton', 'London'],
  },
  Brazil: {
    name: 'Brazil',
    cities: ['São Paulo', 'Rio de Janeiro', 'Brasília', 'Salvador', 'Fortaleza', 'Belo Horizonte', 'Manaus', 'Curitiba', 'Recife', 'Porto Alegre'],
  },
  Japan: {
    name: 'Japan',
    cities: ['Tokyo', 'Osaka', 'Nagoya', 'Sapporo', 'Fukuoka', 'Kobe', 'Kyoto', 'Yokohama', 'Sendai', 'Chiba'],
  },
  Eqypt: {
    name: 'Eqypt',
    cities: ['Cairo', 'Alexandria', 'Giza', 'Shubra El Kheima', 'Port Said', 'Suez', 'Mansoura', 'El Mahalla El Kubra', 'Tanta', 'Asyut'],
  },
};

export const categoryItems = ['health', 'education', 'entertainment', 'transit', 'commercial', 'religious', 'governmental', 'recreational', 'landmark', 'other projects', 'other', 'banks', 'metro stations', 'hotels', 'workspaces', 'restaurants', 'shopping', 'malls', 'parks'];

// export const currencyList = ['usd', 'gbp', 'inr', 'aed', 'eur', 'cad', 'sar' ];

export const ctaTypeList = ['default', 'custom', 'event_emit'];

export const unitplanTypeList = ['1BHK', '2BHK', '3BHK', '4BHK', '5BHK', '6BHK', '7BHK', '8BHK', '9BHK', '10BHK', '1.5BHK', '2.5BHK', '3.5BHK', '4.5BHK', '5.5BHK', '6.5BHK', '7.5BHK', '8.5BHK', '9.5BHK', '10.5BHK', 'studio', 'penthouse', 'townhouse', 'suite', 'duplex' ];

export const measurementTypeList = ['sqft', 'sqmt'];

export const sidebarTypeList = ['projectscene', 'masterscene', 'gallery', 'unitplan', 'custom', 'amenity', 'map'];

export const unitStatusList = ['available', 'onhold', 'reserved', 'sold'];

export const leadProductInterest = ['interactive_website_tour',
  'digital_twins_metaverse_applications',
  'remote_showcase_sales_tool',
  'walkthrough_renders',
  'virtual_reality',
  'augmented_reality',
  'projection_mapping',
  'hologram_display',
  'others'];

export const leadIndustryTypes = [
  'real_estate',
  'hospitality',
  'aviation',
  'marine',
  'government_org',
  'others',
];
export const languages = [
  { name: 'Afrikaans', code: 'af' },
  { name: 'Albanian', code: 'sq' },
  { name: 'Amharic', code: 'am' },
  { name: 'Arabic', code: 'ar' },
  { name: 'Armenian', code: 'hy' },
  { name: 'Azerbaijani', code: 'az' },
  { name: 'Basque', code: 'eu' },
  { name: 'Belarusian', code: 'be' },
  { name: 'Bengali', code: 'bn' },
  { name: 'Bosnian', code: 'bs' },
  { name: 'Bulgarian', code: 'bg' },
  { name: 'Catalan', code: 'ca' },
  { name: 'Cebuano', code: 'ceb' },
  { name: 'Chinese (Simplified)', code: 'zh-CN' },
  { name: 'Chinese (Traditional)', code: 'zh-TW' },
  { name: 'Corsican', code: 'co' },
  { name: 'Croatian', code: 'hr' },
  { name: 'Czech', code: 'cs' },
  { name: 'Danish', code: 'da' },
  { name: 'Dutch', code: 'nl' },
  { name: 'English', code: 'en' },
  { name: 'Esperanto', code: 'eo' },
  { name: 'Estonian', code: 'et' },
  { name: 'Finnish', code: 'fi' },
  { name: 'French', code: 'fr' },
  { name: 'Frisian', code: 'fy' },
  { name: 'Galician', code: 'gl' },
  { name: 'Georgian', code: 'ka' },
  { name: 'German', code: 'de' },
  { name: 'Greek', code: 'el' },
  { name: 'Gujarati', code: 'gu' },
  { name: 'Haitian Creole', code: 'ht' },
  { name: 'Hausa', code: 'ha' },
  { name: 'Hawaiian', code: 'haw' },
  { name: 'Hebrew', code: 'iw' },
  { name: 'Hindi', code: 'hi' },
  { name: 'Hmong', code: 'hmn' },
  { name: 'Hungarian', code: 'hu' },
  { name: 'Icelandic', code: 'is' },
  { name: 'Igbo', code: 'ig' },
  { name: 'Indonesian', code: 'id' },
  { name: 'Irish', code: 'ga' },
  { name: 'Italian', code: 'it' },
  { name: 'Japanese', code: 'ja' },
  { name: 'Javanese', code: 'jw' },
  { name: 'Kannada', code: 'kn' },
  { name: 'Kazakh', code: 'kk' },
  { name: 'Khmer', code: 'km' },
  { name: 'Korean', code: 'ko' },
  { name: 'Kurdish', code: 'ku' },
  { name: 'Kyrgyz', code: 'ky' },
  { name: 'Lao', code: 'lo' },
  { name: 'Latin', code: 'la' },
  { name: 'Latvian', code: 'lv' },
  { name: 'Lithuanian', code: 'lt' },
  { name: 'Luxembourgish', code: 'lb' },
  { name: 'Macedonian', code: 'mk' },
  { name: 'Malagasy', code: 'mg' },
  { name: 'Malay', code: 'ms' },
  { name: 'Malayalam', code: 'ml' },
  { name: 'Maltese', code: 'mt' },
  { name: 'Maori', code: 'mi' },
  { name: 'Marathi', code: 'mr' },
  { name: 'Mongolian', code: 'mn' },
  { name: 'Myanmar (Burmese)', code: 'my' },
  { name: 'Nepali', code: 'ne' },
  { name: 'Norwegian', code: 'no' },
  { name: 'Nyanja (Chichewa)', code: 'ny' },
  { name: 'Pashto', code: 'ps' },
  { name: 'Persian', code: 'fa' },
  { name: 'Polish', code: 'pl' },
  { name: 'Portuguese', code: 'pt' },
  { name: 'Punjabi', code: 'pa' },
  { name: 'Romanian', code: 'ro' },
  { name: 'Russian', code: 'ru' },
  { name: 'Samoan', code: 'sm' },
  { name: 'Scots Gaelic', code: 'gd' },
  { name: 'Serbian', code: 'sr' },
  { name: 'Sesotho', code: 'st' },
  { name: 'Shona', code: 'sn' },
  { name: 'Sindhi', code: 'sd' },
  { name: 'Sinhala', code: 'si' },
  { name: 'Slovak', code: 'sk' },
  { name: 'Slovenian', code: 'sl' },
  { name: 'Somali', code: 'so' },
  { name: 'Spanish', code: 'es' },
  { name: 'Sundanese', code: 'su' },
  { name: 'Swahili', code: 'sw' },
  { name: 'Swedish', code: 'sv' },
  { name: 'Tagalog (Filipino)', code: 'tl' },
  { name: 'Tajik', code: 'tg' },
  { name: 'Tamil', code: 'ta' },
  { name: 'Telugu', code: 'te' },
  { name: 'Thai', code: 'th' },
  { name: 'Turkish', code: 'tr' },
  { name: 'Ukrainian', code: 'uk' },
  { name: 'Urdu', code: 'ur' },
  { name: 'Uzbek', code: 'uz' },
  { name: 'Vietnamese', code: 'vi' },
  { name: 'Welsh', code: 'cy' },
  { name: 'Xhosa', code: 'xh' },
  { name: 'Yiddish', code: 'yi' },
  { name: 'Yoruba', code: 'yo' },
  { name: 'Zulu', code: 'zu' },
];

export const experienceTypesMenu = [
  {
    name: 'propvr_360',
    value: 'PropVR 360',
    svg:
     `<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="arrow-up" clip-path="url(#clip0_977_23218)">
      <path id="Vector" d="M11.871 8.6355L11.7704 4.49428C11.7682 4.39776 11.7467 4.30173 11.7072 4.21182C11.6285 4.03104 11.4829 3.88537 11.3021 3.80673C11.2122 3.76723 11.1161 3.74573 11.0196 3.74348L6.8784 3.64291C6.78117 3.63886 6.68504 3.6542 6.59563 3.68804C6.50622 3.72187 6.42532 3.77352 6.35764 3.83997C6.28996 3.90642 6.23687 3.98635 6.20146 4.07508C6.16604 4.16381 6.14902 4.25958 6.15138 4.35679C6.15374 4.45399 6.17544 4.5507 6.21521 4.64126C6.25497 4.73182 6.31201 4.81441 6.383 4.88423C6.45398 4.95405 6.53749 5.0097 6.62865 5.04792C6.71981 5.08613 6.8168 5.10616 6.91395 5.10683L9.28897 5.16554L4.44725 10.0073C4.3133 10.1412 4.24069 10.3256 4.2454 10.5197C4.25012 10.7139 4.33177 10.9019 4.47239 11.0426C4.61302 11.1832 4.8011 11.2648 4.99525 11.2696C5.18941 11.2743 5.37374 11.2017 5.5077 11.0677L10.3494 6.22599L10.4071 8.59995C10.4151 8.79196 10.4981 8.9768 10.6383 9.11467C10.7784 9.25255 10.9645 9.33241 11.1565 9.33708C11.3485 9.34174 11.5309 9.27082 11.6645 9.1396C11.7982 9.00837 11.8723 8.82735 11.871 8.6355Z" fill="#1E429F"/>
      </g>
      <defs>
      <clipPath id="clip0_977_23218">
      <rect width="10" height="10" fill="white" transform="translate(8 0.428925) rotate(45)"/>
      </clipPath>
      </defs>
      </svg>`,
  },
  {
    name: 'embed_v1',
    value: 'Embed V1',
    svg:
      `<svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <g id="arrow-up" clip-path="url(#clip0_977_23225)">
      <path id="Vector" d="M11.871 8.6355L11.7704 4.49428C11.7682 4.39776 11.7467 4.30173 11.7072 4.21182C11.6285 4.03104 11.4829 3.88537 11.3021 3.80673C11.2122 3.76723 11.1161 3.74573 11.0196 3.74348L6.8784 3.64291C6.78117 3.63886 6.68504 3.6542 6.59563 3.68804C6.50622 3.72187 6.42532 3.77352 6.35764 3.83997C6.28996 3.90642 6.23687 3.98635 6.20146 4.07508C6.16604 4.16381 6.14902 4.25958 6.15138 4.35679C6.15374 4.45399 6.17544 4.5507 6.21521 4.64126C6.25497 4.73182 6.31201 4.81441 6.383 4.88423C6.45398 4.95405 6.53749 5.0097 6.62865 5.04792C6.71981 5.08613 6.8168 5.10616 6.91395 5.10683L9.28897 5.16554L4.44725 10.0073C4.3133 10.1412 4.24069 10.3256 4.2454 10.5197C4.25012 10.7139 4.33177 10.9019 4.47239 11.0426C4.61302 11.1832 4.8011 11.2648 4.99525 11.2696C5.18941 11.2743 5.37374 11.2017 5.5077 11.0677L10.3494 6.22599L10.4071 8.59995C10.4151 8.79196 10.4981 8.9768 10.6383 9.11467C10.7784 9.25255 10.9645 9.33241 11.1565 9.33708C11.3485 9.34174 11.5309 9.27082 11.6645 9.1396C11.7982 9.00837 11.8723 8.82735 11.871 8.6355Z" fill="#99154B"/>
      </g>
      <defs>
      <clipPath id="clip0_977_23225">
      <rect width="10" height="10" fill="white" transform="translate(8 0.428925) rotate(45)"/>
      </clipPath>
      </defs>
      </svg>`,
  },
  {
    name: 'rt_application',
    value: 'RT Application',
  },
];

export const controlButtonSVGs = [
  {
    name: 'Analytics',
    svg: `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="chart-pie" clip-path="url(#clip0_1316_45899)">
<g id="Vector">
<path d="M11.0526 7.36842H6.63158V2.94737C6.63158 2.75195 6.55395 2.56453 6.41576 2.42634C6.27758 2.28816 6.09016 2.21053 5.89474 2.21053C4.72887 2.21053 3.58918 2.55625 2.6198 3.20397C1.65041 3.85169 0.894871 4.77232 0.448713 5.84944C0.00255408 6.92657 -0.114181 8.1118 0.113268 9.25527C0.340718 10.3987 0.902137 11.4491 1.72653 12.2735C2.55092 13.0979 3.60127 13.6593 4.74473 13.8867C5.8882 14.1142 7.07343 13.9974 8.15056 13.5513C9.22768 13.1051 10.1483 12.3496 10.796 11.3802C11.4438 10.4108 11.7895 9.27113 11.7895 8.10526C11.7895 7.90984 11.7118 7.72242 11.5737 7.58424C11.4355 7.44605 11.2481 7.36842 11.0526 7.36842Z" fill="#1C64F2"/>
<path d="M8.10526 0C7.90984 0 7.72242 0.0776313 7.58424 0.215816C7.44605 0.354001 7.36842 0.541419 7.36842 0.736842V5.89474C7.36842 6.09016 7.44605 6.27758 7.58424 6.41576C7.72242 6.55395 7.90984 6.63158 8.10526 6.63158H13.2632C13.4586 6.63158 13.646 6.55395 13.7842 6.41576C13.9224 6.27758 14 6.09016 14 5.89474C13.9982 4.33189 13.3766 2.83356 12.2715 1.72847C11.1664 0.62337 9.66811 0.00175524 8.10526 0Z" fill="#1C64F2"/>
</g>
</g>
<defs>
<clipPath id="clip0_1316_45899">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  }, {
    name: 'Settings',
    svg: `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="cog" clip-path="url(#clip0_1316_45908)">
<path id="Vector" d="M13.2222 5.44444H12.6334L12.0836 4.11678L12.4997 3.69989C12.6455 3.55403 12.7274 3.35624 12.7274 3.15C12.7274 2.94376 12.6455 2.74597 12.4997 2.60011L11.3999 1.50033C11.254 1.35452 11.0562 1.27261 10.85 1.27261C10.6438 1.27261 10.446 1.35452 10.3001 1.50033L9.88322 1.91644L8.55556 1.36656V0.777778C8.55556 0.571498 8.47361 0.373667 8.32775 0.227806C8.18189 0.0819442 7.98406 0 7.77778 0L6.22222 0C6.01594 0 5.81811 0.0819442 5.67225 0.227806C5.52639 0.373667 5.44444 0.571498 5.44444 0.777778V1.36656L4.11678 1.91644L3.69989 1.50033C3.55403 1.35452 3.35624 1.27261 3.15 1.27261C2.94376 1.27261 2.74597 1.35452 2.60011 1.50033L1.50033 2.60011C1.35452 2.74597 1.27261 2.94376 1.27261 3.15C1.27261 3.35624 1.35452 3.55403 1.50033 3.69989L1.91722 4.11678L1.36656 5.44444H0.777778C0.571498 5.44444 0.373667 5.52639 0.227806 5.67225C0.0819442 5.81811 0 6.01594 0 6.22222L0 7.77778C0 7.98406 0.0819442 8.18189 0.227806 8.32775C0.373667 8.47361 0.571498 8.55556 0.777778 8.55556H1.36656C1.63178 9.19567 1.652 9.24311 1.91644 9.88322L1.50033 10.3001C1.35452 10.446 1.27261 10.6438 1.27261 10.85C1.27261 11.0562 1.35452 11.254 1.50033 11.3999L2.60011 12.4997C2.74597 12.6455 2.94376 12.7274 3.15 12.7274C3.35624 12.7274 3.55403 12.6455 3.69989 12.4997L4.11678 12.0836L5.44444 12.6334V13.2222C5.44444 13.4285 5.52639 13.6263 5.67225 13.7722C5.81811 13.9181 6.01594 14 6.22222 14H7.77778C7.98406 14 8.18189 13.9181 8.32775 13.7722C8.47361 13.6263 8.55556 13.4285 8.55556 13.2222V12.6334L9.88322 12.0828L10.3001 12.4997C10.446 12.6455 10.6438 12.7274 10.85 12.7274C11.0562 12.7274 11.254 12.6455 11.3999 12.4997L12.4997 11.3999C12.6455 11.254 12.7274 11.0562 12.7274 10.85C12.7274 10.6438 12.6455 10.446 12.4997 10.3001L12.0836 9.88322L12.6334 8.55556H13.2222C13.4285 8.55556 13.6263 8.47361 13.7722 8.32775C13.9181 8.18189 14 7.98406 14 7.77778V6.22222C14 6.01594 13.9181 5.81811 13.7722 5.67225C13.6263 5.52639 13.4285 5.44444 13.2222 5.44444ZM7 10.1111C6.38468 10.1111 5.78318 9.92865 5.27156 9.58679C4.75994 9.24494 4.36118 8.75905 4.12571 8.19057C3.89024 7.62209 3.82862 6.99655 3.94867 6.39305C4.06871 5.78956 4.36502 5.23521 4.80011 4.80011C5.23521 4.36502 5.78956 4.06871 6.39305 3.94867C6.99655 3.82862 7.62209 3.89024 8.19057 4.12571C8.75905 4.36118 9.24494 4.75994 9.58679 5.27156C9.92865 5.78318 10.1111 6.38468 10.1111 7C10.1111 7.82512 9.78333 8.61644 9.19989 9.19989C8.61644 9.78333 7.82512 10.1111 7 10.1111Z" fill="#1C64F2"/>
</g>
<defs>
<clipPath id="clip0_1316_45908">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  },
  {
    name: 'Creator',
    svg: `<svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="window" clip-path="url(#clip0_1316_45917)">
<path id="Vector" d="M13.1 0.583328H1.9C1.1279 0.583328 0.5 1.22286 0.5 2.00925V11.9907C0.5 12.7771 1.1279 13.4167 1.9 13.4167H13.1C13.8721 13.4167 14.5 12.7771 14.5 11.9907V2.00925C14.5 1.22286 13.8721 0.583328 13.1 0.583328ZM7.85 2.72222C8.2364 2.72222 8.55 3.04162 8.55 3.43518C8.55 3.82874 8.2364 4.14814 7.85 4.14814C7.4636 4.14814 7.15 3.82874 7.15 3.43518C7.15 3.04162 7.4636 2.72222 7.85 2.72222ZM5.75 2.72222C6.1364 2.72222 6.45 3.04162 6.45 3.43518C6.45 3.82874 6.1364 4.14814 5.75 4.14814C5.3636 4.14814 5.05 3.82874 5.05 3.43518C5.05 3.04162 5.3636 2.72222 5.75 2.72222ZM3.65 2.72222C4.0364 2.72222 4.35 3.04162 4.35 3.43518C4.35 3.82874 4.0364 4.14814 3.65 4.14814C3.2636 4.14814 2.95 3.82874 2.95 3.43518C2.95 3.04162 3.2636 2.72222 3.65 2.72222ZM1.9 11.9907V6.28703H13.1007L13.1014 11.9907H1.9Z" fill="#1C64F2"/>
</g>
<defs>
<clipPath id="clip0_1316_45917">
<rect width="14" height="14" fill="white" transform="translate(0.5)"/>
</clipPath>
</defs>
</svg>
`,
  },
];
export const activeControlButtonSVGs = [
  {
    name: 'Analytics',
    svg: `<svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="chart-pie" clip-path="url(#clip0_2242_13691)">
<g id="Vector">
<path d="M11.0526 7.86842H6.63158V3.44737C6.63158 3.25195 6.55395 3.06453 6.41576 2.92634C6.27758 2.78816 6.09016 2.71053 5.89474 2.71053C4.72887 2.71053 3.58918 3.05625 2.6198 3.70397C1.65041 4.35169 0.894871 5.27232 0.448713 6.34944C0.00255408 7.42657 -0.114181 8.6118 0.113268 9.75527C0.340718 10.8987 0.902137 11.9491 1.72653 12.7735C2.55092 13.5979 3.60127 14.1593 4.74473 14.3867C5.8882 14.6142 7.07343 14.4974 8.15056 14.0513C9.22768 13.6051 10.1483 12.8496 10.796 11.8802C11.4438 10.9108 11.7895 9.77113 11.7895 8.60526C11.7895 8.40984 11.7118 8.22242 11.5737 8.08424C11.4355 7.94605 11.2481 7.86842 11.0526 7.86842Z" fill="white"/>
<path d="M8.10526 0.5C7.90984 0.5 7.72242 0.577631 7.58424 0.715816C7.44605 0.854001 7.36842 1.04142 7.36842 1.23684V6.39474C7.36842 6.59016 7.44605 6.77758 7.58424 6.91576C7.72242 7.05395 7.90984 7.13158 8.10526 7.13158H13.2632C13.4586 7.13158 13.646 7.05395 13.7842 6.91576C13.9224 6.77758 14 6.59016 14 6.39474C13.9982 4.83189 13.3766 3.33356 12.2715 2.22847C11.1664 1.12337 9.66811 0.501755 8.10526 0.5Z" fill="white"/>
</g>
</g>
<defs>
<clipPath id="clip0_2242_13691">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
`,
  }, {
    name: 'Settings',
    svg: `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3587_16268)">
<path d="M13.2222 5.44444H12.6334L12.0836 4.11678L12.4997 3.69989C12.6455 3.55403 12.7274 3.35624 12.7274 3.15C12.7274 2.94376 12.6455 2.74597 12.4997 2.60011L11.3999 1.50033C11.254 1.35452 11.0562 1.27261 10.85 1.27261C10.6438 1.27261 10.446 1.35452 10.3001 1.50033L9.88322 1.91644L8.55556 1.36656V0.777778C8.55556 0.571498 8.47361 0.373667 8.32775 0.227806C8.18189 0.0819442 7.98406 0 7.77778 0L6.22222 0C6.01594 0 5.81811 0.0819442 5.67225 0.227806C5.52639 0.373667 5.44444 0.571498 5.44444 0.777778V1.36656L4.11678 1.91644L3.69989 1.50033C3.55403 1.35452 3.35624 1.27261 3.15 1.27261C2.94376 1.27261 2.74597 1.35452 2.60011 1.50033L1.50033 2.60011C1.35452 2.74597 1.27261 2.94376 1.27261 3.15C1.27261 3.35624 1.35452 3.55403 1.50033 3.69989L1.91722 4.11678L1.36656 5.44444H0.777778C0.571498 5.44444 0.373667 5.52639 0.227806 5.67225C0.0819442 5.81811 0 6.01594 0 6.22222L0 7.77778C0 7.98406 0.0819442 8.18189 0.227806 8.32775C0.373667 8.47361 0.571498 8.55556 0.777778 8.55556H1.36656C1.63178 9.19567 1.652 9.24311 1.91644 9.88322L1.50033 10.3001C1.35452 10.446 1.27261 10.6438 1.27261 10.85C1.27261 11.0562 1.35452 11.254 1.50033 11.3999L2.60011 12.4997C2.74597 12.6455 2.94376 12.7274 3.15 12.7274C3.35624 12.7274 3.55403 12.6455 3.69989 12.4997L4.11678 12.0836L5.44444 12.6334V13.2222C5.44444 13.4285 5.52639 13.6263 5.67225 13.7722C5.81811 13.9181 6.01594 14 6.22222 14H7.77778C7.98406 14 8.18189 13.9181 8.32775 13.7722C8.47361 13.6263 8.55556 13.4285 8.55556 13.2222V12.6334L9.88322 12.0828L10.3001 12.4997C10.446 12.6455 10.6438 12.7274 10.85 12.7274C11.0562 12.7274 11.254 12.6455 11.3999 12.4997L12.4997 11.3999C12.6455 11.254 12.7274 11.0562 12.7274 10.85C12.7274 10.6438 12.6455 10.446 12.4997 10.3001L12.0836 9.88322L12.6334 8.55556H13.2222C13.4285 8.55556 13.6263 8.47361 13.7722 8.32775C13.9181 8.18189 14 7.98406 14 7.77778V6.22222C14 6.01594 13.9181 5.81811 13.7722 5.67225C13.6263 5.52639 13.4285 5.44444 13.2222 5.44444ZM7 10.1111C6.38468 10.1111 5.78318 9.92865 5.27156 9.58679C4.75994 9.24494 4.36118 8.75905 4.12571 8.19057C3.89024 7.62209 3.82863 6.99655 3.94867 6.39305C4.06871 5.78956 4.36502 5.23521 4.80011 4.80011C5.23521 4.36502 5.78956 4.06871 6.39305 3.94867C6.99655 3.82863 7.62209 3.89024 8.19057 4.12571C8.75905 4.36118 9.24494 4.75994 9.58679 5.27156C9.92865 5.78318 10.1111 6.38468 10.1111 7C10.1111 7.82512 9.78333 8.61644 9.19989 9.19989C8.61644 9.78333 7.82512 10.1111 7 10.1111Z" fill="white"/>
</g>
<defs>
<clipPath id="clip0_3587_16268">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>

`,
  },
  {
    name: 'Creator',
    svg: `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3587_16273)">
<path d="M12.6 0.583252H1.4C0.6279 0.583252 0 1.22278 0 2.00918L0 11.9907C0 12.7771 0.6279 13.4166 1.4 13.4166H12.6C13.3721 13.4166 14 12.7771 14 11.9907V2.00918C14 1.22278 13.3721 0.583252 12.6 0.583252ZM7.35 2.72214C7.7364 2.72214 8.05 3.04155 8.05 3.4351C8.05 3.82866 7.7364 4.14807 7.35 4.14807C6.9636 4.14807 6.65 3.82866 6.65 3.4351C6.65 3.04155 6.9636 2.72214 7.35 2.72214ZM5.25 2.72214C5.6364 2.72214 5.95 3.04155 5.95 3.4351C5.95 3.82866 5.6364 4.14807 5.25 4.14807C4.8636 4.14807 4.55 3.82866 4.55 3.4351C4.55 3.04155 4.8636 2.72214 5.25 2.72214ZM3.15 2.72214C3.5364 2.72214 3.85 3.04155 3.85 3.4351C3.85 3.82866 3.5364 4.14807 3.15 4.14807C2.7636 4.14807 2.45 3.82866 2.45 3.4351C2.45 3.04155 2.7636 2.72214 3.15 2.72214ZM1.4 11.9907V6.28696H12.6007L12.6014 11.9907H1.4Z" fill="white"/>
</g>
<defs>
<clipPath id="clip0_3587_16273">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  },
];
export const controlButtonSVGsMobileView = [
  {
    name: 'Analytics',
    svg: `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="chart-pie" clip-path="url(#clip0_1316_45899)">
<g id="Vector">
<path d="M11.0526 7.36842H6.63158V2.94737C6.63158 2.75195 6.55395 2.56453 6.41576 2.42634C6.27758 2.28816 6.09016 2.21053 5.89474 2.21053C4.72887 2.21053 3.58918 2.55625 2.6198 3.20397C1.65041 3.85169 0.894871 4.77232 0.448713 5.84944C0.00255408 6.92657 -0.114181 8.1118 0.113268 9.25527C0.340718 10.3987 0.902137 11.4491 1.72653 12.2735C2.55092 13.0979 3.60127 13.6593 4.74473 13.8867C5.8882 14.1142 7.07343 13.9974 8.15056 13.5513C9.22768 13.1051 10.1483 12.3496 10.796 11.3802C11.4438 10.4108 11.7895 9.27113 11.7895 8.10526C11.7895 7.90984 11.7118 7.72242 11.5737 7.58424C11.4355 7.44605 11.2481 7.36842 11.0526 7.36842Z" fill="#1C64F2"/>
<path d="M8.10526 0C7.90984 0 7.72242 0.0776313 7.58424 0.215816C7.44605 0.354001 7.36842 0.541419 7.36842 0.736842V5.89474C7.36842 6.09016 7.44605 6.27758 7.58424 6.41576C7.72242 6.55395 7.90984 6.63158 8.10526 6.63158H13.2632C13.4586 6.63158 13.646 6.55395 13.7842 6.41576C13.9224 6.27758 14 6.09016 14 5.89474C13.9982 4.33189 13.3766 2.83356 12.2715 1.72847C11.1664 0.62337 9.66811 0.00175524 8.10526 0Z" fill="#1C64F2"/>
</g>
</g>
<defs>
<clipPath id="clip0_1316_45899">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  }, {
    name: 'Settings',
    svg: `<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="cog" clip-path="url(#clip0_1316_45908)">
<path id="Vector" d="M13.2222 5.44444H12.6334L12.0836 4.11678L12.4997 3.69989C12.6455 3.55403 12.7274 3.35624 12.7274 3.15C12.7274 2.94376 12.6455 2.74597 12.4997 2.60011L11.3999 1.50033C11.254 1.35452 11.0562 1.27261 10.85 1.27261C10.6438 1.27261 10.446 1.35452 10.3001 1.50033L9.88322 1.91644L8.55556 1.36656V0.777778C8.55556 0.571498 8.47361 0.373667 8.32775 0.227806C8.18189 0.0819442 7.98406 0 7.77778 0L6.22222 0C6.01594 0 5.81811 0.0819442 5.67225 0.227806C5.52639 0.373667 5.44444 0.571498 5.44444 0.777778V1.36656L4.11678 1.91644L3.69989 1.50033C3.55403 1.35452 3.35624 1.27261 3.15 1.27261C2.94376 1.27261 2.74597 1.35452 2.60011 1.50033L1.50033 2.60011C1.35452 2.74597 1.27261 2.94376 1.27261 3.15C1.27261 3.35624 1.35452 3.55403 1.50033 3.69989L1.91722 4.11678L1.36656 5.44444H0.777778C0.571498 5.44444 0.373667 5.52639 0.227806 5.67225C0.0819442 5.81811 0 6.01594 0 6.22222L0 7.77778C0 7.98406 0.0819442 8.18189 0.227806 8.32775C0.373667 8.47361 0.571498 8.55556 0.777778 8.55556H1.36656C1.63178 9.19567 1.652 9.24311 1.91644 9.88322L1.50033 10.3001C1.35452 10.446 1.27261 10.6438 1.27261 10.85C1.27261 11.0562 1.35452 11.254 1.50033 11.3999L2.60011 12.4997C2.74597 12.6455 2.94376 12.7274 3.15 12.7274C3.35624 12.7274 3.55403 12.6455 3.69989 12.4997L4.11678 12.0836L5.44444 12.6334V13.2222C5.44444 13.4285 5.52639 13.6263 5.67225 13.7722C5.81811 13.9181 6.01594 14 6.22222 14H7.77778C7.98406 14 8.18189 13.9181 8.32775 13.7722C8.47361 13.6263 8.55556 13.4285 8.55556 13.2222V12.6334L9.88322 12.0828L10.3001 12.4997C10.446 12.6455 10.6438 12.7274 10.85 12.7274C11.0562 12.7274 11.254 12.6455 11.3999 12.4997L12.4997 11.3999C12.6455 11.254 12.7274 11.0562 12.7274 10.85C12.7274 10.6438 12.6455 10.446 12.4997 10.3001L12.0836 9.88322L12.6334 8.55556H13.2222C13.4285 8.55556 13.6263 8.47361 13.7722 8.32775C13.9181 8.18189 14 7.98406 14 7.77778V6.22222C14 6.01594 13.9181 5.81811 13.7722 5.67225C13.6263 5.52639 13.4285 5.44444 13.2222 5.44444ZM7 10.1111C6.38468 10.1111 5.78318 9.92865 5.27156 9.58679C4.75994 9.24494 4.36118 8.75905 4.12571 8.19057C3.89024 7.62209 3.82862 6.99655 3.94867 6.39305C4.06871 5.78956 4.36502 5.23521 4.80011 4.80011C5.23521 4.36502 5.78956 4.06871 6.39305 3.94867C6.99655 3.82862 7.62209 3.89024 8.19057 4.12571C8.75905 4.36118 9.24494 4.75994 9.58679 5.27156C9.92865 5.78318 10.1111 6.38468 10.1111 7C10.1111 7.82512 9.78333 8.61644 9.19989 9.19989C8.61644 9.78333 7.82512 10.1111 7 10.1111Z" fill="#1C64F2"/>
</g>
<defs>
<clipPath id="clip0_1316_45908">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>
`,
  },
];

export const createProjectSVG=`
<svg width="101" height="100" viewBox="0 0 101 100" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<rect x="0.5" width="100" height="100" fill="url(#pattern0_709_12616)"/>
<defs>
<pattern id="pattern0_709_12616" patternContentUnits="objectBoundingBox" width="1" height="1">
<use xlink:href="#image0_709_12616" transform="scale(0.0025)"/>
</pattern>
<image id="image0_709_12616" width="400" height="400" xlink:href="data:image/png;base64,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"/>
</defs>
</svg>
`;

export const uploadFileSVG =`
<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="desktop-pc" clip-path="url(#clip0_711_5567)">
<path id="Vector" d="M14.4 0H1.6C1.17565 0 0.768687 0.168571 0.468629 0.468629C0.168571 0.768687 0 1.17565 0 1.6V10.4C0 10.8243 0.168571 11.2313 0.468629 11.5314C0.768687 11.8314 1.17565 12 1.6 12H7.2V14.4H4.8C4.58783 14.4 4.38434 14.4843 4.23431 14.6343C4.08429 14.7843 4 14.9878 4 15.2C4 15.4122 4.08429 15.6157 4.23431 15.7657C4.38434 15.9157 4.58783 16 4.8 16H11.2C11.4122 16 11.6157 15.9157 11.7657 15.7657C11.9157 15.6157 12 15.4122 12 15.2C12 14.9878 11.9157 14.7843 11.7657 14.6343C11.6157 14.4843 11.4122 14.4 11.2 14.4H8.8V12H14.4C14.8243 12 15.2313 11.8314 15.5314 11.5314C15.8314 11.2313 16 10.8243 16 10.4V1.6C16 1.17565 15.8314 0.768687 15.5314 0.468629C15.2313 0.168571 14.8243 0 14.4 0ZM14.4 1.6V8H1.6V1.6H14.4Z" fill="#1C64F2"/>
</g>
<defs>
<clipPath id="clip0_711_5567">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>
`;
export const currencyList = [
  'AED', 'ARS', 'AUD', 'BGN', 'BRL', 'BSD', 'CAD', 'CHF', 'CLP', 'CNY',
  'COP', 'CZK', 'DKK', 'DOP', 'EGP', 'EUR', 'FJD', 'GBP', 'GTQ', 'HKD',
  'HRK', 'HUF', 'IDR', 'ILS', 'INR', 'ISK', 'JPY', 'KRW', 'KZT', 'MXN',
  'MYR', 'NOK', 'NZD', 'PAB', 'PEN', 'PHP', 'PKR', 'PLN', 'PYG', 'RON',
  'RUB', 'SAR', 'SEK', 'SGD', 'THB', 'TRY', 'TWD', 'UAH', 'USD', 'UYU',
  'VND', 'ZAR', 'LBP', 'QAR', 'JOD', 'PKR',
];

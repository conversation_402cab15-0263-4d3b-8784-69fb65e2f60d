<script setup>
import { UserStore } from '../../store/index';
// import DropDown from './DropDown.vue';
import { menuListTop } from '../../config/Organizationsidebar.ts';
import { ref, watch, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import ProfileCard from './ProfileCard.vue';
import { deleteCookie, getCookie } from '../../helpers/domhelper';
import { getAuth, signOut } from 'firebase/auth';
import { onMessage } from 'firebase/messaging';
import { messaging } from '../../firebaseConfig';
import { Org_Store } from '@/store/organization';
import { uiOperations } from '@/store/uiOperations';
import { computed } from 'vue';

const route = useRoute();
const store = UserStore();
const opt = ref(null);
const user_email = ref(null);
const user_id = ref(null);
const user_first_name = ref(null);
const user_last_name = ref(null);
const user_phone_no = ref(null);
const user_picture = ref(null);
const user_role = ref(null);
const orgStore = Org_Store();
const showMoreMenu = ref(false);
const dropdownOpen = ref(false);
const ui = uiOperations();

//  the watcher runs on initialization

watch(() => store.user_data, (newData) => {
  if (newData) {
    user_email.value = newData.email;
    user_phone_no.value = newData.phone;
    user_picture.value = newData.profilePicture;
    user_role.value = newData.role;
    user_id.value = newData._id;
    user_first_name.value = newData.first_name;
    user_last_name.value = newData.last_name;
    user_first_name.value = store.user_data.first_name;
    user_last_name.value = store.user_data.last_name;
    console.log(user_picture.value);

    // localStorage.setItem("user_data", JSON.stringify(newData));
  }
} );

const handleSignOut = async () => {
  if (getCookie('pushToken')){
    console.log(getCookie('pushToken'));
    const payload = {
      _id: store.user_data._id,
      fcmToken: getCookie('pushToken'),
      action: "remove",
    };
    console.log(payload);
    await store.UpdateFcmToken(payload);
  }
  const auth = getAuth();
  signOut(auth).then(() => {
    deleteCookie('refreshToken');
    deleteCookie('refreshToken');
    deleteCookie('accessToken');
    deleteCookie('pushToken');
    window.location = '/login';
  });

};

opt.value = [
  {
    name: 'log out',
  },
];

const menuListBottom = [
  {
    name: 'About',
    route: '/about',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
          <g clip-path="url(#clip0_713_8928)">
            <path d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM7.6 3.2C7.83734 3.2 8.06935 3.27038 8.26669 3.40224C8.46402 3.53409 8.61783 3.72151 8.70866 3.94078C8.79948 4.16005 8.82325 4.40133 8.77694 4.63411C8.73064 4.86688 8.61635 5.0807 8.44853 5.24853C8.28071 5.41635 8.06689 5.53064 7.83411 5.57694C7.60133 5.62324 7.36005 5.59948 7.14078 5.50865C6.92151 5.41783 6.7341 5.26402 6.60224 5.06668C6.47038 4.86934 6.4 4.63734 6.4 4.4C6.4 4.08174 6.52643 3.77651 6.75147 3.55147C6.97652 3.32643 7.28174 3.2 7.6 3.2ZM9.6 12H6.4C6.18783 12 5.98435 11.9157 5.83432 11.7657C5.68429 11.6157 5.6 11.4122 5.6 11.2C5.6 10.9878 5.68429 10.7843 5.83432 10.6343C5.98435 10.4843 6.18783 10.4 6.4 10.4H7.2V8H6.4C6.18783 8 5.98435 7.91571 5.83432 7.76568C5.68429 7.61565 5.6 7.41217 5.6 7.2C5.6 6.98782 5.68429 6.78434 5.83432 6.63431C5.98435 6.48428 6.18783 6.4 6.4 6.4H8C8.21218 6.4 8.41566 6.48428 8.56569 6.63431C8.71572 6.78434 8.8 6.98782 8.8 7.2V10.4H9.6C9.81217 10.4 10.0157 10.4843 10.1657 10.6343C10.3157 10.7843 10.4 10.9878 10.4 11.2C10.4 11.4122 10.3157 11.6157 10.1657 11.7657C10.0157 11.9157 9.81217 12 9.6 12Z" fill="#6B7280"/>
          </g>
          <defs>
            <clipPath id="clip0_713_8928">
              <rect width="16" height="16" fill="white"/>
            </clipPath>
          </defs>
        </svg>`,
  },
  {
    name: 'Unitplans',
    route: '/unitplans',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-6 w-6">
                <g data-name="Layer 2">
                    <g data-name="calendar">
                        <rect width="24" height="24" opacity="0" />
                        <path
                            d="M18 4h-1V3a1 1 0 0 0-2 0v1H9V3a1 1 0 0 0-2 0v1H6a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3zM6 6h1v1a1 1 0 0 0 2 0V6h6v1a1 1 0 0 0 2 0V6h1a1 1 0 0 1 1 1v4H5V7a1 1 0 0 1 1-1zm12 14H6a1 1 0 0 1-1-1v-6h14v6a1 1 0 0 1-1 1z" />
                        <circle cx="8" cy="16" r="1" />
                        <path d="M16 15h-4a1 1 0 0 0 0 2h4a1 1 0 0 0 0-2z" />
                    </g>
                </g>
            </svg>`,
  },
  {
    name: 'Units',
    route: '/units',
    icon: `<svg width="24" height="24" viewBox="0 0 24 24" class="h-6 w-6"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M19.4062 4.71096C18.6436 3.93261 17.7334 3.31408 16.729 2.89154C15.7245 2.46899 14.6459 2.25091 13.5562 2.25003H13.5253C12.4406 2.24724 11.366 2.45967 10.364 2.87501C9.36189 3.29036 8.45217 3.90036 7.68748 4.66971L1.93216 10.5C1.65353 10.7818 1.49773 11.1624 1.49878 11.5587C1.49984 11.955 1.65766 12.3347 1.93779 12.615L4.62466 15.3019C4.76375 15.4417 4.92914 15.5525 5.11129 15.6279C5.29345 15.7034 5.48875 15.742 5.68591 15.7416H5.69435C5.89294 15.7405 6.08935 15.7 6.27217 15.6225C6.45499 15.5449 6.62058 15.4318 6.75935 15.2897L12.4687 9.43971C12.7475 9.16371 13.1229 9.00726 13.5152 9.00358C13.9075 8.99991 14.2858 9.1493 14.5697 9.42003C14.71 9.55634 14.8215 9.71942 14.8977 9.89962C14.9739 10.0798 15.0131 10.2735 15.0131 10.4691C15.015 10.6773 14.9758 10.8838 14.8978 11.0769C14.8198 11.2699 14.7044 11.4457 14.5584 11.5941L8.71873 17.2388C8.57609 17.3773 8.46242 17.5427 8.38431 17.7256C8.30619 17.9084 8.26521 18.1049 8.26372 18.3037C8.26224 18.5025 8.30029 18.6996 8.37567 18.8836C8.45104 19.0676 8.56224 19.2347 8.70279 19.3753L11.3897 22.0622C11.6698 22.3406 12.0481 22.4978 12.443 22.4999C12.8379 22.502 13.2178 22.3489 13.5009 22.0735L19.2947 16.4063C22.5243 13.1719 22.574 7.92753 19.4062 4.71096ZM5.68591 14.2397L2.99998 11.5538L5.23123 9.29253L7.90498 11.9663L5.68591 14.2397ZM12.449 21L9.76029 18.3141L12.0459 16.1044L14.7187 18.78L12.449 21ZM18.239 15.3338L15.7912 17.7282L13.125 15.0638L15.6037 12.6666L15.6131 12.6582C15.9006 12.3683 16.1279 12.0244 16.2819 11.6463C16.4359 11.2681 16.5135 10.8633 16.5103 10.455C16.5086 10.0605 16.4277 9.67042 16.2725 9.30776C16.1173 8.94511 15.8908 8.6173 15.6065 8.34378C15.0369 7.80195 14.2784 7.50369 13.4923 7.51245C12.7062 7.5212 11.9546 7.83627 11.3972 8.39065L8.95216 10.8919L6.28123 8.22565L8.75154 5.72721C9.3769 5.09825 10.1208 4.59963 10.9403 4.26024C11.7597 3.92085 12.6384 3.74743 13.5253 3.75003H13.5506C14.4421 3.7508 15.3246 3.92928 16.1463 4.27504C16.9681 4.62081 17.7127 5.12691 18.3365 5.76378C20.9297 8.39534 20.8828 12.6919 18.239 15.3357V15.3338Z" />
                    </svg>`,
  },
  {
    name: 'Landmarks',
    route: '/landmarks',
    icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M200-120v-680h360l16 80h224v400H520l-16-80H280v280h-80Zm300-440Zm86 160h134v-240H510l-16-80H280v240h290l16 80Z"/></svg>',
  },
  {
    name: 'Assets',
    route: '/assets',
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="briefcase"><rect opacity="0"/><path d="M19 7h-3V5.5A2.5 2.5 0 0 0 13.5 3h-3A2.5 2.5 0 0 0 8 5.5V7H5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3zm-4 2v10H9V9zm-5-3.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5V7h-4zM4 18v-8a1 1 0 0 1 1-1h2v10H5a1 1 0 0 1-1-1zm16 0a1 1 0 0 1-1 1h-2V9h2a1 1 0 0 1 1 1z"/></g></g></svg>',
  },
  {
    name: 'Exterior',
    route: '/exterior',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
  <path d="M7.99935 2.58008H2.66602V13.2467H7.99935V4.58008H11.3327V13.2467H13.3327V5.24674V2.58008H7.99935Z" fill="#6B7280" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
</svg>`,
  },
  {
    name: 'Amenities',
    route: '/amenities',
    icon: `
    <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M760-600q-57 0-99-34t-56-86H354q-11 42-41.5 72.5T240-606v251q52 14 86 56t34 99q0 66-47 113T200-40q-66 0-113-47T40-200q0-57 34-99t86-56v-251q-52-14-86-56t-34-98q0-66 47-113t113-47q56 0 98 34t56 86h251q14-52 56-86t99-34q66 0 113 47t47 113q0 66-47 113t-113 47ZM200-120q33 0 56.5-24t23.5-56q0-33-23.5-56.5T200-280q-32 0-56 23.5T120-200q0 32 24 56t56 24Zm0-560q33 0 56.5-23.5T280-760q0-33-23.5-56.5T200-840q-32 0-56 23.5T120-760q0 33 24 56.5t56 23.5ZM760-40q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113T760-40Zm0-80q33 0 56.5-24t23.5-56q0-33-23.5-56.5T760-280q-33 0-56.5 23.5T680-200q0 32 23.5 56t56.5 24Zm0-560q33 0 56.5-23.5T840-760q0-33-23.5-56.5T760-840q-33 0-56.5 23.5T680-760q0 33 23.5 56.5T760-680ZM200-200Zm0-560Zm560 560Zm0-560Z"/></svg>`,
  },
  {
    name: 'Analytics',
    route: '/analytics',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 -35.5 170 170">
<g clip-path="url(#clip0)">
<path d="M159.524 0.393145C156.399 0.123788 154.058 0.750571 152.37 2.30179C150.171 4.32164 149.108 7.85474 149.031 13.4077C147.212 15.4816 145.399 17.5445 143.593 19.5965C139.42 24.3401 135.106 29.2446 130.918 34.0834C126.73 38.9222 122.505 43.8811 118.419 48.6762C116.665 50.7333 114.91 52.7923 113.152 54.8533C112.677 54.843 112.182 54.8083 111.663 54.7723C110.128 54.5701 108.57 54.6493 107.064 55.0063C105.06 55.6145 103.869 55.0462 102.107 53.6307C92.4085 45.8476 83.3948 39.8002 74.55 35.1446C73.8555 34.8254 73.2439 34.3489 72.7624 33.752C72.2816 33.1552 71.9436 32.4542 71.7758 31.704C71.0743 29.0054 69.3499 26.6922 66.9731 25.2626C64.5969 23.833 61.7583 23.4013 59.0692 24.0605C56.3348 24.7041 53.9423 26.364 52.3692 28.7085C50.7961 31.0531 50.1584 33.9097 50.5836 36.7075C50.6933 37.4468 50.826 38.1861 50.9536 38.89L51.056 39.4685L15.5387 73.8969C15.3582 73.8795 15.1783 73.8596 14.9991 73.8409C14.4072 73.7767 13.7946 73.7124 13.1805 73.6963C7.30631 73.5259 3.69542 76.116 1.80964 81.8503C0.395138 86.151 1.94355 89.9895 3.23178 92.5031C4.10457 94.3089 5.43379 95.8517 7.0859 96.9748C8.73803 98.0985 10.6546 98.7639 12.6428 98.9034C12.843 98.9143 13.0427 98.9195 13.2422 98.9201C15.156 98.8912 17.0327 98.382 18.7028 97.4396C20.3728 96.4965 21.7836 95.1497 22.8082 93.5201C25.8693 88.8825 26.3451 84.5362 24.2534 80.2489L58.7173 47.1571L68.318 44.1679L96.7993 63.863C97.0238 68.0989 98.0703 71.2753 100.173 74.1232C101.397 75.8724 103.174 77.1517 105.213 77.7521C107.252 78.3519 109.432 78.2368 111.398 77.4262C117.081 75.2495 120.237 70.4261 120.088 64.1697L154.653 20.8963C159.556 21.8606 163.362 21.4107 165.969 19.5528C167.985 18.1186 169.212 15.895 169.615 12.9436C169.846 11.4554 169.772 9.93496 169.397 8.4767C169.022 7.01851 168.354 5.65349 167.434 4.46625C166.462 3.27794 165.259 2.30335 163.898 1.60274C162.538 0.902131 161.049 0.490445 159.524 0.393145Z"/>
</g>
<defs>
<clipPath id="clip0">
<rect width="169" height="99" transform="translate(0.777344)"/>
</clipPath>
</defs>
</svg>`,
  },
  {
    name: 'Gallery',
    route: '/gallery',
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"  viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="image"><rect opacity="0"/><path d="M18 3H6a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3zM6 5h12a1 1 0 0 1 1 1v8.36l-3.2-2.73a2.77 2.77 0 0 0-3.52 0L5 17.7V6a1 1 0 0 1 1-1zm12 14H6.56l7-5.84a.78.78 0 0 1 .93 0L19 17v1a1 1 0 0 1-1 1z"/><circle cx="8" cy="8.5" r="1.5"/></g></g></svg>',
  },
  {
    name: 'Settings',
    route: '/settings',
    icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="m370-80-16-128q-13-5-24.5-12T307-235l-119 50L78-375l103-78q-1-7-1-13.5v-27q0-6.5 1-13.5L78-585l110-190 119 50q11-8 23-15t24-12l16-128h220l16 128q13 5 24.5 12t22.5 15l119-50 110 190-103 78q1 7 1 13.5v27q0 6.5-2 13.5l103 78-110 190-118-50q-11 8-23 15t-24 12L590-80H370Zm70-80h79l14-106q31-8 57.5-23.5T639-327l99 41 39-68-86-65q5-14 7-29.5t2-31.5q0-16-2-31.5t-7-29.5l86-65-39-68-99 42q-22-23-48.5-38.5T533-694l-13-106h-79l-14 106q-31 8-57.5 23.5T321-633l-99-41-39 68 86 64q-5 15-7 30t-2 32q0 16 2 31t7 30l-86 65 39 68 99-42q22 23 48.5 38.5T427-266l13 106Zm42-180q58 0 99-41t41-99q0-58-41-99t-99-41q-59 0-99.5 41T342-480q0 58 40.5 99t99.5 41Zm-2-140Z"/></svg>',
  },
  {
    name: 'sidebar',
    route: '/sidebar',
    icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="menu"><rect transform="rotate(180 12 12)" opacity="0"/><rect x="3" y="11" width="18" height="2" rx=".95" ry=".95"/><rect x="3" y="16" width="18" height="2" rx=".95" ry=".95"/><rect x="3" y="6" width="18" height="2" rx=".95" ry=".95"/></g></g></svg>',
  },
];

const project_id = ref(route.params.project_id);

const filteredMenulist = computed(() => {
  const role = store.user_role?.userrole?.role;

  // For editor or reader roles, show all menu items with proper active/disabled states
  if (role === 'editor' || role === 'reader') {
    console.log("Role is editor or reader, showing all menu items with active states");
    return menuListTop.map((menu) => ({
      ...menu,
      active: menu.roles.includes(role),
    }));
  }

  // For other roles (like admin), show all menu items with active states
  console.log("Role is not editor or reader, returning all menu items", menuListTop.map((menu) => ({
    ...menu,
    active: role ? menu.roles.includes(role) : false,
  })));
  return menuListTop.map((menu) => ({
    ...menu,
    active: role ? menu.roles.includes(role) : false,
  }));
});

// const sidePanelOpen = ref(false);
const current_menu = window.location.href;
// const findObjectUsingId = (id, array) => {
//   let newObject = null;
//   if (array.length > 0) {
//     array.forEach((item) => {
//       if (item._id === id) {
//         newObject = item;
//       }
//     });
//   }
//   return newObject;
// };

// user_email.value = store.user_role.userrole.email;
// user_first_name.value = store.user_data.first_name;
// user_last_name.value = store.user_data.last_name;

onMounted(async () => {
  onMessage(messaging, (payload) => {
    if (payload.data.organization === getCookie("organization") && payload.data.user_id === store.user_data._id && payload.data.type === 'meeting_reminder') {
      orgStore.FetchNewNotifications(store.user_data._id, false);
      orgStore.FetchNotifications(store.user_data._id);
    } else {
      ui.handleSuccessMessage("Invite sent successfully");
    }
  });
});
const getContainerClass = (elem) => {
  const isCurrent = current_menu.includes(elem.route);
  const isActive = elem.active;

  return [
    isCurrent && isActive
      ? 'selected bg-white shadow-md rounded-lg'
      : !isActive && isCurrent
        ? 'bg-white shadow-md rounded-lg'
        : 'bg-transparent',
    isActive ? 'transition-all duration-200 hover:scale-105 active:scale-95' : '',
  ];
};

const getIconClass = (elem) => {
  const isCurrent = current_menu.includes(elem.route);
  const isActive = elem.active;

  if (isCurrent && isActive) {
    return 'fill-[#1c64f2] dark:fill-bg-950';
  }
  if (!isActive && isCurrent) {
    return 'fill-blue-400';
  }
  if (!isActive) {
    return 'fill-gray-400';
  }
  return 'fill-bg-400 dark:fill-bg-600';
};

const getTextClass = (elem) => {
  const isCurrent = current_menu.includes(elem.route);
  const isActive = elem.active;

  if (isCurrent && isActive) {
    return 'text-txt-50 dark:text-txt-950';
  }
  if (!isActive && isCurrent) {
    return 'text-gray-500 dark:text-gray-400';
  }
  if (!isActive) {
    return 'text-gray-400';
  }
  return 'text-bg-400 dark:text-txt-600';
};
</script>

<template>
  <div
    :class="filteredMenulist && filteredMenulist.length > 1 ? 'sm:flex sm:flex-col w-40' : 'hidden sm:flex sm:flex-col w-40'"
    class="sm:h-full h-fit fixed sm:static bottom-0 sm:bottom-auto z-10 sm:w-[14rem] w-full bg-[#f3f4f6] dark:bg-bg-default justify-center sm:justify-between  pt-2 !sm:pt-0 items-end sm:items-start">
    <div v-if="filteredMenulist" class="flex sm:flex-col flex-row w-full h-full items-start pb-4 justify-between p-3 ">
      <div v-if="filteredMenulist.length > 1 && !project_id"
    class="hidden sm:flex sm:flex-col w-full gap-2 items-end sm:items-start h-8 font-sans ">

    <!-- Show All Menu Items -->
   <router-link v-for="(elem, index) in filteredMenulist"
    :key="index" :to="elem.active ? elem.route : '#'"
    class="desktop-nav-button flex sm:flex-row flex-col w-[10rem] justify-start items-center relative z-0 p-2 !md:pb-0 h-fit gap-3"
    :class="[getContainerClass(elem), elem.active ? 'cursor-pointer' : 'cursor-not-allowed']"
    @click="!elem.active && $event.preventDefault()">
      <div v-html="elem.icon"
    :class="getIconClass(elem)">
  </div>
  <p class="text-base capitalize flex"
    :class="getTextClass(elem)">
    {{ elem.name }}
  </p>
  <div v-if="elem.name.toLowerCase() === 'notification' && orgStore.newNotifications.length" class="w-5 h-5 px-2.5 py-0.5 bg-[#e02424] rounded-[80px] justify-center items-center inline-flex absolute right-3 -top-1 sm:static sm:left-0 sm:right-0">
    <div class="text-center text-white text-xs font-medium leading-[18px]">{{ orgStore.newNotifications.length }}</div>
  </div>
</router-link>

  </div>

<!-- Mobile View - Bottom Navbar -->
   <div v-if="filteredMenulist.length > 1 && !project_id"
    class="fixed bottom-0 left-0 right-0 flex sm:hidden bg-white dark:bg-gray-900 shadow-lg p-2 justify-around items-center z-50">
    <!-- Show first four items -->
    <router-link v-for="(elem, index) in filteredMenulist.slice(0, 4)"
      :key="index" :to="elem.active ? elem.route : '#'"
      :class="[
        { 'transition-all duration-200 hover:scale-105': elem.active },
        elem.active ? 'cursor-pointer' : 'cursor-not-allowed'
      ]"
      class="flex flex-col items-center p-2 gap-1 relative"
      @click="!elem.active && $event.preventDefault()">
      <div v-html="elem.icon"
        :class="getIconClass(elem)">
      </div>
      <p class="text-sm font-sans capitalize"
         :class="getTextClass(elem)">
        {{ elem.name }}
      </p>
      <div v-if="elem.name.toLowerCase() === 'notification' && orgStore.newNotifications.length" class="w-5 h-5 px-2.5 py-0.5 bg-[#e02424] rounded-[80px] justify-center items-center inline-flex absolute right-3 -top-1 sm:static sm:left-0 sm:right-0">
        <div class="text-center text-white text-xs font-medium leading-[18px]">{{ orgStore.newNotifications.length }}</div>
      </div>
    </router-link>

    <!-- "More" button with interactive dropdown -->
    <div v-if="filteredMenulist.length > 5"
      @click.stop="showMoreMenu = !showMoreMenu"
      class="more-menu-container flex flex-col items-center cursor-pointer p-2 gap-1 relative ">

      <div class="flex items-center justify-center w-8 h-8">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg>
      </div>
      <p class="text-xs font-sans capitalize text-bg-400 dark:text-txt-600">More</p>

      <!-- More Menu - Animated Dropdown -->

      <transition name="slide-up">
  <div v-if="showMoreMenu"
    class="fixed bottom-20 left-0 w-full bg-white dark:bg-gray-800 backdrop-blur-lg rounded-t-lg space-y-2 border border-gray-200 dark:border-gray-700 max-h-[50vh] overflow-y-auto pb-2">

    <div class="sticky top-0 flex items-center justify-between bg-white dark:bg-gray-800 p-3 border-b">
      <p class="text-lg font-semibold">More</p>

      <!-- Back Icon (SVG) -->
      <button @click="!dropdownOpen" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
             class="w-6 h-6 text-gray-700 dark:text-gray-300">
          <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
    <router-link v-for="(elem, index) in filteredMenulist.slice(4)"
      :key="'more-' + index" :to="elem.active ? elem.route : '#'"
      :class="[
        elem.active ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700' : 'cursor-not-allowed opacity-50'
      ]"
      class="flex items-center gap-3 px-4 py-2 rounded-lg text-sm text-gray-700 dark:text-gray-300 transition-all duration-200"
      @click="!elem.active && $event.preventDefault()">

      <!-- Add icon next to name -->
      <div v-html="elem.icon"
        :class="`${current_menu.includes(elem.route) ? 'fill-[#1c64f2] dark:fill-bg-950' : 'fill-bg-400 dark:fill-bg-600'}`">
      </div>

      <span class="font-medium text-sm capitalize">{{ elem.name }}</span>
    </router-link>

  </div>
</transition>

    </div>
    </div>

      <!-- profile card -->
<div  class="sm:flex hidden absolute bottom-0">
  <ProfileCard
        :userid="user_id"
        :firstName="user_first_name"
        :lastName="user_last_name"
        :email="user_email"
        :phone="user_phone_no"
        :profilePicture="user_picture"
        designation="Sales Executive"
        :role="user_role"
        @sign-out="handleSignOut"
        @edit-profile="handleEditProfile" />
  </div>

      <!-- Project Menu -->

      <div class="flex flex-col sm:w-80 w-full items-end sm:items-start">

<!-- Desktop Menu (Full List) -->
<div v-if="store.user_role && store.user_role.userrole.role !== 'reader' && project_id"
  class="hidden sm:flex flex-col w-full gap-2 items-end h-8 sm:items-start font-sans">

  <router-link
    v-for="(elem, index) in menuListBottom"
    :key="index"
    :to="'/projects/' + project_id + elem.route"
    class="project-menu-button w-[10rem] flex sm:flex-row flex-col justify-start items-center cursor-pointer relative z-0 p-2 h-fit gap-3 transition-all duration-200 hover:scale-105 active:scale-95"
    :class="`${current_menu.includes(elem.route) ? 'selected bg-white shadow-md rounded-lg' : 'bg-transparent'}`">

    <div v-html="elem.icon"
    :class="`${current_menu.includes(elem.route) ? ( elem.name === 'Exterior' ? 'definer dark:fill-bg-950': 'fill-[#1c64f2] dark:fill-bg-950' ): 'fill-bg-400 dark:fill-bg-600'}`">
    </div>
    <p class="text-base capitalize"
      :class="`${current_menu.includes(elem.route) ? 'text-txt-50 dark:text-txt-950' : 'text-bg-400 dark:text-txt-600'}`">
      {{ elem.name }}
    </p>

  </router-link>

</div>

<!-- Mobile Bottom Navbar -->
<div v-if="menuListBottom.length > 1 && project_id"
  class="fixed bottom-0 left-0 right-0 flex sm:hidden bg-white dark:bg-gray-900 shadow-md p-2 justify-around items-center z-50">

  <!-- Show first 4 items -->
  <router-link v-for="(elem, index) in menuListBottom.slice(0, 4)"
    :key="index"
    :to="'/projects/' + project_id + elem.route"
    class="flex flex-col items-center cursor-pointer p-2 gap-1 transition-all hover:scale-105 active:scale-95">

    <div v-html="elem.icon"
      :class="`${current_menu.includes(elem.route) ? 'fill-[#1c64f2] dark:fill-bg-950' : 'fill-bg-400 dark:fill-bg-600'}`">
    </div>

    <p class="text-base font-sans capitalize"
      :class="`${current_menu.includes(elem.route) ? 'text-txt-50 dark:text-txt-950' : 'text-bg-400 dark:text-txt-600'}`">
      {{ elem.name }}
    </p>

  </router-link>

  <!-- More Button -->
  <div v-if="menuListBottom.length > 4" @click="dropdownOpen = !dropdownOpen"
    class="flex flex-col items-center cursor-pointer p-2 gap-1 transition-all relative">

    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="4" x2="20" y1="12" y2="12"></line><line x1="4" x2="20" y1="6" y2="6"></line><line x1="4" x2="20" y1="18" y2="18"></line></svg>

    <p class="text-xs font-sans capitalize text-bg-400 dark:text-txt-600">More</p>

    <!-- Dropdown -->

    <transition name="slide-up">
  <div v-if="dropdownOpen"
    class="fixed bottom-20 left-0 w-full bg-white dark:bg-gray-800 backdrop-blur-lg
           rounded-t-lg space-y-2 border border-gray-200 dark:border-gray-700
           max-h-[50vh] overflow-y-auto pb-2">

    <!-- Header with "More" and Back Icon -->
    <div class="sticky top-0 flex items-center justify-between bg-white dark:bg-gray-800 p-3 border-b">
      <p class="text-lg font-semibold">More</p>

      <!-- Back Icon (SVG) -->
      <button @click="!dropdownOpen" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor"
             class="w-6 h-6 text-gray-700 dark:text-gray-300">
          <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Menu Items -->
    <router-link v-for="(elem, index) in menuListBottom.slice(4)"
      :key="index"
      :to="'/projects/' + project_id + elem.route"
      class="flex items-center gap-3 px-4 py-2 rounded-lg text-sm text-gray-700 dark:text-gray-300
             hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-200">

             <div v-html="elem.icon"
        :class="`${current_menu.includes(elem.route) ? 'fill-[#1c64f2] dark:fill-bg-950' : 'fill-bg-400 dark:fill-bg-600'}`">
      </div>

      <span class="font-medium text-sm capitalize">{{ elem.name }}</span>

    </router-link>
  </div>
</transition>

  </div>

</div>

</div>

    </div>
  </div>
</template>

<style scoped>

/* Scale-down effect on click */
.desktop-nav-button:active {
  transform: scale(0.95);
}
/* Slide-up animation for dropdown */
.slide-up-enter-active, .slide-up-leave-active {
  transition: all 0.3s ease-out;
}
.slide-up-enter-from, .slide-up-leave-to {
  transform: translateY(10px);
  opacity: 0;
}
.definer{
  fill: #1c64f2;
  stroke: #1c64f2;
}

</style>

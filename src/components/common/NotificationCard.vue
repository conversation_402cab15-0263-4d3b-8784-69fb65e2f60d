<script setup>
import { computed } from 'vue';
import Button from './Button.vue';

const props = defineProps({
  status: {type: String, default: 'active'},
  timeStamp: {type: String, default: ""},
  url: {type: String, default: ""},
});
defineEmits(['handleClick']);
console.log(props.url);
const formattedTime = computed(() => {
  if (!props.timeStamp) {
    return 'a few moments ago';
  }

  const now = new Date();
  const timestamp = new Date(props.timeStamp);
  const diffInMs = now - timestamp;

  // Convert to minutes, hours, days
  const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
  const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInMinutes < 1) {
    return 'just now';
  }
  if (diffInMinutes < 30) {
    return 'a few moments ago';
  }
  if (diffInMinutes < 60) {
    return `${diffInMinutes} mins ago`;
  }
  if (diffInHours < 24) {
    return `${diffInHours} hr${diffInHours > 1 ? 's' : ''} ago`;
  }
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  // For older dates, show the actual date
  return timestamp.toLocaleDateString();
});
</script>
<template>
    <div class="max-w-[357px] p-[16px] rounded-lg shadow justify-start items-start gap-3 inline-flex" :class="status === 'cancelled' && 'bg-[#fdf2f2]'">
        <div class="grow shrink basis-0 flex-col justify-start items-start gap-1.5 inline-flex">
          <div v-if="status === 'cancelled'" class="h-[25px] px-3 py-0.5 bg-[#fde8e8] rounded-md justify-start items-center gap-1 inline-flex">
            <div class="text-center text-[#9b1c1c] text-sm font-medium leading-[21px]">Canceled</div>
          </div>
          <div v-if="status === 'cancelled'">
            Your meeting with Joseph McFall was canceled as you didn’t join within 10 minutes of the start time
          </div>
          <div v-else>
            <div class="self-stretch text-[#111928] text-base font-medium leading-normal">
                Your meeting with joseph mc fall starts in 5 mins !
            </div>
            <div class="text-gray-500 text-base font-normal leading-normal">
                Meeting will be canceled If you're not joined within 15 minutes
            </div>
          </div>

          <Button v-if="status === 'active'" title="Join Now" class="!px-3 !py-2 !w-full" type="button" theme="primary" @click="$emit('handleClick',url)" />
          <div class="justify-start items-center gap-1.5 inline-flex">
            <div data-svg-wrapper class="relative">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_3240_5341)">
                    <path d="M6 0C4.81331 0 3.65328 0.351894 2.66658 1.01118C1.67989 1.67047 0.910851 2.60754 0.456725 3.7039C0.0025997 4.80025 -0.11622 6.00665 0.115291 7.17054C0.346802 8.33443 0.918247 9.40352 1.75736 10.2426C2.59648 11.0818 3.66557 11.6532 4.82946 11.8847C5.99335 12.1162 7.19975 11.9974 8.2961 11.5433C9.39246 11.0891 10.3295 10.3201 10.9888 9.33342C11.6481 8.34672 12 7.18669 12 6C11.9983 4.40924 11.3656 2.88413 10.2407 1.75929C9.11587 0.634449 7.59076 0.00174696 6 0ZM8.3892 8.3892C8.27668 8.50168 8.1241 8.56487 7.965 8.56487C7.8059 8.56487 7.65332 8.50168 7.5408 8.3892L5.5764 6.4248C5.46417 6.31181 5.40082 6.15925 5.4 6V3.6C5.4 3.44087 5.46322 3.28826 5.57574 3.17574C5.68826 3.06321 5.84087 3 6 3C6.15913 3 6.31174 3.06321 6.42427 3.17574C6.53679 3.28826 6.6 3.44087 6.6 3.6V5.7516L8.3892 7.5408C8.50168 7.65331 8.56487 7.8059 8.56487 7.965C8.56487 8.1241 8.50168 8.27668 8.3892 8.3892Z" fill="#6B7280"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_3240_5341">
                      <rect width="12" height="12" fill="white"/>
                    </clipPath>
                  </defs>
                </svg>
            </div>
            <div class="text-gray-500 text-sm font-normal leading-[17.50px]">{{ formattedTime }}</div>
          </div>
        </div>
    </div>
</template>

<style>

</style>

```vue
<script setup>
import { uiOperations } from '../../store/uiOperations';
const store = uiOperations();
</script>

<template>
    <div class="fixed bottom-20 sm:bottom-4 left-0 flex items-center justify-center px-4 pointer-events-none transition-all duration-300"
        :class="[store.toaster.show ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8',store.disableToaster?'z-[10]':'z-[9999]']">
        <div class="pointer-events-auto flex items-center gap-2 px-4 py-3 rounded-lg shadow-lg max-w-[90vw] sm:max-w-md"
            :class="{
                'bg-red-500': store.toaster.state === 'error',
                'bg-green-500': store.toaster.state === 'success',
                'bg-orange-400': store.toaster.state === 'warning'
            }">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 flex-shrink-0 text-white" viewBox="0 0 24 24"
                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
            <p class="text-sm text-white font-medium">
                {{ store.toaster.message }}
            </p>
        </div>
    </div>
</template>

<style scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(1rem);
}
</style>
```

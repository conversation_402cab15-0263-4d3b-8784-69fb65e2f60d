<script setup>

import { onMounted, onUnmounted, ref} from 'vue';
import { UserStore } from '@/store';

const { url } = defineProps({
  url: {type: String, default: ""},
});
console.log(url);
const videoRef = ref(null);
const isPlaying = ref(false);
const showControls = ref(null);
const totalDuration = ref(null);
const currentTime = ref('0:00');
// Const isMute = ref(false);
const isScrubbing = ref(false);
const emits = defineEmits(['handleLoadingComplete']);

const Store = UserStore();

function togglePlayPause (){
  const video = videoRef.value;
  if (video.paused) {
    video.play();
    isPlaying.value=true;
    showControls.value = false;
  } else {
    video.pause();
    isPlaying.value=false;
    showControls.value = true;
  }
}

let hideControlsTimeout;
function resetHideControlsTimer () {
  const video = videoRef.value;
  if (video.paused) {
    isPlaying.value=false;
    showControls.value = true;
    return;
  }

  showControls.value = true;
  if (hideControlsTimeout) {
    clearTimeout(hideControlsTimeout);
  }
  hideControlsTimeout = setTimeout(() => {

    if (video.currentTime === video.duration || video.paused){
      showControls.value = true;
    } else {
      showControls.value = false;
    }
  }, 1000);
}

function formatDuration (time) {
  const seconds  = Math.floor(time % 60);
  const minutes  = Math.floor(time / 60) % 60;
  const hours  = Math.floor(time / 3600);
  if (hours === 0){
    return `${minutes}:${seconds < 10 ? `0${seconds}` : seconds }`;
  }
  return `${hours}:${minutes < 10 ? `0${minutes}` : minutes }:${seconds < 10 ? `0${seconds}` : seconds }`;

}

function currentDuration () {
  const timelineContainer = document.querySelector('.timeline-container');
  const video = videoRef.value;
  currentTime.value = formatDuration(video.currentTime);
  const percent = video.currentTime / video.duration;
  timelineContainer.style.setProperty("--progress-position", percent);
}

function videoDuration () {
  const video = videoRef.value;
  totalDuration.value = formatDuration(video.duration);
}

function handleTimelineUpdate (e) {
  const timelineContainer = document.querySelector('.timeline-container');
  const rect = timelineContainer.getBoundingClientRect();
  const percent = Math.min(Math.max(0, e.x - rect.x), rect.width) / rect.width;

  if (isScrubbing.value) {
    e.preventDefault();
    timelineContainer.style.setProperty("--progress-position", percent);
  }
}

let wasPaused;
function toggleScrubbing (e) {
  const timelineContainer = document.querySelector('.timeline-container');
  const video = videoRef.value;
  const rect = timelineContainer.getBoundingClientRect();
  const percent = Math.min(Math.max(0, e.x - rect.x), rect.width) / rect.width;

  isScrubbing.value = (e.buttons & 1) === 1;
  timelineContainer.classList.toggle("scrubbing", isScrubbing.value);
  if (isScrubbing.value) {
    wasPaused = video.paused;
    video.pause();
  } else {
    video.currentTime = percent * video.duration;
    if (!wasPaused) {
      video.play();
    } isPlaying.value = true;
  }

  handleTimelineUpdate(e);
}

const handleMouseUp = (e) => {
  if (isScrubbing.value) {
    toggleScrubbing(e);
  }
};

const handleMouseMove = (e) => {
  if (isScrubbing.value) {
    handleTimelineUpdate(e);
  }
};

onMounted(() => {
  document.addEventListener("mouseup", handleMouseUp);
  document.addEventListener("mousemove", handleMouseMove);
});

onUnmounted(() => {
  document.removeEventListener("mouseup", handleMouseUp);
  document.removeEventListener("mousemove", handleMouseMove);
});

</script>

<template>
  <div
    class="h-full w-full flex items-center justify-center"
    @mousemove="resetHideControlsTimer"
  >
    <div
      class="relative w-full h-full sm:translate-y-0"
    >
      <div class="w-full h-full   flex items-center justify-center">
        <video
          ref="videoRef"

          class="h-full w-full m-auto"
          @ended="showControls = true; isPlaying = false;"
          @loadedmetadata="showControls = true; videoDuration();"
          @loadeddata="() => emits('handleLoadingComplete')"
          @timeupdate="currentDuration"
        >
          <source
            :src="url"
            type="video/mp4"
          >
        </video>
      </div>
      <div
        v-show="showControls && Store.isMobile"
        class="absolute inset-0 flex gap-4 items-center justify-center"
      >
        <div
          class="hover:cursor-pointer"
          @click="videoRef[0].currentTime -= 15"
        >
          <svg
            width="17"
            height="17"
            viewBox="0 0 17 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_2914_3153)">
              <path
                d="M11.7813 15.3766L4.56569 9.60443C4.56558 9.60434 4.56547 9.60425 4.56536 9.60416C4.53677 9.58113 4.5165 9.55961 4.47614 9.51679C4.46002 9.49968 4.4407 9.47917 4.41636 9.45381L3.5556 8.5568V9.8V15.5C3.5556 15.7286 3.33364 16 2.94449 16C2.55533 16 2.33337 15.7286 2.33337 15.5V1.5C2.33337 1.27139 2.55533 1 2.94449 1C3.33364 1 3.5556 1.27139 3.5556 1.5V7.2V8.4432L4.41636 7.54619C4.4412 7.52031 4.4609 7.49937 4.47741 7.48183C4.51677 7.44 4.53794 7.4175 4.56709 7.39345L11.7813 1.62244L11.7821 1.6218C12.2971 1.20802 13.018 1.10437 13.6676 1.36578C14.3109 1.62559 14.6667 2.1562 14.6667 2.727V14.273C14.6667 14.844 14.3108 15.3744 13.6678 15.6332L13.6676 15.6332C13.4285 15.7296 13.1797 15.777 12.9367 15.777C12.5159 15.777 12.1084 15.639 11.7818 15.377L11.7813 15.3766Z"
                fill="white"
                stroke="white"
              />
            </g>
            <defs>
              <clipPath id="clip0_2914_3153">
                <rect
                  width="16"
                  height="16"
                  fill="white"
                  transform="translate(0.5 0.5)"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
        <div
          class="bg-blue-700 h-[60px] w-[60px] z-20 hover:cursor-pointer rounded-[50%] flex"
          @click="togglePlayPause"
        >
          <svg
            v-if="!isPlaying"
            class="h-6 w-6 m-auto"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M6.38996 2.02448C4.58685 0.99019 3.125 1.83749 3.125 3.91547V19.25C3.125 21.3301 4.58685 22.1763 6.38996 21.143L19.7931 13.4563C21.5968 12.4217 21.5968 10.7454 19.7931 9.71098L6.38996 2.02448Z"
              fill="white"
            />
          </svg>
          <svg
            v-else
            class="h-5 w-4 m-auto"
            viewBox="0 0 16 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.102 1.11111C10.102 0.497462 10.6046 0 11.2245 0H14.5918C15.2117 0 15.7143 0.497461 15.7143 1.11111V18.8889C15.7143 19.5025 15.2117 20 14.5918 20H11.2245C10.6046 20 10.102 19.5025 10.102 18.8889V1.11111Z"
              fill="white"
            />
            <path
              d="M0 1.11111C0 0.497462 0.502538 0 1.12245 0H4.4898C5.10971 0 5.61225 0.497461 5.61225 1.11111V18.8889C5.61225 19.5025 5.10971 20 4.4898 20H1.12245C0.502538 20 0 19.5025 0 18.8889V1.11111Z"
              fill="white"
            />
          </svg>
        </div>
        <div
          class="hover:cursor-pointer"
          @click="videoRef.currentTime += 15"
        >
          <svg
            width="17"
            height="17"
            viewBox="0 0 17 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_2914_3157)">
              <path
                d="M14.0556 0.5C13.4412 0.5 12.9445 0.947 12.9445 1.5V7.2C12.8812 7.134 12.8212 7.065 12.7478 7.006L5.53226 1.232C4.86893 0.701 3.9556 0.576 3.1456 0.902C2.3356 1.229 1.83337 1.929 1.83337 2.727V14.274C1.83337 15.072 2.3356 15.771 3.1456 16.098C3.44337 16.218 3.7556 16.278 4.06449 16.278C4.59449 16.278 5.11337 16.104 5.53226 15.768L12.7478 9.995C12.8212 9.936 12.8812 9.867 12.9445 9.801V15.5C12.9445 16.053 13.4412 16.5 14.0556 16.5C14.67 16.5 15.1667 16.053 15.1667 15.5V1.5C15.1667 0.947 14.67 0.5 14.0556 0.5Z"
                fill="white"
              />
            </g>
            <defs>
              <clipPath id="clip0_2914_3157">
                <rect
                  width="16"
                  height="16"
                  fill="white"
                  transform="translate(0.5 0.5)"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
        <div
          class="hover:cursor-pointer"
          @click="()=>{videoRef.currentTime -= videoRef.currentTime; isPlaying=true; videoRef.play();}"
        >
          <svg
            width="17"
            height="17"
            viewBox="0 0 17 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g clip-path="url(#clip0_2914_3159)">
              <path
                d="M14.1007 9.31499C13.659 9.31499 13.3006 9.68002 13.3006 10.1298V11.7594H3.23054L4.26507 10.7059C4.57791 10.3873 4.57791 9.87231 4.26507 9.55372C3.95223 9.23514 3.44657 9.23514 3.13373 9.55372L0.733429 11.9981C0.65982 12.0731 0.601413 12.1635 0.560608 12.2629C0.479797 12.4626 0.479797 12.6866 0.560608 12.8855C0.601413 12.9849 0.65982 13.0753 0.733429 13.1503L3.13373 15.5947C3.28975 15.7536 3.49457 15.8334 3.6994 15.8334C3.90423 15.8334 4.10905 15.7536 4.26507 15.5947C4.57791 15.2761 4.57791 14.7611 4.26507 14.4425L3.23054 13.389H14.1007C14.5424 13.389 14.9008 13.024 14.9008 12.5742V10.1298C14.9008 9.68002 14.5424 9.31499 14.1007 9.31499Z"
                fill="white"
              />
              <path
                d="M16.4394 4.11491C16.3986 4.01551 16.3402 3.92506 16.2666 3.8501L13.8663 1.40569C13.5534 1.0871 13.0478 1.0871 12.7349 1.40569C12.4221 1.72428 12.4221 2.23923 12.7349 2.55782L13.7695 3.61136H2.8993C2.45764 3.61136 2.0992 3.97639 2.0992 4.42617V6.87058C2.0992 7.32035 2.45764 7.68538 2.8993 7.68538C3.34096 7.68538 3.6994 7.32035 3.6994 6.87058V5.24097H13.7695L12.7349 6.29451C12.4221 6.6131 12.4221 7.12805 12.7349 7.44664C12.8909 7.60553 13.0958 7.68538 13.3006 7.68538C13.5054 7.68538 13.7103 7.60553 13.8663 7.44664L16.2666 5.00223C16.3402 4.92727 16.3986 4.83683 16.4394 4.73742C16.5202 4.53861 16.5202 4.31372 16.4394 4.11491Z"
                fill="white"
              />
            </g>
            <defs>
              <clipPath id="clip0_2914_3159">
                <rect
                  width="16"
                  height="16"
                  fill="white"
                  transform="translate(0.5 0.5)"
                />
              </clipPath>
            </defs>
          </svg>
        </div>
      </div>
      <div
        v-show="showControls"
        class="absolute text-white w-full z-50"
        :class="Store.isMobile ? 'bottom-[198px]' : ' bottom-24'"
      >
        <div class="sm:w-[92%] w-[90%] m-auto">
          <div>
            <div class="flex flex-col gap-4 items-center">
              <div v-if="!Store.isMobile">
                <div class="flex gap-4 items-center">
                  <div
                    class="hover:cursor-pointer bg-blue-700 p-2 rounded-full"
                    @click="videoRef.currentTime -= 15"
                  >
                  <svg
                      width="17"
                      height="17"
                      viewBox="0 0 17 17"
                      fill="white"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clip-path="url(#clip0_2914_3153)">
                        <path
                          d="M11.7813 15.3766L4.56569 9.60443C4.56558 9.60434 4.56547 9.60425 4.56536 9.60416C4.53677 9.58113 4.5165 9.55961 4.47614 9.51679C4.46002 9.49968 4.4407 9.47917 4.41636 9.45381L3.5556 8.5568V9.8V15.5C3.5556 15.7286 3.33364 16 2.94449 16C2.55533 16 2.33337 15.7286 2.33337 15.5V1.5C2.33337 1.27139 2.55533 1 2.94449 1C3.33364 1 3.5556 1.27139 3.5556 1.5V7.2V8.4432L4.41636 7.54619C4.4412 7.52031 4.4609 7.49937 4.47741 7.48183C4.51677 7.44 4.53794 7.4175 4.56709 7.39345L11.7813 1.62244L11.7821 1.6218C12.2971 1.20802 13.018 1.10437 13.6676 1.36578C14.3109 1.62559 14.6667 2.1562 14.6667 2.727V14.273C14.6667 14.844 14.3108 15.3744 13.6678 15.6332L13.6676 15.6332C13.4285 15.7296 13.1797 15.777 12.9367 15.777C12.5159 15.777 12.1084 15.639 11.7818 15.377L11.7813 15.3766Z"
                          fill="white"
                          stroke="white"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_2914_3153">
                          <rect
                            width="16"
                            height="16"
                            fill="white"
                            transform="translate(0.5 0.5)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  <div
                    class="bg-blue-700 h-[60px] w-[60px] z-20 hover:cursor-pointer rounded-[50%] flex"
                    @click="togglePlayPause"
                  >
                    <svg
                      v-if="!isPlaying"
                      class="h-6 w-6 m-auto"
                      viewBox="0 0 24 24"
                      fill="white"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M6.38996 2.02448C4.58685 0.99019 3.125 1.83749 3.125 3.91547V19.25C3.125 21.3301 4.58685 22.1763 6.38996 21.143L19.7931 13.4563C21.5968 12.4217 21.5968 10.7454 19.7931 9.71098L6.38996 2.02448Z"
                        fill="white"
                      />
                    </svg>
                    <svg
                      v-else
                      class="h-5 w-4 m-auto"
                      viewBox="0 0 16 20"
                      fill="white"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.102 1.11111C10.102 0.497462 10.6046 0 11.2245 0H14.5918C15.2117 0 15.7143 0.497461 15.7143 1.11111V18.8889C15.7143 19.5025 15.2117 20 14.5918 20H11.2245C10.6046 20 10.102 19.5025 10.102 18.8889V1.11111Z"
                        fill="white"
                      />
                      <path
                        d="M0 1.11111C0 0.497462 0.502538 0 1.12245 0H4.4898C5.10971 0 5.61225 0.497461 5.61225 1.11111V18.8889C5.61225 19.5025 5.10971 20 4.4898 20H1.12245C0.502538 20 0 19.5025 0 18.8889V1.11111Z"
                        fill="white"
                      />
                    </svg>
                  </div>
                  <div
                    class="hover:cursor-pointer bg-blue-700 p-2 rounded-full"
                    @click="videoRef.currentTime += 15"
                  >
                    <svg
                      width="17"
                      height="17"
                      viewBox="0 0 17 17"
                      fill="white"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clip-path="url(#clip0_2914_3157)">
                        <path
                          d="M14.0556 0.5C13.4412 0.5 12.9445 0.947 12.9445 1.5V7.2C12.8812 7.134 12.8212 7.065 12.7478 7.006L5.53226 1.232C4.86893 0.701 3.9556 0.576 3.1456 0.902C2.3356 1.229 1.83337 1.929 1.83337 2.727V14.274C1.83337 15.072 2.3356 15.771 3.1456 16.098C3.44337 16.218 3.7556 16.278 4.06449 16.278C4.59449 16.278 5.11337 16.104 5.53226 15.768L12.7478 9.995C12.8212 9.936 12.8812 9.867 12.9445 9.801V15.5C12.9445 16.053 13.4412 16.5 14.0556 16.5C14.67 16.5 15.1667 16.053 15.1667 15.5V1.5C15.1667 0.947 14.67 0.5 14.0556 0.5Z"
                          fill="white"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_2914_3157">
                          <rect
                            width="16"
                            height="16"
                            fill="white"
                            transform="translate(0.5 0.5)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                  <div
                    class="hover:cursor-pointer bg-blue-700 p-2 rounded-full"
                    @click="()=>{videoRef.currentTime -= videoRef.currentTime; isPlaying=true; videoRef.play();}"
                  >
                    <svg
                      width="17"
                      height="17"
                      viewBox="0 0 17 17"
                      fill="white"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g clip-path="url(#clip0_2914_3159)">
                        <path
                          d="M14.1007 9.31499C13.659 9.31499 13.3006 9.68002 13.3006 10.1298V11.7594H3.23054L4.26507 10.7059C4.57791 10.3873 4.57791 9.87231 4.26507 9.55372C3.95223 9.23514 3.44657 9.23514 3.13373 9.55372L0.733429 11.9981C0.65982 12.0731 0.601413 12.1635 0.560608 12.2629C0.479797 12.4626 0.479797 12.6866 0.560608 12.8855C0.601413 12.9849 0.65982 13.0753 0.733429 13.1503L3.13373 15.5947C3.28975 15.7536 3.49457 15.8334 3.6994 15.8334C3.90423 15.8334 4.10905 15.7536 4.26507 15.5947C4.57791 15.2761 4.57791 14.7611 4.26507 14.4425L3.23054 13.389H14.1007C14.5424 13.389 14.9008 13.024 14.9008 12.5742V10.1298C14.9008 9.68002 14.5424 9.31499 14.1007 9.31499Z"
                          fill="white"
                        />
                        <path
                          d="M16.4394 4.11491C16.3986 4.01551 16.3402 3.92506 16.2666 3.8501L13.8663 1.40569C13.5534 1.0871 13.0478 1.0871 12.7349 1.40569C12.4221 1.72428 12.4221 2.23923 12.7349 2.55782L13.7695 3.61136H2.8993C2.45764 3.61136 2.0992 3.97639 2.0992 4.42617V6.87058C2.0992 7.32035 2.45764 7.68538 2.8993 7.68538C3.34096 7.68538 3.6994 7.32035 3.6994 6.87058V5.24097H13.7695L12.7349 6.29451C12.4221 6.6131 12.4221 7.12805 12.7349 7.44664C12.8909 7.60553 13.0958 7.68538 13.3006 7.68538C13.5054 7.68538 13.7103 7.60553 13.8663 7.44664L16.2666 5.00223C16.3402 4.92727 16.3986 4.83683 16.4394 4.73742C16.5202 4.53861 16.5202 4.31372 16.4394 4.11491Z"
                          fill="white"
                        />
                      </g>
                      <defs>
                        <clipPath id="clip0_2914_3159">
                          <rect
                            width="16"
                            height="16"
                            fill="white"
                            transform="translate(0.5 0.5)"
                          />
                        </clipPath>
                      </defs>
                    </svg>
                  </div>
                </div>
              </div>
              <div class="w-full flex felx-col gap-2 items-center">
                <div class="text-sm font-medium leading-[14px]">
                  {{ currentTime }}
                </div>
                <div
                  class="w-full timeline-container mb-0 cursor-pointer"
                  @mousemove="handleTimelineUpdate"
                  @mousedown="toggleScrubbing"
                >
                  <div class="timeline bg-green-600 bg-opacity-40 h-1.5 w-full relative rounded-sm">
                    <div class="thumb-indicator" />
                  </div>
                </div>
                <div class="text-sm font-medium leading-[14px]">
                  {{ totalDuration }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>

.timeline::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: calc(100% - var(--progress-position) * 100%);
  background-color: #3f83f8;
}
.video-container.scrubbing .timeline::before,
.timeline-container:hover .timeline::before {
  display: block;
}

.video-container.scrubbing .thumb-indicator,
.timeline-container:hover .thumb-indicator {
  --scale: 1;
}

svg path {
  fill: var(--primaryText);
  stroke: none;
}
</style>

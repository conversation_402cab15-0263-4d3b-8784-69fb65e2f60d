<script setup>
import { ref } from "vue";
// import { useRouter } from 'vue-router';
import { Form, Field, ErrorMessage } from "vee-validate";
import { ScheduleMeetingsValidation, ScheduleStartMeetingsValidation } from '@/validationSchema/session';
import Multiselect from 'vue-multiselect';
import { UserStore } from '../../store/index';
import Modal from "./Modal/Modal.vue";
import Spinner from "./Spinner.vue";
import DatePicker from "../UIElements/DatePicker.vue";
import VueDatePicker from '@vuepic/vue-datepicker';

const emits = defineEmits(['closeModal', 'schedule', 'startSession']);
const props = defineProps({
  loader: Boolean,
  calenderStartTime: String,
  calenderEndTime: String,
  availableSlots: Object,
  showOnlyNameAndEmail: Boolean,
});
// const router = useRouter();
const Store = UserStore();
// Form fields
// const name = ref();
const dp = ref();
// const email = ref("");
// const slot = ref("30 min");
const date = ref("");
// const initialDate = ref('');
const startTime = ref("");
const initialStartTime = ref("");
const endTime = ref("");
const initialEndTime = ref("");
const selectedSlot = ref("");
const AvailableSlots = ref(['30 min', '60 min', '120 min']);

function formatTime (dateString) {
  const date = new Date(dateString);
  const hours = String(date.getHours()).padStart(2, '0'); // Ensure 2 digits
  const minutes = String(date.getMinutes()).padStart(2, '0'); // Ensure 2 digits
  return `${hours}:${minutes}`;
}
console.log("oooo", props.calenderEndTime);
console.log("availableSlots", props.availableSlots);

if (props.calenderStartTime){
  const initialDate = new Date(props.calenderStartTime);
  date.value = `${initialDate.getFullYear()}-${String(initialDate.getMonth() + 1).padStart(2, '0')}-${String(initialDate.getDate()).padStart(2, '0')}`;
  console.log("dateee", date.value);

  startTime.value = formatTime(props.calenderStartTime);
  initialStartTime.value = formatTime(props.calenderStartTime);
}
if (props.calenderEndTime){
  endTime.value = formatTime(props.calenderEndTime);
  initialEndTime.value = formatTime(props.calenderEndTime);
}
// Form submission handler
const handleForm = async (values) => {
  console.log("Form Submitted:", values);
  if (!values.startTime && !values.endTime){
    emits('startSession', values);
  } else {
    emits('schedule', values);
  }
};

const handleStartTime = (time, setFieldValue) => {
  const timeString = `${String(time.hours).padStart(2, '0')}:${String(time.minutes).padStart(2, '0')}`;
  startTime.value = timeString;
  setFieldValue('startTime', timeString);

};
const handleEndTime = (time, setFieldValue) => {
  const timeString = `${String(time.hours).padStart(2, '0')}:${String(time.minutes).padStart(2, '0')}`;
  endTime.value = timeString;
  setFieldValue('endTime', timeString);
};
const handleDate = (val) => {
  date.value = val.split("T")[0];
};
</script>

<template>
  <Modal :open="true">
    <div class="z-[10]  bg-white  py-2  " :class="Store.isMobile?'w-full h-full':'rounded-lg h-fit w-[50%] xl:w-[25%] max-lg:w-[45%] relative top-[5%] px-3'">
        <div v-if="Store.isMobile" class="flex w-full  gap-5 items-center border-b-2 py-2 px-2">
            <svg @click="emits('closeModal')" width="24" height="19" viewBox="0 0 24 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.925781 9.52734C0.925781 9.16016 1.06641 8.83984 1.34766 8.56641L9.05859 0.867188C9.19922 0.726562 9.34766 0.621094 9.50391 0.550781C9.66016 0.480469 9.82422 0.445312 9.99609 0.445312C10.3555 0.445312 10.6562 0.5625 10.8984 0.796875C11.1406 1.03125 11.2617 1.32422 11.2617 1.67578C11.2617 1.86328 11.2266 2.03516 11.1562 2.19141C11.0859 2.33984 10.9922 2.47266 10.875 2.58984L8.25 5.25L3.89062 9.24609L3.46875 8.48438L7.53516 8.23828H21.7734C22.1641 8.23828 22.4766 8.35938 22.7109 8.60156C22.9531 8.83594 23.0742 9.14453 23.0742 9.52734C23.0742 9.90234 22.9531 10.2109 22.7109 10.4531C22.4766 10.6875 22.1641 10.8047 21.7734 10.8047H7.53516L3.46875 10.5703L3.89062 9.82031L8.25 13.8047L10.875 16.4531C10.9922 16.5703 11.0859 16.707 11.1562 16.8633C11.2266 17.0195 11.2617 17.1875 11.2617 17.3672C11.2617 17.7188 11.1406 18.0117 10.8984 18.2461C10.6562 18.4805 10.3555 18.5977 9.99609 18.5977C9.65234 18.5977 9.34375 18.4648 9.07031 18.1992L1.34766 10.4883C1.06641 10.2148 0.925781 9.89453 0.925781 9.52734Z" fill="#747577"/>
            </svg>
            <p class="text-xl font-bold">Schedule Meeting</p>
        </div>
      <div class="w-full h-[5%] flex items-center justify-start" :class="Store.isMobile?'px-3':''">
        <p class="text-[#111928] text-xl font-bold">Enter Contact</p>
      </div>
    <div v-if="!Store.isMobile" class="absolute top-6 right-6 cursor-pointer" @click="emits('closeModal')">
        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="x" clip-path="url(#clip0_707_6588)">
            <path id="Vector" d="M5.90741 5L9.30409 1.60332C9.36538 1.54412 9.41427 1.47331 9.4479 1.39502C9.48153 1.31672 9.49924 1.23252 9.49998 1.14731C9.50072 1.0621 9.48448 0.977595 9.45221 0.898729C9.41995 0.819863 9.3723 0.748212 9.31204 0.687958C9.25179 0.627705 9.18014 0.580054 9.10127 0.547787C9.0224 0.515521 8.9379 0.499284 8.85269 0.500024C8.76748 0.500765 8.68328 0.518468 8.60498 0.5521C8.52669 0.585733 8.45588 0.634621 8.39668 0.695913L5 4.09259L1.60332 0.695913C1.48229 0.579017 1.32019 0.514333 1.15193 0.515796C0.983666 0.517258 0.822712 0.584748 0.70373 0.70373C0.584748 0.822712 0.517258 0.983666 0.515796 1.15193C0.514333 1.32019 0.579017 1.48229 0.695913 1.60332L4.09259 5L0.695913 8.39668C0.634621 8.45588 0.585733 8.52669 0.5521 8.60498C0.518468 8.68328 0.500765 8.76748 0.500024 8.85269C0.499284 8.9379 0.515521 9.0224 0.547787 9.10127C0.580054 9.18014 0.627705 9.25179 0.687958 9.31204C0.748212 9.3723 0.819863 9.41995 0.898729 9.45221C0.977595 9.48448 1.0621 9.50072 1.14731 9.49998C1.23252 9.49924 1.31672 9.48153 1.39502 9.4479C1.47331 9.41427 1.54412 9.36538 1.60332 9.30409L5 5.90741L8.39668 9.30409C8.51771 9.42098 8.67981 9.48567 8.84807 9.4842C9.01633 9.48274 9.17729 9.41525 9.29627 9.29627C9.41525 9.17729 9.48274 9.01633 9.4842 8.84807C9.48567 8.67981 9.42098 8.51771 9.30409 8.39668L5.90741 5Z" fill="#6B7280"/>
            </g>
            <defs>
            <clipPath id="clip0_707_6588">
            <rect width="10" height="10" fill="white"/>
            </clipPath>
            </defs>
        </svg>
    </div>
      <Form v-if="!showOnlyNameAndEmail" :validation-schema="ScheduleMeetingsValidation" v-slot="{ values, setFieldValue }"  @submit="handleForm" :class="Store.isMobile?'px-3':''">
        {{ console.log("values",values)
         }}
        <!-- Name -->
        <div class="h-[90px]">
          <label for="name" class="text-[14px] font-medium text-black">Lead Name*</label>
          <Field type="text" name="name" class="input-primary w-full !bg-gray-50" placeholder="Full Name" />
          <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="name" />
        </div>
        <!-- Phone -->
        <div v-if="Store.isMobile" class="h-[90px]">
          <label for="phone" class="text-[14px] font-medium text-black">Phone*</label>
          <Field type="text" name="phone" class="input-primary w-full !bg-gray-50" placeholder="Phone" />
          <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="phone" />
        </div>

        <!-- Email -->
        <div class="h-[90px]">
          <label for="email" class="text-[14px] font-medium text-black">Email ID*</label>
          <Field type="email" name="email" class="input-primary w-full !bg-gray-50" placeholder="Email ID" />
          <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="email" />
        </div>

        <!-- Slot & Date -->

        <div class="flex gap-3 h-[100px]">
          <div class="w-1/2  relative">
          <label for="slot" class="text-[14px] font-medium text-black">Date</label>
                <Field name="date"  v-model="date" v-slot="{ field }">
                  <DatePicker v-bind="field" :initalValue="date" class="h-[40px] bg-gray-50 rounded-md"   @select-date="(selectedDate) => handleDate(selectedDate)"  ref="dp" label="Enter Date" css_id="picker" :required="true" :showLabelHigh="false" />
                </Field>
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="date" />
          </div>
          <div class="w-1/2">
            <label for="slot" class="text-[14px] font-medium text-black">Slot</label>
            <Field name="slot" v-model="selectedSlot" v-slot="{field}">
                 <Multiselect v-model="selectedSlot" :options="AvailableSlots" :searchable="false" :close-on-select="true" :show-labels="false"
                 placeholder="Select a Slot" aria-label="Select a Slot" v-bind="field" class="!bg-gray-50 !rounded-lg" maxHeight="100">
                </Multiselect>
            </Field>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="slot" />
          </div>
        </div>

        <!-- Start & End Time -->
        <div class="flex gap-3 h-[100px]">
          <div class="w-1/2 relative">
            <label for="startTime" class="text-[14px] font-medium text-black">Start</label>
              <Field name="startTime" :model-value="initialStartTime" v-slot="{field}" class="w-full p-2 border border-black rounded !bg-gray-50">
                <VueDatePicker
                class="!bg-gray-50 border rounded-lg h-[44px] flex items-center relative"
                  v-bind="field"
                  v-model="startTime"
                  :format="()=>startTime"
                  @update:modelValue="(time) => handleStartTime(time, setFieldValue)"
                  time-picker
                />
                <label v-if="!startTime" class="absolute top-[2.7rem] left-10">- -:- -</label>
              </Field>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="startTime" />
          </div>

          <div class="w-1/2 relative">
            <label for="endTime" class="text-[14px] font-medium text-black">End</label>
              <Field name="endTime" :model-value="initialEndTime" v-slot="{field}" class="w-full p-2 border border-black rounded !bg-gray-50">
                <VueDatePicker
                class="!bg-gray-50 border rounded-lg h-[44px] flex items-center relative"
                  v-bind="field"
                  v-model="endTime"
                  :format="()=>endTime"
                  @update:modelValue="(time) => handleEndTime(time, setFieldValue)"
                  time-picker
                />
                <label v-if="!endTime" class="absolute top-[2.7rem] left-10">--:--</label>
              </Field>
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="endTime" />
          </div>
        </div>
        <!-- Submit Button -->
        <div class=" w-full flex items-center justify-between" :class="Store.isMobile?'h-[45px] gap-3':'h-[60px]'">
          <div @click="emits('closeModal')" v-if="Store.isMobile" class=" hover:bg-[#1a56db] border border-gray-400 bg-white text-black active:!bg-gray-100 px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
              rounded-lg flex flex-row justify-center items-center gap-2" :class="Store.isMobile?'h-full w-[50%]':'h-[90px] w-full'">
            Cancel
          </div>
          <button type="submit" class=" hover:bg-[#1a56db] bg-[#1c64f2] text-white active:bg-[#1e429f] px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
              rounded-lg flex flex-row justify-center items-center gap-2" :class="Store.isMobile?'h-full w-[50%]':'h-[90px] w-full'">
            Schedule
            <Spinner v-if="loader" />
          </button>
        </div>
      </Form>
      <Form v-if="showOnlyNameAndEmail" :validation-schema="ScheduleStartMeetingsValidation"  @submit="handleForm" :class="Store.isMobile?'px-3':''">
        <!-- Name -->
        <div class="h-[90px]">
          <label for="name" class="text-[14px] font-medium text-black">Lead Name*</label>
          <Field type="text" name="name" class="input-primary w-full !bg-gray-50" placeholder="Full Name" />
          <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="name" />
        </div>
        <!-- Phone -->
        <div v-if="Store.isMobile" class="h-[90px]">
          <label for="phone" class="text-[14px] font-medium text-black">Phone*</label>
          <Field type="text" name="phone" class="input-primary w-full !bg-gray-50" placeholder="Phone" />
          <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="phone" />
        </div>

        <!-- Email -->
        <div class="h-[90px]">
          <label for="email" class="text-[14px] font-medium text-black">Email ID*</label>
          <Field type="email" name="email" class="input-primary w-full !bg-gray-50" placeholder="Email ID" />
          <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="email" />
        </div>

        <!-- Submit Button -->
        <div class=" w-full flex items-center justify-between" :class="Store.isMobile?'h-[45px] gap-3':'h-[60px]'">
          <div @click="emits('closeModal')" v-if="Store.isMobile" class=" hover:bg-[#1a56db] border border-gray-400 bg-white text-black active:!bg-gray-100 px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
              rounded-lg flex flex-row justify-center items-center gap-2" :class="Store.isMobile?'h-full w-[50%]':'h-[90px] w-full'">
            Cancel
          </div>
          <button type="submit" class=" hover:bg-[#1a56db] bg-[#1c64f2] text-white active:bg-[#1e429f] px-4 py-1 max-sm:px-2 sm:py-2 sm:h-10
              rounded-lg flex flex-row justify-center items-center gap-2" :class="Store.isMobile?'h-full w-[50%]':'h-[90px] w-full'">
            {{ showOnlyNameAndEmail ? 'Start Meeting':'Schedule' }}
            <Spinner v-if="loader" />
          </button>
        </div>
      </Form>
    </div>
  </ModaL>
</template>

<style>
.multiselect__single{
    background-color: #f9fafb;
}

</style>

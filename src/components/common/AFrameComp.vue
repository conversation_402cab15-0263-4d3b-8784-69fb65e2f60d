<script setup>
import { ref, onMounted } from 'vue';
import { cdn, loadImageData } from '@/helpers/index';

const emit = defineEmits('progressData');

const props = defineProps({
  thumbnail: {
    type: String,
    required: true,
  },
  highRes: {
    type: String,
    required: true,
  },
});
const highResDataLoaded = ref(false);

const loadHighResImage = (imgURL) => {
  loadImageData(imgURL, (progress) => emit('progressData', 50+(progress/2)), () => emit('progressData', false)).then((highRes) => {
    highResDataLoaded.value = highRes;
  });
};
onMounted(() => {
  emit('progressData', 0);
  loadHighResImage(cdn(props.highRes));
});

</script>

<template>
  <a-scene
    embedded
    class="h-full sample"
    loading-screen="enabled: false"
    vr-mode-ui="enabled: false"
    device-orientation-permission-ui="enabled: false"
    renderer="colorManagement: true; sortObjects: true; maxCanvasWidth: 1920; maxCanvasHeight: 1920;"
    gltf-model="dracoDecoderPath: https://propvr.tech/draco/"
  >
    <a-assets>
      <img
        v-if="!highResDataLoaded"
        id="sky"
        :src="cdn(thumbnail)"
        crossorigin="anonymous"
        @load="()=>$emit('progressData',50)"
      >
      <img
        v-else
        id="skyHigh"
        :src="highResDataLoaded"
        crossorigin="anonymous"
      >
    </a-assets>
    <a-sky
      v-if="!highResDataLoaded"
      :src="'#sky'"
    />
    <a-sky
      v-else
      :src="'#skyHigh'"
    />
    <a-entity
      camera
      look-controls="enabled: true"
      orbit-controls="minDistance: 2; maxDistance: 180; initialPosition: 0 5 90; rotateSpeed: 0.5"
    />
  </a-scene>
</template>

<style scoped>
</style>

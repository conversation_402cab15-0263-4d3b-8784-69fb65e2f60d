<script setup>
import { ref, watch, onMounted } from 'vue';

const emits = defineEmits(['handleSelect']);

const props = defineProps({
  options: Array,
});

const searchValue = ref('');
const filteredOptions = ref([]);
const recentSearches = ref([]);
const showDropdown = ref(false);
const isOpen = ref(false);
const searchInput = ref(null);

// Load recent searches
const loadRecentSearches = () => {
  try {
    const storedSearches = localStorage.getItem('recentSearches');
    if (storedSearches) {
      recentSearches.value = JSON.parse(storedSearches);
    }
  } catch (error) {
    console.error('Failed to load recent searches:', error);
  }
};

// Save recent searches
const saveRecentSearches = () => {
  try {
    localStorage.setItem('recentSearches', JSON.stringify(recentSearches.value));
  } catch (error) {
    console.error('Failed to save recent searches:', error);
  }
};

// On mount, load recent searches
onMounted(() => {
  loadRecentSearches();
});

// Watch for changes in recent searches
watch(recentSearches, saveRecentSearches, { deep: true });

// Filter options
const filterOptions = (val) => {
  if (props.options.length > 0) {
    filteredOptions.value = props.options.filter((option) => {
      const optionName = option.name.toLowerCase();
      return optionName.includes(val.toLowerCase());
    });
  }
};

// Handle input and dropdown
const handleFilterList = (val) => {
  if (val && val.length > 0) {
    filterOptions(val);
    showDropdown.value = true;
  } else {
    filteredOptions.value = [];
    showDropdown.value = true;
  }
};

// Select option
const selectOption = (option) => {
  searchValue.value = option.name;
  showDropdown.value = false;

  const recentItem = {
    // _id: option._id,
    name: option.name,
    value: option.value,
  };

  if (!recentSearches.value.find((item) => item.name === option.name)) {
    recentSearches.value.unshift(recentItem);
  }

  if (recentSearches.value.length > 5) {
    recentSearches.value.pop();
  }

  emits('handleSelect', option);
};

watch(isOpen, (val) => {
  if (val) {
    // Wait for DOM update, then focus
    setTimeout(() => {
      searchInput.value?.focus();
    }, 0);
  }
});

// Watch input changes
watch(searchValue, (val) => {
  handleFilterList(val);
});

// open and close dropdown on search input
const handleFocus = () => {
  showDropdown.value = true;
};

// open and close dropdown on recent searches
const handleFocusOut = (event) => {
  if (!event.relatedTarget || !event.relatedTarget.closest('.dropdown-list')) {
    showDropdown.value = false;
  }
};

// Prevent closing when clicking in the dropdown
const handleDropdownMousedown = (event) => {
  event.preventDefault();
  event.stopPropagation();
};
</script>

<template>
 <div
  class="relative sm:w-[325px] w-screen z-100 font-sans text-gray-900 "
  :class="!isOpen ? 'hidden sm:flex' : 'flex'"
>
  <!-- Search Input -->
  <div class="sm:relative w-full fixed left-0 z-50 px-4">
    <div
      class="w-full h-10 px-3 py-2 bg-gray-200 rounded-lg flex items-center gap-2 !border transition-all duration-200"
      :class="showDropdown ? 'border-[#1c64f2] shadow-lg' : ''"
    >
      <!-- Search Icon -->
      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none"
      class="hidden sm:block">
        <g id="search-outline" clip-path="url(#clip0_977_24778)">
          <g id="Vector">
            <path
              d="M5.60007 11.2C4.49248 11.2 3.40976 10.8716 2.48884 10.2562C1.56791 9.64089 0.850138 8.76629 0.426282 7.74302C0.0024264 6.71976 -0.108474 5.59379 0.107606 4.50749C0.323686 3.4212 0.857041 2.42338 1.64022 1.6402C2.42341 0.85703 3.42124 0.323682 4.50755 0.107605C5.59386 -0.108472 6.71984 0.00242637 7.74312 0.426277C8.7664 0.850127 9.64101 1.56789 10.2564 2.48881C10.8717 3.40972 11.2001 4.49242 11.2001 5.6C11.1985 7.0847 10.6079 8.50811 9.55807 9.55795C8.50822 10.6078 7.08479 11.1983 5.60007 11.2ZM5.60007 1.4C4.76938 1.4 3.95734 1.64633 3.26665 2.10783C2.57595 2.56933 2.03762 3.22528 1.71973 3.99273C1.40184 4.76018 1.31866 5.60466 1.48072 6.41938C1.64278 7.2341 2.0428 7.98247 2.63018 8.56985C3.21757 9.15723 3.96595 9.55724 4.78068 9.71929C5.59541 9.88135 6.4399 9.79818 7.20736 9.48029C7.97482 9.1624 8.63077 8.62408 9.09228 7.93339C9.55379 7.24271 9.80012 6.43068 9.80012 5.6C9.799 4.48643 9.35614 3.41879 8.56872 2.63138C7.7813 1.84397 6.71365 1.40111 5.60007 1.4Z"
              fill="#6B7280" />
            <path
              d="M13.3001 14C13.1145 14 12.9365 13.9262 12.8052 13.7949L10.0052 10.9949C9.87769 10.8629 9.80713 10.6861 9.80873 10.5025C9.81032 10.319 9.88394 10.1434 10.0137 10.0136C10.1435 9.88384 10.3191 9.81022 10.5026 9.80863C10.6862 9.80703 10.863 9.87759 10.995 10.0051L13.795 12.8051C13.8929 12.903 13.9596 13.0277 13.9866 13.1635C14.0136 13.2992 13.9997 13.44 13.9467 13.5679C13.8938 13.6957 13.8041 13.8051 13.689 13.882C13.5739 13.9589 13.4386 14 13.3001 14Z"
              fill="#6B7280" />
          </g>
        </g>
        <defs>
          <clipPath id="clip0_977_24778">
            <rect width="14" height="14" fill="white" />
          </clipPath>
        </defs>
      </svg>
      <svg xmlns="http://www.w3.org/2000/svg"
     fill="none"
     viewBox="0 0 24 24"
     stroke-width="1.5"
     stroke="currentColor"
     class="w-6 h-6 text-gray-700 sm:hidden block"
     @click="isOpen = !isOpen">
  <path stroke-linecap="round"
        stroke-linejoin="round"
        d="M15.75 19.5L8.25 12l7.5-7.5" />
</svg>

      <input
        type="text"
        v-model="searchValue"
        ref="searchInput"
        @blur="isOpen = false"
        @focus="handleFocus"
        @focusout="handleFocusOut"
        class="w-full bg-transparent outline-none text-left placeholder:text-left text-gray-900"
        placeholder="Search Projects"
      />
    </div>

    <div>
  <ul
    v-if="showDropdown && filteredOptions.length > 0"
    class="sm:absolute left-0 w-full relative bg-white border border-gray-300 rounded shadow-md z-50 mt-2 p-2 max-h-[50vh] overflow-y-auto"
    @mousedown="handleDropdownMousedown"
  >
    <!-- Filtered Options -->
    <li
      v-for="option in filteredOptions"
      :key="option._id"
      @click="selectOption(option)"
      class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
    >
      {{ option.name }}
    </li>

    <!-- No Results -->
    <li v-if="filteredOptions.length === 0" class="px-4 py-2 text-gray-500 text-sm">
      No results found
    </li>
  </ul>
</div>

   <!-- with recent search -->
    <!-- <div>

    <ul
      v-if="showDropdown && (filteredOptions.length > 0 || recentSearches.length > 0)"
      class=" sm:absolute left-0 w-full relative bg-white border border-gray-300 rounded shadow-md z-50 mt-2 p-2 max-h-[50vh] overflow-y-auto "
      @mousedown="handleDropdownMousedown"
    >

      <li
        v-for="option in filteredOptions"
        :key="option._id"
        @click="selectOption(option)"
        class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
      >
        {{ option.name }}
      </li>

      <template v-if="!filteredOptions.length === 0 && recentSearches.length > 0" >
        <li class="px-4 py-1 text-gray-700 text-sm">Recent</li>
        <li
          v-for="recent in recentSearches"
          :key="recent._id"
          @click="selectOption(recent)"
          class="px-4 py-2 hover:bg-gray-100 cursor-pointer flex gap-2 items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
            <path d="..." fill="#6B7280" />
          </svg>
          {{ recent.name }}
        </li>
      </template>
      <li v-if="filteredOptions.length === 0 && recentSearches.length === 0" class="px-4 py-2 text-gray-500 text-sm">
        No recent searches
      </li>
    </ul>
</div> -->

    <!-- Dropdown -->

  </div>
</div>

  <div  class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center sm:hidden"
  @click="isOpen = !isOpen">

    <!-- Search Icon -->
    <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none">
      <g id="search-outline" clip-path="url(#clip0_977_24778)">
        <g id="Vector">
          <path
            d="M5.60007 11.2C4.49248 11.2 3.40976 10.8716 2.48884 10.2562C1.56791 9.64089 0.850138 8.76629 0.426282 7.74302C0.0024264 6.71976 -0.108474 5.59379 0.107606 4.50749C0.323686 3.4212 0.857041 2.42338 1.64022 1.6402C2.42341 0.85703 3.42124 0.323682 4.50755 0.107605C5.59386 -0.108472 6.71984 0.00242637 7.74312 0.426277C8.7664 0.850127 9.64101 1.56789 10.2564 2.48881C10.8717 3.40972 11.2001 4.49242 11.2001 5.6C11.1985 7.0847 10.6079 8.50811 9.55807 9.55795C8.50822 10.6078 7.08479 11.1983 5.60007 11.2ZM5.60007 1.4C4.76938 1.4 3.95734 1.64633 3.26665 2.10783C2.57595 2.56933 2.03762 3.22528 1.71973 3.99273C1.40184 4.76018 1.31866 5.60466 1.48072 6.41938C1.64278 7.2341 2.0428 7.98247 2.63018 8.56985C3.21757 9.15723 3.96595 9.55724 4.78068 9.71929C5.59541 9.88135 6.4399 9.79818 7.20736 9.48029C7.97482 9.1624 8.63077 8.62408 9.09228 7.93339C9.55379 7.24271 9.80012 6.43068 9.80012 5.6C9.799 4.48643 9.35614 3.41879 8.56872 2.63138C7.7813 1.84397 6.71365 1.40111 5.60007 1.4Z"
            fill="#6B7280" />
          <path
            d="M13.3001 14C13.1145 14 12.9365 13.9262 12.8052 13.7949L10.0052 10.9949C9.87769 10.8629 9.80713 10.6861 9.80873 10.5025C9.81032 10.319 9.88394 10.1434 10.0137 10.0136C10.1435 9.88384 10.3191 9.81022 10.5026 9.80863C10.6862 9.80703 10.863 9.87759 10.995 10.0051L13.795 12.8051C13.8929 12.903 13.9596 13.0277 13.9866 13.1635C14.0136 13.2992 13.9997 13.44 13.9467 13.5679C13.8938 13.6957 13.8041 13.8051 13.689 13.882C13.5739 13.9589 13.4386 14 13.3001 14Z"
            fill="#6B7280" />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_977_24778">
          <rect width="14" height="14" fill="white" />
        </clipPath>
      </defs>
    </svg>
  </div>
  <div  class="w-full h-full absolute top-0 right-0 z-10 bg-transparent backdrop-blur-[0.1rem]"
  :class="!isOpen? 'hidden':'block'"
  @click="isOpen = !isOpen">
  </div>
</template>

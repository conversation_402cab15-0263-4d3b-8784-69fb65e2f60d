<script setup>

</script>

<template>
   <div class="w-full tableScrollable overflow-y-auto overflow-x-auto h-[700px]">
      <table class="w-full">
         <thead class="bg-slate-300 w-full sticky top-0">
            <th class="px-2 py-3">
               <div class="tableSkeletonLoader">

               </div>
            </th>
            <th class="px-2 py-3">
               <div class="tableSkeletonLoader">

               </div>
            </th>
            <th class="px-2 py-3">
               <div class="tableSkeletonLoader">

               </div>
            </th>
            <th class="px-2 py-3">
               <div class="tableSkeletonLoader">

               </div>
            </th>
            <th class="px-2 py-3">
               <div class="tableSkeletonLoader">

               </div>
            </th>
         </thead>
         <tbody class="w-full [&>tr:last-child>td]:border-none">
            <tr class="text-white">

               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
            </tr>
            <tr class="text-white">

               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
            </tr>
            <tr class="text-white">

               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
            </tr>
            <tr class="text-white">

               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
               <td class="px-2 py-3  border-b-[1px] border-gray-300 ">
                  <div class="tableSkeletonLoader">

                  </div>
               </td>
            </tr>
         </tbody>
      </table>
   </div>
</template>

<style scoped>
.tableSkeletonLoader {
   border-radius: 10px;
   width: 200px;
   height: 16px;
   background-color: #262626 !important;
   background: linear-gradient(100deg,
         rgba(255, 255, 255, 0) 40%,
         rgba(255, 255, 255, .5) 50%,
         rgba(255, 255, 255, 0) 60%) #262626;
   background-size: 200% 100%;
   background-position-x: 180%;
   animation: 1s loading ease-in-out infinite;
}

@keyframes loading {
   to {
      background-position-x: -20%;
   }
}
</style>

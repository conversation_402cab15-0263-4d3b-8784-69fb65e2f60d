<script setup>
import { watch } from 'vue';
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue';
import Multiselect from 'vue-multiselect';
const props = defineProps({
  options: Array,
  label: String,
  selectedOption: Object,
  value: String,
  center: Boolean,
  width: Number,
  trackBy: String,
});

console.log(props.options, props.label, props.selectedOption, props.value);

watch(() => props.selectedOption, (val) => {
  console.log(val);
});

const emit = defineEmits(['handleSelect']);
const selectOption = ref(null);
const dropdown = ref(false);
const top = ref();
const left = ref();
const container = ref();
const dropdownBox = ref();
const selected = ref(props.selectedOption);

const dropdownWidth = computed(() => {
  if (dropdownBox.value) {
    return dropdownBox.value.offsetWidth;
  }
  return 0;
});

const containerStyle = computed(() => {
  if (props.width) {
    return { width: typeof props.width === 'number' ? `${props.width}px` : props.width };
  }
  return {
    width: 'fit-content',
  };
});

function adjustToViewport (dropdownRect, viewportWidth, viewportHeight) {
  let adjustedTop = top.value;
  let adjustedLeft = left.value;

  // Check right boundary
  if (adjustedLeft + dropdownRect.width > viewportWidth) {
    adjustedLeft = viewportWidth - dropdownRect.width - 10; // 10px padding
  }

  // Check left boundary
  if (adjustedLeft < 0) {
    adjustedLeft = 10; // 10px padding
  }

  // Check bottom boundary
  if (adjustedTop + dropdownRect.height > viewportHeight) {
    // Position above the button instead
    const buttonHeight = container.value.getBoundingClientRect().height;
    adjustedTop = adjustedTop - dropdownRect.height - buttonHeight - 10;
  }

  // Check top boundary
  if (adjustedTop < 0) {
    adjustedTop = 10;
  }

  return { adjustedTop, adjustedLeft };
}

function openDropdown (event) {
  const containerRect = container.value.getBoundingClientRect();
  const viewportWidth = window.innerWidth;
  const viewportHeight = window.innerHeight;

  if (props.center) {
    // Center alignment
    top.value = containerRect.top + 50;
    left.value = containerRect.left;

    dropdown.value = !dropdown.value;

    nextTick(() => {
      if (dropdownBox.value) {
        const dropdownRect = dropdownBox.value.getBoundingClientRect();
        const containerCenter = containerRect.left + (containerRect.width / 2);
        const dropdownHalfWidth = dropdownWidth.value / 2;
        left.value = containerCenter - dropdownHalfWidth;

        // Adjust position if it exceeds viewport
        const { adjustedTop, adjustedLeft } = adjustToViewport(dropdownRect, viewportWidth, viewportHeight);
        top.value = adjustedTop;
        left.value = adjustedLeft;
      }
    });
  } else {
    // Original alignment
    top.value = event.currentTarget.getBoundingClientRect().top + 50;
    left.value = event.currentTarget.getBoundingClientRect().left;

    dropdown.value = !dropdown.value;

    nextTick(() => {
      if (dropdownBox.value) {
        const dropdownRect = dropdownBox.value.getBoundingClientRect();
        // Adjust position if it exceeds viewport
        const { adjustedTop, adjustedLeft } = adjustToViewport(dropdownRect, viewportWidth, viewportHeight);
        top.value = adjustedTop;
        left.value = adjustedLeft;
      }
    });
  }

  // Adjust dropdown width if container width is specified
  if (props.width) {
    nextTick(() => {
      if (dropdownBox.value) {
        dropdownBox.value.style.width = `${containerRect.width}px`;
      }
    });
  }

  event.stopPropagation();
}

function handleSelectOption (item) {
  selectOption.value = item;
  dropdown.value = !dropdown.value;
  emit('handleSelect', item);
}

function closeDropdown (event) {
  if (!event.target.closest('.custom-dropdown')) {
    dropdown.value = false;
  }
}

onMounted(() => {
  if (props.selectedOption !== null && props.selectedOption !== undefined) {
    selectOption.value = props.selectedOption;
  } else {
    if (Array.isArray(props.options) && props.options !== null) {
      selectOption.value = props.options[0];
    }

    if (typeof props.options === 'object' && props.options !== null && !Array.isArray(props.options)) {
      const objectValues = Object.values(props.options);
      selectOption.value = objectValues[0];
    }
  }
  document.body.addEventListener('click', closeDropdown);
});

onUnmounted(() => {
  document.body.removeEventListener('click', closeDropdown);
});
</script>

<template>
  <div ref="container" class="flex" v-if="options" :style="containerStyle">
    <div
      v-if="label"
      class="w-fit px-4 py-2.5 bg-stone-50 dark:bg-bg-default rounded-tl-lg rounded-bl-lg border border-gray-300 justify-center items-center gap-2.5 inline-flex truncate">
      <div class="text-txt-50 dark:text-txt-1000 text-sm capitalize truncate">
        {{ label }}
      </div>
    </div>
    <div
      @click="openDropdown"
      class="dropdown flex-1 bg-bg-1000 dark:bg-bg-default rounded-tr-lg rounded-br-lg border border-gray-300 hover:cursor-pointer text-txt-50 dark:text-txt-1000"
      :class="[width ? 'min-w-0' : 'min-w-[208px]', !label  && 'rounded-tl-lg rounded-bl-lg']"
    >
      <Multiselect v-model="selected" :options="options" :custom-label="(val) => val[value]" @select="handleSelectOption" :searchable="false" :track-by="trackBy ? trackBy : value" :show-labels="false" :maxHeight="170"/>
    </div>
</div>
</template>

<style >
.dropdown .multiselect__tags{
  border: none !important;
  padding-top: 10px !important;
  font-weight: 500 !important;
}
.dropdown .multiselect__select{
  padding-top: 7px;
}
</style>

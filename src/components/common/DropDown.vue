<script setup>
import { ref, watch } from "vue";

// Props definition
const props = defineProps({
  options: { type: Array, required: true },
  selectedOption: { type: Object, default: null },
  value: { type: String, default: "name" },
});

// Emit for selection
const emit = defineEmits(["handleSelect"]);

// State variables
const isOpen = ref(false); // Controls dropdown visibility
const selectedValue = ref(props.selectedOption || null); // Holds the current selected value
const name = ref(selectedValue.value ? selectedValue.value[props.value] : ""); // Displayed name

// Watch for changes in props.selectedOption and update the selectedValue and name accordingly
watch(
  () => props.selectedOption,
  (newOption) => {
    selectedValue.value = newOption;
    name.value = newOption ? newOption[props.value] : "";
  },
  { immediate: true }, // Watch runs initially as well
);

// Handle option selection
function handleSelectOption (option) {
  selectedValue.value = option; // Update the selected value
  name.value = option[props.value]; // Update the displayed name
  isOpen.value = false; // Close dropdown
  emit("handleSelect", option); // Emit selected option to parent
}
</script>

  <template>
    <div class="relative">
      <!-- Button -->
      <button
        @click="isOpen = !isOpen"
        class="h-[37px] bg-gray-200 px-3 py-2 rounded-lg !border justify-center items-center gap-2 inline-flex overflow-hidden"
        :class="isOpen ? 'text-[#1c64f2] border-[#1c64f2] bg-white' : 'text-black'"
      >
        <div class="flex items-center">
          <!-- First Letter Box -->
          <div
            class="w-5 h-5 left-0 top-0 relative bg-gradient-to-b from-[#ff22f0] to-[#ff93f7] rounded-md flex justify-center items-center"
          >
            <div class="absolute text-white text-[10.50px] leading-none font-bold">
              {{ name[0]  }} <!-- Fallback for empty name -->
            </div>
          </div>
        </div>
        <!-- Button Text -->
        <div class="text-sm w-[39px] text-ellipsis overflow-hidden text-nowrap">
          {{ name }} <!-- Fallback for empty options -->
        </div>
        <!-- Arrow Icon -->
        <svg
          viewBox="0 0 14 15"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          :class="`h-4 w-4 transform transition-transform duration-300 ${isOpen ? 'rotate-180 fill-[#1c64f2]' : 'fill-gray-400'}`"
        >
          <path
            d="M7.0147 4C6.68056 4.00007 6.36012 4.12278 6.12388 4.34114L1.08388 8.99826C0.963539 9.10566 0.86755 9.23414 0.801514 9.37618C0.735479 9.51823 0.70072 9.67101 0.699266 9.8256C0.697813 9.98019 0.729692 10.1335 0.793046 10.2766C0.8564 10.4197 0.949959 10.5497 1.06826 10.659C1.18657 10.7683 1.32725 10.8548 1.4821 10.9133C1.63695 10.9718 1.80287 11.0013 1.97017 11C2.13747 10.9986 2.30281 10.9665 2.45653 10.9055C2.61026 10.8445 2.74929 10.7558 2.86552 10.6446L7.0147 6.81058L11.1639 10.6446C11.4015 10.8566 11.7198 10.974 12.0502 10.9713C12.3805 10.9687 12.6966 10.8462 12.9302 10.6304C13.1638 10.4145 13.2963 10.1225 13.2992 9.81722C13.302 9.51195 13.175 9.21785 12.9455 8.99826L7.90552 4.34114C7.66928 4.12278 7.34885 4.00007 7.0147 4Z"
          />
        </svg>
      </button>

      <!-- Dropdown -->
      <transition name="fade" mode="out-in">
        <div
          v-if="isOpen"
          class="absolute mt-2 w-max bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10 max-h-[50vh] overflow-scroll"
        >
          <ul class="py-1">
            <li
              v-for="(option, index) in options"
              :key="index"
              @click="handleSelectOption(option)"
              class="flex gap-2 items-center px-4 py-2 text-gray-700 hover:bg-blue-100 hover:text-blue-900 cursor-pointer"
            >
              <!-- First Letter of Each Option -->
              <div class="flex items-center">
                <div
                  class="w-5 h-5 left-0 top-0 relative bg-gradient-to-b from-[#ff22f0] to-[#ff93f7] rounded-md flex justify-center items-center"
                >
                  <div
                    class="absolute text-white text-[10.50px] leading-none font-bold"
                  >
                    {{ option.name[0] }}
                  </div>
                </div>
              </div>
              {{ option.name }}
            </li>
          </ul>
        </div>
      </transition>
    </div>
  </template>

  <style scoped>
  /* Transition for dropdown */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
    transform: translateY(-5%);
  }
  .fade-enter-to,
  .fade-leave-from {
    opacity: 1;
    transform: translateY(0);
  }
  </style>

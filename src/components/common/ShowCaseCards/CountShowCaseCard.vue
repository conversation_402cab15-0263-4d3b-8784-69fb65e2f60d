<script setup>

import { UserStore } from '../../../../src/store/index.ts';

const store = UserStore();
defineProps({
  title: String,
  count: Number,
  description: String,
  isMobile: {
    type: Boolean,
    default: false,
  },
});
store.callbackFunctionMonitorChanges();

</script>

<template>
    <div  v-if="!store.isMobile" class="p-4 rounded-2xl border-2 border-bg-950 dark:border-bg-default flex flex-col justify-start items-start gap-7 bg-bg-1000 dark:bg-bg-default">
            <div class="flex  items-center w-full flex-row justify-between">
                   <div class="text-txt-450 dark:text-txt-900 font-medium whitespace-nowrap text-nowrap">{{ title }}</div>
                    <slot name="svg"></slot>
            </div>

            <div class="flex justify-start items-start flex-col gap-[18px] w-full">
                <h5 class="text-txt-100 dark:text-txt-1000 text-5xl font-bold ">
                        {{  count }}
                </h5>

                <div class="flex justify-start items-center gap-2">
                  <p class=" text-txt-100 dark:text-txt-1000  text-base font-medium"> {{  description }} </p>
                </div>

            </div>
    </div>

    <div v-else class="p-2 rounded-xl border-2 border-bg-950 dark:border-bg-default flex flex-col justify-start items-start gap-7 bg-bg-1000 dark:bg-bg-default"
    :class="isMobile?'flex-row':''">
            <div class="flex flex-col justify-between items-start gap-2 w-full">
                   <slot name="svg"></slot>

                   <div class="text-txt-450 text-xs dark:text-txt-900 font-medium whitespace-nowrap text-wrap">{{ title }}</div>
                   <h5 class="text-txt-100 dark:text-txt-1000 text-2xl font-bold ">
                           {{  count }}
                   </h5>
                   <div class="flex justify-start items-center gap-2">
                     <p class=" text-txt-100 dark:text-txt-1000  text-base font-medium"> {{  description }} </p>
                   </div>
            </div>

    </div>
</template>

<!--  (60) , 27 (18) -->

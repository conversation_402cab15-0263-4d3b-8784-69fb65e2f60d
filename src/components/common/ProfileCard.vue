<script setup>
import { defineProps, defineEmits, ref, watch } from 'vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
const API_URL = import.meta.env.VITE_API_URL;
import { PutRequestWithHeaders } from '../../helpers/apihelper';
import Spinner from './Spinner.vue';
import { processFirebaseFile } from '@/helpers/helpers';
import DnDFileUploader from './DnDFileUploader.vue';
import { UserStore } from '../../store/index';

// Define props
const props = defineProps(['firstName', 'email', 'phone', 'access', 'role', 'profilePicture', 'lastName', 'userid']);
const emit = defineEmits(['update:name', 'update:email', 'update:phone', 'update:access', 'update:role', 'update:profilePicture', "sign-out", "edit-profile"]);

const isLoading = ref();
const firstName = ref(props.firstName);
const lastName = ref(props.lastName);
const localEmail = ref(props.email);
const localPhone = ref(props.phone);
const localRole = ref(props.role);
const profilePictureUrl = ref(props.profilePicture);
const profilePictureFile = ref(null);
const editProfileValue = ref(false);
const firebasePictureUrl = ref(null);

const userStore = UserStore(); // Make sure you instantiate it

// Usage

watch(profilePictureUrl, async (result) => {
  if (!result) {
    return;
  }
  firebasePictureUrl.value = await processFirebaseFile(result);
  console.log("Profile Picture:", firebasePictureUrl.value);
});

// Methods

const signOut = () => {
  emit("sign-out");
};
console.log(props.userid);
watch(() => props, (newProps) => {
  firstName.value = newProps.firstName;
  lastName.value = newProps.lastName;
  localEmail.value = newProps.email;
  localPhone.value = newProps.phone;
  profilePictureUrl.value = newProps.profilePicture;
}, { deep: true, immediate: true });

const submitProfile = async () => {
  try {
    isLoading.value = true;

    const formData = new FormData();
    formData.append('_id', props.userid);
    formData.append('email', localEmail.value);
    formData.append('first_name', firstName.value);
    formData.append('last_name', lastName.value);
    formData.append('phone_number', String(localPhone.value));

    if (profilePictureFile.value) {
      formData.append('profilePicture', profilePictureFile.value);
    } else {
      const response = await fetch(profilePictureUrl.value);
      const blob = await response.blob();
      const file = new File([blob], "profilePicture.jpg", { type: blob.type });
      formData.append('profilePicture', file);
    }

    const response = await PutRequestWithHeaders({
      url: `${API_URL}/user/UpdateUserDetails`,
      body: formData,
    });

    console.log('Profile updated:', response);

    // Call updateUser with the updated data
    userStore.updateUser({
      first_name: response.first_name,
      last_name: response.last_name,
      email: response.email,
      phone_number: response.phone_number,
      profilePicture: response.profilePicture || profilePictureUrl.value,
    });

    // Update local refs to reflect changes
    firstName.value = response.first_name || firstName.value;
    lastName.value = response.last_name || lastName.value;
    localEmail.value = response.email || localEmail.value;
    localPhone.value = response.phone_number || localPhone.value;

    profilePictureUrl.value = profilePictureFile.value
      ? URL.createObjectURL(profilePictureFile.value)
      : profilePictureUrl.value;

    editProfileValue.value = false;

  } catch (error) {
    console.error("Error updating profile:", error);
  } finally {
    isLoading.value = false;
  }
};

const cancelEdit = () => {
  firstName.value = props.firstName;
  lastName.value = props.lastName;
  localEmail.value = props.email;
  localPhone.value = props.phone;
  localRole.value = props.role;
  profilePictureUrl.value = props.profilePicture;

  profilePictureFile.value = null;
  editProfileValue.value = false;
};

// edit
const editProfile = async () => {
  if (editProfileValue.value) {

    const success = await submitProfile();

    if (success) {
      editProfileValue.value = false;
      emit("edit-profile");

      // Optional: emit updated values if parent uses v-model
      emit("update:name", firstName.value);
      emit("update:email", localEmail.value);
      emit("update:phone", localPhone.value);
      emit("update:profilePicture", profilePictureUrl.value);
    }
  } else {

    firstName.value = props.firstName;
    lastName.value = props.lastName;
    localEmail.value = props.email;
    localPhone.value = props.phone;
    localRole.value = props.role;
    profilePictureUrl.value = props.profilePicture;

    editProfileValue.value = true;
    emit("edit-profile");
  }
};

// Reactive variable for toggling the dropdown
const isOpen = ref(false);

// Toggle profile card
const toggleProfileCard = () => {
  isOpen.value = !isOpen.value;
};
</script>

<template>

  <div class="relative sm:bottom-4 z-50 ">

    <!-- Profile Card -->
    <div class=" transition-all duration-300 fixed sm:right-auto right-0 sm:w-auto w-full"
      :class="isOpen ? 'sm:bottom-[5rem] top-[4rem] sm:top-auto  ' : 'sm:bottom-[-18rem] top-[-24rem] sm:top-auto'">

      <div
        class="relative min-w-80 p-3 w-auto h-auto bg-white rounded-lg shadow-lg flex-col justify-start items-start gap-3 z-50 ">
        <!-- Header -->
        <div class="h-3.5 justify-start items-center inline-flex mb-3">
          <div class="text-[#111928] text-base font-semibold  leading-[14px]">Profile</div>
        </div>

        <!-- Profile Details -->
        <div>
          <div class="flex items-center">
            <!-- Profile Picture -->
            <div class="w-[114px] h-[114px]  bg-gray-100 rounded-lg justify-center items-center inline-flex">
              <div
                class="w-[114px] h-[114px] relative flex-col justify-center items-center flex overflow-hidden bg-gray-200 rounded-lg">
                <img v-if="profilePictureUrl" :src="profilePictureUrl" alt="Profile"
                  class="rounded-lg object-cover w-full h-full" />
                <div v-else class="flex justify-center items-center w-full h-full text-blue-500 text-4xl">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" class="w-12 h-12">
                    <path
                      d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 4c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14c-2.67 0-5.2-1.08-7.07-2.93.03-1.99 4-3.07 7.07-3.07s7.04 1.08 7.07 3.07C17.2 18.92 14.67 20 12 20z" />
                  </svg>
                </div>
              </div>

            </div>
            <!-- Profile Info -->
            <div class="ml-4 flex flex-col gap-2">
              <div>
                <!-- <span v-if="role" class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                  {{ role }}
                </span> -->
              </div>
              <div class="gap-2 flex flex-col text-gray-500 ">
                <div class="flex gap-2 justify-start items-center">
                  <p class="font-bold text-gray-900">
                    <span class="text-sm sm:w-32 w-auto truncate font-sans">
                      {{ firstName + " " + lastName }}
                    </span>
                  </p>
                </div>
                <div v-if="email" class="flex gap-2 justify-start items-center">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M7 7.6398L13.4925 2.0804C13.2432 1.86859 12.9271 1.75158 12.6 1.75H1.4C1.07287 1.75158 0.756802 1.86859 0.5075 2.0804L7 7.6398Z"
                      fill="#6B7280" />
                    <path
                      d="M7.875 8.7339C7.62023 8.92877 7.30884 9.03519 6.9881 9.037C6.6892 9.03761 6.39824 8.94079 6.1593 8.7612L0 3.4888V10.85C0 11.2213 0.1475 11.5774 0.41005 11.8399C0.672601 12.1025 1.0287 12.25 1.4 12.25H12.6C12.9713 12.25 13.3274 12.1025 13.5899 11.8399C13.8525 11.5774 14 11.2213 14 10.85V3.4888L7.875 8.7339Z"
                      fill="#6B7280" />
                  </svg>

                  <p class="text-sm sm:w-32 w-auto truncate font-sans">

                    {{ email }}
                  </p>
                </div>

                <div v-if="phone" class="flex gap-2 justify-start items-center">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_991_25227)">
                      <path
                        d="M6.68646 9.16067C7.01188 9.48503 7.45264 9.66717 7.91214 9.66717C8.37164 9.66717 8.8124 9.48503 9.13782 9.16067L9.75066 8.54793C10.0761 8.22357 10.5168 8.04144 10.9763 8.04144C11.4358 8.04144 11.8766 8.22357 12.202 8.54793L13.4277 9.77342C13.6069 9.92344 13.751 10.1109 13.8499 10.3227C13.9488 10.5344 14 10.7652 14 10.9989C14 11.2326 13.9488 11.4634 13.8499 11.6752C13.751 11.8869 13.6069 12.0744 13.4277 12.2244C10.2182 15.4343 6.52362 13.9095 3.30447 10.6908C0.0853122 7.47212 -1.4319 3.78164 1.7785 0.571733C1.92863 0.392706 2.11617 0.248741 2.32792 0.149964C2.53968 0.0511883 2.77051 0 3.00418 0C3.23784 0 3.46867 0.0511883 3.68043 0.149964C3.89218 0.248741 4.07972 0.392706 4.22985 0.571733L5.45553 1.79722C5.78015 2.12248 5.96246 2.56322 5.96246 3.02272C5.96246 3.48221 5.78015 3.92295 5.45553 4.24821L4.84269 4.86095C4.51807 5.18621 4.33576 5.62694 4.33576 6.08644C4.33576 6.54594 4.51807 6.98668 4.84269 7.31193L6.68646 9.16067Z"
                        fill="#6B7280" />
                    </g>
                    <defs>
                      <clipPath id="clip0_991_25227">
                        <rect width="14" height="14" fill="white" />
                      </clipPath>
                    </defs>
                  </svg>

                  <p class="text-sm sm:w-32 w-auto truncate font-sans">

                    {{ phone }}
                  </p>
                </div>

              </div>

            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="w-full mt-2 flex sm:justify-start gap-4 pt-6 border-t border-gray-200">

          <button
            class="h-[34px] w-full sm:w-auto px-3 py-2 rounded-lg !border border-[#c81e1e] justify-center items-center gap-2 inline-flex"
            @click="signOut">
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_991_25232)">
                <path
                  d="M11.9432 5.7135C11.9049 5.62125 11.8502 5.53875 11.7804 5.469L8.78094 2.46975C8.48766 2.1765 8.01363 2.1765 7.72035 2.46975C7.42708 2.763 7.42708 3.237 7.72035 3.53025L9.44024 5.25H3.00023C2.58545 5.25 2.25018 5.586 2.25018 6C2.25018 6.414 2.58545 6.75 3.00023 6.75H9.44024L7.72035 8.46975C7.42708 8.763 7.42708 9.237 7.72035 9.53025C7.86661 9.6765 8.05863 9.75 8.25064 9.75C8.44266 9.75 8.63467 9.6765 8.78094 9.53025L11.7804 6.531C11.8502 6.462 11.9049 6.37875 11.9432 6.2865C12.0189 6.1035 12.0189 5.8965 11.9432 5.7135Z"
                  fill="#E02424" />
                <path
                  d="M4.50035 10.5H2.25018C1.83614 10.5 1.50012 10.1632 1.50012 9.75V2.25C1.50012 1.83675 1.83614 1.5 2.25018 1.5H4.50035C4.91513 1.5 5.25041 1.164 5.25041 0.75C5.25041 0.336 4.91513 0 4.50035 0H2.25018C1.00958 0 0 1.0095 0 2.25V9.75C0 10.9905 1.00958 12 2.25018 12H4.50035C4.91513 12 5.25041 11.664 5.25041 11.25C5.25041 10.836 4.91513 10.5 4.50035 10.5Z"
                  fill="#E02424" />
              </g>
              <defs>
                <clipPath id="clip0_991_25232">
                  <rect width="12" height="12" fill="white" />
                </clipPath>
              </defs>
            </svg>

            <div class="text-[#c81e1e] text-xs font-medium ">Sign Out</div>
          </button>

          <button @click="editProfile"
            class="h-[34px] w-full sm:w-auto px-3 py-2 rounded-lg !border bg-blue-700 justify-center items-center inline-flex">
            <svg class="w-8 h-8" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">

              <g clip-path="url(#clip0_991_25235)">
                <path
                  d="M15.993 16.6842C17.5198 16.6842 18.7575 15.4118 18.7575 13.8421C18.7575 12.2725 17.5198 11 15.993 11C14.4663 11 13.2286 12.2725 13.2286 13.8421C13.2286 15.4118 14.4663 16.6842 15.993 16.6842Z"
                  fill="white" />
                <path
                  d="M16.9145 17.3158H15.0716C14.2572 17.3168 13.4765 17.6498 12.9007 18.2418C12.3249 18.8338 12.001 19.6365 12 20.4737V22.3684C12 22.5359 12.0647 22.6966 12.1799 22.815C12.2951 22.9335 12.4514 23 12.6143 23H15.2307C15.1059 22.6707 15.074 22.3119 15.1385 21.9648L15.5729 19.7322C15.6441 19.3654 15.8194 19.0285 16.0766 18.764L17.433 17.3695C17.262 17.3365 17.0885 17.3186 16.9145 17.3158Z"
                  fill="white" />
                <path
                  d="M23.4619 15.6377C23.1157 15.2834 22.6471 15.0844 22.1586 15.0844C21.6701 15.0844 21.2015 15.2834 20.8553 15.6377L16.9465 19.6571C16.8606 19.7452 16.802 19.8575 16.7781 19.9798L16.3432 22.213C16.325 22.3049 16.3268 22.3997 16.3487 22.4907C16.3705 22.5817 16.4118 22.6665 16.4695 22.7391C16.5272 22.8117 16.5999 22.8701 16.6823 22.9102C16.7647 22.9503 16.8547 22.9711 16.9458 22.9709C16.9871 22.9709 17.0282 22.9668 17.0687 22.9589L19.2409 22.5118C19.3599 22.4872 19.4691 22.427 19.5548 22.3387L19.9861 21.8897L23.4619 18.3175C23.8065 17.9616 24 17.4798 24 16.9776C24 16.4754 23.8065 15.9936 23.4619 15.6377Z"
                  fill="white" />
              </g>

            </svg>
            <div class="text-white text-xs font-medium ">Edit</div>
          </button>
        </div>
      </div>
    </div>
    <!-- Profile Button -->
    <button @click="toggleProfileCard"
      class="h-10 px-2 py-2 rounded-lg !border justify-center items-center gap-2 inline-flex overflow-hidden sm:absolute sticky bottom-4"
      :class="isOpen ? 'text-[#1c64f2] border-[#1c64f2]  bg-white' : 'text-black bg-gray-200'">
      <div class="h-5 w-5 flex items-center justify-center overflow-hidden bg-gray-200 rounded-full">
        <img v-if="profilePictureUrl" :src="profilePictureUrl" alt="Profile"
          class="rounded-full object-cover w-full h-full" />
        <svg v-else xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24"
          class="w-4 h-4 text-gray-500 ">
          <path
            d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 4c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14c-2.67 0-5.2-1.08-7.07-2.93.03-1.99 4-3.07 7.07-3.07s7.04 1.08 7.07 3.07C17.2 18.92 14.67 20 12 20z" />
        </svg>
      </div>
      <!-- Button Text -->
      <div
        class="text-sm w-20 font-sans font-semibold text-ellipsis overflow-hidden text-nowrap text-left sm:block hidden">
        {{ firstName +" "+ lastName }}
      </div>
      <!-- Arrow Icon -->
      <svg viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg" :class="`h-4 w-4 transform transition-transform duration-300 sm:flex hidden ${isOpen ? 'rotate-180 fill-[#1c64f2]' : 'fill-black'
                }`">
        <path
          d="M7.0147 4C6.68056 4.00007 6.36012 4.12278 6.12388 4.34114L1.08388 8.99826C0.963539 9.10566 0.86755 9.23414 0.801514 9.37618C0.735479 9.51823 0.70072 9.67101 0.699266 9.8256C0.697813 9.98019 0.729692 10.1335 0.793046 10.2766C0.8564 10.4197 0.949959 10.5497 1.06826 10.659C1.18657 10.7683 1.32725 10.8548 1.4821 10.9133C1.63695 10.9718 1.80287 11.0013 1.97017 11C2.13747 10.9986 2.30281 10.9665 2.45653 10.9055C2.61026 10.8445 2.74929 10.7558 2.86552 10.6446L7.0147 6.81058L11.1639 10.6446C11.4015 10.8566 11.7198 10.974 12.0502 10.9713C12.3805 10.9687 12.6966 10.8462 12.9302 10.6304C13.1638 10.4145 13.2963 10.1225 13.2992 9.81722C13.302 9.51195 13.175 9.21785 12.9455 8.99826L7.90552 4.34114C7.66928 4.12278 7.34885 4.00007 7.0147 4Z" />
      </svg>
    </button>

  </div>

  <!-- profile edit -->
  <div class="fixed justify-center items-center w-screen h-screen top-0 left-0 z-50 bg-[#00000087] backdrop-blur-sm"
    :class="editProfileValue ? 'flex ' : 'hidden'">
    <div
      class=" sm:w-[40rem] w-full sm:h-[auto] h-fit p-6 bg-white rounded-lg shadow-md flex-col gap-6 sm:bottom-auto bottom-0 absolute">
      <div class="flex justify-between items-center pb-6">
        <h2 class="text-[#111928] text-xl font-bold">Edit Profile</h2>
        <button @click="cancelEdit" class="text-gray-500 hover:text-gray-700">✖</button>
      </div>

      <Form :validation-schema="projectSchema" @submit="handleForm" class="flex flex-col justify-center">
        <div class="  sm:grid grid-cols-2 gap-6 ">
          <div class="col-span-auto">
            <label class="text-sm font-medium  text-black">Profile Picture</label>
            <div class=" relative flex justify-center items-center rounded-md ">

              <Field name="project_thumbnail" id="project_thumbnail"
                :model-value="profilePictureUrl || projectThumbnail">
                <!-- Image Preview with "Change" Button -->
                <!-- <label for="uploadFile" class="text-center cursor-pointer h-full w-full flex justify-center items-center">
              <div v-if="profilePictureUrl"
                class="h-10 w-14 absolute px-5 py-2.5 bg-white rounded-lg border border-gray-200 justify-center items-center gap-2 inline-flex">
                <div class="text-[#111928] text-sm font-medium">Change </div>
              </div>
              <img v-if="profilePictureUrl" :src="profilePictureUrl" class="w-full h-full object-cover rounded-lg">
              <div v-else class="text-gray-500 text-xs">
                <span class="text-[#1c64f2] font-medium">Browse</span>
                <div>(PNG, JPEG, or WEBP)</div>
              </div>
            </label> -->

                <DnDFileUploader ref="fileUploader" inputType="image/*" class="sm:!w-[20rem] sm:!h-[9.4rem] w-40 h-40"
                  inputPlaceholder="Click to upload or drag & drop files here"
                  @fileData="(val)=>{ profilePictureFile = val}" readyToUploadMessage="Ready to upload"
                  :previousFileData="firebasePictureUrl || projectThumbnail"
                  :key="firebasePictureUrl + projectThumbnail" />
              </Field>

              <!-- <img v-if="profilePictureUrl" :src="profilePictureUrl" class="w-full h-full object-cover rounded-lg absolute"> -->
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 absolute bottom-[-20px]"
                name="project_thumbnail" />

            </div>
          </div>
          <div class="col-span-auto h-[200px] sm:max-h-[200px] sm:flex flex-col justify-between hidden">
            <div class="h-[90px]">
              <label class="text-sm font-medium text-black font-sans">First Name</label>
              <div class="mt-1 ">
                <Field v-model="firstName" type="text" name="firstName" id="name" autocomplete="name"
                  class=" input-field px-4 py-2 w-full bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex font-sans placeholder:text-left text-sm text-black"
                  placeholder="First Name" />
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="firstName" />
              </div>
            </div>
            <div class="h-[90px]">
              <label class="text-sm font-medium text-black font-sans">Last Name</label>
              <div class="mt-1">
                <Field v-model="lastName" type="text" name="lastName" id="name" autocomplete="name"
                  class="input-field px-4 py-2 w-full bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex font-sans placeholder:text-left text-sm text-black"
                  placeholder="Last Name" />
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="lastName" />
              </div>
            </div>
          </div>

          <div class="h-[90px] sm:hidden block">
            <label class="text-sm font-medium text-black font-sans">First Name</label>
            <div class="mt-1 ">
              <Field v-model="firstName" type="text" name="firstName" id="name" autocomplete="name"
                class=" input-field px-4 py-2 w-full bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex font-sans placeholder:text-left text-sm text-black"
                placeholder="First Name" />
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="firstName" />
            </div>
          </div>
          <div class="h-[90px] sm:hidden block">
            <label class="text-sm font-medium text-black font-sans">Last Name</label>
            <div class="mt-1">
              <Field v-model="lastName" type="text" name="lastName" id="name" autocomplete="name"
                class="input-field px-4 py-2 w-full bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex font-sans placeholder:text-left text-sm text-black"
                placeholder="Last Name" />
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="lastName" />
            </div>
          </div>

          <div class="h-[90px]">
            <label class="text-sm font-medium text-black font-sans">Email</label>
            <div class="mt-1">
              <Field v-model="localEmail" type="email" name="Email" id="name" autocomplete="name"
                class="input-field px-4 py-2 w-full bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex font-sans placeholder:text-left text-sm text-black"
                placeholder="Email" />
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="lastName" />
            </div>
          </div>

          <div class="h-[90px]">
            <label class="text-sm font-medium text-black font-sans">Phone</label>
            <div class="mt-1">
              <Field v-model="localPhone" type="tel" name="localPhone" id="name" autocomplete="name"
                class="input-field px-4 py-2 w-full bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex font-sans placeholder:text-left text-sm text-black"
                placeholder="Phone" />
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="lastName" />
            </div>
          </div>

        </div>
        <div class="flex justify-end gap-4 sm:mt-2 mt-8 text-sm font-sans">
          <button @click="submitProfile" :disabled="isLoading"
            class="bg-blue-700 sm:w-fit w-full text-white px-5 py-2.5 rounded-lg hover:bg-[#1a56cb]">
            <span v-if="!isLoading">save</span>
            <span v-else class="flex gap-3 m-0 p-0 w-10">
              <Spinner /> Saving...
            </span>
          </button>
        </div>

      </Form>
      <!-- <div class="flex gap-4 mt-8">
              <div class="flex flex-col gap-2 w-80">
            <label class="text-lg font-medium text-black">Email*</label>
            <input v-model="localEmail" type="email" placeholder="Enter Email" class="input-field px-4 py-2 bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex"
              :disabled="isLoading">
          </div>

          <div class="flex flex-col gap-2">
            <label class="text-lg font-medium text-black">Phone*</label>
            <input v-model="localPhone" type="tel" placeholder="Enter phone number" class="input-field px-4 py-2 bg-gray-50 rounded-lg border border-gray-300 justify-start items-center gap-2.5 inline-flex"
              :disabled="isLoading">
          </div>
          </div> -->
      <div>
        <!-- <div class="flex flex-col gap-2 w-full">
          <label>Role</label>

          <template v-if="localRole === 'admin'">
            <select v-model="localRole" class="input-field">
              <option value="admin">Admin</option>
              <option value="user">User</option>
            </select>
          </template>

          <template v-else>
            <input v-model="localRole" class="input-field bg-gray-200" disabled />
          </template>
        </div> -->

      </div>

    </div>

    <!-- Loading Overlay -->
    <!-- <div v-if="isLoading" class="loading-overlay gap-3">
      <div class="loading-spinner"></div>
      <p>Updating User Profile</p>
    </div> -->
  </div>
</template>

<style scoped>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
</style>

<script setup>
defineProps({
  modelValue: {
    type: String,
    required: true,
    validator: (value) => ['table', 'grid'].includes(value),
  },
});

defineEmits(['toggle', 'update:modelValue']);
</script>

<template>
    <div class="flex items-center">
        <button @click="$emit('update:modelValue', 'grid')"
            class="py-1 w-[75.50px] border rounded-tl-lg rounded-bl-lg flex items-center justify-center transition-all" :class="{
                'bg-gray-900 text-white border-gray-900': modelValue === 'grid',
                'border-gray-200 hover:border-gray-300': modelValue !== 'grid'
            }">
            <svg :class="modelValue === 'grid' ? 'fill-white' : 'fill-[#262626]'" class="w-8 h-8" width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4.28711 26.288H28.2871C28.8175 26.288 29.3263 26.0773 29.7013 25.7022C30.0764 25.3271 30.2871 24.8184 30.2871 24.288V8.28796C30.2871 7.75753 30.0764 7.24882 29.7013 6.87375C29.3263 6.49868 28.8175 6.28797 28.2871 6.28797H4.28711C3.75668 6.28797 3.24797 6.49868 2.8729 6.87375C2.49783 7.24882 2.28711 7.75753 2.28711 8.28796V24.288C2.28711 24.8184 2.49783 25.3271 2.8729 25.7022C3.24797 26.0773 3.75668 26.288 4.28711 26.288ZM4.28711 24.288V21.288H28.2871V24.288H4.28711ZM4.28711 8.28796H28.2871V19.288H4.28711V8.28796ZM6.28711 11.288C6.28711 11.0227 6.39247 10.7684 6.58 10.5809C6.76754 10.3933 7.02189 10.288 7.28711 10.288H11.2871C11.5523 10.288 11.8067 10.3933 11.9942 10.5809C12.1818 10.7684 12.2871 11.0227 12.2871 11.288C12.2871 11.5532 12.1818 11.8075 11.9942 11.9951C11.8067 12.1826 11.5523 12.288 11.2871 12.288H7.28711C7.02189 12.288 6.76754 12.1826 6.58 11.9951C6.39247 11.8075 6.28711 11.5532 6.28711 11.288ZM14.2871 11.288C14.2871 11.0227 14.3925 10.7684 14.58 10.5809C14.7675 10.3933 15.0219 10.288 15.2871 10.288H17.2871C17.5523 10.288 17.8067 10.3933 17.9942 10.5809C18.1818 10.7684 18.2871 11.0227 18.2871 11.288C18.2871 11.5532 18.1818 11.8075 17.9942 11.9951C17.8067 12.1826 17.5523 12.288 17.2871 12.288H15.2871C15.0219 12.288 14.7675 12.1826 14.58 11.9951C14.3925 11.8075 14.2871 11.5532 14.2871 11.288Z"/>
            </svg>
        </button>
        <button @click="$emit('update:modelValue', 'table')"
            class="py-1 w-[75.50px] border rounded-tr-lg rounded-br-lg flex items-center justify-center transition-all" :class="{
                'bg-gray-900 text-white border-gray-900': modelValue === 'table',
                'border-gray-200 hover:border-gray-300': modelValue !== 'table'
            }">
            <svg :class="modelValue === 'table' ? 'fill-white' : 'fill-[#262626]'" class="w-8 h-8" width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M28 6H4C3.73478 6 3.48043 6.10536 3.29289 6.29289C3.10536 6.48043 3 6.73478 3 7V24C3 24.5304 3.21071 25.0391 3.58579 25.4142C3.96086 25.7893 4.46957 26 5 26H27C27.5304 26 28.0391 25.7893 28.4142 25.4142C28.7893 25.0391 29 24.5304 29 24V7C29 6.73478 28.8946 6.48043 28.7071 6.29289C28.5196 6.10536 28.2652 6 28 6ZM5 14H10V18H5V14ZM12 14H27V18H12V14ZM27 8V12H5V8H27ZM5 20H10V24H5V20ZM27 24H12V20H27V24Z"/>
            </svg>
        </button>
    </div>
</template>

<style scoped>
button {
    min-width: 36px;
    min-height: 36px;
}

button:focus {
    outline: none;
}

.dark button {
    border-color: rgb(55, 65, 81);
}

.dark button:hover {
    border-color: rgb(75, 85, 99);
}

.dark button.bg-gray-900 {
    background-color: rgb(17, 24, 39);
    border-color: rgb(17, 24, 39);
}
</style>

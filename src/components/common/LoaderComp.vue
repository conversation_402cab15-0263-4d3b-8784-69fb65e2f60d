<script setup>

</script>

<template>
    <div class="fixed z-100 top-0 left-0 bg-black opacity-50 flex h-screen w-screen">
        <div class="loader m-auto"></div>
    </div>
</template>

<style scoped>
.loader{
  @apply w-20 h-20 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-8 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

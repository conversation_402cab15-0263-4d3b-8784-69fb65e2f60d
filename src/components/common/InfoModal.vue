<script setup>
import {defineProps} from 'vue';
const props = defineProps({
  infoText: {type: String, default: ""},
  infoIcon: {type: String, default: ""},
});

</script>
<template>
  <div class="z-[9] pointer-events-none absolute top-0 left-0 w-full h-full flex items-center justify-center">
    <div class="bg-secondary rounded-md flex-col justify-center items-center inline-flex py-3 px-4">
      <div class="flex-col justify-start items-center gap-2 flex ">
        <div class="w-14 h-14 justify-center items-center inline-flex">
          <img
            :src="props.infoIcon"
          >
        </div>
        <div class="text-center text-secondaryText text-sm font-medium leading-relaxed pb-2 px-2">
           {{props.infoText}}
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</template>
<style scoped>
.fade-out {
  animation: fadeOut 0.5s ease-in-out;
}
.fade-in{
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
</style>

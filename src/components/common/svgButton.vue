<script setup>
const props = defineProps({
  primaryTheme: {type: Boolean, default: true},
  svg: {type: String},
  title: {type: String},
});
console.log(props.title);
</script>

<template>
    <button :class="primaryTheme && 'bg-bg-1000 p-2 border border-bg-750 rounded-lg flex justify-center items-center gap-2'">
        <slot name="svg"></slot>
        <span v-if="title" class="text-txt-50 text-sm font-medium text-nowrap">{{ title }}</span>
    </button>
</template>

<style scoped>

</style>

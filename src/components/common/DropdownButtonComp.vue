<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed } from 'vue';
import Button from '../../components/common/Button.vue';

const props = defineProps({
  options: {
    type: Array,
    required: true,
  },
  buttonTitle: {
    type: String,
    default: 'Create',
  },
  buttonSvg: {
    type: String,
    default: `<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 4.5V20.5M20 12.5H4" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
    </svg>`,
  },
  theme: {
    type: String,
    default: 'primary',
  },
  buttonStyle: {
    type: Object,
    default: () => ({
      size: 'default',
      variant: 'default',
    }),
  },
  position: {
    type: String,
    default: 'auto', // can be 'top', 'bottom', or 'auto'
    validator: (value) => ['top', 'bottom', 'auto'].includes(value),
  },
});

const emit = defineEmits(['select']);

const isOpen = ref(false);
const dropdownRef = ref(null);
const menuRef = ref(null);

const positionMenu = () => {
  if (!menuRef.value) {
    return;
  }

  const rect = menuRef.value.getBoundingClientRect();
  const spaceBelow = window.innerHeight - rect.bottom;
  const spaceAbove = rect.top;

  // Handle position based on prop
  if (props.position === 'top') {
    menuRef.value.style.bottom = `${rect.height-40}px`;
    menuRef.value.style.top = 'auto';
  } else if (props.position === 'bottom') {
    menuRef.value.style.top = '100%';
    menuRef.value.style.bottom = 'auto';
  } else {
    // Auto positioning logic
    if (spaceBelow < 0 && spaceAbove > Math.abs(spaceBelow)) {
      menuRef.value.style.bottom = `${rect.height}px`;
      menuRef.value.style.top = 'auto';
    } else {
      menuRef.value.style.top = '100%';
      menuRef.value.style.bottom = 'auto';
    }
  }

  // Handle horizontal positioning
  if (rect.right > window.innerWidth) {
    menuRef.value.style.right = '0';
    menuRef.value.style.left = 'auto';
  } else {
    menuRef.value.style.left = 'auto';
    menuRef.value.style.right = '0';
  }
};

const toggleDropdown = () => {
  isOpen.value = !isOpen.value;
  if (isOpen.value) {
    nextTick(() => {
      positionMenu();
    });
  }
};

const selectOption = (option) => {
  emit('select', option);
  isOpen.value = false;
  if (option.action) {
    option.action();
  }
};

const closeDropdown = (event) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target)) {
    isOpen.value = false;
  }
};

// Dynamic classes based on button style
const buttonClasses = computed(() => {
  const classes = ['flex items-center justify-center !p-0'];
  const { size, variant } = props.buttonStyle;

  // Size variations
  switch (size) {
    case 'small':
      classes.push('text-xs !px-2 !h-8');
      break;
    case 'large':
      classes.push('text-base !px-4 !h-12');
      break;
    case 'icon':
      classes.push(' !px-2 !pb-5 !pt-3 sm:!px-3 !h-10');
      break;
    default:
      classes.push('text-sm !px-2 !pb-5 !pt-3 sm:!px-3 !h-10');
  }

  // Variant variations
  switch (variant) {
    case 'minimal':
      classes.push('hover:bg-gray-100 bg-transparent');
      break;
    case 'icon':
      classes.push('!p-2 rounded-lg hover:bg-gray-100 bg-transparent');
      break;
    default:
      classes.push('w-full');
  }

  return classes.join(' ');
});

onMounted(() => {
  document.addEventListener('click', closeDropdown);
  window.addEventListener('resize', positionMenu);
});

onUnmounted(() => {
  document.removeEventListener('click', closeDropdown);
  window.removeEventListener('resize', positionMenu);
});
</script>

<template>
  <div class="relative" ref="dropdownRef">
    <Button
      :theme="theme"
      @click.stop="toggleDropdown"
      :class="buttonClasses"
      class="!p-0"
    >
      <template #svg>
        <span v-html="buttonSvg" :class="[
          buttonStyle.size === 'icon' ? 'w-auto h-auto !p-0' : 'w-4 h-4 sm:w-5 sm:h-5'
        ]"></span>
      </template>
      <span v-if="buttonTitle" :class="{ 'ml-2': buttonStyle.variant !== 'icon' }">
        {{ buttonTitle }}
      </span>
    </Button>

    <div
      v-if="isOpen"
      ref="menuRef"
      class="absolute mt-1 w-48 bg-white rounded-xl shadow-xl z-10 border border-gray-200"
    >
      <div class="py-1">
        <button
          v-for="(option, index) in options"
          :key="index"
          @click="selectOption(option)"
          class="block w-full text-left px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100"
        >
          <div class="flex items-center">
            <component v-if="option?.iconComp" :is="option.iconComp" class="mr-2 text-base w-5 h-5" />
            <span class="mr-2 text-base w-5 h-5 flex items-center" v-if="option.icon" v-html="option.icon"></span>
            {{ option.title }}
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.absolute {
  position: absolute;
}
</style>

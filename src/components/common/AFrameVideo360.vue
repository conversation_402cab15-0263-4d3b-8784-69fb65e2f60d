<script setup>
import { ref, onMounted } from 'vue';
import { cdn } from '@/helpers/index';
import InfoModal from './InfoModal.vue';
import infoSvg from '../../../public/assets/infoModal/rotate_white.svg';

defineProps({
  videoSrc: {
    type: String,
    required: true,
  },
});

const isPlaying = ref(false);
const videoEl = ref(null);
const sceneEl = ref(null);

const playVideo = () => {
  if (videoEl.value) {
    videoEl.value.play();
    isPlaying.value = true;
  }
};

onMounted(() => {
  if (videoEl.value) {
    videoEl.value.load();
    videoEl.value.pause();
  }
});
</script>

<template>
  <div
    class="video-360-container h-full w-full"
    @click="playVideo"
  >
    <a-scene
      ref="sceneEl"
      embedded
      class="h-[100vh] sample"
      loading-screen="enabled: false"
      vr-mode-ui="enabled: false"
      device-orientation-permission-ui="enabled: false"
    >
      <a-assets>
        <video
          id="video360"
          ref="videoEl"
          :src="cdn(videoSrc)"
          crossorigin="anonymous"
          loop
          playsinline
        />
      </a-assets>
      <a-sky :src="'#video360'" />
      <a-entity
        camera
        look-controls="enabled: true; reverseMouseDrag: false; mouseEnabled: true; touchEnabled: true; pointerLockEnabled: false; enableDamping: true; dampingFactor: 0.1"
        :animation="isPlaying ? 'property: rotation; to: 0 -360 0; loop: true; dur: 100000; easing: linear; pauseEvents: mousedown; resumeEvents: mouseup' : ''"
      />
    </a-scene>

    <InfoModal
      v-if="!isPlaying"
      :infoText="'Click and drag to explore the 360-degree view'"
      :infoIcon="infoSvg"
    />
  </div>
</template>

<style scoped>
.video-360-container {
  position: relative;
  overflow: hidden;
}
</style>


<script setup>
import { UserStore } from '@/store';
import { onMounted, onUnmounted, ref } from 'vue';
const parentContainerRef = ref(null);
const Store = UserStore();
defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  type: {
    type: Boolean,
    default: false,
  },

});
const emits = defineEmits(['loadData']);

const handleScroll = () => {
//   console.log("Scroll function called");
  const element = parentContainerRef.value;

  if (element) {
    const isBottom =
          element.scrollTop + element.clientHeight >= element.scrollHeight - 10;
    if (isBottom) {
    //   console.log("Reached bottom",true);
      emits('loadData', true);

    }
  }
};
onMounted(() => {
  const element = parentContainerRef.value;

  if (element) {
    element.addEventListener("scroll", handleScroll);
  }
});

onUnmounted(() => {
  const element = parentContainerRef.value;

  if (element) {
    element.removeEventListener("scroll", handleScroll);
  }
});

</script>

<template>
    <div ref="parentContainerRef" class="h-full w-full infinite_scrollbar"
    :class="type === 'card'?'grid xl:grid-cols-4 sm:grid-cols-2 lg:grid-cols-3 gap-x-3 gap-y-7 overflow-y-auto w-[95%] h-full mx-auto'
    :type==='table'?'w-full overflow-y-auto overflow-x-auto mx-auto':''">

    <slot>

    </slot>
    <div  v-if="loading && type === 'table'" :class="Store.isMobile?'!h-[75px] ':'h-full'" class="w-full skeleton-loader-row" style=" object-fit: cover;" ></div>
    <div  v-if="loading && type === 'table'" :class="Store.isMobile?'!h-[75px] ':'h-full'" class="w-full skeleton-loader-row" style=" object-fit: cover;" ></div>
    <div  v-if="loading && type === 'table'" :class="Store.isMobile?'!h-[75px] ':'h-full'" class="w-full skeleton-loader-row" style=" object-fit: cover;" ></div>
    <div  v-if="loading && type === 'card'" :class="Store.isMobile?'!h-[158px]  w-[331px]':'h-full'" class="w-full skeleton-loader" style=" object-fit: cover;" ></div>
    <div v-if="loading && type === 'card'" :class="Store.isMobile?'!h-[158px]  w-[331px]':'h-full'" class="w-full skeleton-loader" style=" object-fit: cover;" ></div>
    <div v-if="loading && type === 'card'" :class="Store.isMobile?'!h-[158px]  w-[331px]':'h-full'" class="w-full skeleton-loader" style=" object-fit: cover;" ></div>
    </div>

</template>

<style>
.infinite-scroll-wrapper {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  position: relative;
}

@media (max-width: 640px) { /* max-sm */
  .emptyCards {
    border-radius: 0.125rem; /* Rounded-sm for small screens */
  }
  /* .infinite_scrollbar::-webkit-scrollbar {
    width: 0;
    height: 0;             //Scrolbar for the card and table
} */

}
.skeleton-loader {
        height: 240px;
        border-radius: 5px;
        background-color: #5a5757;
        background: linear-gradient(
          100deg,
          rgba(255, 255, 255, 0) 40%,
          rgba(255, 255, 255, .5) 50%,
          rgba(255, 255, 255, 0) 60%
        ) #e5e7eb;
        background-size: 200% 100%;
        background-position-x: 180%;
        animation: 1s loading ease-in-out infinite;
    }
.skeleton-loader-row {
        height: 75px;
        margin-bottom: 5px;
        background-color: #cdc7c7;
        border-bottom: #5a5757;
        background: linear-gradient(
          100deg,
          rgba(255, 255, 255, 0) 40%,
          rgba(255, 255, 255, .5) 50%,
          rgba(255, 255, 255, 0) 60%
        ) #f9fafb;
        background-size: 200% 100%;
        background-position-x: 180%;
        animation: 1s loading ease-in-out infinite;
    }

    @keyframes loading {
        to {
          background-position-x: -20%;
        }
    }
</style>

<script setup>
import  { onMounted } from 'vue';
import { Field, ErrorMessage } from 'vee-validate';

const { label, type, name, required } = defineProps(['type', 'name', 'label', 'required']);

const handleSelection = (e) => {

  if (e.target.value){

    e.target.nextElementSibling.classList.remove('floatLow');
    e.target.nextElementSibling.classList.add('floatHigher');

  } else {

    e.target.nextElementSibling.classList.remove('floatHigher');
    e.target.nextElementSibling.classList.add('floatLow');

  }
};

onMounted(() => {
  if (name){

    const inputElem = document.querySelector(`[name="${name}"]`).value;

    if (inputElem.length > 0){
      document.querySelector(`[for="${name}"]`).classList.remove('floatLow');
      document.querySelector(`[for="${name}"]`).classList.add('floatHigher');
    }

  }
});

</script>

<template>

      <div class="relative">

                     <Field @input="(e) => handleSelection(e)" as="input" :type="type ? type : 'text'" :name="name" class="customField flex w-full rounded-lg h-10 transition-all duration-[0.3s] ease-in-out px-3 py-0 border-[1px] border-bg-700 focus:border-bg-default" :id="name"  />
                     <label :for="name" class="bg-white absolute cursor-text z-10 text-sm text-bg-550 font-normal transition-all duration-[0.3s] ease-in-out px-2.5 py-0 left-2.5 customLabel"> {{ label }} <strong v-if="required" >*</strong> </label>

                     <ErrorMessage :name="name"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-1 mb-2 absolute -bottom-[27px]">
                                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                        </svg>
                                                        <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>

       </div>

</template>

<style>

.customField:focus ~ .customLabel{
      @apply text-black text-xs -top-2;
}

.customLabel , .floatLow{
    @apply top-2;
}

.floatHigher{
    @apply text-xs -top-2;
}

</style>

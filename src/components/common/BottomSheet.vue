<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import Button from './Button.vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  showSearch: {
    type: Boolean,
    default: false,
  },
  searchPlaceholder: {
    type: String,
    default: '',
  },
  tabs: {
    type: Array,
    default: () => [],
  },
  options: {
    type: Object,
    default: () => ({}),
  },
  selectedTab: {
    type: String,
    default: '',
  },
  selectedValues: {
    type: Object,
    default: () => ({}),
  },
  multiSelect: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: '',
  },
});

const emit = defineEmits([
  'update:modelValue',
  'update:selectedTab',
  'update:selectedValues',
  'apply',
  'search',
  'clear',
]);

const searchQuery = ref('');
const activeTab = ref(props.selectedTab || (props.tabs[0]?.id || ''));

// Initialize selections with "All" selected for each tab
const initializeSelections = () => {
  const initialSelections = {};
  props.tabs.forEach((tab) => {
    const options = props.options[tab.id] || [];
    const allOption = options.find((opt) => {
      if (tab.id === 'users') {
        return opt.value?.first_name === 'All';
      } else if (tab.id === 'tags') {
        return opt.value?.value === 'All';
      }
      return false;
    });

    if (allOption) {
      initialSelections[tab.id] = [allOption];
    }
  });
  return initialSelections;
};

const selections = ref(Object.keys(props.selectedValues).length > 0 ? props.selectedValues : initializeSelections());

// Initialize on mount to ensure "All" is selected
onMounted(() => {
  if (Object.keys(selections.value).length === 0) {
    selections.value = initializeSelections();
  }
});

const selectionCount = computed(() => {
  const currentSelections = selections.value[activeTab.value] || [];
  if (currentSelections.some((item) =>
    item.id === 'all' ||
        item.value === 'All' ||
        item.value?.value === 'All',
  )) {
    return 0;
  }
  return currentSelections.length;
});

const currentOptions = computed(() => {
  const tabOptions = props.options[activeTab.value] || [];
  if (!searchQuery.value) {
    return tabOptions;
  }

  return tabOptions.filter((option) =>
    option.label.toLowerCase().includes(searchQuery.value.toLowerCase()),
  );
});

const dynamicPlaceholder = computed(() => {
  switch (activeTab.value) {
    case 'users':
      return 'Search by user...';
    case 'tags':
      return 'Search by tag...';
    default:
      return props.searchPlaceholder;
  }
});

watch(() => props.selectedValues, (newValues) => {
  if (Object.keys(newValues).length > 0) {
    selections.value = JSON.parse(JSON.stringify(newValues));
  } else {
    selections.value = initializeSelections();
  }
}, { deep: true });

watch(() => props.selectedTab, (newTab) => {
  if (newTab && newTab !== activeTab.value) {
    activeTab.value = newTab;
  }
});

const handleTabChange = (tabId) => {
  activeTab.value = tabId;
  emit('update:selectedTab', tabId);
};

const handleSearch = (e) => {
  searchQuery.value = e.target.value;
  emit('search', e.target.value);
};

const toggleOption = (option) => {
  const currentTabSelections = selections.value[activeTab.value] || [];
  let newSelections;

  const isAllOption = option.id === 'all' || option.value === 'All' || option.value?.value === 'All';

  if (isAllOption) {
    // If "All" is clicked, only select "All"
    newSelections = [option];
  } else if (props.multiSelect) {
    // Remove "All" when selecting other options
    const filteredSelections = currentTabSelections.filter((item) =>
      !(item.id === 'all' || item.value === 'All' || item.value?.value === 'All'),
    );

    const isSelected = filteredSelections.some((selected) => selected.id === option.id);
    newSelections = isSelected
      ? filteredSelections.filter((item) => item.id !== option.id)
      : [...filteredSelections, option];
  } else {
    newSelections = [option];
  }

  selections.value = {
    ...selections.value,
    [activeTab.value]: newSelections,
  };

  emit('update:selectedValues', selections.value);
};

const isSelected = (option) => {
  const currentSelections = selections.value[activeTab.value] || [];
  if (activeTab.value === 'users') {
    return currentSelections.some((selected) =>
      selected.id === option.id ||
            selected.value?.user_id === option.value?.user_id,
    );
  } else if (activeTab.value === 'tags') {
    return currentSelections.some((selected) =>
      selected.id === option.id ||
            selected.value?.value === option.value?.value,
    );
  }
  return false;
};
const handleApply = () => {
  emit('update:selectedValues', selections.value);
  emit('apply', selections.value);
  emit('update:modelValue', false);
};

const handleClear = () => {
  const defaultSelections = initializeSelections();
  selections.value = defaultSelections;
  emit('update:selectedValues', defaultSelections);
  emit('clear');
};
</script>
<template>
    <div v-if="modelValue" class="fixed inset-0 bg-black bg-opacity-50 z-50">
        <div class="absolute bottom-0 left-0 right-0 bg-white dark:bg-bg-150 rounded-t-2xl"
            :class="type === 'filter' ? 'h-[55vh]' : 'max-h-[55vh] min-h-max'">
            <!-- Header -->
            <div class="sticky z-10 bg-white dark:bg-bg-150 rounded-t-2xl">
                <div class="px-4 py-3 flex justify-between items-center">
                    <div class="flex items-center gap-2">
                        <h3 class="text-base font-semibold text-txt-50 dark:text-txt-950">{{ title }}</h3>
                        <!-- Selection counter badge -->
                        <span v-if="selectionCount > 0"
                            class="inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-full">
                            {{ selectionCount }}
                        </span>
                    </div>
                    <Button title="" @click="$emit('update:modelValue', false)" theme="secondary"
                        class="!p-2 !pr-0 !rounded-full">
                        <template #svg>
                            <slot name="close-icon"></slot>
                        </template>
                    </Button>
                </div>
            </div>

            <!-- Conditional rendering based on whether we have tabs -->
            <div v-if="tabs && tabs.length > 0" class=" flex h-[calc(100%-56px)]">
                <!-- Filter view content -->
                <div class="!w-24 min-w-[5rem] border-r border-gray-200 dark:border-gray-700">
                    <div v-for="tab in tabs" :key="tab.id" @click="handleTabChange(tab.id)"
                        class="flex gap-2 items-center px-2 py-3 cursor-pointer relative" :class="{
                            'before:absolute before:left-0 before:top-0 before:bottom-0 before:w-0.5 before:rounded-full before:bg-black': activeTab === tab.id
                        }">
                        <div class="w-5 h-5 flex justify-center items-center"
                            :class="activeTab === tab.id ? 'text-black' : 'text-gray-500'" v-html="tab.icon">
                        </div>
                        <span class="text-sm font-medium text-center"
                            :class="activeTab === tab.id ? 'text-black' : 'text-gray-500'">
                            {{ tab.label }}
                        </span>
                    </div>
                </div>

                <div class="flex-1 flex flex-col h-full w-[calc(100%-80px)] ">
                    <!-- Search input -->
                    <div v-if="showSearch" class="px-3 py-2 relative border-b border-gray-200 dark:border-gray-700">
                        <div class="relative flex items-center">
                            <div class="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400">
                                <slot name="search-icon"></slot>
                            </div>
                            <input type="text" :placeholder="dynamicPlaceholder" v-model="searchQuery"
                                @input="handleSearch"
                                class="w-full pl-10 pr-4 py-2 rounded-3xl bg-gray-50 dark:bg-gray-800 text-sm border border-gray-200 dark:border-gray-700 focus:outline-none focus:ring-1 focus:ring-gray-400" />
                        </div>
                    </div>

                    <div class="flex-1 overflow-y-auto" style="height: calc(100% - 140px)">
                        <div class="px-2">
                            <div v-for="option in currentOptions" :key="option.id" @click="toggleOption(option)"
                                class="flex items-center justify-between py-3 px-2 cursor-pointer">
                                <span class="text-sm text-gray-900 dark:text-gray-100">{{ option.label }}</span>
                                <div class="w-5 h-5 rounded-lg border-2 flex items-center justify-center"
                                    :class="isSelected(option) ? 'bg-black dark:bg-white border-black dark:border-white' : 'border-gray-300 dark:border-gray-600'">
                                    <svg v-if="isSelected(option)" width="20" height="20" viewBox="0 0 20 20"
                                        fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M8.7841 15C9.03366 15 9.23099 14.8888 9.37028 14.6723L14.8549 5.96548C14.9594 5.79579 15 5.66706 15 5.53248C15 5.21065 14.7911 5 14.4719 5C14.2397 5 14.112 5.07607 13.9727 5.29842L8.76088 13.6717L6.0563 10.1024C5.9112 9.8976 5.76611 9.81568 5.55717 9.81568C5.22635 9.81568 5 10.0439 5 10.3657C5 10.5003 5.05804 10.6524 5.16831 10.7929L8.1805 14.6606C8.35461 14.8888 8.53453 15 8.7841 15Z"
                                            fill="white" />
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Apply/Clear buttons -->
                    <div
                        class="bottom-0 left-0 right-0 p-4 bg-white dark:bg-bg-150 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex gap-3">
                            <button @click="handleClear"
                                class="flex-1  w-[80px] h-[32px] justify-center items-center bg-white text-black dark:bg-bg-150 dark:text-white rounded-lg text-sm font-medium border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                                Clear
                            </button>
                            <button @click="handleApply"
                                class="flex-1  w-[146px] h-[32px] justify-center items-center bg-black dark:bg-white text-white dark:text-black rounded-lg text-sm font-medium">
                                Apply
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Participants view content -->
            <div v-else class="h-[calc(100%-56px)] overflow-y-auto p-3.5 border-t border-gray-700 dark:border-gray-700">
                <slot name="content"></slot>
            </div>
        </div>
    </div>
</template>

<style scoped>
.overflow-y-auto {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.overflow-y-auto::-webkit-scrollbar {
    display: none;
}

@keyframes slideUp {
    from {
        transform: translateY(100%);
    }

    to {
        transform: translateY(0);
    }
}

.bottom-0 {
    animation: slideUp 0.2s ease-out;
}

input::placeholder {
    text-align: left;
}
</style>

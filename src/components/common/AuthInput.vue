<script setup>
import { ref, computed } from 'vue';
import {  ErrorMessage, useField } from 'vee-validate';

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String,
    default: 'text',
  },
  placeholder: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    required: true,
  },
  hasError: {
    type: Boolean,
    default: false,
  },
  variant: {
    type: String,
    default: 'signup', // 'signup' or 'login'
    validator: (value) => ['signup', 'login'].includes(value),
  },
  modelValue: {
    type: String,
    default: '',
  },
  value: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const { value: fieldValue } = useField(props.name, undefined, {
  initialValue: computed(() => props.value || props.modelValue || ''),
});

const showPassword = ref(false);

const getInputFieldClasses = (hasError) => {
  const baseClasses = "px-4 py-3 bg-gray-50 rounded-lg border justify-start items-center gap-2.5 inline-flex";
  return hasError
    ? `${baseClasses} !border-2 !border-red-300 text-[#c81e1e] text-sm bg-red-50`
    : `${baseClasses} border-gray-300 text-txt-default dark:text-txt-1000`;

};

const getLabelClasses = (variant) => {
  if (variant === 'signup') {
    return "self-stretch text-lg text-gray-500 font-normal font-sans leading-[21px]";
  }
  return "self-stretch text-[#111928] text-sm font-medium font-sans leading-[21px]";
};

</script>

<template>
  <div class="w-full flex flex-col justify-start items-start gap-2">
    <div :class="getLabelClasses(variant)">
      {{ label }}
    </div>

    <div
      class="self-stretch"
      :class="getInputFieldClasses(hasError)">
      <div class="h-[18px] w-full justify-start items-center gap-2.5 flex">
        <input
          :type="type === 'password' ? (showPassword ? 'text' : 'password') : type"
          :id="name"
          :name="name"
          class="w-full bg-transparent text-sm font-sans text-start placeholder:text-left placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal placeholder:font-sans text-txt-default dark:text-txt-1000"
          :placeholder="placeholder"
          v-model="fieldValue"
          @input="emit('update:modelValue', $event.target.value)"
        />

        <!-- Password visibility toggle -->
        <div v-if="type === 'password'" class="flex justify-center items-center cursor-pointer">
          <!-- Eye Open Icon -->
          <svg v-if="showPassword" @click="showPassword = !showPassword"
            class="w-6 h-6 fill-white cursor-pointer" viewBox="0 0 24 24" fill="none"
            xmlns="http://www.w3.org/2000/svg">
            <path :fill="hasError ? '#c81e1e' : '#5B616E'"
              d="M23.1853 11.6963C23.1525 11.6222 22.3584 9.86062 20.5931 8.09531C18.2409 5.74312 15.27 4.5 12 4.5C8.72999 4.5 5.75905 5.74312 3.40687 8.09531C1.64155 9.86062 0.843741 11.625 0.814679 11.6963C0.772035 11.7922 0.75 11.896 0.75 12.0009C0.75 12.1059 0.772035 12.2097 0.814679 12.3056C0.847491 12.3797 1.64155 14.1403 3.40687 15.9056C5.75905 18.2569 8.72999 19.5 12 19.5C15.27 19.5 18.2409 18.2569 20.5931 15.9056C22.3584 14.1403 23.1525 12.3797 23.1853 12.3056C23.2279 12.2097 23.25 12.1059 23.25 12.0009C23.25 11.896 23.2279 11.7922 23.1853 11.6963ZM12 18C9.11437 18 6.59343 16.9509 4.50655 14.8828C3.65028 14.0313 2.92179 13.0603 2.34374 12C2.92164 10.9396 3.65014 9.9686 4.50655 9.11719C6.59343 7.04906 9.11437 6 12 6C14.8856 6 17.4066 7.04906 19.4934 9.11719C20.3514 9.9684 21.0815 10.9394 21.6609 12C20.985 13.2619 18.0403 18 12 18ZM12 7.5C11.11 7.5 10.2399 7.76392 9.49993 8.25839C8.7599 8.75285 8.18313 9.45566 7.84253 10.2779C7.50194 11.1002 7.41282 12.005 7.58646 12.8779C7.76009 13.7508 8.18867 14.5526 8.81801 15.182C9.44735 15.8113 10.2492 16.2399 11.1221 16.4135C11.995 16.5872 12.8998 16.4981 13.7221 16.1575C14.5443 15.8169 15.2471 15.2401 15.7416 14.5001C16.2361 13.76 16.5 12.89 16.5 12C16.4988 10.8069 16.0242 9.66303 15.1806 8.81939C14.337 7.97575 13.1931 7.50124 12 7.5ZM12 15C11.4066 15 10.8266 14.8241 10.3333 14.4944C9.83993 14.1648 9.45542 13.6962 9.22835 13.148C9.00129 12.5999 8.94188 11.9967 9.05764 11.4147C9.17339 10.8328 9.45911 10.2982 9.87867 9.87868C10.2982 9.45912 10.8328 9.1734 11.4147 9.05764C11.9967 8.94189 12.5999 9.0013 13.148 9.22836C13.6962 9.45542 14.1648 9.83994 14.4944 10.3333C14.824 10.8266 15 11.4067 15 12C15 12.7956 14.6839 13.5587 14.1213 14.1213C13.5587 14.6839 12.7956 15 12 15Z" />
          </svg>

          <!-- Eye Closed Icon -->
          <svg v-else @click="showPassword = !showPassword" class="w-6 h-6 fill-white cursor-pointer"
            viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path :fill="hasError ? '#c81e1e' : '#5B616E'"
              d="M21.375 16.4063C21.2894 16.4551 21.1949 16.4866 21.0971 16.4989C20.9992 16.5112 20.8999 16.5041 20.8048 16.478C20.7097 16.4518 20.6207 16.4072 20.5429 16.3467C20.4651 16.2861 20.3999 16.2108 20.3513 16.125L18.57 13.0125C17.5344 13.7127 16.3921 14.2402 15.1875 14.5744L15.7378 17.8763C15.7541 17.9735 15.751 18.0729 15.7288 18.1689C15.7065 18.2649 15.6656 18.3556 15.6083 18.4358C15.551 18.516 15.4785 18.5841 15.3949 18.6363C15.3113 18.6884 15.2182 18.7236 15.121 18.7397C15.081 18.7462 15.0405 18.7497 15 18.75C14.8226 18.7498 14.651 18.6866 14.5157 18.5718C14.3805 18.4569 14.2903 18.2979 14.2613 18.1228L13.7203 14.881C12.5796 15.0397 11.4223 15.0397 10.2816 14.881L9.74064 18.1228C9.71153 18.2982 9.62109 18.4575 9.48545 18.5724C9.3498 18.6872 9.17776 18.7502 9.00001 18.75C8.95856 18.7498 8.91718 18.7464 8.87626 18.7397C8.77904 18.7236 8.68596 18.6884 8.60233 18.6363C8.5187 18.5841 8.44617 18.516 8.38888 18.4358C8.33159 18.3556 8.29067 18.2649 8.26845 18.1689C8.24623 18.0729 8.24315 17.9735 8.25939 17.8763L8.81251 14.5744C7.60842 14.2391 6.4667 13.7107 5.43189 13.0097L3.65626 16.125C3.55681 16.2983 3.39258 16.425 3.19971 16.4772C3.00684 16.5294 2.80113 16.5029 2.62783 16.4035C2.45452 16.304 2.32783 16.1398 2.27561 15.9469C2.22339 15.754 2.24993 15.5483 2.34939 15.375L4.22439 12.0938C3.56579 11.5248 2.96019 10.8972 2.41501 10.2188C2.34702 10.1429 2.29522 10.0539 2.26276 9.95729C2.2303 9.86069 2.21787 9.75849 2.22622 9.65693C2.23457 9.55536 2.26353 9.45657 2.31133 9.36657C2.35914 9.27657 2.42478 9.19726 2.50426 9.13347C2.58373 9.06969 2.67537 9.02277 2.77359 8.99558C2.8718 8.96839 2.97452 8.9615 3.07548 8.97533C3.17645 8.98916 3.27353 9.02342 3.36082 9.07602C3.4481 9.12862 3.52374 9.19846 3.58314 9.28127C5.13939 11.2069 7.86189 13.5 12 13.5C16.1381 13.5 18.8606 11.2041 20.4169 9.28127C20.4756 9.19676 20.5511 9.12525 20.6386 9.07117C20.7262 9.01708 20.8239 8.9816 20.9257 8.96691C21.0276 8.95222 21.1314 8.95865 21.2306 8.98579C21.3299 9.01293 21.4225 9.06021 21.5027 9.12468C21.5829 9.18915 21.649 9.26943 21.6968 9.36054C21.7447 9.45164 21.7732 9.55161 21.7808 9.65423C21.7883 9.75686 21.7747 9.85993 21.7407 9.95706C21.7067 10.0542 21.653 10.1433 21.5831 10.2188C21.038 10.8972 20.4324 11.5248 19.7738 12.0938L21.6488 15.375C21.6991 15.4606 21.732 15.5552 21.7454 15.6535C21.7589 15.7519 21.7527 15.8519 21.7273 15.9478C21.7018 16.0437 21.6576 16.1337 21.5971 16.2123C21.5366 16.291 21.4611 16.357 21.375 16.4063Z" />
          </svg>
        </div>

        <slot name="suffix"></slot>
      </div>
    </div>

    <!-- Error Message -->
    <ErrorMessage :name="name" as="div" v-slot="{ message }">
      <div v-if="message" class="flex items-center mt-1">
        <i class="fa fa-exclamation-circle mr-1 text-red-600" aria-hidden="true"></i>
        <p class="text-sm font-normal text-red-600 font-sans">{{ message }}</p>
      </div>
    </ErrorMessage>
  </div>
</template>

<script setup>
import { ref } from 'vue';
const emit = defineEmits(['closeModal', 'addApi']);

const name = ref('');
const handleCloseModal = () => {
  emit('closeModal');
};
const HandleAddApiKey = () => {
  emit('addApi', name.value);
  emit('closeModal');
};
</script>

<template>
    <div
        class="relative transform overflow-hidden rounded-t-2xl rounded-b-none sm:rounded-t-md sm:rounded-b-md bg-neutral-800 text-left shadow-xl transition-all sm:my-8 w-full sm:max-w-sm">
        <div class="flex justify-center items-center pt-2 sm:hidden"><div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div></div>
        <div class="p-3 sm:p-6 ">
        <div class="mb-2">
            <h1 class="text-base text-white font-semibold">Add API Key</h1>
            <p class="text-sm text-white">Lorem ipsum dolor sit amet elit.</p>
        </div>
        <form @submit.prevent="register" class="w-full mb-1">
            <div class="">
                <div class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                    <label class="text-[#F5F5F5] text-xs font-semibold mb-2 ml-1">
                        Name
                    </label>
                    <input v-model="name" style="border: 1px solid #737373;" type="text" id="Username" name="Username"
                        class="w-full h-11 p-2 rounded-lg justify-start items-center inline-flex text-white bg-inherit placeholder:text-left placeholder:text-[#ffffffba] placeholder:text-xs"
                        placeholder="Name" />
                </div>

                <div class="text-center mt-3 flex justify-center items-center">
                    <button @click="handleCloseModal()"
                        class="w-1/2 sm:w-fit h-11 sm:h-10 rounded-full sm:rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent m-0 px-3 text-xs text-white font-semibold leading-6">Discard</button>
                    <button @click="HandleAddApiKey()" id="submit" type="submit"
                        class="ml-3 w-1/2 sm:w-fit h-11 sm:h-10 rounded-full  sm:rounded bg-[#36f] hover:bg-[#4572fc] border-0 m-0 px-3 text-xs text-white font-semibold leading-6">
                        Create API Key
                    </button>
                </div>
            </div>
        </form>
    </div>
    </div>
</template>

<style></style>

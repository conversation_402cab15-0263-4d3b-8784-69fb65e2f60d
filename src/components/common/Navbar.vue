
<script setup>
import DropDown from './DropDown.vue';
import SearchBox from './SearchBox.vue';
import { deleteCookie, getCookie, setCookie } from '../../helpers/domhelper';
import { getAuth, signOut } from 'firebase/auth';
import { UserStore } from '../../store/index';
import { ListProjectsFromOrganization } from '@/api/organization';
import { ref, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { isMasterScenePath } from '@/helpers/helpers';

const router = useRouter();
const route = useRoute();
const preview_domain = import.meta.env.VITE_PREVIEW_DOMAIN;
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const sceneId = ref(route.params.scene_id);

import ProfileCard from './ProfileCard.vue';

import { Org_Store } from '@/store/organization';
const userStore = UserStore();
defineProps({
  notifications: Number,
});

const selectedOrg = ref(null);
const options = ref(null);
const opt=ref(null);
const user_email = ref(null);
const filteredOrganizations = ref(null);
const NavBar = ref(false);
const showLogo = ref(false);
const store = Org_Store();
opt.value=[
  {
    'name': 'log out',
  },
];

const user_id = ref(null);
const user_first_name = ref(null);
const user_last_name = ref(null);
const user_phone_no = ref(null);
const user_picture = ref(null);
const user_role = ref(null);
const projects = ref(null);

const getListOfProjects = () => {
  ListProjectsFromOrganization().then((projects_list) => {
    const projectEntries = Object.values(projects_list || {});
    // Extract project names or structure options as needed
    projects.value = projectEntries.map((project) => ({
      name: project.name,
      value: project._id,
    }));
    console.log(projects.value); // Check the options format
  });
};

//  the watcher runs on initialization

watch(() => userStore.user_data, (newData) => {
  if (newData) {
    user_email.value = newData.email;
    user_phone_no.value = newData.phone;
    user_picture.value = newData.profilePicture;
    user_role.value = newData.role;
    user_id.value = newData._id;
    user_first_name.value = newData.first_name;
    user_last_name.value = newData.last_name;
    user_first_name.value = userStore.user_data.first_name;
    user_last_name.value = userStore.user_data.last_name;

    if (userStore.user_data && userStore.user_data._id && !userStore.fcmToken){
      if (getCookie('pushToken')){
        const payload = {
          _id: userStore.user_data._id,
          fcmToken: getCookie('pushToken'),
          action: "add",
        };
        userStore.UpdateFcmToken(payload);
      }
    }
  }
} );
const findObjectUsingId = (id, array) => {
  let newObject = null;
  if (array.length > 0){
    array.forEach((item) => {
      if (item._id === id){
        newObject = item;
      }
    });
  }
  return newObject;
};

const handleSignOut = async () => {
  if (getCookie('pushToken')){
    console.log(getCookie('pushToken'));
    const payload = {
      _id: userStore.user_data._id,
      fcmToken: getCookie('pushToken'),
      action: "remove",
    };
    console.log(payload);
    await userStore.UpdateFcmToken(payload);
  }
  const auth = getAuth();
  signOut(auth).then(() => {
    deleteCookie('refreshToken');
    deleteCookie('refreshToken');
    deleteCookie('accessToken');
    deleteCookie('pushToken');
    window.location = '/login';
  });

};

const handleSelectionOrganization = (item)  => {
  setCookie('organization', item._id);
  const firstPath = '/'+new URL(window.location.href).pathname.split('/')[1];
  window.location = firstPath;
};

const handleSelectionProject = (item) => {
  const cookieName = 'projects';
  const existing = document.cookie
    .split('; ')
    .find((row) => row.startsWith(`${cookieName}=`));

  let projects = [];
  if (existing) {
    try {
      projects = JSON.parse(decodeURIComponent(existing.split('=')[1]));
    } catch (e) {
      console.error('Invalid projects cookie:', e);
    }
  }

  if (!projects.includes(item.value)) {
    projects.unshift(item.value);
  }

  projects = projects.slice(0, 5);
  document.cookie = `${cookieName}=${encodeURIComponent(JSON.stringify(projects))}; path=/`;
  router.push(`/projects/${item.value}`);
};

watch(() => userStore.user_data, () => {
  if (userStore.user_data) {
    store.FetchNewNotifications(userStore.user_data._id, false);
    user_email.value = userStore.user_data.email;
    const newArray = JSON.stringify(userStore.user_data.organization_id);
    options.value = JSON.parse(newArray);
    selectedOrg.value = findObjectUsingId(getCookie('organization'), JSON.parse(newArray));
    filteredOrganizations.value = userStore.user_data.organization_id.filter((item) => item._id === selectedOrg.value._id);
    if (filteredOrganizations.value){
      showLogo.value = true;
    }
  }
});

// Watch(() => userStore.user_role, (val) => {
//   If (userStore?.user_role) {
//     Const currentPath = router.currentRoute.value.fullPath.toLowerCase().replace(/^\/|\/$/g, '');
//     Const matchedMenuItem = menuListTop.find(item => {
//       Const menuItemPath = item.route.toLowerCase().replace(/^\/|\/$/g, '');
//       Return currentPath.startsWith(menuItemPath);
//     });

//     If (matchedMenuItem && !matchedMenuItem.roles.includes(userStore.user_role.userrole.role)) {
//       Const fallbackRoute = menuListTop.find(item => item.roles.includes(userStore.user_role.userrole.role));
//       If (fallbackRoute) {
//         Router.push(fallbackRoute.route);
//       } else {
//         Router.push('/schedules');
//       }
//     }
//   }
// });

if (userStore.user_data && userStore.user_data._id && !userStore.fcmToken){
  if (getCookie('pushToken')){
    const payload = {
      _id: userStore.user_data._id,
      fcmToken: getCookie('pushToken'),
      action: "add",
    };
    userStore.UpdateFcmToken(payload);
  }
}

onMounted (() => {
  console.log("before getauth");
  getListOfProjects();
  userStore.GetAuth().then(() => {
    filteredOrganizations.value = userStore.user_data.organization_id.filter((item) => item._id === selectedOrg.value._id);
    if (filteredOrganizations.value){
      showLogo.value = true;
    }
  });
});

</script>

<template>
  <div
    class="w-full h-[65px] bg-gray-100 dark:bg-bg-default text-white z-50">
    <div class="w-full flex justify-between h-full items-center">
      <div class="flex h-full items-center px-3 gap-3 relative ">
        <router-link to="/projects" class="h-full flex justify-center items-center"><svg class="fill-black" width="31" height="18" viewBox="0 0 31 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M13.7438 17.0324C13.2952 17.4964 12.7215 17.5895 12.2009 17.624C11.1324 17.6947 10.0481 17.7269 8.98463 17.625C7.55984 17.4885 6.16456 17.1941 4.8973 16.4312C2.56698 15.0281 1.17005 13.034 0.693293 10.3387C0.431842 8.86071 0.549419 7.4402 1.05984 6.0719C2.0377 3.45036 3.92588 1.71815 6.54958 0.782845C7.52061 0.436683 8.5253 0.306157 9.56078 0.310403C13.5981 0.326971 17.6356 0.30913 21.673 0.319564C25.2524 0.328812 27.902 1.97394 29.5227 5.10984C31.1167 8.19383 30.6373 12.1795 28.0858 14.7466C26.5179 16.3241 24.7135 17.2718 22.4729 17.524C21.097 17.6789 19.7335 17.5523 18.3693 17.6533C17.9321 17.6856 17.8753 17.4993 17.8795 17.1291C17.8963 15.6472 17.8536 14.164 17.9019 12.6835C17.9225 12.0574 17.7918 11.5954 17.1915 11.357C16.8206 11.2097 16.585 10.6173 16.2067 10.7797C15.604 11.0383 14.9752 11.4114 14.5794 11.9116C14.3404 12.2137 14.5152 12.8476 14.5139 13.3325C14.5106 14.5784 14.5128 14.5784 13.3039 14.5784C12.2126 14.5784 12.2126 14.5784 12.2125 13.5214C12.2125 12.7038 12.2281 11.8856 12.2047 11.0686C12.1958 10.7556 12.2886 10.5309 12.5326 10.3534C13.655 9.53674 14.7736 8.71456 15.903 7.90751C16.0825 7.77925 16.2388 7.68195 16.5229 7.89695C17.7071 8.79291 18.9391 9.62583 20.1546 10.4803C20.3825 10.6404 20.34 10.8757 20.34 11.1013C20.3399 12.3023 20.3355 13.5033 20.3444 14.7043C20.3459 14.9018 20.2239 15.1675 20.6205 15.1976C22.7907 15.3626 24.7909 14.9347 26.2976 13.2728C28.6041 10.7288 28.649 7.22907 26.316 4.72081C25.3151 3.64477 24.0316 2.90664 22.4955 2.77043C18.6284 2.4275 14.753 2.71549 10.8828 2.61434C9.29511 2.57285 7.72212 2.63066 6.29487 3.42307C2.86499 5.32725 1.95432 9.43903 4.0305 12.3898C5.26731 14.1476 7.08409 15.2467 9.39821 15.1978C10.9052 15.166 12.4143 15.2237 13.9206 15.1776C14.6191 15.1563 14.6468 15.4896 14.4304 15.947C14.2544 16.319 14.1545 16.751 13.7438 17.0324Z"/>
</svg>
</router-link>
        <div></div>
        <DropDown
            :selectedOption="selectedOrg"
            :options="options"
            @handleSelect="handleSelectionOrganization"
            value="name"
            />

            <a v-if="sceneId && isMasterScene"  :style="{ pointerEvents: sceneId ? 'auto' : 'none' }"
  :href="sceneId && isMasterScene
             ? preview_domain + `/${getCookie('organization')}/masterscene/${sceneId}`
              : '#'
          "
            target="_blank" type="button"
  class="w-fit flex justify-center min-w-10 items-center h-[37px] rounded-md bg-gray-200 cursor-pointer"><svg class="fill-gray-500 w-4 h-4" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.3842 6.86054L4.88161 2.18183C4.6909 2.06364 4.47446 2.00095 4.25393 2.00001C4.0334 1.99907 3.8165 2.05993 3.62492 2.1765C3.43463 2.29049 3.27636 2.45669 3.16649 2.65789C3.05662 2.85909 2.99914 3.08797 3.00001 3.32084V12.6783C2.99914 12.9111 3.05662 13.14 3.16649 13.3412C3.27636 13.5424 3.43463 13.7086 3.62492 13.8226C3.81643 13.9397 4.0335 14.0009 4.25425 14C4.47499 13.9991 4.6916 13.936 4.88223 13.8173L12.3855 9.13854C12.5724 9.02274 12.7276 8.85657 12.8354 8.65669C12.9432 8.4568 13 8.23019 13 7.99954C13 7.76889 12.9432 7.54229 12.8354 7.3424C12.7276 7.14251 12.5724 6.97635 12.3855 6.86054H12.3842Z"/>
</svg>
</a>
<div class="right-6 sm:right-auto sm:relative fixed flex gap-6 ">
  <SearchBox
  :options="projects"
  @handleSelect="handleSelectionProject"
/>
      <div class="flex sm:hidden items-center justify-center z-50 right-0" >
        <ProfileCard
        :userid="user_id"
        :firstName="user_first_name"
        :lastName="user_last_name"
        :email="user_email"
        :phone="user_phone_no"
        :profilePicture="user_picture"
        designation="Sales Executive"
        :role="user_role"
        @sign-out="handleSignOut"
        @edit-profile="handleEditProfile" />

      </div>
    </div>
      </div>
      <!-- <div class="flex items-center md:hidden w-fit h-10 hover:cursor-pointer" @click="()=>NavBar=!NavBar">
        <svg v-if="NavBar" xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
          <rect width="36" height="36" rx="8" fill="black"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M17.5028 21.3647C13.9953 21.3647 10.9998 21.8962 10.9998 24.0188C10.9998 26.1402 13.9768 26.687 17.5028 26.687C21.0102 26.687 24.0047 26.161 24.0047 24.0373C24.0047 21.9137 21.0287 21.3647 17.5028 21.3647Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M17.5029 18.3354C19.8044 18.3354 21.6705 16.4704 21.6705 14.1678C21.6705 11.8662 19.8044 10.0001 17.5029 10.0001C15.2014 10.0001 13.3352 11.8662 13.3352 14.1678C13.3265 16.4617 15.1785 18.3278 17.4723 18.3354H17.5029Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <svg v-if="!NavBar" xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 36 36" fill="none">
          <rect width="36" height="36" rx="8" fill="white"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M17.5028 21.3647C13.9953 21.3647 10.9998 21.8962 10.9998 24.0188C10.9998 26.1402 13.9768 26.687 17.5028 26.687C21.0102 26.687 24.0047 26.161 24.0047 24.0373C24.0047 21.9137 21.0287 21.3647 17.5028 21.3647Z" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
          <path fill-rule="evenodd" clip-rule="evenodd" d="M17.5029 18.3354C19.8044 18.3354 21.6705 16.4704 21.6705 14.1678C21.6705 11.8662 19.8044 10.0001 17.5029 10.0001C15.2014 10.0001 13.3352 11.8662 13.3352 14.1678C13.3265 16.4617 15.1785 18.3278 17.4723 18.3354H17.5029Z" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div> -->
    </div>
  </div>
    <!-- <div v-if="NavBar" class="fixed right-0 h-screen w-full z-10 bg-opacity-50 mt-[3.8rem]" @click="(e)=>{
      e.stopPropagation();
      NavBar=!NavBar;
    }">
    </div> -->
    <transition name="slide-fade">
      <div v-if="NavBar" class="md:hidden fixed h-screen w-full flex flex-col z-20 bg-gray-50 top-0 mt-[3.8rem]" :class="NavBar ? 'right-0' : '-right-[280px]'">
        <!-- <div class="px-3 pb-3 flex items-center" @click="()=>NavBar=!NavBar">
                    <button class="text-gray-500 hover:text-gray-700">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
        </div> -->
        <!-- <hr> -->
        <div class="px-3 flex flex-col w-full h-full pt-3 gap-5">
          <div class="flex text-[#0F0F0F] text-xl font-medium items-center gap-3">
            <span @click="NavBar=!NavBar" class="cursor-pointer">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M5 12.0054C5 12.2216 5.07447 12.4054 5.23404 12.5568L13.7447 20.7838C13.883 20.9243 14.0638 21 14.266 21C14.6809 21 15 20.6865 15 20.2541C15 20.0486 14.9149 19.8649 14.7979 19.7351L6.79787 12.0054L14.7979 4.27568C14.9149 4.13514 15 3.95135 15 3.74595C15 3.32432 14.6809 3 14.266 3C14.0638 3 13.883 3.07568 13.7447 3.21622L5.23404 11.4541C5.07447 11.6054 5 11.7892 5 12.0054Z" fill="black"/>
              </svg>
            </span>
            <span>Accounts</span>
          </div>
          <div class="flex justify-between items-end">
            <div class="flex flex-col gap-2">
              <span class="text-[#5B616E] text-xs font-normal">User</span>
              <span class="text-[#0F0F0F] text-base font-normal">{{ user_email }}</span>
            </div>
            <div @click="handleLogout" class="flex items-center justify-center border border-[#E6E6E6] text-nowrap rounded-lg text-center hover:cursor-pointer text-black text-sm font-medium px-3 h-8">
              Log Out
            </div>
          </div>

        </div>
      </div>
    </transition>
</template>

<style>
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease-out;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(280px);
  opacity: 0;
}
</style>

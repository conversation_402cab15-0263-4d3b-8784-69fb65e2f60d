<script setup>
import { onMounted } from 'vue';
import { OrganizationStore } from '../../store/archiveprojects';
import { ChartBarIcon } from '@heroicons/vue/24/outline';
import { ref } from 'vue';
import { uiOperations } from '../../store/uiOperations';
import Datepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';
import { getCookie } from '../../helpers/domhelper';
import { GetAnalytics } from '../../api/sessions/index.ts';
import { UserStore } from '../../store';

const organizationStore = OrganizationStore();
const uiStore = uiOperations();
const userStore = UserStore();
const dateRange = ref(null);
const sessionAnalytics = ref();
const requestParmsDTO = {
  userId: null, // 0
  projectId: null, // 1
  startDate: null, // 2
  endDate: null, // 3
  type: null, // 4
  source: null, // 5
};
const parmsPropertyNamesList = Object.keys(requestParmsDTO);

/* Methods */

const convertMinutesToHoursAndMinutes = (mins) => {
  const hours = Math.floor(mins / 60);
  const minutes = Math.round(mins % 60);
  return `${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`;
};

const GetAnalyticsSessionApiParms = (values) => {
  console.log('if');
  console.log(values);
  uiStore.handleLoader(true);
  const organizationId = getCookie('organization');

  if (organizationId) {

    GetAnalytics(values).then((res) => {
      sessionAnalytics.value = res.data;
      uiStore.handleLoader(false);
    }).catch((error) => {
      uiStore.handleLoader(false);
      if (userStore.verifyAuth()) {
        // Router.push('/login');
      } else {
        if (error.message) {
          uiStore.handleApiErrorMessage(error.message);
        } else if (error.error)  {
          uiStore.handleApiErrorMessage(error.error);
        }
      }
    });

  }

};

const handleChange = (e) => {

  switch (e.target.name) {
    case 'userId': {
      console.log('userId');
      requestParmsDTO.userId = e.target.value;
      GetAnalyticsSessionApiParms(requestParmsDTO);
      break;
    }
    case 'projectId': {
      requestParmsDTO.projectId = e.target.value;
      GetAnalyticsSessionApiParms(requestParmsDTO);
      break;
    }
    case 'startDate':
      break;
    case 'endDate':
      break;
    case 'type': {
      requestParmsDTO.type = e.target.value;
      GetAnalyticsSessionApiParms(requestParmsDTO);
      break;
    }
    case 'source': {
      requestParmsDTO.source = e.target.value;
      GetAnalyticsSessionApiParms(requestParmsDTO);
      break;
    }
    default:
      break;
  }
};

const handleDateRangeChange = (values) => {
  console.log(values);
  if (values) {
    if (values.length > 0) {
      const [startDate, endDate] = values; // Destruct Start & End
      if (startDate && endDate) {
        // Both
        requestParmsDTO.startDate = new Date(startDate).toISOString();
        requestParmsDTO.endDate = new Date(endDate).toISOString();
      } else if (startDate) {
        // Start Date
        requestParmsDTO.startDate = new Date(startDate).toISOString();
      } else if (endDate) {
        // End Date
        requestParmsDTO.endDate = new Date(endDate).toISOString();
      }

    }
  } else {
    requestParmsDTO.startDate = null;
    requestParmsDTO.endDate = null;
  }
  GetAnalyticsSessionApiParms(requestParmsDTO);
};

/* Hooks */
onMounted(() => {
  // Users
  if (!organizationStore.users || organizationStore.users.length === 0) {
    organizationStore.GetUsers();
  }
  // Projects
  if (!organizationStore.projects || organizationStore.projects.length === 0) {
    organizationStore.GetProjects();
  }

  GetAnalyticsSessionApiParms(); // Initial Call
});

</script>

<template>
  <div class="">
    <div class="p-4">
      <div class="sm:flex sm:items-center sm:justify-between mb-5">
        <div class="min-w-0 flex-1">
          <h2 class="text-2xl font-bold -tracking-2 leading-7 text-white sm:truncate sm:text-3xl sm:tracking-tight">
            Analytics </h2>

        </div>

      </div>

      <div class="h-[70vh] overflow-y-auto">

        <div class="border-b-[1px] border-zinc-500 mb-4 pb-4">
          <h3 class="text-white font-medium text-xl mb-4"> Filters </h3>
          <div class="grid md:grid-cols-2 xl:grid-cols-3 gap-10">
            <div class="flex flex-col justify-start items-start gap-1">
              <label :for="parmsPropertyNamesList[0]" class="text-[#F5F5F5] text-xs font-semibold ml-1"> Users </label>
              <select :name="parmsPropertyNamesList[0]" :id="parmsPropertyNamesList[0]"
                style="border: 1px solid #737373"
                class="w-full h-11 p-2 text-white bg-neutral-800 rounded-lg justify-start items-center inline-flex
                                     bg-inherit placeholder:text-left placeholder:text-[#F5F5F5] placeholder:text-xs focus:outline-none"
                @change="handleChange">
                <option selected disabled>Select an Option</option>
                <option disabled class="p-4" v-if="organizationStore.users === null
                || organizationStore.users.length < 0"> No data Found ! </option>
                <option :value="users.user_id" class="p-4 " v-else v-for="users, index in organizationStore.users"
                  :key="index">
                  {{ users.email ? users.email : 'nan' }}</option>
              </select>
            </div>

            <div class="flex flex-col justify-start items-start gap-1">
              <label :for="parmsPropertyNamesList[1]" class="text-[#F5F5F5] text-xs font-semibold ml-1">
                Projects </label>
              <select :name="parmsPropertyNamesList[1]" :id="parmsPropertyNamesList[1]"
                style="border: 1px solid #737373" class="w-full h-11 p-2 text-white bg-neutral-800 rounded-lg justify-start items-center
                                     inline-flex bg-inherit placeholder:text-left placeholder:text-[#F5F5F5] placeholder:text-xs
                                      focus:outline-none" @change="handleChange">
                <option selected disabled>Select an Option</option>
                <option disabled class="p-4"
                  v-if="organizationStore.projects === null || organizationStore.projects.length < 0"> No data Found !
                </option>
                <option :value="project._id" class="p-4 " v-else v-for="project, index in organizationStore.projects"
                  :key="index">
                  {{ project.name ? project.name : 'nan' }}</option>
              </select>
            </div>

            <div class="flex flex-col justify-start items-start gap-1">
              <label for="dateRange" class="text-[#F5F5F5] text-xs font-semibold ml-1"> dateRange </label>
              <Datepicker :showLabelHigh="true" v-model="dateRange" :enable-time-picker="false" input-class-name="dp-custom-input" range
                multi-calendars dark @update:model-value="handleDateRangeChange" /> <!-- :min-date="new Date()" -->

            </div>

            <div class="flex flex-col justify-start items-start gap-1">
              <label :for="parmsPropertyNamesList[4]" class="text-[#F5F5F5] text-xs font-semibold ml-1">
                Type </label>
              <select :name="parmsPropertyNamesList[4]" :id="parmsPropertyNamesList[4]"
                style="border: 1px solid #737373"
                class="w-full h-11 p-2 text-white bg-neutral-800 rounded-lg justify-start items-center inline-flex
                                     bg-inherit placeholder:text-left placeholder:text-[#F5F5F5] placeholder:text-xs focus:outline-none"
                @change="handleChange">
                <option selected disabled>Select an Option</option>
                <option value="default" class="p-4 "> default </option>
              </select>
            </div>

            <div class="flex flex-col justify-start items-start gap-1">
              <label :for="parmsPropertyNamesList[5]" class="text-[#F5F5F5] text-xs font-semibold ml-1">
                Source </label>
              <select :name="parmsPropertyNamesList[5]" :id="parmsPropertyNamesList[5]"
                style="border: 1px solid #737373"
                class="w-full h-11 p-2 text-white bg-neutral-800 rounded-lg justify-start items-center inline-flex
                                        bg-inherit placeholder:text-left placeholder:text-[#F5F5F5] placeholder:text-xs focus:outline-none"
                @change="handleChange">
                <option selected disabled>Select an Option</option>
                <option value="default" class="p-4"> default </option>
              </select>
            </div>
          </div>
        </div>
        <div v-if="!uiStore.loader">

          <div class="grid grid-cols-3 gap-20">

            <div v-for="item, index in Object.keys(sessionAnalytics.analytics)"
              class="bg-zinc-700 backdrop-blur-xl bg-opacity-75 rounded-md p-5 flex flex-col justify-start items-center gap-2"
              :id="item" :key="index">
              <div class="bg-pink-100 p-2.5 rounded-[50%]">
                <ChartBarIcon class="w-10" />
              </div>
              <h5 class="text-white font-medium text-4xl"> {{ item.toLowerCase() === 'total_duration' ?
                convertMinutesToHoursAndMinutes(sessionAnalytics.analytics[item]) : sessionAnalytics.analytics[item] }}
              </h5>
              <h6 class="text-white font-medium text-sm capitalize"> {{ item.toLowerCase() === 'total_duration' &&
                'Usage time' || item.toLowerCase() === 'num_sessions' && 'Number of sessions hosted' ||
                item.toLowerCase() === 'unique_participants' && 'Unique Customers' }} </h6>
            </div>

          </div>

        </div>

        <div>
          <h3 class="text-base font-semibold leading-6 text-gray-900">Last 30 days</h3>
          <dl
            class="mt-5 grid grid-cols-1 divide-y divide-gray-200 overflow-hidden rounded-lg bg-gray-100 shadow md:grid-cols-3 md:divide-x md:divide-y-0">
            <div class="px-4 py-5 sm:p-6">
              <dt class="text-base font-normal text-gray-900">Total Subscribers</dt>
              <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                  71,897
                  <span class="ml-2 text-sm font-medium text-gray-500">from 70,946</span>
                </div>
              </dd>
            </div>
            <div class="px-4 py-5 sm:p-6">
              <dt class="text-base font-normal text-gray-900">Avg. Open Rate</dt>
              <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                  58.16%
                  <span class="ml-2 text-sm font-medium text-gray-500">from 56.14%</span>
                </div>

              </dd>
            </div>
            <div class="px-4 py-5 sm:p-6">
              <dt class="text-base font-normal text-gray-900">Avg. Click Rate</dt>
              <dd class="mt-1 flex items-baseline justify-between md:block lg:flex">
                <div class="flex items-baseline text-2xl font-semibold text-indigo-600">
                  24.57%
                  <span class="ml-2 text-sm font-medium text-gray-500">from 28.62%</span>
                </div>

              </dd>
            </div>
          </dl>

        </div>

      </div>
      <div class="px-4 sm:px-6 lg:px-8">
        <div class="mt-8 flow-root">
          <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
              <table class="min-w-full divide-y divide-gray-300">
                <thead>
                  <tr>
                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0">Name
                    </th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Title</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Email</th>
                    <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Role</th>
                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-0">
                      <span class="sr-only">Edit</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr>
                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-0">Lindsay
                      Walton</td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">Front-end Developer</td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500"><EMAIL></td>
                    <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">Member</td>
                    <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                      <a href="#" class="text-indigo-600 hover:text-indigo-900">Edit<span class="sr-only">, Lindsay
                          Walton</span></a>
                    </td>
                  </tr>

                  <!-- More people... -->
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<style>
/* DatePicker */
.dp-custom-input {
  color: #F5F5F5;
  border: 1px solid #737373;
}
</style>

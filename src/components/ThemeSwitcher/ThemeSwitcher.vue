<script setup>

import { uiOperations } from '../../store/uiOperations.ts';

defineProps({
  position: String,
});

const Store = uiOperations();

/* Methods */
const handleToggleClick = () => {
  if (Store.toggleTheme.toLowerCase() === 'light'){

    Store.handleToggleTheme('dark');
  } else {

    Store.handleToggleTheme('light');
  }
};

</script>

<template>
    <div>
        <button  @click="handleToggleClick" :class="[(Store?.toggleTheme?.toLowerCase() === 'light' ?  'border-zinc-800 text-black text-end before:left-[2px] before:bg-zinc-800' : ' border-white text-[#F2F2F2] text-start before:left-[39px] before:bg-white' ),( position ? (position.toLowerCase() === 'fixed'  ? 'absolute top-3 right-3' : 'relative') : 'relative' ),' bg-transparent border-2 w-[65px] h-[32px]  px-2 text-xs rounded-[30px] z-[2] cursor-pointer  toggleShape '] ">
                             {{ Store?.toggleTheme?.toLowerCase() === 'light' ? 'Light'  : 'Dark' }}
        </button>
    </div>
</template>

<style scoped>

/* Shape */

.toggleShape::before{
    content: '';
    position: absolute;
    top: 5px;
    width: 30%;
    height: 65%;
    font-size: 18px;
    z-index: 2;
    border-radius: 50%;
    transition: all linear 200ms;
}

</style>

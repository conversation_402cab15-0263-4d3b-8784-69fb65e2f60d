<script setup>
import { activeRepeatAgainIcon, deleteIcon, editIcon, repeatAgainIcon, searchIcon} from '@/helpers/icons';
import { onMounted, ref, watch } from 'vue';
import NewDropDown from '../common/NewDropDown.vue';
import { onBeforeRouteUpdate, useRoute, useRouter } from 'vue-router';
import { DeleteInvitation, GetInvitations} from '@/api/invitations';
import { ListUsersInOrganization } from '@/api/organization';
import Modal from '../common/Modal/Modal.vue';
import DeleteModalContent from '../common/ModalContent/DeleteModalContent.vue';
import { UserStore } from '../../store/index';
import EditUser from './EditUser.vue';
import { Org_Store } from '@/store/organization';
import { uiOperations } from '@/store/uiOperations';

const Store = UserStore();
const searchText = ref('');
const router = useRouter();
const route = useRoute();
const activePage = ref('member');
const headerTitlesMembers = ['name', 'email', 'phone', 'role', ''];
const headerTitlesInvitation = ['email', 'status', 'role', ''];
const users = ref({});
const totalUsersData = ref({});
const updatedUsersData = ref({});
const invitations = ref({});
const userEditModalRef = ref({});
const editIconRef = ref({});
const openDeleteModal = ref(false);
const openEditModal = ref(false);
const userToEdit = ref();
const userPreviousRoleData = ref('');
const invitationToDelete = ref();
const deleteLoader = ref(false);
const showFilters = ref(true);
const OrganizationStore = Org_Store();
const uistore = uiOperations();
const isScrolled = ref(false);
const scrollContainer = ref(null);
const parentContainerRef = ref(null);
const getListOfUsersCallBack = () => {
  ListUsersInOrganization().then((users_data) => {
    users.value = users_data;
  });
};
const getInvitationsCallBack = () => {
  GetInvitations().then((res) => {
    invitations.value = res;
    console.log("invitations", invitations.value);
  });
};

function handleFilterRole (val){
  console.log("from filter", val);
  router.push({
    query: {
      ...router.currentRoute.value.query,
      role: val.map((item) => item.toLowerCase()),
    },
  });
}
function handlePageChange (val){
  if (val === 'invitation'){
    activePage.value = 'invitation';
    showFilters.value = false;
    router.push({
      query: {
        ...router.currentRoute.value.query,
        type: 'invitation',
      },
    });
  } else {
    activePage.value = 'member';
    showFilters.value = true;
    router.push({
      query: {
        ...router.currentRoute.value.query,
        type: 'members',
      },
    });
  }
}
onMounted(() => {
  Store.callbackFunctionMonitorChanges();
  router.push({
    query: {
      type: 'members',
    },
  });
  GetInvitations().then((res) => {
    console.log("onMounted invitation");
    invitations.value = res;
    console.log("invitations", invitations.value);
  });
  ListUsersInOrganization().then((users_data) => {
    console.log("onMounted users");
    users.value = users_data;
    totalUsersData.value = users_data;
    OrganizationStore.SyncMultipleUsers(users_data);
  });
});

watch(() => route.query, async (newQuery) => {
  if (newQuery.role) {
    console.log("New API Call");
    ListUsersInOrganization(route.query.role).then((users_data) => {
      if (Object.values(users_data).length > 0) {
        users.value = users_data;
      } else {
        users.value = {};
        uistore.showToast("Failed to load users", "error");
      }
    }).catch((err) => {
      uistore.showToast(`Failed to load users: ${err}`, "error");
    });
  } else {
    console.log("No API Call, Using Old Values");
    users.value = Object.values(updatedUsersData.value).length > 0 ? updatedUsersData.value : totalUsersData.value;
  }
});
// watch(
//   () => route.query.role,
//    async (newQuery) => {
//     console.log("current query",newQuery);
//     console.log("queries",filterQueries.value);
//     if (abortController.value) {
//         abortController.value.abort(); // Cancel previous API call if it exists
//     }
//     if(newQuery?.length > 0){
//         console.log("New API Call");
//         abortController.value = new AbortController();
//         ListUsersInOrganization(route.query.role,{
//         signal: abortController.value.signal, // Pass signal to API call
//       }).then((users_data)=>{
//             if(Object.values(users_data).length > 0){
//                 users.value = users_data;
//             }else{
//                 users.value = {};
//                 uistore.showToast('Failed to load users', 'error');
//             }
//         })
//     }
//     else{
//         console.log("No Api Call Old Values");
//         users.value = Object.values(updatedUsersData.value).length > 0 ? updatedUsersData.value : totalUsersData.value;
//     }
//   },
//   { deep: true },
// );

function handleEditMember (e, id, userId, role){
  openEditModal.value = true;
  userToEdit.value = id;
  userPreviousRoleData.value = role;
  console.log("__________", userPreviousRoleData.value);

  const listOfRemovalClass = ['hidden', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (parentContainerRef.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (parentContainerRef.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - parentContainerRef.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - parentContainerRef.value.getBoundingClientRect().top;  // get the y position based on the parent container
    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      console.log("upper");
      element.classList.add('right-[220px]');
      element.classList.add(`top-[-85px]`); // both x and y are exceeded the parent container or only y has exceded the parent container

    } else {
      console.log("lower");
      element.classList.add(`top-[0]`);
      element.classList.add('right-[200px]');  // Only x has exceed the parent container
    }
  };

  if (userEditModalRef.value[userId]){
    // active item
    userEditModalRef.value[userId].classList.remove(...listOfRemovalClass); // remove class
    dynamicAbsolutePositions(userEditModalRef.value[userId]); // apply absolute positions depending on the current position (i.e is based on parent container)
    userEditModalRef.value[userId].classList.add('flex');
    editIconRef.value[userId].classList.add('bg-[#EBF5FF]');
  }
}
function handleEditMemberMobileView (e, id, userId, role){
  e.preventDefault();
  if (openEditModal.value){
    if (userToEdit.value !== null && userToEdit.value !== userId){
      console.log("second box");

      if (userEditModalRef.value[userToEdit.value]){
        console.log("removing first box");
        const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
        userEditModalRef.value[userToEdit.value].classList.remove(...listOfRemovalClass); // remove class
        userEditModalRef.value[userToEdit.value].classList.add('hidden');   // add class
      }
      if (editIconRef.value[userToEdit.value]){
        console.log("removing first box");
        const listOfRemovalClass = ['bg-[#EBF5FF]'];
        editIconRef.value[userToEdit.value].classList.remove(...listOfRemovalClass); // remove class
      }
      openEditModal.value = true;
      userToEdit.value = userId;
      userPreviousRoleData.value = role;

    } else {
      console.log("first box");
      userToEdit.value = null;
      openEditModal.value = false;
    }
  } else {
    console.log("first box");
    userToEdit.value = userId;
    openEditModal.value = true;
  }

  const listOfRemovalClass = ['hidden', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
  const dynamicAbsolutePositions = (element) => {
    const calcParentWidth = (scrollContainer.value.getBoundingClientRect().width * 90) / 100; // find the parent container 90% of the width
    const calcParentHeight = (scrollContainer.value.getBoundingClientRect().height * 70) / 100; // find the parent container 70% of the height
    const xPosition = e.clientX - scrollContainer.value.getBoundingClientRect().left; // get the x position based on the parent container
    const yPosition = e.clientY - scrollContainer.value.getBoundingClientRect().top;  // get the y position based on the parent container
    if ((xPosition > calcParentWidth && yPosition > calcParentHeight) || (xPosition < calcParentWidth && yPosition > calcParentHeight) )  {
      console.log("upper");
      element.classList.add('right-[86px]');
      element.classList.add(`top-[-115px]`);
    } else {
      console.log("lower");

      element.classList.add(`top-[1px]`);
      element.classList.add('right-[86px]');  // Only x has exceed the parent container
    }
  };
  if (openEditModal.value){
    if (userEditModalRef.value[userId]){
      console.log("displayed");
      // active item
      userEditModalRef.value[userId].classList.remove(...listOfRemovalClass); // remove class
      dynamicAbsolutePositions(userEditModalRef.value[userId]); // apply absolute positions depending on the current position (i.e is based on parent container)
      userEditModalRef.value[userId].classList.add('flex', 'z-50');   // add class
    }
    if (editIconRef.value[userId]){
      console.log("displayed");
      // active item // remove class
      editIconRef.value[userId].classList.add('bg-[#EBF5FF]');   // add class
    }

  } else {
    if (userEditModalRef.value ? userEditModalRef.value[userId].classList.contains('flex') : false){
      console.log("hidden");
      const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
      userEditModalRef.value[userId].classList.remove(...listOfRemovalClass); // remove class
      userEditModalRef.value[userId].classList.add('hidden');   // add class
    }
  }

}
const handleCloseEditPopup = (userId) => {
  openEditModal.value = false;
  console.log("leaving mouse");
  if (userEditModalRef.value ? userEditModalRef.value[userId].classList.contains('flex') : false){
    const listOfRemovalClass = ['flex', 'top-[-85px]', 'top-[-122px]', 'right-[5px]', 'right-[20px]'];
    userEditModalRef.value[userId].classList.remove(...listOfRemovalClass); // remove class
    userEditModalRef.value[userId].classList.add('hidden');   // add class
  }

  if (editIconRef.value ? editIconRef.value[userId].classList.contains('bg-[#EBF5FF]') : false){
    const listOfRemovalClass = ['bg-[#EBF5FF]'];
    // const listOfAddingClass = ['opacity-70', 'bg-black/50'];
    editIconRef.value[userId].classList.remove(...listOfRemovalClass); // remove class
    // editIconRef.value[userId].classList.add(...listOfRemovalClass);   // add class
  }
};
function handleDeleteInvitation (id){
  openDeleteModal.value = true;
  invitationToDelete.value = id;
}
function handleMoveInvitaionToTrash (id){
  deleteLoader.value = true;
  //       const obj = {
  //     ...({invitation_id: [invitationToDelete.value]}),
  //     timeStamp: Date.now(),
  //   };
  DeleteInvitation(id).then(async () => {
    getInvitationsCallBack();
  }).catch((err) => {
    console.log(err);
  }).finally(() => {
    openDeleteModal.value = false;
    deleteLoader.value = false;
  });
//   MoveInvitationToTrash(id,obj).then(async ()=>{
//     getInvitationsCallBack();
//   }).catch((err)=>{
//     console.log(err);
//   }).finally(()=>{
//    openDeleteModal.value = false;
//     deleteLoader.value = false;
//   })
}

// const handleMoveToTrash = (userId) => {
//   deleteLoader.value = true;
//   const obj = {
//     ...({user_id: [userToDelete.value]}),
//     timeStamp: Date.now(),
//   };

//   MoveProjectToTrash(userId, obj).then(() => {
//     console.log("obj to delete");

//   }).catch((err) => {
//     deleteLoader.value = false;
//     console.log(err);
//   });
// };
function handleDeleteMember (){
  console.log("iii");

}
onBeforeRouteUpdate((to, from) => {
  if (to.path === "/users" && to.query.type === "members" && from.path === '/users/create') {
    getInvitationsCallBack();
  } else if (to.path === "/users" && to.query.type === "invitation" && from.path === '/users/create') {
    getInvitationsCallBack();
  } else if (to.path === "/users" && to.query.type === "members" && from.path.startsWith('/users/edit/')){
    ListUsersInOrganization().then((users_data) => {
      console.log("onMounted users");
      users.value = users_data;
      updatedUsersData.value = users_data;
      totalUsersData.value = users_data;
      OrganizationStore.SyncMultipleUsers(users_data);
    });
  }
});

watch(activePage, () => {
  searchText.value = '';
});
function resetFilters (){
  router.push({
    path: router.currentRoute.value.path,
    query: {
      type: 'members',
    },
  });
}
// function handleCloseEditModal(val){
//     console.log("val",val);

//     if(val){
//         console.log("1");

//         openEditModal.value = false;
//     }else{
//         console.log("2");
//         openEditModal.value = false
//     }

// }
// const handleCloseEditModal = computed(() => {
//   return openEditModal.value;
// });
const handleScroll = (event) => {
//   console.log("yes", event.target.scrollTop);

  isScrolled.value = event.target.scrollTop > 1;
  //   console.log("isScrolled", isScrolled.value);

};
</script>
<template>
    <div v-if="!Store.isMobile" class="">
      <div class="dynamic-header">
        <div class="h-full  flex max-xl:flex-col max-xl:!gap-3 md:!gap-3 gap-8 items-start">
          <div class="dynamic-heading ">
              <p class="dynamic-topic">Team Members</p>
              <p class="dynamic-sub-topic">Add and manage members.</p>
          </div>
          <div class="min-w-[220px] w-[220px]">
              <div class="flex w-full h-[37px]  justify-center relative ">
                      <span v-html="searchIcon" class="absolute top-[9px] left-[20px]"></span>
                      <input type="search"
                      v-model="searchText"
                      class="w-full bg-gray-100  text-gray-500 text-sm font-normal border rounded-lg pr-[0.5rem] pl-[2.6rem] placeholder-start py-2"
                      placeholder="Search"/>
              </div>
          </div>
        </div>
        <div class=" lg:flex lg:justify-end lg:gap-7 md:grid md:grid-cols-3 md:w-[60%] md:gap-x-1 md:gap-y-1 !h-full w-[70%] flex  justify-end gap-5 items-center relative">
          <NewDropDown
                 v-show="showFilters"
                    title="Role"
                    type="select"
                    inputType="checkbox"
                    :options="['Admin','Editor','Reader']"
                    @optionSelected="handleFilterRole"/>

              <button type="button" @click="router.push(`/users/create`)"
                class=" flex gap-1 items-center w-[140px] h-10 sm:rounded-md bg-[#1c64f2] rounded-lg hover:bg-[#1a56db] active:bg-[#1e429f]  m-0 px-2 text-xs text-white font-semibold leading-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                  class="w-6 h-5 fill-white mr-1">
                  <path fill-rule="evenodd"
                    d="M12 5.25a.75.75 0 01.75.75v5.25H18a.75.75 0 010 1.5h-5.25V18a.75.75 0 01-1.5 0v-5.25H6a.75.75 0 010-1.5h5.25V6a.75.75 0 01.75-.75z"
                    clip-rule="evenodd" />
                </svg>
                <p class="text-white text-sm font-medium">Add Member</p></button>
        </div>
      </div>

        <div class="flex w-full h-[35px]  border-b-2">
            <div class=" relative  flex gap-8">
                <span class=" w-[6rem] mt-[2px] h-full cursor-pointer text-center" :class="activePage === 'member'?'border-b-2 border-b-[#1a56db]':''" @click="()=>handlePageChange('members')">
                    <p class="text-sm font-medium" :class="activePage === 'member' ? 'text-[#1a56db]':'text-black'">Members</p>
                </span>
                <span class=" w-[6rem] mt-[2px] h-full cursor-pointer text-center" :class="activePage === 'invitation'?'border-b-2 border-b-[#1a56db]':''" @click="()=>handlePageChange('invitation')">
                    <p class="text-sm font-medium" :class="activePage === 'invitation' ? 'text-[#1a56db]':'text-black'">Invitation</p>
                </span>
            </div>
        </div>
        <div ref="parentContainerRef"  class="w-full mx-auto h-[78%] overflow-y-auto overflow-x-auto border-right border-left">
            <table v-if="activePage === 'member'"  class="w-full" >
             <thead >
                <tr class="bg-gray-50">
                    <th v-for="item in headerTitlesMembers" :key="item"
                    class=" text-gray-500 text-xs font-semibold uppercase h-[50px] pl-[1.8rem]"
                    :class="item === 'name' || item === 'email' ?'w-[200px]':item === 'phone' ||  item === 'role' ? 'w-[150px]':item === 'permission'?'w-[100px]':''">{{ item }}</th>
                </tr>
            </thead>

            <tbody>
                <tr v-for="user, userId in users"
                v-show="searchText.length > 0 ? user.first_name?.toLowerCase().includes(searchText):true"
                    :id="userId" :key="userId"
                     @mouseleave="()=>handleCloseEditPopup(userId)"
                    class="h-[55px] !w-full border-b-2 items-center relative">
                    <td class="pl-[1.8rem] w-[200px] ">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ user.first_name+' '+user.last_name }}</p>
                        </div>
                    </td>
                    <td class="pl-[1.8rem] w-[200px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium ">{{ user.email }}</p>
                        </div>
                    </td>
                    <td class="pl-[1.8rem] w-[150px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ user.phone ? user.phone:'XXXXXX' }}</p>
                        </div>
                    </td>
                    <td class="pl-[1.8rem] w-[150px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ user.role }}</p>
                        </div>
                    </td>
                    <td class="!pt-0 !bt-0 pl-[1.8rem] w-[150px]">
                    <div :ref="el => { if(el) userEditModalRef[userId] = el}"
                        :key="'editUserCardMobileItem'+ userId" class="hidden absolute z-20 "
                         @mouseleave="()=>handleCloseEditPopup(userId)">
                        <EditUser
                        :userId="user.user_id"
                        :previousRole="userPreviousRoleData"
                        @closeEditModal="()=>{openEditModal = false;handleCloseEditPopup(userId);getListOfUsersCallBack()}"/>
                    </div>
                        <div class="h-[52px] w-[90%] flex justify-end items-center gap-8">
                            <span :ref="el => { if(el) editIconRef[userId] = el}" v-html="editIcon" class="relative h-[55px] w-[40px] justify-center flex items-center top-[1px] cursor-pointer"
                            @click="(e)=>handleEditMember(e,user.user_id,userId,user.role)">
                            </span>
                            <span v-html="deleteIcon"  class="cursor-pointer h-full flex items-center rounded-full hover:cursor-not-allowed"
                            @click="()=>handleDeleteMember(user.user_id)">
                            </span>
                        </div>
                    </td>

                </tr>

            </tbody>
            </table>
            <table v-if="activePage === 'invitation'"  class="w-full">
             <thead >
                <tr class="bg-gray-50">
                    <th v-for="item in headerTitlesInvitation" :key="item"
                    class=" text-gray-500 text-[12px] font-semibold uppercase !pl-[2rem] h-[50px]"
                    :class="item === 'email' ?'w-[200px]':item === 'role'?'w-[200px]':item === 'status'?'w-[200px]':''">{{ item }}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="invitation, invitationId in invitations"
                v-show="searchText.length > 0 ? invitation.email?.toString().toLowerCase().includes(searchText) : true"
                    :id="invitationId" :key="invitationId"
                    class="h-[55px] !w-full border-b-2 items-center">
                    <td class="pl-[1.8rem] w-[200px] !h-full flex items-center relative top-4">
                        <div class="h-full w-full flex items-center">
                            <p class="text-[14px] font-medium ">{{ invitation.email }}</p>
                        </div>
                    </td>
                    <td class="pl-[1.8rem] w-[200px]">
                      <span class="h-full w-[5rem] justify-center flex items-center  px-2 py-1 rounded-md"
                            :class="invitation.status === 'joined'?'bg-[#def7ec]':'bg-gray-100'">
                                <p class="text-sm font-medium capitalize "
                                :class="invitation.status === 'joined'?'text-[#03543f]':'text-[#111928]'">{{ invitation.status }}</p>
                            </span>

                    </td>
                    <td class="pl-[1.8rem] w-[250px]">
                      <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ invitation.role }}</p>
                        </div>
                    </td>
                    <td class="pl-[1.8rem] w-[150px]">
                    <div class="w-full h-full flex gap-6 justify-end pr-3 items-center">
                    <div class="h-8 w-[8rem] flex items-center justify-center cursor-pointer gap-2 px-1 py-1 bg-white rounded-lg border !border-[#1c64f2]
                    hover:!bg-[#e1effe] active:!bg-[#1c64f2] group">
                        <span v-html="repeatAgainIcon" class="group-active:hidden"></span>
                        <span v-html="activeRepeatAgainIcon" class="hidden group-active:inline"></span>
                        <p class="text-sm font-medium  capitalize text-[#1c64f2] group-active:text-white"> Sent Again</p>
                    </div>
                        <div class="h-[40px] w-[40px] flex justify-end items-center gap-8">
                            <span v-html="deleteIcon"  class="cursor-pointer h-full flex items-center rounded-full"
                            @click="()=>handleDeleteInvitation(invitation._id)">
                            </span>
                        </div>
                    </div>
                    </td>
                </tr>
            </tbody>
            </table>
        </div>
            <!-- <Modal :open="openEditModal">
                <EditUser
                :userId="userToEdit"
                :previousData="userPreviousRoleData"
                @closeEditModal="()=>{openEditModal = false;getListOfUsersCallBack()}"/>
            </Modal> -->
            <Modal :open="openDeleteModal">
              <DeleteModalContent
                  :trash="false"
                  :loader="deleteLoader"
                  @closeModal="(e) => openDeleteModal = false"
                  @handleDelete="()=>handleMoveInvitaionToTrash(invitationToDelete)"
                  :dataName="activePage" />
            </Modal>
    </div>
    <div v-else class="h-full w-full select-none flex flex-col gap-4 ">
        <div class="h-[18%] mt-2 w-full flex flex-col justify-between mx-auto gap-2 transition-all duration-300"
        v-if="!isScrolled" >
            <div class="w-full h-full flex gap-8 items-center">
                <div class="w-[60%] flex flex-col gap-2">
                    <p class="text-[#111928] text-lg font-medium">Team Members</p>
                    <p class="text-gray-500 text-xs font-normal">Add and manage members</p>
                </div>
                <div class="w-[40%]">
            <div class="h-full w-full flex items-center justify-end gap-4">
                 <NewDropDown
                    v-show="activePage === 'member'"
                    :title="['Role']"
                    type="select"
                    inputType="checkbox"
                    :options="{role:['Admin','Editor','Reader']}"
                    @roleOptionSelected="handleFilterRole"
                     @resetFilters="resetFilters"/>

              <button type="button" @click="router.push('/users/create')"
                    class="w-[40px] h-10 flex justify-center items-center  sm:rounded-md bg-[#1c64f2] rounded-lg hover:bg-[#1a56db] active:bg-[#1e429f] px-2 text-xs text-white font-semibold">
                    <svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                    class="w-6 h-5 fill-white">
                    <path fill-rule="evenodd"
                        d="M12 5.25a.75.75 0 01.75.75v5.25H18a.75.75 0 010 1.5h-5.25V18a.75.75 0 01-1.5 0v-5.25H6a.75.75 0 010-1.5h5.25V6a.75.75 0 01.75-.75z"
                        clip-rule="evenodd" />
                    </svg>
                </button>
            </div>

                </div>

            </div>
                <div class="flex w-full h-[37px] justify-center relative">
                        <span v-html="searchIcon" class="absolute top-[9px] left-[20px]"></span>
                        <input type="search"
                        v-model="searchText"
                        class="w-full bg-gray-100  text-gray-500 text-sm font-normal border rounded-lg pr-[0.5rem] pl-[2.6rem] placeholder-start py-2"
                        placeholder="Search"/>
                </div>
        </div>

        <div class="flex w-full h-[35px]  border-b-2 sticky top-0"
        :class="isScrolled?'mt-3':''">
            <div class="w-full relative  flex gap-8">
                <span class=" w-[50%] mt-[2px] h-full cursor-pointer text-center" :class="activePage === 'member'?'border-b-2 border-b-[#1a56db]':''" @click="()=>handlePageChange('members')">
                    <p class="text-sm font-medium" :class="activePage === 'member' ? 'text-[#1a56db]':'text-black'">Members</p>
                </span>
                <span class=" w-[50%] mt-[2px] h-full cursor-pointer text-center" :class="activePage === 'invitation'?'border-b-2 border-b-[#1a56db]':''" @click="()=>handlePageChange('invitation')">
                    <p class="text-sm font-medium" :class="activePage === 'invitation' ? 'text-[#1a56db]':'text-black'">Invitation</p>
                </span>
            </div>
        </div>
        <div ref="scrollContainer"   class="w-full mx-auto   overflow-x-auto overflow-y-auto scrollbar rounded-tl-lg"
        @scroll="handleScroll" :class="!isScrolled?'!h-[58%]':isScrolled && activePage === 'invitation'?'!h-[75%]':isScrolled && activePage === 'member'?'!max-h-[63%] h-[63%]':''">
            <table v-if="activePage === 'member'"  class="w-full ">
             <thead >
                <tr class="bg-gray-50">
                    <th v-for="item in headerTitlesMembers" :key="item"
                    class=" text-gray-500 text-xs font-semibold uppercase p-3 "
                    :class="item === 'name' || item === 'email' ?'w-[150px]':item === 'phone' ||  item === 'role' ? 'w-[150px]':item === 'permission'?'w-[100px]':''">{{ item }}</th>
                </tr>
            </thead>
            <tbody class="relative">
                <tr v-for="user, userId in users"
                    v-show="searchText.length > 0 ? user.first_name.toLowerCase().includes(searchText):true"
                    :id="userId" :key="userId"
                    class="h-[55px] !w-full border-b-[1px] items-center ">
                    <td class="pl-[1rem] !w-[150px]">
                        <div class="h-full w-[150px] flex items-center">
                            <p class="text-sm font-medium capitalize">{{ user.first_name+' '+user.last_name }}</p>
                        </div>
                    </td>
                    <td class="pl-[1rem] w-[200px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium ">{{ user.email }}</p>
                        </div>
                    </td>
                    <td class="pl-[1rem] w-[150px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ user.phone ? user.phone:'XXXXXX' }}</p>
                        </div>
                    </td>
                    <td class="pl-[1rem] w-[150px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ user.role }}</p>
                        </div>
                    </td>
                    <td class="!pt-0 !bt-0 right-0 bg-white">
                        <div :ref="el => { if(el) userEditModalRef[userId] = el}"
                            :key="'editUserCardItem'+ userId" class="hidden absolute z-20"
                           >
                            <EditUser
                            :userId="user.user_id"
                            :previousRole="user.role"
                            @closeEditModal="()=>{openEditModal = false;handleCloseEditPopup(userId);getListOfUsersCallBack()}"/>
                        </div>
                        <div class=" h-[55px] w-[100%] flex justify-end items-center gap-1">
                            <span :ref="el => { if(el) editIconRef[userId] = el}" v-html="editIcon" class="relative h-[58px] w-[40px] justify-center flex items-center top-[1px] cursor-pointer "
                           @click="(e)=>handleEditMemberMobileView(e,user.user_id,userId,user.role)">
                            </span>
                            <span v-html="deleteIcon"  class="cursor-pointer h-[55px] w-[40px] flex items-center justify-center rounded-full"
                            @click="()=>handleDeleteMember(user.user_id)">
                            </span>
                        </div>
                    </td>

                </tr>
            </tbody>
            </table>
            <table v-if="activePage === 'invitation'"  class="w-full">
             <thead >
                <tr class="bg-gray-100">
                    <th v-for="item in headerTitlesInvitation" :key="item"
                    class=" text-gray-500 text-[12px] font-semibold uppercase !pl-[2rem] !py-[1rem]"
                    :class="{
                        'w-[100px] ': item === 'email',
                        'w-[100px] ': item === 'role',
                        'w-[100px] ': item === 'status',
                    }">{{ item }}</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="invitation, invitationId in invitations"
                    v-show="searchText.length > 0 ? invitation.email?.toString().toLowerCase().includes(searchText) : true"
                    :id="invitationId" :key="invitationId"
                    class="h-[55px] !w-full border-b-2 items-center">
                    <td class="pl-[1.8rem] w-[100px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-[14px] font-medium ">{{ invitation.email }}</p>
                        </div>
                    </td>
                    <!-- <td class="pl-[1.8rem] w-[150px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ invitation.phone && invitation.phone }}</p>
                        </div>
                    </td> -->
                    <td class="pl-[1.8rem] w-[100px]">

                            <div class="h-full w-fit flex items-center  px-2 py-1 rounded-md"
                            :class="invitation.status === 'joined'?'bg-[#def7ec]':'bg-gray-100'">
                                <p class="text-sm font-medium capitalize "
                                :class="invitation.status === 'joined'?'text-[#03543f]':'text-[#111928]'">{{ invitation.status }}</p>
                            </div>
                    </td>
                    <td class="pl-[1.8rem] w-[100px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ invitation.role }}</p>
                        </div>
                    </td>

                    <td class="pl-[1rem]  pr-[10px] w-[190px] sticky right-0 bg-white ">
                        <div class="w-full h-full  flex gap-5 items-center">
                        <div class="w-[50%] flex justify-end">
                            <span v-html="repeatAgainIcon"></span>
                        </div>
                        <div class="h-[40px] w-[50%] flex justify-end items-center gap-8">
                            <span v-html="deleteIcon"  class="cursor-pointer h-full flex items-center rounded-full"
                            @click="()=>handleDeleteInvitation(invitation._id)">
                            </span>
                        </div>
                        </div>

                    </td>
                    <!-- <td class="pl-[1.8rem] w-[100px]">
                        <div class="h-full w-full flex items-center">
                            <p class="text-sm font-medium capitalize">{{ invitation.permissions }}</p>
                        </div>
                    </td> -->
                    <!-- <td class="pl-[1.8rem] !w-[50px]">

                    </td> -->

                </tr>
            </tbody>
            </table>
        </div>
            <!-- <Modal :open="openEditModal">
                <EditUser :userId="userToEdit" :previousData="userPreviousRoleData" @handleClose="()=>{openEditModal = false;getListOfUsersCallBack()}"/>
            </Modal> -->
            <Modal :open="openDeleteModal">
              <DeleteModalContent
                  :trash="false"
                  :loader="deleteLoader"
                  @closeModal="(e) => openDeleteModal = false"
                  @handleDelete="()=>handleMoveInvitaionToTrash(invitationToDelete)"
                  :dataName="activePage" />
            </Modal>
    </div>
</template>

<style>
.scrollbar::-webkit-scrollbar{
    width: 4px;
    height: 4px !important;

}
.scrollbar::-webkit-scrollbar-thumb {
    background-color:#D1D5DB ;
    border-radius: 10px;
}

</style>

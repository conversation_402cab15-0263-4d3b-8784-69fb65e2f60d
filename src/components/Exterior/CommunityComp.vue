<script setup>
import { CommunitySchema } from '@/validationSchema/building';
import { Form, Field } from 'vee-validate';
import { ref, defineProps, defineEmits, onMounted, nextTick } from 'vue';
import Modal from '../common/Modal/Modal.vue';
import { processFirebaseFile, resizeImage } from '@/helpers/helpers';
import { createCommunity, updateCommunity } from '@/api/projects/communities';
import FilesUploader from './FilesUploader.vue';
import { ProjectStore } from '@/store/project';
import { useRoute } from 'vue-router';
import { getListofAssets } from '@/api/projects/assets';
import ImgPreviewModal from '../common/Modal/imgPreviewModal.vue';

const route = useRoute();
const projectStore = ProjectStore();

const { row, index, id} = defineProps({
  row: {type: Object, default: () => ({})},
  index: {type: String, default: ''},
  id: {type: String, default: ''},
});

const emit = defineEmits(['deleteRow', 'openDeleteModal', 'handleImgPreview', 'refreshData']);
const uploadStatus = ref({}), showSelector=ref(false), filesData = ref({}), initialFileObj = ref(null), currentFileObj=ref(null);
const selectedFile = ref(null), initialValues = ref({...row}), filePreviewUrl= ref(null), openUploadModal = ref(false), isEdited= ref(false);
const imgModal = ref({ status: false, url: '' }), copied = ref(false), showLoader = ref(false), rowRef = ref(null);

const categoryList = [
  { name: 'Tower', value: 'tower' },
  { name: 'Villa', value: 'villa' },
];

if (route.params.project_id) {
  projectStore.RefreshAssets(route.params.project_id);
  getListofAssets(route.params.project_id)
    .then((res) => {
      filesData.value = res.assets.items;
    })
    .catch((err) => {
      console.log("Error:", err);
    });
}
if (row?.isNew || initialValues.value.isNew === undefined ) {
  processFirebaseFile(initialValues.value.thumbnail).then((res) => {
    initialFileObj.value = res;
  });
}

const getFileName = (url) => {
  try {
    const decodedUrl = decodeURIComponent(url);
    // Extract the last part of the path after the last '/'
    const match = decodedUrl.match(/\/([^/?]+)\?alt=media/);
    if (match) {
      const fullFileName = match[1];
      // Remove timestamp before the file extension (e.g., _1739546193154)
      return fullFileName.replace(/(_\d+)(?=\.)/, '');
    }
    return null;
  } catch (error) {
    console.error('Error extracting filename:', error);
    return null;
  }
};

const openSelector = () => {
  showSelector.value = true;
  openUploadModal.value = true;
};

// Tooltip popUp
const templateElementForTooltip = (left, top, msg) => {
  const div = document.createElement("div");
  div.setAttribute('data-name', 'toursImagesTooltip');
  div.classList.add(...['flex', 'absolute', 'text-xs', 'bg-[#111111]', 'text-white', 'text-left', 'z-20', 'px-3', 'py-[3px]', 'rounded-[3px]', 'whitespace-nowrap', '-translate-x-[50%]', 'min-w-[auto]', 'max-w-fit', 'mt-2', 'custom_shadow']);
  div.style.left = `${left}px`;
  div.style.top = `${top}px`;
  div.innerText = msg; // message
  return div;
};
const handleOpenTooltip = (e, msg) => {
  document.body.appendChild(templateElementForTooltip(e.clientX, e.clientY + 12, msg));
};
const handleCloseTooltip = () => {
  const toolTipElement = document.querySelector("[data-name='toursImagesTooltip']");
  toolTipElement.remove(); // remove directly from dom
};

const simulateFileUpload = async (index) => {
  return new Promise((resolve) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      uploadStatus.value[index] = {
        ...uploadStatus.value[index],
        progress,
      };
      if (progress >= 100) {
        clearInterval(interval);
        resolve();
      }
    }, 300);
  });
};
const handleFileSelect = async (event, selectedFile) => {
  isEdited.value = true;
  const file = event?.target?.files?.[0]  || selectedFile;

  if (!file) {
    return;
  }
  showSelector.value = false;
  try {
    currentFileObj.value = file;
    if (file.type.startsWith('image/')) {
      filePreviewUrl.value =  URL.createObjectURL(file);
    }

    uploadStatus.value[index] = {
      status: 'uploading',
      progress: 0,
      fileName: file.name,
    };

    await simulateFileUpload(index);

    uploadStatus.value[index] = {
      ...uploadStatus.value[index],
      status: 'completed',
    };
  } catch (error) {
    console.error('File upload error:', error);
    uploadStatus.value[index] = {
      ...uploadStatus.value[index],
      status: 'error',
    };
  }
};

const removeFile = (index) => {
  isEdited.value= true;
  if (initialValues.value.isNew){
    delete uploadStatus.value[index];
  } else {
    if (uploadStatus.value[index]){
      delete uploadStatus.value[index];
    } else {
      delete initialValues.value.thumbnail;
    }
  }
  selectedFile.value = null;
};

function getObjectDifference (previousObj, newObj) {
  const differences = {};

  for (const key in newObj) {
    const newValue = newObj[key];
    const prevValue = previousObj[key];

    if (key === 'modified') {
      continue;
    }

    if (newValue === false || newValue === null || newValue === undefined) {
      continue;
    }

    // new property
    if (!(key in previousObj)) {
      differences[key] = newValue;
      continue;
    }

    if (prevValue !== newValue) {
      differences[key] = newValue;
    }
  }

  return differences;
}

const trackChanges = () => {
  isEdited.value = true;
};

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(initialValues.value._id);
    copied.value = true;
    setTimeout(() => (copied.value = false), 1000);
  } catch (e) {
    console.error('Failed to copy ID:', e);
  }
};

const handleSubmit = async (values) => {
  showLoader.value = true;
  if (!values) {
    console.error("Validation failed");
    return;
  }

  if (initialValues.value.isNew) {
    try {
      const formData = new FormData();
      formData.append("name", values.name);
      formData.append("project_id", initialValues.value.project_id);
      formData.append("category", values.category);

      // Handle file if it exists
      if (values.file instanceof File) {
        const resizedThumbnail = await resizeImage(values.file, 720, 720);
        formData.append("thumbnail", resizedThumbnail);
      }

      // Create community
      await createCommunity(formData);
      showLoader.value = false;
      emit('refreshData', id);
      document.dispatchEvent(new Event("refreshcommunities"));
    } catch (error) {
      console.error("Error creating community:", error);
    }
  } else {
    const newObj  = getObjectDifference(row, values);
    const formdata = new FormData();
    if (Object.keys(newObj).length > 0 || currentFileObj.value ){

      newObj.category && formdata.append('category', newObj.category);
      newObj.name && formdata.append('name', newObj.name);
      currentFileObj.value  && formdata.append('thumbnail',  currentFileObj.value );
      formdata.append('project_id', route.params.project_id);
      formdata.append('community_id', id);

      updateCommunity(formdata).then(() => {
        showLoader.value = false;
        isEdited.value = false;
        emit('refreshData');
      }).catch((err) => {
        console.log('err', err);
      });
    }
  }
};

onMounted(() => {
  if (row.isNew) {
    nextTick(() => {
      if (rowRef.value) {
        rowRef.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
        const input = rowRef.value.querySelector('input');
        if (input) {
          input.focus();
        }
      }
    });
  }
});

</script>

<template>
    <Form :validation-schema="CommunitySchema" @submit="handleSubmit"  v-slot="{ errors , meta }">
      <div ref="rowRef" class="flex w-full h-[50px] gap-8 relative " :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-100'">

            <div class="py-2 text-black text-sm relative flex items-center  ml-1 px-2 cursor-pointer w-32" @click="copyToClipboard">
                {{ !row.isNew ? '..' + initialValues._id.slice(-4)  : '' }}
                <span
                  v-if="copied && !row.isNew"
                  class="absolute -top-1 left-1/2 transform -translate-x-1/2 text-xs text-black"
                >
                  Copied!
                </span>
            </div>

            <div class="py-3 text-sm  flex items-center w-32" :class="errors.name ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
                <div class="w-full">
                    <Field
                    name="name"
                    type="text"
                    v-model="initialValues.name"
                    placeholder="Enter name"
                    class="w-full px-2 py-2 placeholder:text-left focus:outline-none text-sm leading-[21px]"
                    :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100',errors.name ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']"
                    id="name"
                    @input="trackChanges"
                    />
                </div>
            </div>

            <div class="py-3 text-gray-500 text-sm  flex items-center w-[255px] p-2" :class="errors.file ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
                <Field name="file"  :model-value="initialFileObj || currentFileObj" :class="errors.file ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-grey-400 placeholder-gray-400'">
                <div class="flex flex-row items-center gap-3">
                    <div class="w-full">
                    <!-- Uploading status -->
                    <div v-if="uploadStatus[index]?.status === 'uploading'" class="w-full flex flex-row">
                        <div class="w-full flex flex-col">
                        <div class="flex flex-row gap-2  justify-between">
                            <p class="text-sm">
                            {{ uploadStatus[index]?.name || 'uploading..' }}
                            </p>
                            <p class="text-sm mt-1">
                            {{ uploadStatus[index]?.progress }}%
                            </p>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                            <div class="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                            :style="{ width: `${uploadStatus[index]?.progress}%` }"></div>
                        </div>
                        </div>
                        <button class="p-1 hover:bg-gray-100 rounded-full">
                        </button>
                    </div>
                    <!-- Completed upload status -->
                    <div v-else-if="uploadStatus[index]?.status === 'completed' || ( !initialValues.isNew && initialValues.thumbnail)" class="flex justify-between gap-2 items-center">
                        <div v-if="!initialValues.isNew && initialValues.thumbnail">
                          <img :src="initialValues.thumbnail" alt="Preview" class="w-10 h-10 rounded-lg" loading="lazy" />
                        </div>
                        <p class="text-sm max-w-[93px] overflow-hidden text-ellipsis !text-black whitespace-nowrap"  @mouseenter="(e) => { handleOpenTooltip(e,uploadStatus[index]?.fileName || getFileName(initialValues.thumbnail)) }"  @mouseleave="handleCloseTooltip()">
                          {{ uploadStatus[index]?.fileName || getFileName(initialValues.thumbnail) }}
                        </p>
                        <div class="flex flex-row gap-3">
                          <div v-if="!initialValues.isNew" @click="() =>{ imgModal.status = true ; imgModal.url = row?.thumbnail} "
                            class="w-6 h-6 bg-gray-100 rounded-md flex justify-center items-center">
                              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/><circle cx="12" cy="12" r="3"/></svg>
                          </div>

                          <div  @click="removeFile(index)"  class="w-6 h-6 bg-gray-100 rounded-md flex justify-center items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                              <g clip-path="url(#clip0_1249_10593)">
                              <path d="M14.5179 3.36842H11.2586V1.68421C11.2586 1.23753 11.0869 0.809144 10.7813 0.493294C10.4757 0.177443 10.0612 0 9.62898 0L6.36972 0C5.93751 0 5.52301 0.177443 5.2174 0.493294C4.91178 0.809144 4.74009 1.23753 4.74009 1.68421V3.36842H1.48083C1.26473 3.36842 1.05748 3.45714 0.904669 3.61507C0.751862 3.77299 0.666016 3.98719 0.666016 4.21053C0.666016 4.43387 0.751862 4.64806 0.904669 4.80598C1.05748 4.96391 1.26473 5.05263 1.48083 5.05263H2.29565V14.3158C2.29565 14.7625 2.46734 15.1909 2.77295 15.5067C3.07857 15.8226 3.49307 16 3.92527 16H12.0734C12.5056 16 12.9201 15.8226 13.2257 15.5067C13.5314 15.1909 13.7031 14.7625 13.7031 14.3158V5.05263H14.5179C14.734 5.05263 14.9412 4.96391 15.094 4.80598C15.2468 4.64806 15.3327 4.43387 15.3327 4.21053C15.3327 3.98719 15.2468 3.77299 15.094 3.61507C14.9412 3.45714 14.734 3.36842 14.5179 3.36842ZM6.36972 1.68421H9.62898V3.36842H6.36972V1.68421ZM7.18453 12.6316C7.18453 12.8549 7.09869 13.0691 6.94588 13.227C6.79307 13.385 6.58582 13.4737 6.36972 13.4737C6.15362 13.4737 5.94637 13.385 5.79356 13.227C5.64075 13.0691 5.5549 12.8549 5.5549 12.6316V6.73684C5.5549 6.5135 5.64075 6.29931 5.79356 6.14138C5.94637 5.98346 6.15362 5.89474 6.36972 5.89474C6.58582 5.89474 6.79307 5.98346 6.94588 6.14138C7.09869 6.29931 7.18453 6.5135 7.18453 6.73684V12.6316ZM10.4438 12.6316C10.4438 12.8549 10.3579 13.0691 10.2051 13.227C10.0523 13.385 9.84508 13.4737 9.62898 13.4737C9.41288 13.4737 9.20562 13.385 9.05282 13.227C8.90001 13.0691 8.81416 12.8549 8.81416 12.6316V6.73684C8.81416 6.5135 8.90001 6.29931 9.05282 6.14138C9.20562 5.98346 9.41288 5.89474 9.62898 5.89474C9.84508 5.89474 10.0523 5.98346 10.2051 6.14138C10.3579 6.29931 10.4438 6.5135 10.4438 6.73684V12.6316Z" fill="#6B7280"/>
                              </g>
                              <defs>
                              <clipPath id="clip0_1249_10593">
                                  <rect width="16" height="16" fill="white"/>
                              </clipPath>
                              </defs>
                          </svg>
                          </div>
                        </div>
                    </div>

                    <!-- Default state-->
                    <div v-else @click="openSelector($event, index)" class="flex flex-row gap-4 p-2 cursor-pointer" :class="[errors.file ? '!bg-red-50 !text-red-700' : ' bg-white text-grey-400', index % 2 === 0 ? 'bg-white' : '!bg-gray-100']">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="17 8 12 3 7 8"/><line x1="12" y1="3" x2="12" y2="15"/></svg>
                        <p >upload</p>
                    </div>
                    </div>
                </div>
                </Field>
            </div>

            <div class="py-3 text-gray-500 text-sm  flex items-center w-32" :class="errors.category ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
                <Field as="select" type="text" name="category" v-model="initialValues.category" id="category" autocomplete="category"
                class="select-primary border-none"   @input="trackChanges"
                :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.category ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']">
                <option value="" disabled>Choose</option>
                <option value="" disabled v-if="!categoryList">
                    No  Category !
                </option>
                <option v-else :value="option.value" v-for="(option, index) in categoryList"
                    :key="index" class="text-black">
                    {{ option.name }}
                </option>
                </Field>
            </div>

            <!-- <div class="py-3 text-gray-500 text-sm  flex items-center w-32">
              <p> {{ initialValues.updated_at ? formatTimestamp(initialValues.updated_at) : 'Not Modified' }}</p>
            </div> -->

            <div  class="py-3 text-gray-500 text-sm  flex items-center !justify-end w-32 gap-6 h-full absolute right-0">
              <div v-if="row.isNew ? meta.dirty : isEdited"  class="py-3 text-gray-500 text-sm  flex items-center w-32">
                  <button  type="submit"
                      class="py-2 px-3 text-sm font-medium text-white bg-blue-700 rounded-lg flex items-center gap-2">
                      <div v-if="showLoader" class="loader !h-5 !w-5 !border-2"></div>
                      Save
                  </button>
              </div>

              <div class=" text-gray-500 text-sm  flex items-center pr-3">
                  <button type="button" @click="initialValues.isNew || initialValues.isNew !== undefined ? emit('deleteRow',initialValues._id) : emit('openDeleteModal',initialValues._id ,index)" class="text-red-600">
                      <div class="w-8 h-8 flex justify-center items-center bg-gray-100 rounded-lg">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                      </div>
                  </button>
              </div>
            </div>

        </div>
   </Form>

   <ImgPreviewModal v-if="imgModal.url"  :isOpen="imgModal.status" :imageUrl="imgModal.url"   @close="imgModal.status = false"/>

   <Modal :open="showSelector">
      <FilesUploader :data="filesData"
        @fileUploaded="(selectedFile) => handleFileSelect(e,selectedFile)"
        @closeModal="() => showSelector = false" />
    </Modal>
</template>

<style scoped>
.loader{
  @apply w-20 h-20 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-8 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}
</style>

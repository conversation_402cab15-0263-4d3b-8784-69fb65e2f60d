<script setup>
import {  ref, watch, provide } from 'vue';
import { useRoute } from 'vue-router';
import { ProjectStore } from '@/store/project';
import Modal from '../common/Modal/Modal.vue';
import {  getListOfCommunities, moveCommunityToTrash } from '@/api/projects/communities';
import { getListOfBuildings, moveBuildingToTrash } from '@/api/projects/buildings/index';
import TowerComp from './TowerComp.vue';
import DeleteModalContent from '../common/ModalContent/DeleteModalContent.vue';
import CommunityComp from './CommunityComp.vue';

const route = useRoute();
const projectStore = ProjectStore();
const initialTowerData =ref({}), initialCommunityData = ref({}) ;
const openTowerDeleteModal = ref(false), trashTowerLoader =ref(null), isTowerDataRefreshed = ref(false);
const newTowerData= ref({}), newCommunityData= ref({}), trashTowerId = ref({id: null, index: null});
const projectId = route.params.project_id, isLoadingCommunityData = ref(true), isLoadingTowerData=ref(true);
const towerTableHeaders=['S.NO', 'Name', 'No.Of Floors', 'Upload File', 'Community', 'Type']; // 'Modified'
const communityTableHeaders=['S.NO', 'Name', 'Upload File', 'Category', '', '']; // , 'Modified'
const openCommunityDeleteModal = ref(false), trashCommunityLoader = ref(null), trashCommunityId =ref({id: null, index: null});

if (route.params.project_id) {
  getListOfBuildings(projectId).then((res) => {
    isLoadingTowerData.value = false;
    initialTowerData.value = {...res};
    projectStore.SyncMultipleBuildings(res);
  });

  getListOfCommunities(projectId).then((res) => {
    isLoadingCommunityData.value = false;
    initialCommunityData.value = {...res};
    projectStore.SyncMultipleCommunities(res);
  });
}

const addNewTowerRow = () => {
  const id = crypto.randomUUID();
  const newFile = {
    _id: id,
    project_id: projectId,
    name: '',
    total_floors: '',
    file: '',
    community_id: '',
    type: '',
    isNew: true,
  };

  newTowerData.value[id] = newFile;
};

const deleteTowerRow=(ind) => {
  delete newTowerData.value[ind];
};

const openBuildingTrashModal=(id, ind) => {
  openTowerDeleteModal.value=true;
  trashTowerId.value.id =id;
  trashTowerId.value.index=ind;
};

const handleMoveTowerToTrash = () => {
  trashTowerLoader.value = true;
  projectStore.ForceRefreshBuildings(projectId);
  if ( projectId && trashTowerId.value.id ) {
    const newObj = {
      building_id: [trashTowerId.value.id],
      timeStamp: Date.now(),
    };
    moveBuildingToTrash(newObj, projectId).then(async () => {
      trashTowerLoader.value = false;
      openTowerDeleteModal.value = false;
      trashTowerId.value = {id: null, index: null};
      location.reload();
    });
  }
};

// community functionalities
const addNewCommunityRow = () => {
  const id = crypto.randomUUID();
  const newFile = {
    _id: id,
    project_id: projectId,
    name: '',
    file: '',
    category: '',
    isNew: true,
  };

  newCommunityData.value[id] = newFile;
};

const deleteCommunityRow=(ind) => {
  delete newCommunityData.value[ind];
};

const openCommunityTrashModal=(id, ind) => {
  openCommunityDeleteModal.value=true;
  trashCommunityId.value.id =id;
  trashCommunityId.value.index=ind;
};

const handleMoveCommunityToTrash = () => {
  trashCommunityLoader.value = true;
  projectStore.ForceRefreshBuildings(projectId.value);
  if ( projectId && trashCommunityId.value.id ) {
    const newObj = {
      community_id: [trashCommunityId.value.id],
      timeStamp: Date.now(),
    };
    moveCommunityToTrash(newObj, projectId).then(async () => {
      trashCommunityLoader.value = false;
      openCommunityDeleteModal.value = false;
      trashCommunityId.value = {id: null, index: null};
      location.reload();
      // archivedModal.value = true;
      // setTimeout(() => {
      //   archivedModal.value = false;
      // }, 2000);
    });
  }
};

const handleCommunityRefreshData = (id) => {
  delete newCommunityData.value[id];
  getListOfCommunities(projectId).then(async (res) => {
    isLoadingCommunityData.value = false;
    initialCommunityData.value = {...res};
    projectStore.SyncMultipleCommunities(res);
  });
};

const handleTowerRefreshData = (id) => {
  delete newTowerData.value[id];
  getListOfBuildings(projectId).then(async (res) => {
    isLoadingTowerData.value = false;
    initialTowerData.value = { ...res };
    isTowerDataRefreshed.value = true;
    projectStore.SyncMultipleBuildings(res);
  });
};

watch(initialTowerData, (newVal) => {
  initialTowerData.value = newVal;
}, { deep: true });

provide('isTowerDataRefreshed', isTowerDataRefreshed);

</script>

<template>
  <div class="w-full h-full bg-white gap-3  flex flex-col">
    <div class="dynamic-heading">
          <p class="dynamic-topic">Exterior </p>
          <p class="dynamic-sub-topic">This information serves as a reference for building the experience
          </p>
      </div>

      <!-- community table  -->
      <div class="h-min relative w-full flex flex-col border border-gray-300  rounded-lg">
        <div class="h-[50px] flex-shrink-0  px-3 py-2 flex justify-between rounded-t-lg border !border-b-gray-300">
            <p class="text-base font-medium text-black flex items-center">Enter Community Details </p>
            <button @click="addNewCommunityRow" type="button"
             class="!py-2 !px-3 text-base font-medium text-blue-700  bg-white rounded-lg flex gap-2 !border border-blue-700 items-center justify-center whitespace-nowrap">
               <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                <path
                  d="M10.2659 5.96686H6.53255V2.23353C6.53255 2.09208 6.47636 1.95642 6.37634 1.85641C6.27632 1.75639 6.14067 1.7002 5.99922 1.7002C5.85777 1.7002 5.72211 1.75639 5.6221 1.85641C5.52208 1.95642 5.46589 2.09208 5.46589 2.23353V5.96686H1.73255C1.5911 5.96686 1.45545 6.02305 1.35543 6.12307C1.25541 6.22309 1.19922 6.35875 1.19922 6.5002C1.19922 6.64164 1.25541 6.7773 1.35543 6.87732C1.45545 6.97734 1.5911 7.03353 1.73255 7.03353H5.46589V10.7669C5.46589 10.9083 5.52208 11.044 5.6221 11.144C5.72211 11.244 5.85777 11.3002 5.99922 11.3002C6.14067 11.3002 6.27632 11.244 6.37634 11.144C6.47636 11.044 6.53255 10.9083 6.53255 10.7669V7.03353H10.2659C10.4073 7.03353 10.543 6.97734 10.643 6.87732C10.743 6.7773 10.7992 6.64164 10.7992 6.5002C10.7992 6.35875 10.743 6.22309 10.643 6.12307C10.543 6.02305 10.4073 5.96686 10.2659 5.96686Z"
                  fill="#1C64F2" />
              </svg>
              Add File
            </button>
        </div>
      <div class="w-full flex-1 overflow-auto">
        <div class="w-full h-full  overflow-x-auto">
          <div class="flex w-full h-[30px] gap-8 bg-gray-50">
            <div v-for="(item, index) in communityTableHeaders" :key="index"
              class="rounded-lg p-2 text-gray-500 text-xs font-semibold !text-center uppercase leading-[18px] text-nowrap flex items-center"
              :class="[item==='Upload File'?'!w-[255px]' : 'w-32', index === 0 ? 'p-3':'p-2']">
              {{ item }}
            </div>
          </div>
          <div v-if="Object.values(initialCommunityData).length > 0 || Object.values(newCommunityData).length > 0" class="min-h-[50px] max-h-[200px]  overflow-auto">
           <CommunityComp v-for="(data,id,index) in initialCommunityData" :key="data._id"
             :row="data" :index="index" :id="id"  @refreshData="handleCommunityRefreshData"
             @openDeleteModal="openCommunityTrashModal"/>
           <CommunityComp v-for="(data,id,index) in newCommunityData" :key="data._id" :id="id"
             :row="data" :index="Object.values(initialCommunityData).length + index"
             @deleteRow="deleteCommunityRow"   @refreshData="handleCommunityRefreshData"
            />
          </div>
          <div v-else-if="isLoadingCommunityData" class="flex gap-3 items-center justify-center p-4">
              <div class="loader !h-6 !w-6 !border-3 !border-[#1C64F2] !border-t-gray-200"></div>
              <p class="text-black text-xs font-normal pt-1">Loading...</p>
          </div>

          <div  v-else-if="Object.values(initialCommunityData).length === 0"   class="flex  w-full h-[50px] gap-3 justify-center items-center" >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none"><g clip-path="url(#clip0_4982_18313)"><path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z" fill="#6B7280"></path></g><defs><clipPath id="clip0_4982_18313"><rect width="16" height="16" fill="white" transform="translate(0 0.5)"></rect></clipPath></defs></svg>
            <p>Empty</p>
          </div>
        </div>
      </div>
      </div>

      <Modal :open="openCommunityDeleteModal">
        <DeleteModalContent
            :trash="true"
            @closeModal="(e) => openCommunityDeleteModal = false"
            :loader="trashCommunityLoader"
            @handleDelete="handleMoveCommunityToTrash"
            :dataName="'Community'"
        />
      </Modal>

      <!-- tower table -->
      <div class="!h-min !mt relative w-full flex flex-col border border-gray-300 rounded-lg">
        <div class="!h-[50px] flex-shrink-0  px-3 py-2 flex justify-between rounded-t-lg border border-t-gray-400">
            <p class="text-base font-medium text-black flex items-center">Enter Tower Details </p>
            <button @click="addNewTowerRow" type="button"
             class="!py-2 !px-3 text-base font-medium text-blue-700  bg-white rounded-lg flex gap-2 !border border-blue-700 items-center justify-center whitespace-nowrap">
               <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                <path
                  d="M10.2659 5.96686H6.53255V2.23353C6.53255 2.09208 6.47636 1.95642 6.37634 1.85641C6.27632 1.75639 6.14067 1.7002 5.99922 1.7002C5.85777 1.7002 5.72211 1.75639 5.6221 1.85641C5.52208 1.95642 5.46589 2.09208 5.46589 2.23353V5.96686H1.73255C1.5911 5.96686 1.45545 6.02305 1.35543 6.12307C1.25541 6.22309 1.19922 6.35875 1.19922 6.5002C1.19922 6.64164 1.25541 6.7773 1.35543 6.87732C1.45545 6.97734 1.5911 7.03353 1.73255 7.03353H5.46589V10.7669C5.46589 10.9083 5.52208 11.044 5.6221 11.144C5.72211 11.244 5.85777 11.3002 5.99922 11.3002C6.14067 11.3002 6.27632 11.244 6.37634 11.144C6.47636 11.044 6.53255 10.9083 6.53255 10.7669V7.03353H10.2659C10.4073 7.03353 10.543 6.97734 10.643 6.87732C10.743 6.7773 10.7992 6.64164 10.7992 6.5002C10.7992 6.35875 10.743 6.22309 10.643 6.12307C10.543 6.02305 10.4073 5.96686 10.2659 5.96686Z"
                  fill="#1C64F2" />
              </svg>
              Add File
            </button>
        </div>
        <div class="w-full flex-1 overflow-auto">
      <div class="w-full overflow-x-auto relative">
        <div class="sticky top-0 bg-gray-50 flex w-full h-[30px] gap-6 z-10 !items-center">
          <div v-for="(item, index) in towerTableHeaders" :key="index"
              class="rounded-lg text-gray-500 text-xs font-semibold !text-center uppercase leading-[18px] text-nowrap flex"
              :class="[item==='Upload File'?'!w-[255px]' : 'w-32', index === 0 ? 'p-3':'p-2']">
            {{ item }}
          </div>
        </div>
        <div v-if="Object.values(initialTowerData).length > 0 || Object.values(newTowerData).length > 0"  class="min-h-[50px]  overflow-auto">
          <TowerComp class="!h-[50px]" v-for="(data,id,index) in initialTowerData" :key="data._id"
            :row="data" :index="index" :id="id"
            @openDeleteModal="openBuildingTrashModal"
            @handleImgPreview="handleTowerImgPreview"  @refreshData="handleTowerRefreshData"
          />
          <TowerComp class="!h-[50px]" v-for="(data,id,index) in newTowerData" :key="data._id"
            :row="data" :id="id"
            :index="Object.values(initialTowerData).length + index"
            @deleteRow="deleteTowerRow"  @refreshData="handleTowerRefreshData"
          />
        </div>

        <div v-else-if="isLoadingTowerData" class="flex gap-3 items-center justify-center p-4">
              <div class="loader !h-6 !w-6 !border-3 !border-[#1C64F2] !border-t-gray-200"></div>
              <p class="text-black text-xs font-normal pt-1">Loading...</p>
        </div>

        <div v-else-if="Object.values(initialTowerData).length === 0"  class="flex  w-full h-[50px] gap-3 justify-center items-center" >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none"><g clip-path="url(#clip0_4982_18313)"><path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z" fill="#6B7280"></path></g><defs><clipPath id="clip0_4982_18313"><rect width="16" height="16" fill="white" transform="translate(0 0.5)"></rect></clipPath></defs></svg>
            <p>Empty</p>
        </div>
      </div>
        </div>
      </div>

      <Modal :open="openTowerDeleteModal">
        <DeleteModalContent
            :trash="true"
            @closeModal="(e) => openTowerDeleteModal = false"
            :loader="trashTowerLoader"
            @handleDelete="handleMoveTowerToTrash"
            :dataName="'Building'"
        />
      </Modal>
  </div>
</template>
<style>
.sticky {
  position: sticky;
}

.loader{
  @apply w-20 h-20 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-8 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}
</style>

<script setup>
import { deleteFloor } from '@/api/projects/buildings';
import FloorReOrderComp from './FloorReOrderComp.vue';
import { defineEmits, defineProps } from 'vue';
import { useRoute } from 'vue-router';
import DeleteModalContent from '../common/ModalContent/DeleteModalContent.vue';
import Modal from '../common/Modal/Modal.vue';
import { ref } from 'vue';

const route = useRoute();
const props = defineProps({
  floorData: {type: Object, default: () => ({})},
  buildingId: {type: String, default: ''},
});

const emit = defineEmits(['finalSortedData', 'close', 'addfloor', 'updateData']);
const  showDeleteModal = ref(false), deleteItem = ref({});
const projectId = route.params.project_id;
const buildingId= ref(props.buildingId);

const handleSortedData = (val) => {
  emit('finalSortedData', val);
};

const openFloorDeleteModal = (item) => {
  showDeleteModal.value = true;
  deleteItem.value = {
    building_id: props.buildingId,
    project_id: projectId,
    floor_id: item.floor_id,
  };
};

const handledeleteFloor = () => {
  // opening delete modal
  showDeleteModal.value = false;
  deleteFloor(deleteItem.value).then(() => {
    emit('updateData');
  }).catch((err) => {
    console.log('err', err);
  });
};

const handleupdateData = () => {
  emit('updateData');
};
</script>

<template>
  <div class="w-[708px]  h-3/4 bg-white">
    <div class=" border-b border-gray-200 sticky">
      <div class="h-[49.5px] flex justify-between items-center p-7 border-b border-gray-200">
        <span class="text-base font-semibold text-gray-900">Edit Floors</span>

        <div class="flex items-center gap-6">
          <button @click="emit('addfloor')" class="inline-flex items-center gap-1 px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M6 1v10M1 6h10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
            Add
          </button>
          <button @click="emit('close')" class="p-2 text-gray-500 hover:text-gray-700">
            <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13 1L1 13M1 1L13 13" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
          </button>
        </div>
      </div>
    </div>
    <div class="h-full bg-gray-50 overflow-y-scroll">
      <FloorReOrderComp :floorData="floorData" @sortedData="handleSortedData" @updateData="handleupdateData"
      @deleteFloor="openFloorDeleteModal" :buildingId="buildingId" class="w-full h-full"/>
    </div>
  </div>

  <Modal :open="showDeleteModal" :key="showDeleteModal">
    <DeleteModalContent
        @closeModal="(e) => showDeleteModal = false"
        :loader="trashTowerLoader"
        @handleDelete="handledeleteFloor"
        :dataName="'Floor'"
    />
  </Modal>

</template>

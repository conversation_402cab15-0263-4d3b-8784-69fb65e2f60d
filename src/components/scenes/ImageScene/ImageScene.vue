<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import router from '@/router';
import SvgOverlay from '../Svg/SvgOverlay.vue';
import { getAllScenesFrames, moveSceneToTrash, updateBulkSceneFrames } from '../../../api/projects/scene/index.ts';
import { getScene } from '../../../api/projects/scene/index.ts';
import { getScene as getMasterScene,  getAllScenesFrames as getAllMasterScenesFrames, updateBulkSceneFrames as updateBulkMasterSceneFrames } from '../../../api/masterScene/index';
import GlobeComponent from '../Earth/globeComponent.vue';
import { isMasterScenePath } from '../../../helpers/helpers';
import SafeAreaHighlight from '../SafeAreaHighlight.vue';
import CategorySideBar from './CategorySideBar.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { updateLayersVideoTag } from '@/api/projects/scene/svg';
import { updateLayersVideoTag as updateMasterLayersVideoTag } from '../../../api/masterScene/svg/index';
import { uploadVideoTagValidation } from '@/validationSchema/scene/updateProjectLayerSchema';
import NestedReOrder from '../../common/NestedReOrder.vue';
import DeleteConfirmationModal from '../../common/DeleteConfirmationModal.vue';
import { onClickOutside } from '@vueuse/core';
import Spinner from '@/components/common/Spinner.vue';

const route = useRoute();
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const sceneFrameList = ref();
const sceneDetails = ref();
const reorderFrames = ref(false);
const originalListOfItems = ref([]);
const listOfItems = ref([]);
const deleteFrameRef = ref();
const safeFrame = ref(false);
const videoTagFile = ref();
const selectedMenu = ref('pages');
const frameToDelete = ref(null);
const currentLayerId = ref(route.query.layerId);
const videoPreviewUrl = ref(null);
const isChangingVideo = ref(false);
const existingVideoTag = ref(null);
const tempPreviewUrl = ref(null);

const uploadVideoLoader = ref(false);

const sceneKey = ref(0);

/* Methods */

const getSceneById = async () => {
  console.log(isMasterScene.value);

  if (isMasterScene.value && sceneId.value) {
    try {
      const res = await getMasterScene(sceneId.value);
      sceneDetails.value = res;
    } catch (error) {
      console.error('Error fetching master scene:', error);
    }
  } else if (projectId.value && sceneId.value) {
    try {
      const res = await getScene(projectId.value, sceneId.value);
      sceneDetails.value = res;
      console.log(res);
      // sceneKey.value++
    } catch (error) {
      console.error('Error fetching scene:', error);
    }
  }
};

getSceneById();

document.addEventListener('refreshAddProjectSvg', () => { // After adding new svgs
  getSceneById();
});

// Rotatable || Rotatable Image Frames scene
const sortByOrder = (data) => {
  let sortSceneFrames = {};
  if (Object.keys(data)){
    sortSceneFrames =  Object.entries(data).sort((a, b) => a[1].sceneData.order - b[1].sceneData.order);
  }
  return Object.fromEntries(sortSceneFrames);
};
// If(projectStore.scenes[sceneId.value].sceneData.type !== 'rotatable_image')
const getSceneFrames = (project_id, scene_id) => {
  if (isMasterScene.value){
    getAllMasterScenesFrames(scene_id).then((res) => {
      sceneFrameList.value = sortByOrder(res);
    });
  } else {
    getAllScenesFrames(project_id, scene_id).then((res) => {
      sceneFrameList.value = sortByOrder(res);
    });
  }
};

watch(sceneDetails, (val) => {
  if (val.sceneData.type==='rotatable_image'){
    getSceneFrames(projectId.value, sceneId.value);
  } else if (val.sceneData.type==='rotatable_image_frame'){
    getSceneFrames(projectId.value, val.sceneData.parent);
  }
});

const handleRotatableSceneSwitch = (elem) => {
  if (isMasterScene.value){
    router.push({ path: `/masterscenes/${elem.id}`, query: {} });
  } else {
    router.push({ path: `/projects/${projectId.value}/design/scenes/${elem.id}`, query: {} });
  }
};

document.addEventListener('getRotatableImageFrames', () => { // After creating new frame
  getSceneFrames(projectId.value, sceneId.value);
});

// Reorder Methods
const frameTheArrayOfItems = () => {
  if (Object.keys(sceneFrameList.value).length > 0){
    console.log(sceneFrameList.value);
    const generateArrayofItems =Object.values(sceneFrameList.value).map((item, index) => {
      return {
        'id': item.sceneData._id,
        'order': item.sceneData.order ? item.sceneData.order : index + 1,
        'name': item.sceneData.name,
        'low_resolution': item.sceneData.background.low_resolution,
      };
    });
    originalListOfItems.value = [...generateArrayofItems];
    listOfItems.value = generateArrayofItems;
  }
};

if (sceneFrameList.value){
  frameTheArrayOfItems(); // Initialize
}

watch(() => sceneFrameList.value, () => {
  frameTheArrayOfItems();
});

/* function rendername (url){
  var strings = url.split("%2F");
  var file_name = strings[strings.length-1];
  if (file_name.includes('?')) {
    file_name = file_name.split('?')[0];
  }
  file_name = file_name.replaceAll('%20', ' ');
  file_name = file_name.replaceAll('_thumb', '');
  return file_name;
} */

const onClickOptions = [
  { label: 'Move To Next Page', value: 'Move To Next Page' },
  { label: 'Open Media', value: 'Open Media' },
];
const selected = ref('1');

if (route.query.svgId){
  selectedMenu.value = 'layers';
} else {
  selectedMenu.value = 'pages';
}

watch(() => route.query.svgId, () => {
  if (route.query.svgId) {
    selectedMenu.value = 'layers';
  } else {
    selectedMenu.value = 'pages';
  }
});

watch(
  () => route.path,
  (newPath) => {
    if (newPath.endsWith('/layers')) {
      console.log('Route ends with /layers', newPath.endsWith('/layers'));
      selectedMenu.value = 'layers';
    } else {
      selectedMenu.value = 'pages';
    }
  },
  { immediate: true }, // in case it's already on that route when mounted
);

watch(() => sceneDetails.value, (newVal) => {
  if (newVal?.svgData) {
    const currentSvgId = route.query.svgId;
    if (currentSvgId && newVal.svgData[currentSvgId]) {
      const currentLayerId = route.query.layerId;
      const layers = newVal.svgData[currentSvgId].layers;

      if (layers && layers[currentLayerId]) {
        const currentLayer = layers[currentLayerId];
        if (currentLayer.video_tag) {
          existingVideoTag.value = currentLayer.video_tag;
          videoPreviewUrl.value = currentLayer.video_tag;
        } else {
          existingVideoTag.value = null;
          videoPreviewUrl.value = null;
        }
      }
    }
  }
}, { immediate: true });

watch(() => videoTagFile.value, (newFile) => {
  if (newFile) {
    // Create temporary preview URL for the selected file
    if (tempPreviewUrl.value) {
      URL.revokeObjectURL(tempPreviewUrl.value); // Clean up old preview if exists
    }
    tempPreviewUrl.value = URL.createObjectURL(newFile);
    // Show preview for newly selected file
    videoPreviewUrl.value = tempPreviewUrl.value;
  } else {
    // Clean up the temporary URL if no file is selected
    if (tempPreviewUrl.value) {
      URL.revokeObjectURL(tempPreviewUrl.value);
      tempPreviewUrl.value = null;
    }
  }
});

const handleVideoChange = () => {
  isChangingVideo.value = true;
  videoTagFile.value = null;
  // Hide the preview when changing video
  videoPreviewUrl.value = null;
};

const handleCancelFileSelection = () => {
  videoTagFile.value = null;
  // Clean up temporary preview
  if (tempPreviewUrl.value) {
    URL.revokeObjectURL(tempPreviewUrl.value);
    tempPreviewUrl.value = null;
  }
  // If we were in change video mode, restore the original video
  if (isChangingVideo.value) {
    videoPreviewUrl.value = existingVideoTag.value;
    isChangingVideo.value = false;
  } else {
    // For new uploads, just clear the preview
    videoPreviewUrl.value = null;
  }
};

const handleUploadVideoTag = (values) => {
  uploadVideoLoader.value = true;
  const formData = new FormData();
  formData.append('layer_id', route.query.layerId);
  if (!isMasterScene.value){
    formData.append('project_id', route.params.project_id);
  }
  formData.append('scene_id', sceneId.value);
  formData.append('svg_id', route.query.svgId);

  // Case 3: If changing video and no new file selected, submit null
  if (isChangingVideo.value && !videoTagFile.value) {
    formData.append('video_tag', null);
  } else if (values.video_tag) {
    formData.append('video_tag', values.video_tag);
  }

  const updatePromise = isMasterScene.value ?
    updateMasterLayersVideoTag(formData) :
    updateLayersVideoTag(formData);

  updatePromise.then(() => {
    console.log("success");
    videoTagFile.value = null;
    isChangingVideo.value = false;
    // Clean up temporary preview
    if (tempPreviewUrl.value) {
      URL.revokeObjectURL(tempPreviewUrl.value);
      tempPreviewUrl.value = null;
    }
    uploadVideoLoader.value = false;
    // Refresh scene data to get updated video tag
    getSceneById().then(() => {
      const currentSvgId = route.query.svgId;
      const currentLayerId = route.query.layerId;
      if (sceneDetails.value?.svgData?.[currentSvgId]?.layers?.[currentLayerId]) {
        const updatedLayer = sceneDetails.value.svgData[currentSvgId].layers[currentLayerId];
        // Always update preview with the new video URL
        videoPreviewUrl.value = updatedLayer.video_tag || null;
        existingVideoTag.value = updatedLayer.video_tag || null;
      }
    });
  }).catch((err) => {
    uploadVideoLoader.value = false;
    videoTagFile.value = null;
    console.log('Error');
    console.log('output->err in updateSceneLayer', err);
  });
};

// const setupData = (data) => {
//   console.log("setupData", data);
//   if (data){
//     // Structuring your data's
//     const convertedData = createCategoryBasedSceneData(data);

//     console.log(convertedData);

//     // Update the your component reference
//     SceneReOrderData.value = convertedData;
//   }
// };

// Child Sort
const handleChildSortEmit = (val) => {
  console.log("-----------------------------");
  console.log('Parent~ChildSort', val);
  console.log("-----------------------------");

  const reOrderedItems = [];
  reOrderedItems.push(val.draggedItem);
  val.sortedItems.forEach((item) => {
    reOrderedItems.push(item);
  });

  if (isMasterScene.value){
    const reqBody = {
      'query': reOrderedItems,
    };
      // Api Call
    updateBulkMasterSceneFrames(reqBody).then(() => {
      getAllMasterScenesFrames(sceneId.value).then((res) => {
        sceneFrameList.value = sortByOrder(res);
        // reorderFrames.value = false;
      });
    });
  } else {
    const reqBody = {
      'query': reOrderedItems,
      'project_id': projectId.value,
    };
      // Api Call
    updateBulkSceneFrames(reqBody).then(() => {
      getAllScenesFrames(projectId.value, sceneId.value).then((res) => {
        sceneFrameList.value = sortByOrder(res);
      });
    });
  }

};

const handleDeleteFrames = () => {
  console.log('Delete Frame', frameToDelete.value);
  const payload = {
    scene_id: [frameToDelete.value],
    timeStamp: Date.now(),
  };
  moveSceneToTrash(projectId.value, payload).then(() => {
    // handleGetListOfScenes();
    // handleGetAllTrash();
    console.log('Delete Frame', frameToDelete.value);
    console.log('Frame Deleted', sceneFrameList.value[frameToDelete.value]);
    window.location = `/projects/${projectId.value}/design/scenes/${sceneFrameList.value[frameToDelete.value].sceneData.parent}`;
  });
};

onClickOutside(deleteFrameRef, () => {
  frameToDelete.value = null;
});

const reRenderScene = (newSceneId) => {
  sceneId.value = newSceneId;
  getSceneById().then(() => {
    sceneKey.value++;
  });
};

watch(() => route.params.scene_id, (newSceneId) => {
  if (newSceneId) {
    reRenderScene(newSceneId);
    console.log('Scene ID detected in route:', newSceneId, sceneKey.value);
  } else {
    console.log('No sceneId in current route.');
  }
});

document.addEventListener('refreshAfterSceneEdit', () => { // After adding new svgs
  reRenderScene(sceneId.value);
});

const scrollFrameLeft = () => {
  const scrollContainer = document.getElementById('frames-container');
  scrollContainer.scrollBy({ left: -150, behavior: 'smooth' });
};

const scrollFrameRight = () => {
  const scrollContainer = document.getElementById('frames-container');
  scrollContainer.scrollBy({ left: 150, behavior: 'smooth' });
};

watch(() => route.query.layerId, (newLayerId) => {
  if (newLayerId) {
    currentLayerId.value = newLayerId;
    // Reset video states when layer changes
    videoTagFile.value = null;
    isChangingVideo.value = false;

    // Clean up any temporary preview
    if (tempPreviewUrl.value) {
      URL.revokeObjectURL(tempPreviewUrl.value);
      tempPreviewUrl.value = null;
    }

    // Check if the new layer has a video tag
    if (sceneDetails.value?.svgData) {
      const currentSvgId = route.query.svgId;
      if (currentSvgId && sceneDetails.value.svgData[currentSvgId]) {
        const layers = sceneDetails.value.svgData[currentSvgId].layers;
        if (layers && layers[newLayerId]) {
          const currentLayer = layers[newLayerId];
          if (currentLayer.video_tag) {
            existingVideoTag.value = currentLayer.video_tag;
            videoPreviewUrl.value = currentLayer.video_tag;
          } else {
            existingVideoTag.value = null;
            videoPreviewUrl.value = null;
          }
        } else {
          existingVideoTag.value = null;
          videoPreviewUrl.value = null;
        }
      }
    }
  } else {
    currentLayerId.value = null;
    // Reset video states when no layer is selected
    existingVideoTag.value = null;
    videoPreviewUrl.value = null;
    videoTagFile.value = null;
    isChangingVideo.value = false;
    if (tempPreviewUrl.value) {
      URL.revokeObjectURL(tempPreviewUrl.value);
      tempPreviewUrl.value = null;
    }
  }
}, { immediate: true });

// drag to resize
const container = ref(null);
const left = ref(null);
const right = ref(null);

const BASE = 200;
const MIN = 200;
const MAX = 600;
const MIDDLE_MIN = 50;

let isDraggingLeft = false;
let isDraggingRight = false;

const onMouseMove = (e) => {
  if (isDraggingLeft || isDraggingRight) {
    e.preventDefault(); // Prevent default selection behavior
    if (!container.value) {
      return;
    }

    const totalWidth = container.value.clientWidth;
    const leftWidth = left.value.offsetWidth;
    const rightWidth = right.value.offsetWidth;

    if (isDraggingLeft) {
      const containerLeft = container.value.getBoundingClientRect().left;
      const newLeft = Math.min(Math.max(e.clientX - containerLeft, MIN), MAX);
      const delta = newLeft - leftWidth;

      let newRight = rightWidth;

      if (delta > 0) {
        // left is expanding
        if (rightWidth > BASE) {
          newRight = Math.max(MIN, rightWidth - delta);
          const totalUsed = newLeft + newRight;
          if (totalWidth - totalUsed >= MIDDLE_MIN) {
            left.value.style.width = `${newLeft}px`;
            right.value.style.width = `${newRight}px`;
          }
        } else {
          // right is already at base, don't shrink it
          const totalUsed = newLeft + rightWidth;
          if (totalWidth - totalUsed >= MIDDLE_MIN) {
            left.value.style.width = `${newLeft}px`;
          }
        }
      } else {
        // shrinking left
        const totalUsed = newLeft + rightWidth;
        if (totalWidth - totalUsed >= MIDDLE_MIN) {
          left.value.style.width = `${newLeft}px`;
        }
      }
    }

    if (isDraggingRight) {
      const containerRight = container.value.getBoundingClientRect().right;
      const newRight = Math.min(Math.max(containerRight - e.clientX, MIN), MAX);
      const delta = newRight - rightWidth;

      let newLeft = leftWidth;

      if (delta > 0) {
        // right is expanding
        if (leftWidth > BASE) {
          newLeft = Math.max(MIN, leftWidth - delta);
          const totalUsed = newLeft + newRight;
          if (totalWidth - totalUsed >= MIDDLE_MIN) {
            right.value.style.width = `${newRight}px`;
            left.value.style.width = `${newLeft}px`;
          }
        } else {
          // left is already at base
          const totalUsed = leftWidth + newRight;
          if (totalWidth - totalUsed >= MIDDLE_MIN) {
            right.value.style.width = `${newRight}px`;
          }
        }
      } else {
        // shrinking right
        const totalUsed = leftWidth + newRight;
        if (totalWidth - totalUsed >= MIDDLE_MIN) {
          right.value.style.width = `${newRight}px`;
        }
      }
    }
  }
};

const stopDrag = () => {
  console.log('stopDrag');
  isDraggingLeft = false;
  isDraggingRight = false;
  document.body.classList.remove('no-select');
  window.removeEventListener('mousemove', onMouseMove);
  window.removeEventListener('mouseup', stopDrag);
};

const startDrag = (side, e) => {
  if (e) {
    e.preventDefault();
  } // Prevent text selection on mousedown
  if (side === 'left') {
    isDraggingLeft = true;
  }
  if (side === 'right') {
    isDraggingRight = true;
  }
  document.body.classList.add('no-select');
  window.addEventListener('mousemove', onMouseMove);
  window.addEventListener('mouseup', stopDrag);
};

</script>

<template>
    <div ref="container" class="flex-1 flex justify-evenly h-full w-full overflow-hidden gap-2 relative z-10">
      <div class="w-[200px] h-full relative z-20 ">
        <div class="h-full flex absolute top-0 left-0 z-100">
      <div ref="left" class="w-full min-w-[200px]">
  <CategorySideBar :selectedMenu="selectedMenu"/>
  </div>
  <div class="w-2 cursor-col-resize bg-gray-300 relative flex justify-center items-center z-10" @mousedown="(e) => startDrag('left', e)" >
    <div class="w-3 h-7 rounded-sm bg-gray-200 absolute z-30 flex justify-evenly items-center">
      <div class="h-3 w-[2px] bg-gray-500 rounded-md"></div>
      <div class="h-3 w-[2px] bg-gray-500 rounded-md"></div>
    </div>
  </div>
  </div>
  </div>
  <DeleteConfirmationModal ref="deleteFrameRef" v-if="frameToDelete" class="fixed left-1/2 -translate-x-1/2 top-1/2 transform -translate-y-1/2 z-100" @handleDelete="handleDeleteFrames" @close="frameToDelete=null"/>

<!-- scene section -->
<div class="flex-1 h-full overflow-hidden flex flex-col justify-start items-start bg-white gap-2">
    <div class="h-[90%] w-full flex-none">

  <div  v-if="sceneDetails" class="h-full overflow-hidden">
    <div v-if="sceneDetails.sceneData.type !== 'earth'" class="bg-neutral-900 h-full w-full text-white">
        <div class="h-full flex justify-center items-center text-white relative z-0"
           >
           <SafeAreaHighlight :show="safeFrame" :safePadding="{ top: '70px', bottom: '70px', left: '80px', right: '80px' }">
            <SvgOverlay :key="sceneKey"  ref="containerRef" v-if="sceneDetails.sceneData.type === 'image' || sceneDetails.sceneData.type === 'identical_unitplan' || sceneDetails.sceneData.type === 'rotatable_image_frame'"
                style="height:100%;width:100%"
                :Data="sceneDetails"
                :sceneId="sceneId"
                :projectId="projectId"
                bucketURL="propvr-in-31420.appspot.com"
                replaceURL="storagecdn.propvr.ai" />
              </SafeAreaHighlight>

            <!-- Rotatable Image Frames -->
            <!-- <div v-if="sceneDetails.sceneData.type === 'rotatable_image_frame' || sceneDetails.sceneData.type === 'rotatable_image'" class="absolute w-full h-56 bottom-0 z-[765456789] flex flex-col justify-end">
                <div class="flex justify-end items-center px-2 flex-1 gap-3">
                  <button type="button" v-if="sceneDetails?.sceneData.type === 'rotatable_image'"
            @click="() =>  isMasterScene ?  router.push(`/masterscenes/${sceneId}/createframe`) : router.push(`/projects/${projectId}/scenes/${sceneId}/createframe`)"
            class="createButton inline-flex justify-center items-center rounded-md mt-2  bg-blue-600 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 px-3 py-2 text-sm font-semibold shadow-sm">
            <PlusIcon class="h-4 w-4 mr-2 text-white" aria-hidden="true" /> Create new frame
        </button>
                          <button v-if="reorderFrames && sceneFrameList && Object.keys(sceneFrameList).length" type="button"
                            class="cancel-btn-primary bg-slate-500"
                            @click="() =>reorderFrames = false">cancel</button>
                          <button type="button" v-if="sceneFrameList && Object.keys(sceneFrameList).length > 1" :disabled="reorderFrames && !compareTheOriginalListOfItems() ? true : false" @click="reorderFrames ? handleSubmit() : reorderFrames = true"
                            class="proceed-btn-primary">{{ !reorderFrames?'ReOrder Items':'Save' }}
                            <Spinner v-if="loader" />
                          </button>
                </div>
                <div v-if="!reorderFrames" class="w-full h-40 px-3 py-2 border left-0 flex  items-center  gap-2 overflow-x-scroll bg-white ">
                    <div  v-for="elem,ind in sceneFrameList"  @click="handleRotatableSceneSwitch(elem)" class="relative flex justify-center items-center z-10 w-52 h-full bg-black rounded-md flex-shrink-0" :key="ind">
                        <img :src="elem.sceneData.background.low_resolution" alt="image" class="h-full w-full fit-cover rounded-md cursor-pointer"/>
                        <div class="absolute z-40  text-black w-full h-fit p-1 flex justify-center items-center cursor-pointer bg-gray-100 bg-opacity-50">
                                        <p class="text-md font-semibold">{{ elem.sceneData.name }}</p>
                                      </div>
                                    </div>
                </div>
                <DnDrop v-else-if="listOfItems" orientation="horizontal" :animationDuration="Number('200')" @on-drop="handleDrop" style="display: flex;" class="text-black w-full h-40 px-3 py-4 border left-0 flex items-center  gap-5 overflow-x-scroll bg-white" >
                                <template #items>
                                    <Draggable v-for="item in listOfItems" class="cursor-pointer w-52 h-full bg-black rounded-md flex-shrink-0" :key="item._id">
                                      <div class="relative">
                                        <img :src="item.low_resolution" alt="image" class="h-full w-full fit-cover rounded-md cursor-pointer"/>
                                        <div class="absolute z-40  text-black w-full h-fit p-1 flex justify-center items-center bg-gray-100 bg-opacity-50">
                                        <p class="text-md font-semibold">{{ item.name }}</p>
                                      </div>
                                      </div>

                                    </Draggable>
                                </template>
                </DnDrop>
            </div> -->
      </div>
    </div>
    <div v-else class="bg-neutral-900 h-full w-full text-white">

      <GlobeComponent v-if="sceneDetails" :scene="sceneDetails"></GlobeComponent>

    </div>

  </div>

   </div>
  <div class="h-[10%] w-full flex flex-col justify-center flex-none" >
  <div class="w-full flex items-center px-3 min-h-20 bg-white gap-2" :class="sceneDetails?.sceneData.type === 'rotatable_image_frame' || sceneDetails?.sceneData.type === 'rotatable_image' ? '' : 'justify-end'">
    <div v-if="sceneDetails?.sceneData.type === 'rotatable_image_frame' || sceneDetails?.sceneData.type === 'rotatable_image'" class="flex-1 overflow-scroll w-full h-full flex items-center gap-4 px-4">
      <button @click="scrollFrameLeft()" class="h-8 w-8 rounded-md shadow-md bg-white border border-gray-200 flex justify-center items-center">
        <svg class="w-5 h-5 fill-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-back"><rect width="24" height="24" transform="rotate(90 12 12)" opacity="0"/><path d="M13.83 19a1 1 0 0 1-.78-.37l-4.83-6a1 1 0 0 1 0-1.27l5-6a1 1 0 0 1 1.54 1.28L10.29 12l4.32 5.36a1 1 0 0 1-.78 1.64z"/></g></g></svg>
      </button>

          <div v-if="!reorderFrames" id="frames-container" class="frames-container flex-1 max-w-full h-20 overflow-x-auto overflow-y-hidden flex items-center gap-2 px-2">
            <NestedReOrder :containerClasses="'flex items-center gap-2'" v-model="listOfItems" groupName="scenesFrames" :allowChildReparenting="true" :allowChildSort="true" uniqueKey="id" nestedChildKey="linked_scenes" ghostClass="sampe_ghost" animationMilliSec="450" sortReferenceKey="id" @handleChildSort="(val) => handleChildSortEmit(val) " @handleChildReparentingSort="(val) => handleChildReparentingSortEmit(val)">
              <template #default="{item}" >
                        <div  @click="handleRotatableSceneSwitch(item)" class="flex flex-col justify-center rounded-md flex-shrink-0">
                          <div class="w-14 h-14 relative">
                            <img :src="item.low_resolution" alt="image" class="h-full w-full fit-cover rounded-md cursor-pointer"/>
                            <button @click.stop="frameToDelete=item.id" class="fill-red-600 p-1 bg-white rounded-full absolute top-0.5 right-0.5 z-10">
                              <svg class="h-3 w-3" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="trash-2"><rect width="24" height="24" opacity="0"/><path d="M21 6h-5V4.33A2.42 2.42 0 0 0 13.5 2h-3A2.42 2.42 0 0 0 8 4.33V6H3a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2zM10 16a1 1 0 0 1-2 0v-4a1 1 0 0 1 2 0zm0-11.67c0-.16.21-.33.5-.33h3c.29 0 .5.17.5.33V6h-4zM16 16a1 1 0 0 1-2 0v-4a1 1 0 0 1 2 0z"/></g></g></svg></button></div>

                            <p class="w-14 whitespace-nowrap text-ellipsis overflow-hidden text-[10px] text-gray-900 font-semibold">{{ item.name }}</p>
                          </div>
                        </template>
                        </NestedReOrder>

                                    </div>
        <button @click="scrollFrameRight()" class="h-8 w-8 rounded-md shadow-md bg-white border border-gray-200 flex justify-center items-center">
        <svg class="w-5 h-5 fill-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-forward"><rect width="24" height="24" transform="rotate(-90 12 12)" opacity="0"/><path d="M10 19a1 1 0 0 1-.64-.23 1 1 0 0 1-.13-1.41L13.71 12 9.39 6.63a1 1 0 0 1 .15-1.41 1 1 0 0 1 1.46.15l4.83 6a1 1 0 0 1 0 1.27l-5 6A1 1 0 0 1 10 19z"/></g></g></svg>
      </button>
    </div>
    <div v-if="sceneDetails?.sceneData.type === 'rotatable_image_frame' || sceneDetails?.sceneData.type === 'rotatable_image'"    class="flex flex-col justify-center items-center rounded-md flex-shrink-0 cursor-pointer">
                          <div @click="() =>  isMasterScene ?  router.push(`/masterscenes/${sceneId}/createframe`) : router.push(`/projects/${projectId}/design/scenes/${sceneId}/createframe`)" class="w-14 h-14 border-[2px] border-dashed border-blue-600 rounded-md flex justify-center items-center cursor-pointe">

<svg class="h-5 w-5 fill-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                          </div>

                            <p class="whitespace-nowrap text-ellipsis overflow-hidden text-[10px] text-blue-600 font-semibold">Add</p>
                          </div>
    <div class="w-40 flex items-center gap-3">

<div class="flex items-center gap-2 ">
  <div class="flex justify-between items-center gap-2 rounded-b-md cursor-pointer hover:bg-blue-50">

                                    <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                            <input id="safe_area" v-model="safeFrame" class="sr-only peer" name="safe_area"
                                                type="checkbox" :value="true" />
                                            <label for="safe_area"
                                                class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[3px] after:bg-white after:rounded-full after:h-[19px] after:w-[19px] after:transition-all peer-checked:bg-blue-600 cursor-pointer">
                                            </label>
                                        </div>
                                        <label for="safe_area" class="text-sm text-gray-900 mb-0">Safe Frame</label>
                  </div>
  <svg class="h-4 w-4 fill-gray-500" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_16947)">
<path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z"/>
</g>
<defs>
<clipPath id="clip0_723_16947">
<rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

</div>
    </div>
  </div>
  </div>
</div>
<div class="w-[200px] h-full relative z-20">
        <div class="h-full flex absolute top-0 right-0 z-100">
  <div class="w-2 cursor-col-resize bg-gray-300 relative flex justify-center items-center z-10" @mousedown="(e) => startDrag('right', e)" >
    <div class="w-3 h-7 rounded-sm bg-gray-200 absolute z-30 flex justify-evenly items-center">
      <div class="h-3 w-[2px] bg-gray-500 rounded-md"></div>
      <div class="h-3 w-[2px] bg-gray-500 rounded-md"></div>
    </div>
  </div>
<!-- right section -->
       <div ref="right" class="h-full bg-white p-2 w-full min-w-[200px]">
        <!-- content -->
        <div  class="">

<div v-if="$route.query.layerId && sceneDetails?.sceneData?.type !== 'earth'">
        <div class="my-2.5">
          <Form @submit="handleUploadVideoTag" :validation-schema="uploadVideoTagValidation">
            <div class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit row-span-2 mt-2.5">
              <label for="video_tag" class="mb-2 text-sm font-semibold text-gray-900">Video Tag</label>

              <!-- Video Preview Section - Show when we have a preview URL -->
              <div v-if="videoPreviewUrl" class="w-full mb-2">
                <video
                  class="w-full rounded-lg"
                  controls
                  :src="videoPreviewUrl"
                ></video>
              </div>

              <!-- Upload/Change Video Section - Show only when no video is present -->
              <div v-if="!videoPreviewUrl" class="w-full">
                <label class="mb-0 w-full h-9 rounded-lg border-2 border-gray-200 py-3 px-1 cursor-pointer">
                  <div class="h-full w-full flex justify-center items-center gap-2">
                    <svg class="h-5 w-5 fill-gray-500" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.2949 5.736C10.1636 5.60141 9.98561 5.52579 9.8 5.52579C9.61438 5.52579 9.43637 5.60141 9.3051 5.736L7.7 7.38226V1.21795C7.7 1.02754 7.62625 0.844924 7.49497 0.710282C7.3637 0.575641 7.18565 0.5 7 0.5C6.81435 0.5 6.6363 0.575641 6.50503 0.710282C6.37375 0.844924 6.3 1.02754 6.3 1.21795V7.38226L4.6949 5.736C4.63033 5.66743 4.55309 5.61273 4.46768 5.57511C4.38228 5.53748 4.29043 5.51767 4.19748 5.51685C4.10454 5.51602 4.01236 5.53418 3.92633 5.57028C3.8403 5.60638 3.76215 5.65969 3.69642 5.7271C3.6307 5.79451 3.57872 5.87467 3.54352 5.9629C3.50833 6.05114 3.49062 6.14568 3.49142 6.24101C3.49223 6.33633 3.51154 6.43054 3.54823 6.51814C3.58492 6.60573 3.63824 6.68495 3.7051 6.75118L6.5051 9.62297C6.57012 9.68983 6.64737 9.74288 6.73241 9.77907C6.81746 9.81527 6.90863 9.8339 7.0007 9.8339C7.09277 9.8339 7.18394 9.81527 7.26899 9.77907C7.35403 9.74288 7.43128 9.68983 7.4963 9.62297L10.2963 6.75118C10.4273 6.61635 10.5008 6.43367 10.5006 6.24329C10.5003 6.05292 10.4263 5.87045 10.2949 5.736Z"/>
                      <path d="M12.6 8.75641H10.815L8.7325 10.8923C8.50499 11.1257 8.2349 11.3108 7.93763 11.4371C7.64037 11.5634 7.32176 11.6284 7 11.6284C6.67824 11.6284 6.35963 11.5634 6.06237 11.4371C5.7651 11.3108 5.49501 11.1257 5.2675 10.8923L3.185 8.75641H1.4C1.0287 8.75641 0.672601 8.90769 0.41005 9.17698C0.1475 9.44626 0 9.81148 0 10.1923V13.0641C0 13.4449 0.1475 13.8102 0.41005 14.0794C0.672601 14.3487 1.0287 14.5 1.4 14.5H12.6C12.9713 14.5 13.3274 14.3487 13.5899 14.0794C13.8525 13.8102 14 13.4449 14 13.0641V10.1923C14 9.81148 13.8525 9.44626 13.5899 9.17698C13.3274 8.90769 12.9713 8.75641 12.6 8.75641ZM10.85 13.0641C10.6423 13.0641 10.4393 13.0009 10.2667 12.8826C10.094 12.7643 9.9594 12.5961 9.87993 12.3993C9.80046 12.2025 9.77966 11.986 9.82018 11.7771C9.86069 11.5682 9.96069 11.3763 10.1075 11.2257C10.2544 11.0751 10.4415 10.9725 10.6452 10.9309C10.8488 10.8894 11.06 10.9107 11.2518 10.9922C11.4437 11.0737 11.6077 11.2118 11.723 11.3889C11.8384 11.566 11.9 11.7742 11.9 11.9872C11.9 12.2728 11.7894 12.5467 11.5925 12.7487C11.3955 12.9506 11.1285 13.0641 10.85 13.0641Z"/>
                    </svg>
                    <p class="text-xs font-semibold text-gray-500">Upload Video</p>
                  </div>
                  <div>
                    <Field
                      type="file"
                      v-model="videoTagFile"
                      name="video_tag"
                      id="video_tag"
                      class="hidden"
                      accept="video/*"
                      placeholder="Upload video"
                    />
                  </div>
                </label>
                <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="video_tag" />
              </div>

              <!-- Action Buttons -->
              <div class="mt-2 flex gap-2 w-full">
                <!-- Show Change Video button when we have a preview and not in upload mode -->
                <button
                  v-if="videoPreviewUrl && !videoTagFile && !isChangingVideo"
                  type="button"
                  @click="handleVideoChange"
                  class="h-8 flex-1 text-sm font-medium rounded-lg text-blue-600 flex justify-center items-center border border-blue-600"
                >
                  Change Video
                </button>

                <!-- Single button for both upload and update -->
                <button
                  v-if="videoTagFile || isChangingVideo"
                  type="submit"
                  class="h-8 bg-blue-600 flex-1 text-sm font-medium rounded-lg text-white flex justify-center items-center"
                >
                  {{!uploadVideoLoader ?isChangingVideo? 'Update': 'Upload':''}}
                <Spinner v-if="uploadVideoLoader" class="text-slate-400 fill-white" />
                </button>

                <!-- Show Cancel button when changing video or when a file is selected but not uploaded -->
                <button
                  v-if="isChangingVideo || videoTagFile"
                  type="button"
                  @click.prevent="handleCancelFileSelection"
                  class="h-8 flex-1 text-sm font-medium rounded-lg text-gray-600 flex justify-center items-center border border-gray-300"
                >
                  Cancel
                </button>
              </div>
            </div>
          </Form>
        </div>
        <div class="w-full border border-t border-gray-500"></div>

        <div class="my-3">
          <p class="text-sm leading-tight font-semibold">On Click</p>
          <div class="flex flex-col gap-1 mt-2.5">
    <label v-for="option in onClickOptions" :key="option.value" class="flex items-center cursor-pointer">
      <input
        type="radio"
        v-model="selected"
        :value="option.value"
        class="hidden"
      />
      <div
        class="w-4 h-4 rounded-full border-[1px] flex items-center justify-center transition-colors"
        :class="selected === option.value ? 'border-blue-500 bg-blue-500' : 'border-gray-400'"
      >
        <div class="w-2.5 h-2.5 bg-white rounded-full"></div>
      </div>
      <span class="ml-2 text-xs font-semibold text-gray-900">{{ option.label }}</span>
    </label>
  </div>

  <div
              class="">

              <label for="selectPage" class="w-full h-9 bg-white border border-gray-200 relative flex justify-center items-center gap-2 px-2 mt-1 rounded-md ">
                <div class="border-r border-gray-200 pr-2 h-full flex justify-center items-center">
                <svg class="w-5 h-5 fill-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="file"><rect width="24" height="24" opacity="0"/><path d="M19.74 7.33l-4.44-5a1 1 0 0 0-.74-.33h-8A2.53 2.53 0 0 0 4 4.5v15A2.53 2.53 0 0 0 6.56 22h10.88A2.53 2.53 0 0 0 20 19.5V8a1 1 0 0 0-.26-.67zM14 4l3.74 4h-3a.79.79 0 0 1-.74-.85z"/></g></g></svg>
              </div>
                <p class="w-full text-gray-500 text-xs font-medium">selectPage</p>
                <svg class="w-5 h-5 fill-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-downward"><rect width="24" height="24" opacity="0"/><path d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15 1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16z"/></g></g></svg>

              <Field
                as="select"
                id="selectPage"
                name="selectPage"
                class="absolute inset-0 opacity-0 cursor-pointer w-full h-full flex rounded-lg text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                <option value="" disabled> Choose </option>
                <option value="" disabled
                  v-if="false">
                  No Data found ! </option>
                <option v-else :value="option.value"
                  v-for="option, index in options"
                  :key="index" class="text-black"> {{
                    option.value }} </option>
              </Field>
            </label>
              <ErrorMessage name="selectPage"
                class="text-xs text-rose-500 mt-1" as="p" />
            </div>
        </div>
      </div>

        <!-- <div class="my-2">
        <div class="flex justify-between align-center p-1.5 mb-1.5  rounded-md">
                <div class="flex justify-center items-center"><p class="text-xs text-gray-500">Map Style</p></div>
                <button class="flex justify-center items-center w-6 h-6 rounded-md bg-gray-100 fill-gray-900 cursor-pointer">
                  <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                </button>
          </div>
          <div class=" h-[87px] w-full rounded-md border border-gray-300">

        </div>
        </div>

        <div class="my-2">
        <div class="flex justify-between align-center p-1.5 mb-1.5  rounded-md">
                <p class="text-xs text-gray-500">Map Style</p>
          </div>
          <div class=" h-[170px] w-full rounded-md border border-gray-300">
    </div>
        </div>
        <div class="my-2">
          <p class="text-xs text-gray-500 mb-2">Map Style</p>
        <div class="flex flex-col gap-1.5   rounded-md">
                <button class="h-9 w-fit rounded-md border bg-gray-200 min-w-24 flex items-center px-2.5">
          <p class="text-xs font-medium text-gray-700">Bay 2</p>
        </button>
        <div class="flex items-center gap-2"> <div>

                <label class="flex items-center gap-2 bg-white cursor-text text-xs text-gray-500 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="h-4 w-4 border-[1px] border-gray-300" />
                    Not placed
                </label></div></div>
          </div>
        <div class="flex flex-col gap-1.5   rounded-md">
                <button class="h-9 w-fit rounded-md border bg-gray-200 min-w-24 flex items-center px-2.5">
          <p class="text-xs font-medium text-gray-700">Dubai</p>
        </button>
        <div class="flex items-center gap-2"> <div>

                <label class="flex items-center gap-2 bg-white cursor-text text-xs text-gray-500 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="h-4 w-4 border-[1px] border-gray-300" />
                    Not placed
                </label></div></div>
          </div>

        </div>
        <div class="my-2">
          <p class="text-xs text-gray-500 mb-2">Landmarks</p>
        <div class="flex flex-col gap-1.5   rounded-md">
                <button class="h-9 w-fit rounded-md border bg-gray-200 min-w-24 flex items-center px-2.5">
          <p class="text-xs  font-medium text-gray-700">Dubai Marina</p>
        </button>
        <div class="flex items-center gap-2"> <div>

                <label class="flex items-center gap-2 bg-white cursor-text text-xs text-gray-500 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="h-4 w-4 border-[1px] border-gray-300" />
                    Not placed
                </label></div></div>
          </div>
        <div class="flex flex-col gap-1.5   rounded-md">
                <button class="h-9 w-fit rounded-md border bg-gray-200 min-w-24 flex items-center px-2.5">
          <p class="text-xs  font-medium text-gray-700">Dubai International Airport</p>
        </button>
        <div class="flex items-center gap-2"> <div>

                <label class="flex items-center gap-2 bg-white cursor-text text-xs text-gray-500 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="h-4 w-4 border-[1px] border-gray-300" />
                    Not placed
                </label></div></div>
          </div>
        <div class="flex flex-col gap-1.5   rounded-md">
                <button class="h-9 w-fit rounded-md border bg-gray-200 min-w-24 flex items-center px-2.5">
          <p class="text-xs  font-medium text-gray-700">Burj khalifa</p>
        </button>
        <div class="flex items-center gap-2"> <div>

                <label class="flex items-center gap-2 bg-white cursor-text text-xs text-gray-500 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="h-4 w-4 border-[1px] border-gray-300" />
                    Not placed
                </label></div></div>
          </div>
        <div class="flex flex-col gap-1.5   rounded-md">
                <button class="h-9 w-fit rounded-md border bg-gray-200 min-w-24 flex items-center px-2.5">
          <p class="text-xs  font-medium text-gray-700">Dubai Frame</p>
        </button>
        <div class="flex items-center gap-2"> <div>

                <label class="flex items-center gap-2 bg-white cursor-text text-xs text-gray-500 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="h-4 w-4 border-[1px] border-gray-300" />
                    Not placed
                </label></div></div>
          </div>
        <div class="flex flex-col gap-1.5   rounded-md">
                <button class="h-9 w-fit rounded-md border bg-gray-200 min-w-24 flex items-center px-2.5">
          <p class="text-xs  font-medium text-gray-700">Dubai School 1</p>
        </button>
        <div class="flex items-center gap-2"> <div>

                <label class="flex items-center gap-2 bg-white cursor-text text-xs text-gray-500 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="h-4 w-4 border-[1px] border-gray-300" />
                    Not placed
                </label></div></div>
          </div>
        <div class="flex flex-col gap-1.5   rounded-md">
                <button class="h-9 w-fit rounded-md border bg-gray-200 min-w-24 flex items-center px-2.5">
          <p class="text-xs  font-medium text-gray-700">Dubai School 2</p>
        </button>
        <div class="flex items-center gap-2"> <div>

                <label class="flex items-center gap-2 bg-white cursor-text text-xs text-gray-500 py-0">
                    <input type="checkbox" name="is_enabled" v-bind="field" :value="true" class="h-4 w-4 border-[1px] border-gray-300" />
                    Not placed
                </label></div></div>
          </div>

        </div> -->

      </div>

      </div>
      </div>
      </div>
    </div>

    </template>

<style scoped>

.item {
  padding: 8px;
  background: #f0f0f0;
  margin-bottom: 5px;
}
.sub-item {
  padding: 5px;
  margin-left: 20px;
  background: #d3d3d3;
}

.frames-container div{
  display: flex;
  align-items: center;
}

.left {
  width: 200px;
}

.right {
  width: 200px;
}

.no-select {
  user-select: none !important;
}
</style>

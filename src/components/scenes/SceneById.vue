<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import { getScene } from '../../api/projects/scene/index.ts';
import { getScene as getMasterScene } from '../../api/masterScene/index';
import { isMasterScenePath } from '../../helpers/helpers';
import ImageScene from './ImageScene/ImageScene.vue';
import Gsplat from './Gsplat/Gsplat.vue';
import Deepzoom from './Deepzoom/Deepzoom.vue';

const route = useRoute();
const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const sceneDetails = ref();

const getSceneById = () => {
  console.log('getSceneById', projectId.value, sceneId.value, isMasterScene.value);
  if (isMasterScene.value && sceneId.value) {
    getMasterScene(sceneId.value).then((res) => {
      sceneDetails.value = res;
      console.log('getMasterScene', res);
    });
  } else if (projectId.value && sceneId.value) {
    getScene(projectId.value, sceneId.value).then((res) => {
      sceneDetails.value = res;
      console.log('getScene', res);
    });
  } else {
    console.log('else');
    return;
  }
};

getSceneById();

watch(() => route.params.scene_id, (newSceneId) => {
  if (newSceneId) {
    console.log('sceneId changed');
    sceneId.value = newSceneId;
    getSceneById();
  }
});

document.addEventListener('refreshAddProjectSvg', () => { // After adding new svgs
  getSceneById();
});

</script>

<template>
  <Gsplat v-if="sceneDetails?.sceneData.type === 'gsplat'"/>
  <Deepzoom v-else-if="sceneDetails?.sceneData.type === 'deep_zoom'"/>
  <!-- <GlobeComponent v-else-if="sceneDetails?.sceneData.type === 'earth'"  :scene="sceneDetails" /> -->
  <ImageScene v-else :selectedMenu="selectedMenu"/>
</template>

<style scoped>

.item {
  padding: 8px;
  background: #f0f0f0;
  margin-bottom: 5px;
}
.sub-item {
  padding: 5px;
  margin-left: 20px;
  background: #d3d3d3;
}

.frames-container div{
  display: flex;
  align-items: center;
}
</style>

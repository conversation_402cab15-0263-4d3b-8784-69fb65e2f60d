<script setup>
import { onMounted, ref, watch } from 'vue';
import Spinner from '../common/Spinner.vue';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { CreateCoordinatesSchema } from '../../validationSchema/scene/coordinates';
import { Org_Store } from '../../store/organization';
import { getListofScenes } from '../../api/projects/scene';
import {masterSceneCoordinateLinkType} from '../../enum';

const newOrganizationStore = Org_Store();
const selectedProjectScenes = ref(null);
const dataLoader = ref(false);
const props = defineProps({
  loader: Boolean,
  sceneId: String,
  dataName: String,
  coordinateValues: Object,
});

const emit = defineEmits(['closeModal', 'handleSubmit']);

const initialValues = ref();

// Edit case
const setUpDataCallBack = () => {
  initialValues.value = {
    coordinateName: props.coordinateValues?.name ? props.coordinateValues.name :null,
    latitude: props.coordinateValues?.lat ? props.coordinateValues.lat : null,
    longitude: props.coordinateValues?.lng ? props.coordinateValues.lng  :null,
    linkType: props.coordinateValues?.linkType ? props.coordinateValues.linkType :'master',
    link: props.coordinateValues?.link ?  props.coordinateValues.link : null,
    scene_id: props.coordinateValues?.scene_id ? props.coordinateValues.scene_id : null,
    project_id: props.coordinateValues?.project_id ? props.coordinateValues.project_id : null,
    isActive: props.coordinateValues?.active ? props.coordinateValues.active : false,
  };
};

setUpDataCallBack(); // initialize

const compareValues = (sourceObj, compareObj) => {
  const newObj = {};
  Object.keys(sourceObj).forEach((key) => {
    if (key === 'others'){
      // linktype related fields
      if (sourceObj[key].linkType !== compareObj[key].linkType){
        // different types
        if (sourceObj[key].linkType === masterSceneCoordinateLinkType.MASTER){
          newObj.linkType = masterSceneCoordinateLinkType.MASTER;
          newObj.scene_id = sourceObj[key].scene_id;
        } else if (sourceObj[key].linkType === masterSceneCoordinateLinkType.PROJECT){
          newObj.linkType = masterSceneCoordinateLinkType.PROJECT;
          newObj.scene_id = sourceObj[key].scene_id;
          newObj.project_id = sourceObj.project_id;
        } else if (sourceObj[key].linkType === masterSceneCoordinateLinkType.EXTERNAL){
          newObj.linkType = masterSceneCoordinateLinkType.EXTERNAL;
          newObj.link = sourceObj[key].link;
        }
      } else {
        // same
        console.log("Else");
        console.log(key);
        console.log(sourceObj);

        console.log(sourceObj[key].linkType);
        console.log(masterSceneCoordinateLinkType);

        if (sourceObj[key].linkType === masterSceneCoordinateLinkType.MASTER){
          if (sourceObj[key].scene_id !== compareObj[key].scene_id ){
            newObj.scene_id = sourceObj[key].scene_id;
          }
        }

        if (sourceObj[key].linkType === masterSceneCoordinateLinkType.PROJECT){
          console.log("Yes Project");

          if (sourceObj[key].scene_id !== compareObj[key].scene_id ){
            console.log("Change scene_id");

            newObj.scene_id = sourceObj[key].scene_id;
          }
          if (sourceObj[key].project_id !== compareObj[key].project_id ){
            console.log("Change project_id");
            newObj.project_id = sourceObj[key].project_id;
          }
        }

        if (sourceObj[key].linkType === masterSceneCoordinateLinkType.EXTERNAL){
          if (sourceObj[key].link !== compareObj[key].link ){
            newObj.link = sourceObj[key].link;
          }
        }

      }
    } else {
      if (sourceObj[key] !== compareObj[key]){

        newObj[key] = sourceObj[key];

      }
    }
  });
  return newObj;
};

const getProjectScenes = async (project_id) => {
  console.log("getProjectScenes", project_id);

  dataLoader.value = true; // loader
  if (!project_id){
    selectedProjectScenes.value = null;
    dataLoader.value = false; // loader
    return;
  }

  try {
    const scenes = await getListofScenes(project_id);
    selectedProjectScenes.value = scenes;
    dataLoader.value = false; // loader
  } catch {
    selectedProjectScenes.value = null;
    dataLoader.value = false; // loader
  }

};

if (props.coordinateValues?.linkType === masterSceneCoordinateLinkType.PROJECT){
  getProjectScenes(props.coordinateValues.project_id);
}

// watch project id changes and update the project scenes
watch(() => initialValues.value.project_id, async (val) => {
  console.log("Yes changing");
  getProjectScenes(val);
});

watch(() => props.coordinateValues, () => {
  console.log("watching coordinateValues Changes");
  setUpDataCallBack(); // call back the data callback
});

const handleSubmit = (values) => {
  console.log(values);

  if (values) {
    if (values.isActive === undefined || values.isActive === null){
      values.isActive = false;
    }

    if (props.coordinateValues){

      // Update
      const previousValues = {
        name: props.coordinateValues.name,
        lat: props.coordinateValues.lat,
        lng: props.coordinateValues.lng,
        active: props.coordinateValues.active,
        others: {
          linkType: props.coordinateValues.linkType,
          ...(props.coordinateValues?.link && {link: props.coordinateValues.link}),
          ...(props.coordinateValues?.scene_id && {scene_id: props.coordinateValues.scene_id}),
          ...(props.coordinateValues?.project_id && {project_id: props.coordinateValues.project_id}),
        },
      };

      const sourceObj = {
        name: values.coordinateName,
        lat: String(values.latitude),
        lng: String(values.longitude),
        active: values.isActive,
        others: {
          linkType: values.linkType,
          ...(values.link && {link: values.link}),
          ...(values.scene_id && {scene_id: values.scene_id}),
          ...(values.project_id && {project_id: values.project_id}),
        },
      };

      console.log(sourceObj);
      console.log(previousValues);

      console.log( compareValues(sourceObj, previousValues));

      const newObj = compareValues(sourceObj, previousValues);

      console.log(newObj);

      if (Object.keys(newObj).length > 0){
        newObj.masterSceneId = props.sceneId;
        newObj.coordinateId = props.coordinateValues._id;
        emit('handleSubmit', newObj);
      } else {
        emit('closeModal', newObj);
      }

    } else {
      // Create
      const obj = {
        name: values.coordinateName,
        masterSceneId: props.sceneId,
        lat: String(values.latitude),
        lng: String(values.longitude),
        linkType: values.linkType,
        active: values.isActive,
        ...(values.scene_id && { scene_id: values.scene_id}),
        ...(values.project_id && { project_id: values.project_id}),
        ...(values.link && { link: values.link}),
      };
      console.log(obj);
      emit('handleSubmit', obj);
    }

  }

};

onMounted(() => {
  console.log("yes");
  console.log(props.coordinateValues);
  console.log(initialValues.value);
});

</script>

<template>
    <div
        class="relative transform overflow-hidden rounded-t-2xl rounded-b-none sm:rounded-t-lg sm:rounded-b-lg  text-left  transition-all sm:my-1 w-full h-full sm:max-w-md">

        <div class="flex justify-start items-center p-3 sm:px-6">
            <div class="">
                <h1 class="text-base text-black font-semibold">{{ dataName }} Coordinates:</h1>

            </div>
            </div>
            <div class="p-3 sm:p-6">
            <Form  :validation-schema="CreateCoordinatesSchema" @submit="handleSubmit">

                <div class="flex flex-col gap-2">

                    <div class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                        <label for="coordinateName" class="label-primary">
                            Name</label>
                        <Field as="input" v-model="initialValues.coordinateName" type="text" name="coordinateName" autocomplete id="coordinateName"
                            class="input-primary"
                            :placeholder="`Enter Name`" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="coordinateName" />
                    </div>

                    <div class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                        <label for="latitude" class="label-primary">
                            latitude</label>
                        <Field  as="input" v-model="initialValues.latitude" type="number" name="latitude"
                            class="input-primary"
                            :placeholder="`Enter latitude`" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="latitude" />
                    </div>

                    <div class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                        <label for="longitude" class="label-primary">
                            longitude</label>
                        <Field as="input" v-model="initialValues.longitude" type="number" name="longitude" autocomplete="longitude"
                            class="input-primary"
                            :placeholder="`Enter longitude`" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="longitude" />
                    </div>

                    <!-- <div class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                        <label for="linkType" class="block text-sm font-normal leading-6 text-white">
                            Type</label>
                        <Field v-model="selectedType" as="select" type="text" name="linkType" id="linkType"
                            autocomplete="linkType"
                            class="inline-flex w-full h-10 justify-between items-center   rounded-md  p-2 text-[#ffffffba] text-xs shadow-sm bg-transparent text-white border-[1px] border-b-white"
                            :placeholder="`Enter Link Type`">
                            <option value="" disabled> Choose </option>
                            <option class="cursor-pointer text-black" value="master"> Master </option>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="linkType" />
                    </div> -->

                    <!-- <div class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                        <label for="iconFile" class="block text-sm font-normal leading-6 text-white">Upload info
                            Icon
                            File</label>
                        <div class="mt-2">
                            <Field type="file" name="iconFile" id="iconFile"
                                class="block p-2 w-full rounded-md border-1 bg-[transparent] py-1.5 text-white shadow-sm ring-1 ring-inset ring-[#737373] cursor-pointer  sm:text-sm sm:leading-6 placeholder:text-start placeholder:text-sm placeholder:text-gray-500"
                                placeholder="Upload Info Icon" />
                            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="iconFile" />
                        </div>
                    </div> -->

                    <!-- <div>
                        <label for="link" class="block text-sm font-normal leading-6 text-white">
                            link</label>
                        <Field as="input" v-model="initialValues.link" type="text" name="link" autocomplete="link"
                            class="block p-2 w-full rounded-md border-1 bg-[transparent] py-1.5 text-white shadow-sm ring-1 ring-inset ring-[#737373]  focus:ring-2 focus:ring-inset focus:ring-indigo-500 sm:text-sm sm:leading-6 placeholder:text-start placeholder:text-sm placeholder:text-gray-500"
                            :placeholder="`Enter link`" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="link" />
                    </div> -->

                    <div class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                    <label for="linkType" class="label-primary">
                            Type</label>
                        <Field v-model="initialValues.linkType" as="select" type="text" name="linkType" id="linkType"
                            autocomplete="linkType"
                            class="select-primary"
                            :placeholder="`Enter Link Type`">
                            <option v-for="item,index in masterSceneCoordinateLinkType" :key="index" class="cursor-pointer capitalize text-black" :value="item">
                                {{ item }}
                            </option>
                        </Field>
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="linkType" />
                    </div>

                    <div v-if="initialValues.linkType === masterSceneCoordinateLinkType.EXTERNAL" class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                        <label for="link" class="label-primary">
                            Link</label>
                        <Field as="input" v-model="initialValues.link" type="text" name="link" autocomplete id="link"
                            class="input-primary"
                            :placeholder="`Enter link`" />
                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="link" />
                    </div>

                    <div v-if="initialValues.linkType === masterSceneCoordinateLinkType.MASTER"
                        class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                        <label for="scene_id" class="label-primary">Select
                            scene</label>
                        <Field v-model="initialValues.scene_id" as="select" id="scene_id" name="scene_id"
                            class="select-primary">
                            <option value="" disabled class="text-black"> Choose </option>
                            <option value="" disabled
                                v-if="!newOrganizationStore.masterScenes && Object.keys(newOrganizationStore.masterScenes).length === 0">
                                No Data found ! </option>
                            <option v-else :value="option.sceneData._id"
                                v-for="option, index in newOrganizationStore.masterScenes"
                                :key="index" class="text-black"> {{
                                    option.sceneData.name }} </option>
                        </Field>
                        <ErrorMessage name="scene_id" class="text-sm text-rose-500 mt-1" as="p" />
                    </div>

                    <div v-if="initialValues.linkType === masterSceneCoordinateLinkType.PROJECT" class="w-full flex flex-col justify-start items-start gap-2 mb-2">

                                <div
                                    class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                                    <label for="project_id" class="label-primary">Select
                                        Project</label>
                                    <Field v-model="initialValues.project_id"  as="select" name="project_id"
                                        class="select-primary">
                                        <option value="" disabled class="text-black"> Choose </option>
                                        <option value="" disabled
                                            v-if="!newOrganizationStore.projects && Object.keys(newOrganizationStore.projects).length === 0">
                                            No Data found ! </option>
                                        <option v-else :value="option._id"
                                            v-for="option in newOrganizationStore.projects"
                                            :key="option._id" class="text-black"> {{
                                                option.name }} </option>
                                    </Field>
                                    <ErrorMessage name="project_id" class="text-sm text-rose-500 mt-1" as="p" />
                                </div>

                                 <div
                                    class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit">
                                    <label for="scene_id" class="label-primary">Select
                                        Scene <i v-if="dataLoader" class="text-gray-400">loading data...</i></label>
                                    <Field v-model="initialValues.scene_id" as="select" id="scene_id" name="scene_id"
                                       :class="[dataLoader ? 'pointer-events-none' : 'pointer-events-auto']"
                                        class="select-primary">
                                        <option value="" disabled  class="text-black" > Choose </option>
                                        <option value="" class="text-black"
                                            v-if="!selectedProjectScenes || (selectedProjectScenes ? Object.keys(selectedProjectScenes).length === 0 : true)">
                                            No Data found ! </option>
                                        <option v-else :value="option.sceneData._id"
                                            v-for="option, index in selectedProjectScenes"
                                            :key="index" class="text-black"> {{
                                                option.sceneData.name }} </option>
                                    </Field>

                                    <ErrorMessage name="scene_id" class="text-sm text-rose-500 mt-1" as="p" />
                                </div>
                    </div>

                    <div class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2 ">
                           <div class="flex  justify-start items-start gap-2 w-auto">
                                <label for="isActive" class="label-primary"> isActive</label>
                                    <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                        <div class="relative mb-0 p-0">
                                            <Field v-model="initialValues.isActive" id="isActive" class="sr-only peer" name="isActive" type="checkbox" :value="true" />
                                            <label for="isActive"
                                            class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                            </label>

                                        </div>
                                        <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="isActive"  />
                                    </div>
                           </div>
                    </div>

                </div>
                <div class="mt-4 sm:mt-4 flex justify-end items-center gap-x-3">
                    <button type="button"
                        class="cancel-btn-primary"
                        @click="() => emit('closeModal')">Cancel</button>
                    <button type="submit" :disabled="dataLoader"
                        class="proceed-btn-primary">Save
                        <Spinner v-if="loader" />
                    </button>
                </div>
            </Form>
           </div>
    </div>
</template>

<style scoped>
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}
</style>

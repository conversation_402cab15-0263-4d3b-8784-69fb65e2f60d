<script setup>
import * as slider from '@zag-js/slider';
import { computed, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../common/Spinner.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { sceneSchema } from '../../validationSchema/scene';
import { getCookie } from '../../helpers/domhelper';
import { uiOperations } from '../../store/uiOperations';
import { Org_Store } from '../../store/organization';
import { ProjectStore } from '../../store/project';
import { createScene } from '../../api/projects/scene/index';
import { isMasterScenePath, resizeImage } from '../../helpers/helpers';
import { getStorage, ref as referenece, getDownloadURL, uploadBytesResumable } from 'firebase/storage';
import { createMasterScene } from '@/api/masterScene';
import router from '@/router';
import { getScene } from '../../api/projects/scene/index.ts';
import { getScene as getMasterScene } from '../../api/masterScene/index';
import { useMachine, normalizeProps } from '@zag-js/vue';

const emits = defineEmits(['close']);
const props = defineProps({
  category: {
    type: String,
    default: () => '',
  },
});

const selectedCategory = ref(props.category);

const uiStore = uiOperations();
const typeList = [{ name: 'image', value: 'image' }, { name: 'deep zoom', value: 'deep_zoom' }, { name: 'gsplat', value: 'gsplat' }, { name: 'Rotatable Image', value: 'rotatable_image' }];
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const route = useRoute();
const projectId = ref(route.params.project_id);
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const uploadedImageFile = ref();
const uploadedGsplatFile = ref();

if (isMasterScene.value){
  typeList.push({ name: 'earth', value: 'earth' }); // Master scene
} else {
  typeList.push({ name: 'identical_unitplan', value: 'identical_unitplan' }); // Project scene
}

const selectedType = ref();
const selectedBuildingId = ref();
const parent = ref();
const loader = ref(false);
const uploadProgess = ref(null);
const storage = getStorage();

newOrganizationStore.RefreshMasterScenes();
projectStore.RefreshSidebarOptions(projectId.value);

async function getUploadedStorageLink (file, storagePath) {
  return new Promise((resolve, reject) => {
    const storageRef = referenece(storage, storagePath); // Reference for storage
    loader.value = true;
    // Upload file on task with monitored progress
    const uploadTask = uploadBytesResumable(storageRef, file);
    // State_changed observer
    uploadTask.on('state_changed',
      (snapshot) => {
        // Observe state change events such as progress, pause, and resume
        // Get task progress, including the number of bytes uploaded and the total number of bytes to be uploaded
        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        uploadProgess.value = Math.round(progress);
      },
      (error) => {
        // Handle unsuccessful uploads
        console.log(error);
        reject(error);
      },
      () => {
        // Handle successful uploads on complete
        getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
          console.log(`File available at ${downloadURL}`);
          resolve(downloadURL);
        });
      },
    );

  });
}

watch(selectedType, (value) => {
  if (!isMasterScene.value){
    if (value === 'identical_unitplan') {
      projectStore.RefreshBuildings(projectId.value);
    }
  }
});

const handleCreateProjectScene = (formData) => {

  for (const pair of formData.entries()){
    console.log(pair[0], pair[1]);
  }
  loader.value = true;
  if (isMasterScene.value){
    // MasterScene
    createMasterScene(formData).then((res) => {
      document.dispatchEvent(new Event('getScenes'));
      const newObject = {};
      getMasterScene(res._id).then((sceneDetails) => {
        newObject[res._id] = sceneDetails;
        newOrganizationStore.SyncMultipleMasterScenes(newObject);
        uploadProgess.value = null;
        emits('close');
        router.push({ path: `/masterscenes/${res._id}`});
      });

    //   router.go(-1);
    }).catch((err) => {
      console.log(err);
      uiStore.handleApiErrorMessage(err.message._message);
    }).finally(() => {
      loader.value = false;
    });
  } else {
    // ProjectScene
    createScene(formData).then((res) => {
      document.dispatchEvent(new Event('getScenes'));
      const newObject = {};
      getScene(projectId.value, res._id).then((sceneDetails) => {
        newObject[res._id] = sceneDetails;
        projectStore.SyncMultipleScenes(newObject);
        uploadProgess.value = null;
        emits('close');
        console.log('res', res);
        router.push({ path: `/projects/${projectId.value}/design/scenes/${res._id}`});
      });

      // router.go(-1);
    }).catch((err) => {
      console.log(err);
      uiStore.handleApiErrorMessage(err.message._message);
    }).finally(() => {
      loader.value = false;
    });
  }

};

const handleSubmitVeeValidate = async (values) => {
  console.log(values);

  // Update boolean fields to false if they are undefined or null
  values.isActive = values.isActive ?? false;
  values.root = values.root ?? false;
  values.clouds = values.clouds ?? false;

  // Remove parent field if it is undefined, null, or an empty string
  if (values.parent === undefined || values.parent === null || values.parent === '') {
    delete values.parent;
  }
  if (values.auto_rotate === undefined || values.auto_rotate === null) {
    values.auto_rotate = false;
  }

  if (values) {
    const formData = new FormData();
    const organization = getCookie('organization');
    // Append basic fields to FormData
    formData.append('organization_id', organization);
    formData.append('name', values.sceneName);
    formData.append('type', values.sceneType);

    if (values.parent) {
      formData.append('parent', values.parent);
    }
    if (values.sceneType === 'rotatable_image') {
      formData.append('auto_rotate', values.auto_rotate);
    }
    if (values.sceneType === 'gsplat' && values.gsplat) {
      formData.append('gsplat', values.gsplat);
    }
    if (values.sceneType === 'gsplat') {
      formData.append('position', JSON.stringify({ x: values.gsplat_x, y: values.gsplat_y, z: values.gsplat_z}));
      formData.append('polar_angle', JSON.stringify({max: values.polar_angle_max, min: values.polar_angle_min}));
      formData.append('distance', JSON.stringify({max: values.distance_max, min: values.distance_min}));
      formData.append('auto_rotate', values.auto_rotate);
    }
    values.infoText && (formData.append('info_text', values.infoText));
    formData.append('active', values.isActive);
    formData.append('root', values.root);
    formData.append('clouds', values.clouds);

    if (!isMasterScene.value){
      formData.append('project_id', projectId.value);
      if (
        (values.sceneType === 'identical_unitplan' || values.sceneType === 'deep_zoom') &&
        values.building_id
      ) {
        formData.append('building_id', values.building_id);
      }

      if (values.category) {
        formData.append('category', values.category);
      }
    }

    if (values.sceneType === 'deep_zoom') {
      // File to url conversion
      const stampFileName = `${new Date().getTime()}${values.file.name}`; // Stamp file name
      const filepath = `CreationtoolAssets/${organization}/projects/${projectId.value}/deepzoom/${stampFileName}`; // File path formation
      const response = await  getUploadedStorageLink(values.file, filepath);
      if (response){
        formData.append('file_url', response);
        uploadProgess.value = 'Link successfully created';
      }
      // Thumbail
      const resizedThumbnail = await resizeImage(values.file, 1280, 720);
      formData.append('lowRes', resizedThumbnail);
      if (values.minandmax){
        formData.append('minZoomLevel', values.minandmax[0]);
        formData.append('maxZoomLevel', values.minandmax[1]);
      }
    }

    // Handle file and thumbnail resizing
    if (values.sceneType !== 'rotatable_image' && values.sceneType !== 'earth' && values.sceneType !== 'gsplat' && values.sceneType !== 'deep_zoom') {
      formData.append('highRes', values.file);
      const resizedThumbnail = await resizeImage(values.file, 1280, 720);
      formData.append('lowRes', resizedThumbnail);
      handleCreateProjectScene(formData);
    } else {
      console.log(values);
      handleCreateProjectScene(formData);
    }
  }

  console.log(values);
};

const imagePreviewUrl = ref();

const handleFileUpload = () => {

  const file = event.target.files[0];
  if (file) {
    imagePreviewUrl.value = URL.createObjectURL(file);
  }

};

const minandmaxRef = ref(null);
const [minandmaxState, minandmaxsend] = useMachine(
  slider.machine({
    id: 'minandmax',
    name: 'minandmax',
    value: [1, 10], // Default value
    min: 1,
    max: 10,
  }),
);

const minandmaxComputed = computed(() => slider.connect(minandmaxState.value, minandmaxsend, normalizeProps));
const minandmaxZoomLevel = computed(() => minandmaxState.value.context.value);

</script>

<template>
        <div
            class="bg-white border border-gray-200 w-[536px] rounded-lg"
            >
            <div class="">
                <div class="h-9  flex justify-between items-center px-2.5 border-b border-gray-200 rounded-t-lg">
                    <p class="text-sm font-medium text-gray-900">
                        Create {{ isMasterScene ? 'Master' : 'Project' }} Scene</p>
                        <button  class="fill-gray-400" @click="$emit('close')">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg></button>
                </div>
                <Form :validation-schema="sceneSchema"
                    @submit="handleSubmitVeeValidate">

                    <div
                        class="p-2.5 grid grid-cols-2 gap-4">

                        <div class="col-span-auto">
                            <label for="sceneName"
                                class="text-sm font-medium text-gray-900">
                                Name</label>
                            <Field as="input" type="text"
                                name="sceneName" autocomplete
                                id="sceneName"
                                class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal"
                                :placeholder="`Enter Scene Name`" />
                            <ErrorMessage as="p"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                name="sceneName" />
                        </div>

                        <div class="col-span-auto">
                            <label for="sceneType"
                                class="text-sm font-medium text-gray-900">
                                Type</label>
                            <Field v-model="selectedType"
                                as="select" type="text"
                                name="sceneType" id="sceneType"
                                autocomplete="sceneType"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300"
                                :placeholder="`Enter Project Type`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!typeList">
                                    No Type found ! </option>
                                <option v-else
                                    :value="option.value"
                                    v-for="option, index in  typeList"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.name }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                name="sceneType" />
                        </div>

                        <div v-if="!isMasterScene" class="col-span-auto">
                            <label for="category"
                                class="text-sm font-medium text-gray-900">
                                Category</label>
                            <Field
                                as="select" type="text"
                                name="category" id="category"
                                v-model="selectedCategory"
                                autocomplete="category"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300"
                                :placeholder="`Select Category`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" class="text-rose-500" disabled
                                    v-if="!projectStore.sidebarOptions">
                                    please create category first.</option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.sidebarOptions"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.name }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                name="category" />
                        </div>

                        <div v-if="selectedType!=='rotatable_image' && selectedType!=='gsplat' && selectedType!=='earth'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit row-span-2">
                            <label for="file"
                                class="text-sm font-medium text-gray-900">Upload Image
                                File</label>
                            <div class="w-full">
                                <div class="w-full rounded-lg relative shadow-md" v-if="imagePreviewUrl">
                                <img class="h-28 w-full  object-fill rounded-lg" :src="imagePreviewUrl" alt="">
                                <div class="h-full w-full absolute left-0 top-0 flex justify-center items-center"><button @click="()=>{imagePreviewUrl=null,uploadedImageFile=null}" class="absolute h-10 px-3 rounded-lg bg-white">Change</button></div>

                            </div>
                                    <label v-show="!imagePreviewUrl" class="mb-0 w-full h-28 rounded-lg border-2 border-dashed border-gray-200 py-3 px-1 cursor-pointer">
              <div class="flex justify-center items-center gap-2">
                <svg class="h-5 w-5 fill-gray-500" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_22140)">
<path d="M10.2949 5.736C10.1636 5.60141 9.98561 5.52579 9.8 5.52579C9.61438 5.52579 9.43637 5.60141 9.3051 5.736L7.7 7.38226V1.21795C7.7 1.02754 7.62625 0.844924 7.49497 0.710282C7.3637 0.575641 7.18565 0.5 7 0.5C6.81435 0.5 6.6363 0.575641 6.50503 0.710282C6.37375 0.844924 6.3 1.02754 6.3 1.21795V7.38226L4.6949 5.736C4.63033 5.66743 4.55309 5.61273 4.46768 5.57511C4.38228 5.53748 4.29043 5.51767 4.19748 5.51685C4.10454 5.51602 4.01236 5.53418 3.92633 5.57028C3.8403 5.60638 3.76215 5.65969 3.69642 5.7271C3.6307 5.79451 3.57872 5.87467 3.54352 5.9629C3.50833 6.05114 3.49062 6.14568 3.49142 6.24101C3.49223 6.33633 3.51154 6.43054 3.54823 6.51814C3.58492 6.60573 3.63824 6.68495 3.7051 6.75118L6.5051 9.62297C6.57012 9.68983 6.64737 9.74288 6.73241 9.77907C6.81746 9.81527 6.90863 9.8339 7.0007 9.8339C7.09277 9.8339 7.18394 9.81527 7.26899 9.77907C7.35403 9.74288 7.43128 9.68983 7.4963 9.62297L10.2963 6.75118C10.4273 6.61635 10.5008 6.43367 10.5006 6.24329C10.5003 6.05292 10.4263 5.87045 10.2949 5.736Z"/>
<path d="M12.6 8.75641H10.815L8.7325 10.8923C8.50499 11.1257 8.2349 11.3108 7.93763 11.4371C7.64037 11.5634 7.32176 11.6284 7 11.6284C6.67824 11.6284 6.35963 11.5634 6.06237 11.4371C5.7651 11.3108 5.49501 11.1257 5.2675 10.8923L3.185 8.75641H1.4C1.0287 8.75641 0.672601 8.90769 0.41005 9.17698C0.1475 9.44626 0 9.81148 0 10.1923V13.0641C0 13.4449 0.1475 13.8102 0.41005 14.0794C0.672601 14.3487 1.0287 14.5 1.4 14.5H12.6C12.9713 14.5 13.3274 14.3487 13.5899 14.0794C13.8525 13.8102 14 13.4449 14 13.0641V10.1923C14 9.81148 13.8525 9.44626 13.5899 9.17698C13.3274 8.90769 12.9713 8.75641 12.6 8.75641ZM10.85 13.0641C10.6423 13.0641 10.4393 13.0009 10.2667 12.8826C10.094 12.7643 9.9594 12.5961 9.87993 12.3993C9.80046 12.2025 9.77966 11.986 9.82018 11.7771C9.86069 11.5682 9.96069 11.3763 10.1075 11.2257C10.2544 11.0751 10.4415 10.9725 10.6452 10.9309C10.8488 10.8894 11.06 10.9107 11.2518 10.9922C11.4437 11.0737 11.6077 11.2118 11.723 11.3889C11.8384 11.566 11.9 11.7742 11.9 11.9872C11.9 12.2728 11.7894 12.5467 11.5925 12.7487C11.3955 12.9506 11.1285 13.0641 10.85 13.0641Z"/>
</g>
<defs>
<clipPath id="clip0_723_22140">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
           </svg>
           <p class="text-xs font-medium text-gray-500 mt-2">Upload</p>
</div>
<div>
  <p class="text-xs font-medium text-gray-500 text-center">{{'16:9 Resolution 3840 x 2160px 4k jpeg ( < 10 mb )'}}</p>
  <Field  type="file" @change="(e)=>handleFileUpload(e)"
                                    v-model="uploadedImageFile"
                                    name="file"
                                    id="file"
                                    autocomplete="highRes"
                                    class="hidden"
                                    placeholder="Upload Image" />
</div>
            </label>

                                <ErrorMessage as="p"
                                    class="ml-1 text-xs text-rose-500 mt-1"
                                    name="file" />
                            </div>
                            <div class="mt-2 w-full" v-if="uploadProgess">
                            <div  v-if="!isNaN(uploadProgess)" class="w-full h-[12px] overflow-hidden bg-slate-300 rounded">
                                <div class="h-fit text-[9.5px] flex justify-center items-center text-white bg-[#36f] hover:bg-[#4572fc]"
                                        :style="{ width: uploadProgess + '%' }">
                                       {{ uploadProgess }}%
                                 </div>
                            </div>
                            <div v-else class=" w-full h-fit text-xs text-start text-success bg-transparent">
                                     {{ uploadProgess }}
                               </div>

                            </div>
                        </div>

                        <div class="col-span-auto">
                            <label for="infoText"
                                class="text-sm font-medium text-gray-900">
                                Info Text</label>
                            <Field as="input" type="text"
                                name="infoText"
                                class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal"
                                :placeholder="`Enter Info Text`" />
                            <ErrorMessage as="p"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                name="infoText" />
                        </div>

                        <div
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit">
                            <label for="parent"
                                class="text-sm font-medium text-gray-900">select
                                Parent Scene</label>
                            <Field v-model="parent" as="select"
                                id="parent" name="parent"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                                <option value=""
                                    class="text-gray-700">
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="(!newOrganizationStore.masterScenes || Object.keys(newOrganizationStore.masterScenes).length === 0) && (!projectStore.scenes || Object.keys(projectStore.scenes).length === 0)">
                                    No Data found ! </option>

                                        <option

                                  :value="option.sceneData._id"
                                  v-for="option, index in  newOrganizationStore.masterScenes"
                                  :key="index"
                                  class="text-black">
                                  {{
                                      option.sceneData.name }}
                                    </option>

                                <template v-if="!isMasterScene">
                                <option

                                    :value="option.sceneData._id"
                                    v-for="option, index in  projectStore.scenes"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.sceneData.name }}
                                </option>
                                </template>
                            </Field>
                            <ErrorMessage name="parent"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>
                            <div v-show="selectedType==='deep_zoom'"  class="relative ">
                          <label
                            class="mb-2 text-xs text-gray-500">
                            min and max (zoom level) <br> <span class="italic font-semibold"> min: {{ minandmaxZoomLevel[0] }}</span>  <span class="italic font-semibold"> max: {{ minandmaxZoomLevel[1] }}</span>
                          </label>
                          <div class="w-full pr-2">
                              <Field name="minandmax" v-model="minandmaxZoomLevel" v-slot="{ field }">

                                                    <div ref="minandmaxRef" v-bind="field">
                                                      <div  v-bind="minandmaxComputed.getRootProps()">
                                                          <div v-bind="minandmaxComputed.getControlProps()">
                                                            <div v-bind="minandmaxComputed.getTrackProps()">
                                                              <div v-bind="minandmaxComputed.getRangeProps()" />
                                                            </div>
                                                            <div
                                                              v-for="(_, index) in minandmaxComputed.value"
                                                              :key="index"
                                                              v-bind="minandmaxComputed.getThumbProps({ index })"
                                                            >
                                                              <input v-bind="minandmaxComputed.getHiddenInputProps({ index })" />
                                                            </div>
                                                          </div>
                                                    </div>
                                                  </div>

                                                    <ErrorMessage
                                                  name="minandmax"
                                                  as="p"
                                                  class="text-sm text-rose-500 mt-1" />
                                  </Field>
                          </div>

      </div>

                        <div v-if="!isMasterScene && (selectedType === 'identical_unitplan' || selectedType === 'deep_zoom')"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit">
                            <label :for="building_id"
                                class="text-sm font-medium text-gray-900">select
                                Builidng/Tower</label>
                            <Field v-model="selectedBuildingId"
                                as="select" id="building_id"
                                name="building_id"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!projectStore.buildings || Object.keys(projectStore.buildings).length === 0">
                                    No Data found ! </option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.buildings"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.name }} </option>
                            </Field>
                            <ErrorMessage name="building_id"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>

                        <div v-if="selectedType === 'gsplat'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit row-span-2">
                            <label for="gsplat"
                                class="text-sm font-medium text-gray-900 capitalize"> Upload gsplat zip file </label>
                                <div class="w-full">

                                    {{ uploadedGsplatFile }}

                                    <div class="w-full h-28 rounded-lg relative shadow-md" v-if="uploadedGsplatFile">
                                <div class="h-full w-full absolute left-0 top-0 flex justify-center items-center"><button @click="()=>{uploadedGsplatFile=null}" class="absolute h-10 px-3 rounded-lg bg-white">Change</button></div>

                            </div>
                                    <label v-show="!uploadedGsplatFile" class="mb-0 w-full h-28 rounded-lg border-2 border-dashed border-gray-200 py-3 px-1 cursor-pointer">
              <div class="flex justify-center items-center gap-2">
                <svg class="h-5 w-5 fill-gray-500" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_22140)">
<path d="M10.2949 5.736C10.1636 5.60141 9.98561 5.52579 9.8 5.52579C9.61438 5.52579 9.43637 5.60141 9.3051 5.736L7.7 7.38226V1.21795C7.7 1.02754 7.62625 0.844924 7.49497 0.710282C7.3637 0.575641 7.18565 0.5 7 0.5C6.81435 0.5 6.6363 0.575641 6.50503 0.710282C6.37375 0.844924 6.3 1.02754 6.3 1.21795V7.38226L4.6949 5.736C4.63033 5.66743 4.55309 5.61273 4.46768 5.57511C4.38228 5.53748 4.29043 5.51767 4.19748 5.51685C4.10454 5.51602 4.01236 5.53418 3.92633 5.57028C3.8403 5.60638 3.76215 5.65969 3.69642 5.7271C3.6307 5.79451 3.57872 5.87467 3.54352 5.9629C3.50833 6.05114 3.49062 6.14568 3.49142 6.24101C3.49223 6.33633 3.51154 6.43054 3.54823 6.51814C3.58492 6.60573 3.63824 6.68495 3.7051 6.75118L6.5051 9.62297C6.57012 9.68983 6.64737 9.74288 6.73241 9.77907C6.81746 9.81527 6.90863 9.8339 7.0007 9.8339C7.09277 9.8339 7.18394 9.81527 7.26899 9.77907C7.35403 9.74288 7.43128 9.68983 7.4963 9.62297L10.2963 6.75118C10.4273 6.61635 10.5008 6.43367 10.5006 6.24329C10.5003 6.05292 10.4263 5.87045 10.2949 5.736Z"/>
<path d="M12.6 8.75641H10.815L8.7325 10.8923C8.50499 11.1257 8.2349 11.3108 7.93763 11.4371C7.64037 11.5634 7.32176 11.6284 7 11.6284C6.67824 11.6284 6.35963 11.5634 6.06237 11.4371C5.7651 11.3108 5.49501 11.1257 5.2675 10.8923L3.185 8.75641H1.4C1.0287 8.75641 0.672601 8.90769 0.41005 9.17698C0.1475 9.44626 0 9.81148 0 10.1923V13.0641C0 13.4449 0.1475 13.8102 0.41005 14.0794C0.672601 14.3487 1.0287 14.5 1.4 14.5H12.6C12.9713 14.5 13.3274 14.3487 13.5899 14.0794C13.8525 13.8102 14 13.4449 14 13.0641V10.1923C14 9.81148 13.8525 9.44626 13.5899 9.17698C13.3274 8.90769 12.9713 8.75641 12.6 8.75641ZM10.85 13.0641C10.6423 13.0641 10.4393 13.0009 10.2667 12.8826C10.094 12.7643 9.9594 12.5961 9.87993 12.3993C9.80046 12.2025 9.77966 11.986 9.82018 11.7771C9.86069 11.5682 9.96069 11.3763 10.1075 11.2257C10.2544 11.0751 10.4415 10.9725 10.6452 10.9309C10.8488 10.8894 11.06 10.9107 11.2518 10.9922C11.4437 11.0737 11.6077 11.2118 11.723 11.3889C11.8384 11.566 11.9 11.7742 11.9 11.9872C11.9 12.2728 11.7894 12.5467 11.5925 12.7487C11.3955 12.9506 11.1285 13.0641 10.85 13.0641Z"/>
</g>
<defs>
<clipPath id="clip0_723_22140">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
           </svg>
           <p class="text-xs font-medium text-gray-500 mt-2">Upload</p>
</div>
<div>
  <p class="text-xs font-medium text-gray-500 text-center">16:9 Resolution 3840 x 2160px 4k jpeg,mp4 or 360 Image Sequence Zip</p>
  <Field type="file"
                                    v-model="uploadedGsplatFile"
                                    name="gsplat"
                                    id="gsplat"
                                    autocomplete="gsplat"
                                    class="hidden"
                                    placeholder="Upload gspalt zip" />
</div>
            </label>

                            <ErrorMessage name="gsplat"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>
                        </div>

                        <div v-if="selectedType === 'gsplat'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit">
                            <label for="gsplat_x" class="text-sm font-medium text-gray-900 capitalize">Positions</label>
                            <div class="flex w-full space-x-2">
                                <div class="flex flex-col w-1/3">
                                    <Field type="number" name="gsplat_x" id="gsplat_x" autocomplete="gsplat_x"
                                        class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="X-axis" />
                                    <ErrorMessage name="gsplat_x" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                                <div class="flex flex-col w-1/3">
                                    <Field type="number" name="gsplat_y" id="gsplat_y" autocomplete="gsplat_y"
                                        class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Y-axis" />
                                    <ErrorMessage name="gsplat_y" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                                <div class="flex flex-col w-1/3">
                                    <Field type="number" name="gsplat_z" id="gsplat_z" autocomplete="gsplat_z"
                                        class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Z-axis" />
                                    <ErrorMessage name="gsplat_z" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                            </div>
                        </div>

                        <div v-if="selectedType === 'gsplat'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit">
                            <label class="text-sm font-medium text-gray-900 capitalize">Polar Angle</label>
                            <div class="flex w-full space-x-2">
                                <div class="flex flex-col w-1/2">
                                    <Field type="number" name="polar_angle_max" id="polar_angle_max"
                                        autocomplete="polar_angle_max" class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Max" />
                                    <ErrorMessage name="polar_angle_max" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                                <div class="flex flex-col w-1/2">
                                    <Field type="number" name="polar_angle_min" id="polar_angle_min"
                                        autocomplete="polar_angle_min" class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Min" />
                                    <ErrorMessage name="polar_angle_min" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                            </div>
                        </div>

                        <div v-if="selectedType === 'gsplat'"
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit">
                            <label class="text-sm font-medium text-gray-900 capitalize">Distance</label>
                            <div class="flex w-full space-x-2">
                                <div class="flex flex-col w-1/2">
                                    <Field type="number" name="distance_max" id="distance_max"
                                        autocomplete="distance_max" class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Max" />
                                    <ErrorMessage name="distance_max" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                                <div class="flex flex-col w-1/2">
                                    <Field type="number" name="distance_min" id="distance_min"
                                        autocomplete="distance_min" class="flex w-full rounded-lg h-8 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal" placeholder="Min" />
                                    <ErrorMessage name="distance_min" class="ml-1 text-xs text-rose-500 mt-1" as="p" />
                                </div>
                            </div>
                        </div>

                                <div class="flex  justify-between items-center gap-2 w-auto mr-3 col-start-1">
                                    <label for="isActive" class="mb-0 text-sm font-medium text-gray-900">
                                        publish</label>
                                    <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                        <div class="relative mb-0 p-0">
                                            <Field id="isActive" class="sr-only peer" name="isActive" type="checkbox"
                                                :value="true" />
                                            <label for="isActive"
                                                class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                            </label>
                                        </div>
                                        <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="isActive" />
                                    </div>
                                </div>

                                <div v-if="selectedType === 'gsplat' || selectedType === 'rotatable_image'" class="flex  justify-between items-center gap-2 w-auto mr-3">
                                    <label for="auto_rotate" class="mb-0 text-sm font-medium text-gray-900">autoRotate</label>
                                    <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                        <div class="relative mb-0 p-0">
                                            <Field id="auto_rotate" class="sr-only peer" name="auto_rotate"
                                                type="checkbox" :value="true" />
                                            <label for="auto_rotate"
                                                class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                            </label>
                                        </div>
                                        <ErrorMessage as="p" class="ml-1 text-xs text-rose-500 mt-1" name="auto_rotate" />
                                    </div>
                                </div>

                            <div class="flex  justify-between items-center gap-2 w-auto mr-3">
                                <label for="clouds" class="mb-0 text-sm font-medium text-gray-900">
                                    isCloude</label>
                                <div
                                    class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                    <div
                                        class="relative mb-0 p-0">
                                        <Field id="clouds"
                                            class="sr-only peer"
                                            name="clouds"
                                            type="checkbox"
                                            :value="true" />
                                        <label for="clouds"
                                            class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                        </label>
                                    </div>
                                    <ErrorMessage as="p"
                                        class="ml-1 text-xs text-rose-500 mt-1"
                                        name="clouds" />
                                </div>
                            </div>

                            <div class="flex  justify-between items-center gap-2 w-auto mr-3">
                                <label for="root" class="mb-0 text-sm font-medium text-gray-900">
                                    isRootScene</label>
                                <div
                                    class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                    <div
                                        class="relative mb-0 p-0">
                                        <Field id="root"
                                            class="sr-only peer"
                                            name="root"
                                            type="checkbox"
                                            :value="true" />
                                        <label for="root"
                                            class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full  after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-white after:border after:rounded-full after:h-5 after:w-5 after:transition-all  peer-checked:bg-blue-600 cursor-pointer">
                                        </label>
                                    </div>
                                    <ErrorMessage as="p"
                                        class="ml-1 text-xs text-rose-500 mt-1"
                                        name="root" />
                                </div>
                            </div>
                    </div>
                    <div
                        class="px-2.5">
                        <button type="submit"
                            :disabled="loader"
                            class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">{{loader ? '':'Add Scene'}}
                            <Spinner v-if="loader" class="text-slate-400 fill-white"/>
                        </button>
                    </div>
                </Form>

            </div>
        </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Range Slider */
[data-scope="slider"][data-part="root"] {
  width: 100%;
}
[data-scope="slider"][data-part="root"] > div:first-child {
  display: flex;
  justify-content: space-between;
}
[data-scope="slider"][data-part="label"] {
  margin-right: 0.5rem;
}
[data-scope="slider"][data-part="control"] {
  display: flex;
  align-items: center;
  margin-top: 0rem;
  position: relative;
  padding-block: 0.625rem;
}
[data-scope="slider"][data-part="track"] {
  height: 8px;
  border-radius: 9999px;
  flex: 1;
  background: #A4CAFE;
}
[data-scope="slider"][data-part="range"] {
  height: 100%;
  border-radius: inherit;
  background: #1C64F2;
}
[data-scope="slider"][data-part="thumb"] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 9999px;
  background: #1C64F2;
  box-shadow:
    0 1px 3px 0 rgba(0, 0, 0, 0.1),
    0 1px 2px 0 rgba(0, 0, 0, 0.06);
}
[data-scope="slider"][data-part="thumb"]:is(:focus, [data-focus]) {
  outline: 2px solid #1C64F2;
}
[data-scope="slider"][data-part="thumb"][data-disabled] {
  background: #e2e8f0;
}
</style>

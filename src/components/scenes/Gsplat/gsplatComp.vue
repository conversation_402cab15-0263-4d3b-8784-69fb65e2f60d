<script setup>
import { WebGLR<PERSON>er, Scene, PerspectiveCamera, MathUtils, PlaneGeometry, MeshBasicMaterial, Mesh, Box3} from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { TransformControls } from 'three/examples/jsm/controls/TransformControls.js';
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
import { LumaSplatsThree } from '@lumaai/luma-web';
import * as TWEEN from '@tweenjs/tween.js';
import { onMounted, ref, defineEmits, onUnmounted, watch } from 'vue';
import {useRouter } from 'vue-router';
import { cdn } from '../../../helpers';

const route = useRouter();
const props = defineProps({
  data: {
    type: Object,
    default () {
      return {};
    },
  },
  svgLayers: {
    type: Object,
    default () {
      return {};
    },
  },
  gsplatLayersUpdate: Object,
});
const emit = defineEmits(['updateHotspotPosition', 'updatePlaneScale']);
const containerVal = ref(null);
const gsplat = ref(null);
const mousedown = ref(1);
const autoRotateTimeout = ref(null);
const cameraRef = ref(null);
var camera = cameraRef.value;
const controlsRef = ref(null);
var controls = controlsRef.value;
const sceneRef = ref(null);
var scene = sceneRef.value;
var svgLayers=ref(props.svgLayers);
const transControlsRef = ref(null);
var transControls = transControlsRef.value;
const renderer= ref();
const htmlLayers = ref([]);

/* Methods */

// Remove transControls
function removeTransControls () {
  console.log('Yes Remove TransControls');
  // TransControls.attach(scene.getObjectByName(id));
  transControls.detach();
}

// Mesh
function updateDimensions (mesh) {
  // Compute the bounding box
  const boundingBox = new Box3().setFromObject(mesh);
  // Extract the width and height from the bounding box
  const width = boundingBox.max.x - boundingBox.min.x;
  const height = boundingBox.max.y - boundingBox.min.y;
  emit('updatePlaneScale', {'width': width, 'height': height});
}

// Add transControls
function addTransfromControls (id) {
  console.log(scene.getObjectByName(id));
  console.log('yes Mesh');
  transControls = new TransformControls(camera, renderer.value.domElement);
  transControls.addEventListener('dragging-changed', function (event) {
    controls.enabled = !event.value;
  });
  transControls.attach( scene.getObjectByName(id));
  scene.add(transControls);
  transControls.addEventListener('change', () => {
    // Get the position of the object being transformed
    const position = scene.getObjectByName(id).position; // Assuming 'object' is the object being transformed
    emit('updateHotspotPosition', position);
    if (scene.getObjectByName(id).type === 'Mesh'){
      console.log('yes Mesh');
      updateDimensions(scene.getObjectByName(id));
    }
  });
}

// Reset transControl Positions
function resetHotspotPosition (id){
  scene.getObjectByName(id).position.x =  svgLayers.value[id].position.x;
  scene.getObjectByName(id).position.y =  svgLayers.value[id].position.y;
  scene.getObjectByName(id).position.z =  svgLayers.value[id].position.z;
}

// Add Hotspot
function handleAddHotspot () {
  //  Event.preventDefault();
  // Get mouse coordinates
  /*    Const mouseX = (event.clientX / containerVal.value.offsetWidth) * 2 - 1;
    const mouseY = -(event.clientY / containerVal.value.offsetHeight) * 2 + 1;
    const vector = new Vector3(mouseX, mouseY, 0.5);
    vector.unproject(camera);
    const dir = vector.sub(camera.position).normalize();
    const distance = -camera.position.z / dir.z;
    const pos = camera.position.clone().add(dir.multiplyScalar(distance));
    console.log('Clicked at:', pos); */
  const div = document.createElement('div');
  //    HtmlLayers.value.push(div);
  div.id='00';
  div.setAttribute('class', 'newbeeHotSpot');
  const span = document.createElement('span');
  div.style.pointerEvents = 'auto';
  div.appendChild(span);
  document.body.appendChild(div);
  const loc1 = new CSS2DObject(document.getElementById('00'));
  loc1.name='00';
  scene.add(loc1);
  loc1.position.set(0, 0, 0); // Initial position
  addTransfromControls('00');  // Add transformation controls

}

// Add Plane
function handleAddPlane () {
  const plane =new Mesh(new PlaneGeometry(2, 2), new MeshBasicMaterial({colorWrite: true, depthWrite: true}));
  plane.position.set(0, 0, 0);
  plane.name='plane'; // Name of the mesh
  scene.add(plane);
  plane.layers.enableAll();
  plane.material.side=2;
  addTransfromControls('plane');  // Add transformation controls
}

// Router watch handler
watch(() => route.currentRoute.value, () => {

  console.log('Route Changes');
  console.log(route.options.history.state);

  // Previous route position remove
  if (route.options.history.state.back.includes('layerId')){
    console.log('Previous Hotspot Position Remove');
    const previousHotSpotId = route.options.history.state.back.split('?')[1].split('=')[1];
    if (previousHotSpotId !== '00' && previousHotSpotId !== '000'){

      resetHotspotPosition(previousHotSpotId);
      removeTransControls(previousHotSpotId);

    }
    // Add hotspot
    if (previousHotSpotId === '00'){
      removeTransControls('00');// Remove transforms
      const objectToRemove = scene.getObjectByName('00');
      scene.remove(objectToRemove); // Remove elem
    }

    // Add mesh
    if (previousHotSpotId === '000'){
      removeTransControls('plane');// Remove transforms
      const objectToRemove = scene.getObjectByName('plane'); // Name of mesh
      scene.remove(objectToRemove); // Remove elem
    }

  }

  // Add controls to selection hotspot
  if (Object.keys(route.currentRoute.value.query).length > 0){

    if (route.currentRoute.value.query.layerId !== null){

      // Add Hotspot
      if (route.currentRoute.value.query.layerId === '00'){
        //  AddTransfromControls(route.currentRoute.value.query.layerId)
        handleAddHotspot();
      }
      // Add plane
      if (route.currentRoute.value.query.layerId === '000'){
        // AddTransfromControls(route.currentRoute.value.query.layerId)
        handleAddPlane();
      }

      // Edit
      if (route.currentRoute.value.query.layerId !== '00' && route.currentRoute.value.query.layerId !== '000'){
        // AddTransfromControls(route.currentRoute.value.query.layerId);

        addTransfromControls(route.currentRoute.value.query.layerId);

      }
    }
  }

});
// Update svg for hotspot
watch(() => props.gsplatLayersUpdate, (val) => {
  if (Object.keys(val).length > 0){
    const elem = document.getElementById(val.id);
    // New elem svg update
    if (val.type !== null){
      elem.classList.remove('newbeeHotSpot'); // Remove temporary class
      elem.innerHTML = `<img src="${val.svg_url}" />`;
    }
  }
});

function surroundings () {

  console.log(svgLayers.value);
  if (Object.keys(svgLayers.value).length > 0){
    Object.keys(svgLayers.value).forEach((key) => {
      console.log(key);
      if (svgLayers.value[key].type !== 'plane'){
        const div = document.createElement('div');
        htmlLayers.value.push(div);
        div.id = key;
        div.innerHTML = `<img src="${cdn(svgLayers.value[key].svg_url)}" />`;
        div.style.pointerEvents = 'auto';
        div.setAttribute('class', 'amenityIcons');
        const parentElement = gsplat.value;
        parentElement.appendChild(div);
        document.getElementById(key).addEventListener( 'click', () => route.push({ path: route.currentRoute.value.path, query: {layerId: key} }) );
        const loc1 = new CSS2DObject(document.getElementById(key));
        loc1.name=key;
        scene.add(loc1);
        loc1.position.set(svgLayers.value[key].position.x, svgLayers.value[key].position.y, svgLayers.value[key].position.z);
      } else {
        const plane =new Mesh(new PlaneGeometry(svgLayers.value[key].scale.width, svgLayers.value[key].scale.height), new MeshBasicMaterial({colorWrite: true, depthWrite: true}));
        plane.position.set(svgLayers.value[key].position.x, svgLayers.value[key].position.y, svgLayers.value[key].position.z);
        plane.name= key; // Name of the mesh
        scene.add(plane);
        plane.addEventListener('click', function (){
          console.log('inside click');
        });
        plane.layers.enableAll();
        plane.material.side=2;
      }

    });
  }
}

onMounted(() => {

  if (props.data && props.svgLayers){
    console.log('Hey There ! I\'m on Mounted');
    const container = containerVal.value;
    console.log(containerVal.value.offsetWidth);
    const width = containerVal.value.offsetWidth;
    const height = containerVal.value.offsetHeight;
    renderer.value = new WebGLRenderer({ antialias: false, enableThreeShaderIntegration: false, powerPreference: 'high-performance', precision: 'lowp' });
    renderer.value.setPixelRatio(1);
    renderer.value.setSize(width, height, false);
    container.appendChild(renderer.value.domElement);
    renderer.value.domElement.style.width = width;
    renderer.value.domElement.style.height = height;
    const labelRenderer = new CSS2DRenderer();
    labelRenderer.setSize(width, height);
    labelRenderer.domElement.style.position = 'absolute';
    labelRenderer.domElement.style.top = '0px';
    containerVal.value.appendChild(labelRenderer.domElement);
    labelRenderer.domElement.style.pointerEvents = 'none';
    scene = new Scene();
    camera = new PerspectiveCamera(45, width / height, 1, 10000);
    window.camera = camera;
    controls = new OrbitControls(camera, renderer.value.domElement);
    window.controls = controls;
    controls.minDistance = 2.9; // Set the minimum distance the camera can be from the center
    controls.maxDistance = 3; // Set the maximum distance the camera can be from the center
    // Controls.update() must be called after any manual changes to the camera's transform
    camera.position.set(0, 0.1, 2.9);
    controls.update();
    // Controls.autoRotate = true;
    controls.maxPolarAngle = 1.25;
    controls.minPolarAngle = 1.25;

    controls.enableDamping = true;
    // Controls.dampingFactor = 0.2;
    // Const geometry = new THREE.BoxGeometry( 1, 1, 1 );
    // Const material = new THREE.MeshBasicMaterial( { color: 0x00ff00 } );
    // Const cube = new THREE.Mesh( geometry, material );
    // Scene.add( cube );
    // Const loader = new SplatLoader(renderer)
    // Const testSplat=await loader.loadAsync(props.modelUrl);

    // Const test = new Splat(testSplat, camera)
    // Scene.add(test)
    const splats = new LumaSplatsThree({
      source: props.data.source,
      // Controls the particle entrance animation
      particleRevealEnabled: true,
    });

    scene.add(splats);
    // Console.log(splats)

    transControls = new TransformControls(camera, renderer.value.domElement);
    // GizmotransControls.addEventListener('change', animate);

    transControls.addEventListener('dragging-changed', function (event) {

      controls.enabled = !event.value;

    });

    const animate = function () {
      window.requestAnimationFrame(animate);

      // Required if controls.enableDamping or controls.autoRotate are set to true
      controls.update();
      TWEEN.update();
      renderer.value.render(scene, camera);
      labelRenderer.render(scene, camera);

    };

    animate();

    const setAutoRotate = function (rotate) {
      if (!rotate) {
        mousedown.value = 1;
        controls.autoRotate = false;
        clearTimeout(autoRotateTimeout.value);
      } else {
        mousedown.value = 0;
        autoRotateTimeout.value = setTimeout(() => {
          if (!mousedown.value) {
            controls.autoRotate = false;
          }

        }, 5000);
      }

    };

    container.onmousedown = () => {
      setAutoRotate(false);
    };
    container.ontouchstart = () => {
      setAutoRotate(false);
    };
    container.onmouseup = () => {
      setAutoRotate(true);
    };
    container.ontouchend = () => {
      setAutoRotate(true);
    };

    window.addEventListener('keydown', function (event) {

      switch (event.keyCode) {

        case 81: // Q
          transControls.setSpace(transControls.space === 'local' ? 'world' : 'local');
          break;

        case 16: // Shift
          transControls.setTranslationSnap(100);
          transControls.setRotationSnap(MathUtils.degToRad(15));
          transControls.setScaleSnap(0.25);
          break;

        case 87: // W
          transControls.setMode('translate');
          break;

        case 69: // E
          transControls.setMode('rotate');
          break;

        case 82: // R
          transControls.setMode('scale');
          break;

        case 187:
        case 107: // +, =, num+
          transControls.setSize(transControls.size + 0.1);
          break;

        case 189:
        case 109: // -, _, num-
          transControls.setSize(Math.max(transControls.size - 0.1, 0.1));
          break;

        case 88: // X
          transControls.showX = !transControls.showX;
          break;

        case 89: // Y
          transControls.showY = !transControls.showY;
          break;

        case 90: // Z
          transControls.showZ = !transControls.showZ;
          break;

        case 32: // Spacebar
          transControls.enabled = !transControls.enabled;
          break;

        case 27: // Esc
          transControls.reset();
          break;

      }

    });
    surroundings();
  }
});

onUnmounted(() => {
  scene.clear();
});

</script>

<template>
  <div
    id="container"
    ref="containerVal"
  />
  <div
    id="gsplat"
    ref="gsplat"
  />
</template>

<style scoped>
#container {
  position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%
		/* image-rendering: pixelated; */
}

.menu {
	position: absolute;
	bottom: 0;
	left: 0;
	background: #000000a6;
	color: white;
	display: flex;
	width: 100%;
	height: inherit;
	font-size: 1.75vh;
	flex-direction: row;
	justify-content: space-evenly;
	align-items: center;
}

.menu span {
	border: solid 1px;
	width: 100%;
	text-align: center;
	padding: 10px;
	cursor: pointer;
	user-select: none;
}

</style>
<style>
.newbeeHotSpot{
  width: 46px;
  height: 46px;
  border-radius: 20px;
  background-color: white;
  border: 1px solid black;
}

.amenityIcons{
	opacity: 0.7;
	cursor: pointer;
}
.amenityIcons:hover{
	opacity: 1;
}
</style>

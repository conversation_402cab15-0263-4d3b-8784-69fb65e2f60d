<script setup>
import { nextTick, ref, watch } from 'vue';
import router from '@/router';
import { useRoute } from 'vue-router';
import { Org_Store } from '../../store/organization';
import { ProjectStore } from '../../store/project';
import { getCategories as getAmenitiesCategories} from '../../api/projects/amenties';
import { getAllScenes as getAllMasterScene, getScene as getMasterScene, moveMasterSceneToTrash, restoreMasterScenes } from '../../api/masterScene/index';
import { getListofScenes, getScene, moveSceneToTrash, restoreScenes, updateScene } from '@/api/projects/scene';
import { isMasterScenePath } from '../../helpers/helpers';
import AddSceneOptionsMenu from './AddSceneOptionsMenu.vue';
import EditSceneSettingModal from './EditSceneSettingModal.vue';
import { getListofSidebarOptions, moveSidebarToTrash, updateBulkOptions } from '@/api/projects/sidebar/index';
import { GetAllTrash } from '@/api/trash';
import NestedReOrder from '../common/NestedReOrder.vue';
import { getCookie } from '@/helpers/domhelper';
import { onClickOutside } from '@vueuse/core';
import CreateSidebarModal from '../Projects/sidebar/CreateSidebarModal.vue';
import DeleteModalContent from '../common/ModalContent/DeleteModalContent.vue';
import Modal from '../common/Modal/Modal.vue';

const route = useRoute();

const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const selectedSvg = ref();
const sceneDetails = ref(null);
const categoryList = ref();

const sceneList = ref();
const selectedScene = ref();
const selectedSceneDelete = ref();
const project_id = ref(route.params.project_id);
const currentScene = ref(route.params.scene_id);
const openArchiveList = ref(false);
const TrashItems = ref({});
const openAddSceneOptionsMenu = ref();
const openUnarchiveMenu = ref();
const openSceneSettings = ref(false);
const categorizedScenes = ref();
const createSceneClick = ref({x: null,
  y: null,
});
const unArchiveClick = ref({x: null,
  y: null,
});
const SceneReOrderData = ref(null);
const sidebarOptionList = ref();

const actionMenuRef = ref();
const addSceneMenuRef = ref();
const archiveListRef = ref();
const categoryModalRef = ref();
const openCreateCategory = ref(false);
const openCategoryDeleteModal = ref(false);
const CategoryToDelete = ref();
const categoryDeleteLoader = ref(false);

// const toggleOpenSceneByCategory = (item) => {
//   const index = openSceneByCategory.value.indexOf(item);
//   if (index > -1) {
//     // Item is already in the selected list, remove it
//     openSceneByCategory.value.splice(index, 1);
//   } else {
//     // Item is not in the selected list, add it
//     openSceneByCategory.value.push(item);
//   }
// };

// Refresh projects
if (!isMasterScene.value){
  projectStore.RefreshScenes(projectId.value);
  projectStore.RefreshLandmarks(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
  projectStore.RefreshAmenities(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshCommunities(projectId.value);
}
newOrganizationStore.RefreshMasterScenes();
newOrganizationStore.RefreshProjects();

const setCurrentScene = (sceneId) => {
  currentScene.value = sceneId;
  console.log("current scene id", currentScene.value);
};

const handleGetScene = () => {

  if (isMasterScene.value && sceneId.value) {
    getMasterScene(sceneId.value).then((res) => {
      sceneDetails.value = res;
      console.log(res);
      if (res.sceneData.type==='rotatable_image_frame'){
        setCurrentScene(res.sceneData.parent);
      } else {
        setCurrentScene(res.sceneData._id);
      }
    });
  } else if (projectId.value && sceneId.value) {
    getScene(projectId.value, sceneId.value).then((res) => {
      sceneDetails.value = res;
      console.log(sceneDetails.value);
      if (res.sceneData.type==='rotatable_image_frame'){
        setCurrentScene(res.sceneData.parent);
      } else {
        console.log("**************", res);
        setCurrentScene(res.sceneData._id);
      }

    });
  } else {
    return;
  }
};

handleGetScene(); // Initialize

watch(() => currentScene.value, () => {
  console.log('currentScene changed:', currentScene.value);
});

document.addEventListener('refreshGetScene', () => { // After adding new svgs
  handleGetScene();
});

if (!isMasterScene.value){
  getAmenitiesCategories(projectId.value).then((res) => { // Get list of amenities categories
    console.log(res);
    categoryList.value = res.map((elem) => {
      return {name: elem.category};
    });
  }).catch((err) => {
    console.log('output->err', err);
  });
}

function customSort (items) {
  // First, sort the items
  const sortedItems = [...items].sort((a, b) => {
    // Case 1: If both items have no order property
    if (a.order === undefined && b.order === undefined) {
      // If one is 'UNCATEGORIZED', it comes last
      if (a.category === 'UNCATEGORIZED') {
        return 1;
      }
      if (b.category === 'UNCATEGORIZED') {
        return -1;
      }
      return 0; // Keep original order for items with no order
    }

    // Case 2: If only one item has no order property, it goes to the end
    if (a.order === undefined) {
      return 1;
    }
    if (b.order === undefined) {
      return -1;
    }

    // Case 3: Both have order property
    // If one is 'UNCATEGORIZED', it goes last regardless of order
    if (a.category === 'UNCATEGORIZED' && b.category !== 'UNCATEGORIZED') {
      return 1;
    }
    if (a.category !== 'UNCATEGORIZED' && b.category === 'UNCATEGORIZED') {
      return -1;
    }

    // Case 4: Both have order and neither is 'UNCATEGORIZED' (or both are)
    // Sort by order
    return a.order - b.order;
  });

  // Then reassign order values sequentially
  return sortedItems.map((item, index) => {
    return { ...item, order: index + 1 };
  });
}

// Coordinates

// const getIndividualCoordniatesInfo = (id) => {
//   const coordinates = sceneDetails.value?.sceneData?.coordinates;

//   if (!coordinates?.length) {
//     return false;
//   } // no coordinates

//   const targetIndex = coordinates.findIndex((item) => item._id === id); // find the coordinate index
//   console.log(route.query.svgId);
//   console.log(targetIndex);

//   return targetIndex !== -1 ? coordinates[targetIndex] : false;

// };

// Close Layer Selection
// const handleCloseUpdateSidebar = () => {
//   if (sceneDetails.value.sceneData.type !== 'earth'){
//     //
//     router.push({ path: route.path, query: { svgId: route.query.svgId } });
//   } else if (sceneDetails.value.sceneData.type === 'earth' && route.query.createCorodinates){
//   // create coordinates
//     router.push({ path: route.path, query: {} });
//   } else if (sceneDetails.value.sceneData.type === 'earth' && route.query.svgId) {
//   // update coordinates
//     selectedSvg.value = null;
//   }
// };

watch(() => selectedSvg.value, () => {
  if (!selectedSvg.value) {
    router.push({ path: route.path, query: {} });
  } else {
    router.push({ path: route.path, query: { svgId: selectedSvg.value._id } });
  }
});

watch(() => route.params.params, (newId, oldId) => {
  console.log('Route param changed:', oldId, '=>', newId);
});

const props = defineProps({
  selectedMenu: String,
});

watch(() => props.selectedMenu, (newVal) => {
  if (newVal === 'pages') {
    selectedSvg.value = null;
  }
});

function categorizeScenes (data) {
  const categorizedData = {};

  // Loop through each scene in the input data
  for (const sceneId in data) {
    const scene = data[sceneId];
    const categoryId = scene.sceneData.category;

    // If there's a category, use it; otherwise, use 'uncategorized'
    const category = categoryId || 'uncategorized';

    // If the category doesn't exist in the result object, create it
    if (!categorizedData[category]) {
      categorizedData[category] = {};
    }

    // Add the scene to its corresponding category
    categorizedData[category][sceneId] = scene.sceneData;
  }

  return categorizedData;
}

function createCategoryBasedSceneData (data) {
  // Create a map for categories
  const categoryMap = {};
  const DEFAULT_CATEGORY = "UNCATEGORIZED";

  console.log(sidebarOptionList.value);
  sidebarOptionList.value && Object.values(sidebarOptionList.value).forEach((item) => {
    categoryMap[item._id] = {
      id: item._id,
      name: item.name,
      category: item._id,
      linked_scenes: [],
      draggable: true,
      isCategory: true,
      order: item.order?item.order:null,
    };
  });
  categoryMap.UNCATEGORIZED = {
    id: 'UNCATEGORIZED',
    name: `UNCATEGORIZED`,
    category: `UNCATEGORIZED`,
    draggable: false,
    isCategory: true,
    linked_scenes: [],
  };

  // Process each scene and add to appropriate category
  for (const sceneId in data) {
    const sceneData = data[sceneId].sceneData;

    // Assign to UNCATEGORIZED category if no category exists
    const category = sceneData.category || DEFAULT_CATEGORY;

    // Create simplified scene object
    const sceneObj = {
      id: sceneId,
      name: sceneData.name,
      type: sceneData.type,
      draggable: true,
      category: category,
    };

    // Initialize category in categoryMap if it doesn't exist
    if (!categoryMap[category]) {
      categoryMap[category] = {
        id: category,
        name: category,
        category: category,
        linked_scenes: [],
      };
    }

    // Add scene to its category
    categoryMap[category].linked_scenes.push(sceneObj);
  }

  return customSort(Object.values(categoryMap));
}
/* Setup Data */

// setupData (Modify based on your needs)
const setupData = (data) => {
  console.log("setupData", data);
  // Structuring your data's
  const convertedData = createCategoryBasedSceneData(data);

  console.log(convertedData);

  // Update the your component reference
  SceneReOrderData.value = convertedData;

};

const handleGetSidebarOptions = async () => {
  try {
    const res = await getListofSidebarOptions(projectId.value);
    console.log(res);
    sidebarOptionList.value = res;
    projectStore.SyncMultipleSidebarOptions(res);
  } catch (error) {
    console.error("Failed to fetch sidebar options:", error);
  }
};

const handleGetListOfScenes = () => {
  if (!isMasterScene.value){
    getListofScenes(projectId.value).then((res) => {
      sceneList.value = res;
      setupData(res);
      categorizedScenes.value = categorizeScenes(res);
      console.log("categorized scene -> ", categorizedScenes.value);
    });
  } else {
    getAllMasterScene().then((res) => {
      sceneList.value = res;
      setupData(res);
      projectStore.SyncMultipleScenes(res);
      categorizedScenes.value = categorizeScenes(res);
      console.log("categorized scene -> ", categorizedScenes.value);
    });
  }

};

document.addEventListener('refreshAfterSceneEdit', () => { // after editing scene
  handleGetListOfScenes();
});

if (!isMasterScene.value){
  handleGetSidebarOptions().then(() => {
    handleGetListOfScenes();
  });
} else {
  handleGetListOfScenes();
}

const handleGetAllTrash = () => {
  if (!isMasterScene.value){
    GetAllTrash('scenes', projectId.value).then((res) => {
      console.log("trash scenes",  res.items);
      TrashItems.value = Object.values(res.items).filter((item) => {
        return item.type.split('_')[0]===projectId.value && item.type.split('_')[1]==='scenes';
      });
    });
  } else {
    const organization_id = getCookie('organization');
    GetAllTrash('master_scenes', organization_id).then((res) => {
      console.log("trash master scenes",  res.items);
      TrashItems.value = Object.values(res.items).filter((item) => {
        console.log(typeof item.type,  typeof (organization_id.toLowerCase()+'_master_scenes'),  item.type === (organization_id.toLowerCase()+'_master_scenes'));
        return item.type=== (organization_id.toLowerCase()+'_master_scenes');
      });
    });
  }
};

function getNextScene (categories, catId, sceneId) {

  console.log("getNextScene", categories, catId, sceneId);
  for (let i = 0; i < categories.length; i++) {
    const scenes = categories[i].linked_scenes;
    const idx = scenes.findIndex((s) => s.id === sceneId);

    if (categories[i].id === catId && idx !== -1) {
      // Next in same category
      if (idx + 1 < scenes.length) {
        return scenes[idx + 1];
      }

      // First of next category
      if (i + 1 < categories.length && categories[i + 1].linked_scenes.length > 0) {
        return categories[i + 1].linked_scenes[0];
      }

      // Previous in same category
      if (idx - 1 >= 0) {
        return scenes[idx - 1];
      }

      // Last of previous category
      if (i > 0 && categories[i - 1].linked_scenes.length > 0) {
        return categories[i - 1].linked_scenes.at(-1);
      }
    }
  }
  return null;
}

const handleArchive = (sceneIdToArchive, sceneToArchiveCat) => {
  const nextSceneToLoad =  getNextScene(SceneReOrderData.value, sceneToArchiveCat, sceneIdToArchive)?.id;
  if (!isMasterScene.value){
    const payload = {
      scene_id: [sceneIdToArchive],
      timeStamp: Date.now(),
    };
    moveSceneToTrash(project_id.value, payload).then(() => {
      if (currentScene.value === sceneIdToArchive){
        if (nextSceneToLoad){
          router.push({ path: `/projects/${project_id.value}/design/scenes/${nextSceneToLoad}`, query: { } });
        } else {
          router.push({ path: `/projects/${project_id.value}/design/scenes`, query: { } });
        }

      }
      handleGetListOfScenes();
      handleGetAllTrash();
    });
  } else {
    const payload = {
      scene_Ids: [sceneIdToArchive],
      timeStamp: Date.now(),
    };
    moveMasterSceneToTrash(payload).then(() => {
      handleGetListOfScenes();
      handleGetAllTrash();
    });
  }

};

handleGetAllTrash();

watch(() => selectedSvg.value, () => {
  if (!selectedSvg.value) {
    router.push({ path: route.path, query: {} });
  } else {
    router.push({ path: route.path, query: { svgId: selectedSvg.value._id } });
  }
});

const handleRestoreScene = (trash_id, scene_id) => {
  if (!isMasterScene.value){
    restoreScenes(project_id.value, trash_id).then(() => {
      console.log("done");
      handleGetListOfScenes();
      handleGetAllTrash();
    });
  } else {
    restoreMasterScenes(trash_id, scene_id).then(() => {
      handleGetListOfScenes();
      handleGetAllTrash();
    });
  }
};

document.addEventListener('getScenes', () => {
  handleGetListOfScenes();
});

const handleCloseEditSceneSettings = () => {
  openSceneSettings.value = null;
  selectedSceneDelete.value = null;
};

const handleAddSceneOption = (event, category) => {
  openAddSceneOptionsMenu.value===category?openAddSceneOptionsMenu.value=null:openAddSceneOptionsMenu.value=category;

  const rect = event.target.getBoundingClientRect();

  createSceneClick.value.x = rect.right + window.scrollX;
  createSceneClick.value.y = rect.bottom + window.scrollY;

};

// --------  reorder logic --------

/* ---------------- Methods --------------------- */

// function createCategoryBasedNestedSceneData(data) {
//   // Create maps for faster lookups during processing
//   const sceneMap = {};
//   const categoryMap = {};
//   const DEFAULT_CATEGORY = "UNCATEGORIZED";

//   Object.values(sidebarOptionList.value).forEach(item => {
//     categoryMap[item._id] = {
//       id: item._id,
//       name: `Category ${item.name}`,
//       category: item._id,
//       linked_scenes: [],
//       order:item.order?item.order:null
//     };
//   });
//   categoryMap.UNCATEGORIZED = {
//       id: 'UNCATEGORIZED',
//       name: `Category UNCATEGORIZED`,
//       category: `UNCATEGORIZED`,
//       linked_scenes: []
//     };

//   // First pass: extract essential information and assign categories
//   for (const sceneId in data) {
//     const sceneData = data[sceneId].sceneData;

//     // Assign to UNCATEGORIZED category if no category exists
//     const category = sceneData.category || DEFAULT_CATEGORY;

//     // Create a simplified object with essential properties
//     sceneMap[sceneId] = {
//       id: sceneId,
//       name: sceneData.name,
//       linked_scenes: [],
//       type: sceneData.type,
//       category: category,
//       isRoot: sceneData.root === true,
//       originalParent: sceneData.parent
//     };

//     // Initialize category in categoryMap if it doesn't exist
//     if (!categoryMap[category]) {
//       categoryMap[category] = {
//         id: `category-${category}`,
//         name: `Category ${category}`,
//         category: category,
//         linked_scenes: []
//       };
//     }
//   }

//   // Process each scene to build the hierarchy
//   function processScenes() {
//     // First, identify all top-level scenes (no parent or root flag)
//     for (const sceneId in sceneMap) {
//       const scene = sceneMap[sceneId];
//       if (!scene.originalParent || scene.isRoot) {
//         // Create a copy for the category
//         const sceneCopy = JSON.parse(JSON.stringify(scene));
//         delete sceneCopy.originalParent;
//         delete sceneCopy.isRoot;

//         // Add to its category's linked_scenes
//         categoryMap[scene.category].linked_scenes.push(sceneCopy);

//         // Mark as processed
//         scene.processed = true;
//       }
//     }

//     // Helper to find a scene by ID in the nested structure
//     function findSceneById(nodes, id) {
//       for (let i = 0; i < nodes.length; i++) {
//         if (nodes[i].id === id) {
//           return nodes[i];
//         }

//         if (nodes[i].linked_scenes && nodes[i].linked_scenes.length > 0) {
//           const found = findSceneById(nodes[i].linked_scenes, id);
//           if (found) return found;
//         }
//       }
//       return null;
//     }

//     // Process all remaining scenes that have parents
//     let anyProcessed = true;
//     const processedIds = new Set();

//     // Continue until no more scenes can be processed
//     while (anyProcessed) {
//       anyProcessed = false;

//       for (const sceneId in sceneMap) {
//         // Skip already processed scenes
//         if (processedIds.has(sceneId)) continue;

//         const scene = sceneMap[sceneId];
//         if (!scene.originalParent) continue; // Skip scenes without parents

//         const parentId = scene.originalParent;
//         const parentScene = sceneMap[parentId];

//         // Skip if parent doesn't exist
//         if (!parentScene) {
//           // No parent found, add to its own category
//           const sceneCopy = JSON.parse(JSON.stringify(scene));
//           delete sceneCopy.originalParent;
//           delete sceneCopy.isRoot;
//           categoryMap[scene.category].linked_scenes.push(sceneCopy);
//           processedIds.add(sceneId);
//           anyProcessed = true;
//           continue;
//         }

//         // Check if parent and current scene have different categories
//         if (parentScene.category !== scene.category) {
//           // Different category - add directly to its own category
//           const sceneCopy = JSON.parse(JSON.stringify(scene));
//           delete sceneCopy.originalParent;
//           delete sceneCopy.isRoot;
//           categoryMap[scene.category].linked_scenes.push(sceneCopy);
//           processedIds.add(sceneId);
//           anyProcessed = true;
//           continue;
//         }

//         // Same category - find parent in the structure
//         let parentNode = null;
//         for (const cat in categoryMap) {
//           parentNode = findSceneById(categoryMap[cat].linked_scenes, parentId);
//           if (parentNode) break;
//         }

//         if (parentNode) {
//           // Parent found, add as child
//           const sceneCopy = JSON.parse(JSON.stringify(scene));
//           delete sceneCopy.originalParent;
//           delete sceneCopy.isRoot;
//           parentNode.linked_scenes.push(sceneCopy);
//           processedIds.add(sceneId);
//           anyProcessed = true;
//         }
//       }
//     }

//     // Process any remaining unprocessed scenes (circular references, etc.)
//     for (const sceneId in sceneMap) {
//       if (!processedIds.has(sceneId)) {
//         const scene = sceneMap[sceneId];
//         const sceneCopy = JSON.parse(JSON.stringify(scene));
//         delete sceneCopy.originalParent;
//         delete sceneCopy.isRoot;
//         categoryMap[scene.category].linked_scenes.push(sceneCopy);
//       }
//     }
//   }

//   // Process all scenes to build the hierarchy
//   processScenes();

//   // Convert categoryMap to array of categories
//   const result = Object.entries(categoryMap).map(([, value]) => ({
//     ...value,
//   }));
//   return customSort(result);
// }

const handleCategoryReorder = (project_id, reOrderedItems) => {
  const reqBody = {
    'query': reOrderedItems,
    'project_id': project_id,
  };
    // Api Call
  updateBulkOptions(reqBody).then(() => {
    getListofSidebarOptions(project_id).then((res) => {
      projectStore.SyncMultipleSidebarOptions(res);
    });
  });
};

const handleUpdateScene = (values) => {
  updateScene(values).then(() => {
    handleGetListOfScenes();
  });
};

/* Emits Handler */

// Child Sort
const handleChildSortEmit = (val) => {
  console.log("-----------------------------");
  console.log('Parent~ChildSort', val);
  console.log("-----------------------------");
  if (val.baseParentReference===null  ){
    const check = val.sortedItems.find((item) => item.id === "UNCATEGORIZED");
    console.log(check);
    if (check){
    // Reset by 'sort' again with it's original order
      const resetSortDataItems = [...SceneReOrderData.value].sort((item1, item2) => item1.order - item2.order);
      // set timeout
      setTimeout(() => {
        console.log("Set TimeOut");
        SceneReOrderData.value = resetSortDataItems;
      }, 500);
    } else {
      const reOrderedItems = [];
      reOrderedItems.push(val.draggedItem);
      val.sortedItems.forEach((item) => {
        reOrderedItems.push(item);
      });

      handleCategoryReorder(projectId.value, reOrderedItems);

    }
  }

};

// Child Reparenting Sort
const handleChildReparentingSortEmit = (val) => {
  // console.log("-----------------------------xxxxx");
  console.log('Parent~ChildReparentingSort', val);
  console.log("dragged item - >", val.draggedItem.Item.id);
  console.log("dragged to - >", val.add.baseParentReference.id);
  console.log("dragged to category - >", val.add.baseParentReference.category);
  console.log("dragged from - >", val.remove.baseParentReference.id);
  console.log("-----------------------------xxxxx");

  const obj = {
    project_id: projectId.value,
    organization_id: getCookie('organization'),
    scene_id: val.draggedItem.Item.id,
    ...(val.add.baseParentReference.category !=='UNCATEGORIZED' ? {category: val.add.baseParentReference.category}:{category: null}),
  };
  handleUpdateScene(obj);
};
onClickOutside(actionMenuRef, () => {
  selectedSceneDelete.value = null;
});
onClickOutside(addSceneMenuRef, () => {
  openAddSceneOptionsMenu.value = null;
});
onClickOutside(archiveListRef, () => {
  openArchiveList.value = null;
});
onClickOutside(categoryModalRef, () => {
  openCreateCategory.value = false;
});

const handleSceneClick = (scene_id) => {

  // :href="!isMasterScene ? `/projects/${project_id}/design/scenes/${item.id}` : `/masterscenes/${item.id}`"
  if (isMasterScene.value){
    router.push({ path: `/masterscenes/${scene_id}`, query: { } });
  } else {
    router.push({ path: `/projects/${project_id.value}/design/scenes/${scene_id}`, query: { } });
  }
};

watch(() => route.params.scene_id, (newSceneId, oldSceneId) => {
  if (newSceneId) {
    sceneId.value = newSceneId;
    console.log('&&&&&&&&&&&:', oldSceneId, '=>', newSceneId);
    handleGetScene();
  } else {
    console.log('No sceneId in current route.');
  }
});

const handleCreateCategory = (isApiCalled) => {
  openCreateCategory.value = false;
  if (isApiCalled){
    handleGetSidebarOptions().then(() => {
      handleGetListOfScenes();
    });
  }
};

function scrollToSection (id) {
  nextTick(() => {
    console.log("scrollToSection ->", id);
    const el = document.getElementById(id);
    if (!el) {
      console.warn(`Element with id "${id}" not found.`);
      return;
    }

    el.scrollIntoView({
      behavior: 'smooth',
      // block: 'start',
    });
  });
}

watch(() => route.params.scene_id, () => {
  console.log('fdfd', route.params.scene_id);
  if (route.params.scene_id) {
    scrollToSection(`scene-${route.params.scene_id}`);
  }
});

watch(() => openSceneSettings.value, () => {
  console.log("openSceneSettings changed", openSceneSettings.value);
});

function handleScroll () {
  if (route.params?.scene_id) {
    console.log('mounted scene->', route.params.scene_id);
    scrollToSection('scene-'+route.params.scene_id);
  }
}

const handleOpenUnArchiveMenu = (event, itemId) => {
  if (openUnarchiveMenu.value === itemId){
    openUnarchiveMenu.value = null;
  } else {
    openUnarchiveMenu.value = itemId;
  }

  const rect = event.target.getBoundingClientRect();

  unArchiveClick.value.x = rect.right + window.scrollX;
  unArchiveClick.value.y = rect.bottom + window.scrollY;
};

// console.log("unArchiveClick", unArchiveClick.value);

const handleCategoryToTrash = () => {
  categoryDeleteLoader.value = true;

  const obj = {
    sidebar_id: [CategoryToDelete.value],
    timeStamp: Date.now(),
  };

  moveSidebarToTrash(obj, projectId.value)
    .then(() => {
      const updateScenes = [];

      SceneReOrderData.value.forEach((item) => {
        if (item.id === CategoryToDelete.value) {
          item.linked_scenes.forEach((scene) => {
            updateScenes.push(updateScene({
              scene_id: scene.id,
              category: null,
              project_id: projectId.value,
            }));
          });
        }
      });

      return Promise.all(updateScenes); // 👈 important: return this Promise
    })
    .then(() => {
      // 👇 this runs only after moveSidebarToTrash AND all updateScene calls succeed
      categoryDeleteLoader.value = false;
      openCategoryDeleteModal.value = false;
      CategoryToDelete.value = null;
      getListofSidebarOptions(projectId.value).then((res) => {
        sidebarOptionList.value = res;
        projectStore.SyncMultipleSidebarOptions(res);
        handleGetListOfScenes();
      });
    })
    .catch((err) => {
      console.error('Error during deletion:', err);
      categoryDeleteLoader.value = false;
    });
};

</script>

<template>

      <div class="  w-full h-full bg-white rounded-t-lg p-2 flex flex-col">
        <div class="flex-1 flex flex-col h-full w-full">
        <div class="flex justify-between items-center px-3 w-full">
          <h2 class=" text-gray-500 text-lg font-semibold">
            Pages
          </h2>
           <button   @click="openCreateCategory=true" :class="`${openCreateCategory?'bg-blue-600 fill-white':'bg-gray-100 fill-gray-900'}`" class="flex justify-center items-center w-6 h-6 rounded-md cursor-pointer">
                  <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                </button>
        </div>
        <!-- Skeleton area -->
        <div class="flex-1 mt-3 w-full" v-if="!SceneReOrderData">
                    <div v-for="i in 3" :key="i"  class="mb-3">

                      <div class="my-1.5 h-6 w-full flex items-center gap-1.5">
            <div class="h-4 w-4 rounded-full scene-skeleton-loader ">

              </div>
              <div class="flex-1 w-full h-full scene-skeleton-loader">
              </div>
                 <div class="h-full w-10 rounded-sm  scene-skeleton-loader">

                </div>

          </div>

            <div class="flex justify-between align-center p-1.5 ml-4  my-1 gap-2  rounded-md"
          >
                <div class="w-full flex-1 scene-skeleton-loader"></div>
                <div class="px-1 h-6 w-4 rounded-sm bg-[#e5e7eb]"></div>
          </div>
            <div class="flex justify-between align-center p-1.5 ml-4  my-1 gap-2  rounded-md"
          >
                <div class="w-full flex-1 scene-skeleton-loader"></div>
                <div class="px-1 h-6 w-4 rounded-sm bg-[#e5e7eb]"></div>
          </div>
            <div class="flex justify-between align-center p-1.5 ml-4  my-1 gap-2  rounded-md"
          >
                <div class="w-full flex-1 scene-skeleton-loader"></div>
                <div class="px-1 h-6 w-4 rounded-sm bg-[#e5e7eb]"></div>
          </div>

                    </div>
      </div>
       <div class=" flex-1 mt-3 relative z-10 overflow-y-auto" v-else>
                <!-- v-model ----- :modelValue="parentList" @update:modelValue="parentList = $event"  -->
                <NestedReOrder v-model="SceneReOrderData" groupName="scenes" :allowChildReparenting="true" :allowChildSort="true" uniqueKey="id" nestedChildKey="linked_scenes" ghostClass="sampe_ghost" animationMilliSec="450" sortReferenceKey="id" @handleChildSort="(val) => handleChildSortEmit(val) " @handleChildReparentingSort="(val) => handleChildReparentingSortEmit(val) "  @ready="handleScroll">

                    <template #default="{item}">

                      <div v-if="item.isCategory"  class="my-1.5 h-6 w-full flex gap-1.5 relative">

            <div class="flex gap-1.5">
            <div class="flex justify-center items-center"><svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M8 1C6.61553 1 5.26215 1.41054 4.11101 2.17971C2.95987 2.94888 2.06266 4.04213 1.53285 5.32121C1.00303 6.6003 0.86441 8.00776 1.13451 9.36563C1.4046 10.7235 2.07129 11.9708 3.05026 12.9497C4.02922 13.9287 5.2765 14.5954 6.63437 14.8655C7.99224 15.1356 9.3997 14.997 10.6788 14.4672C11.9579 13.9373 13.0511 13.0401 13.8203 11.889C14.5895 10.7378 15 9.38447 15 8C14.998 6.14411 14.2598 4.36482 12.9475 3.0525C11.6352 1.74019 9.85589 1.00204 8 1ZM7.65 3.8C7.85767 3.8 8.06068 3.86158 8.23335 3.97696C8.40602 4.09233 8.5406 4.25632 8.62008 4.44818C8.69955 4.64004 8.72034 4.85116 8.67983 5.05484C8.63931 5.25852 8.53931 5.44562 8.39246 5.59246C8.24562 5.73931 8.05853 5.83931 7.85485 5.87982C7.65117 5.92034 7.44005 5.89954 7.24818 5.82007C7.05632 5.7406 6.89233 5.60602 6.77696 5.43335C6.66158 5.26068 6.6 5.05767 6.6 4.85C6.6 4.57152 6.71063 4.30445 6.90754 4.10754C7.10445 3.91062 7.37152 3.8 7.65 3.8ZM9.4 11.5H6.6C6.41435 11.5 6.2363 11.4262 6.10503 11.295C5.97375 11.1637 5.9 10.9856 5.9 10.8C5.9 10.6143 5.97375 10.4363 6.10503 10.305C6.2363 10.1737 6.41435 10.1 6.6 10.1H7.3V8H6.6C6.41435 8 6.2363 7.92625 6.10503 7.79497C5.97375 7.6637 5.9 7.48565 5.9 7.3C5.9 7.11435 5.97375 6.9363 6.10503 6.80502C6.2363 6.67375 6.41435 6.6 6.6 6.6H8C8.18565 6.6 8.3637 6.67375 8.49498 6.80502C8.62625 6.9363 8.7 7.11435 8.7 7.3V10.1H9.4C9.58565 10.1 9.7637 10.1737 9.89498 10.305C10.0263 10.4363 10.1 10.6143 10.1 10.8C10.1 10.9856 10.0263 11.1637 9.89498 11.295C9.7637 11.4262 9.58565 11.5 9.4 11.5Z" fill="#6B7280"/>
              </svg>
              </div>
              <div class="flex items-center max-w-20">
                <h2 class="text-xs leading-4 text-gray-500 font-semibold ml-1 overflow-hidden text-ellipsis whitespace-nowrap">{{item.name }}</h2>
              </div>
              </div>
              <div class="w-16 flex-1 flex justify-end gap-2  h-full" >
                <button v-if="item.id!=='UNCATEGORIZED'" class="flex justify-center items-center w-6 h-6 rounded-md cursor-pointer" @click.stop="()=>{openCategoryDeleteModal=true;CategoryToDelete=item.id}">
                <svg class="fill-gray-500 w-4.5 h-4.5" width="14" height="15" viewBox="0 0 14 15" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1268_12790)">
<path d="M12.7024 3.44737H9.85055V1.97368C9.85055 1.58284 9.70032 1.208 9.43291 0.931632C9.16549 0.655263 8.8028 0.5 8.42462 0.5H5.57277C5.19459 0.5 4.8319 0.655263 4.56449 0.931632C4.29708 1.208 4.14685 1.58284 4.14685 1.97368V3.44737H1.29499C1.1059 3.44737 0.92456 3.525 0.790853 3.66318C0.657147 3.80137 0.582031 3.98879 0.582031 4.18421C0.582031 4.37963 0.657147 4.56705 0.790853 4.70524C0.92456 4.84342 1.1059 4.92105 1.29499 4.92105H2.00796V13.0263C2.00796 13.4172 2.15819 13.792 2.4256 14.0684C2.69301 14.3447 3.0557 14.5 3.43388 14.5H10.5635C10.9417 14.5 11.3044 14.3447 11.5718 14.0684C11.8392 13.792 11.9894 13.4172 11.9894 13.0263V4.92105H12.7024C12.8915 4.92105 13.0728 4.84342 13.2065 4.70524C13.3403 4.56705 13.4154 4.37963 13.4154 4.18421C13.4154 3.98879 13.3403 3.80137 13.2065 3.66318C13.0728 3.525 12.8915 3.44737 12.7024 3.44737ZM5.57277 1.97368H8.42462V3.44737H5.57277V1.97368ZM6.28574 11.5526C6.28574 11.7481 6.21062 11.9355 6.07691 12.0737C5.94321 12.2118 5.76186 12.2895 5.57277 12.2895C5.38368 12.2895 5.20234 12.2118 5.06863 12.0737C4.93492 11.9355 4.85981 11.7481 4.85981 11.5526V6.39474C4.85981 6.19931 4.93492 6.0119 5.06863 5.87371C5.20234 5.73553 5.38368 5.65789 5.57277 5.65789C5.76186 5.65789 5.94321 5.73553 6.07691 5.87371C6.21062 6.0119 6.28574 6.19931 6.28574 6.39474V11.5526ZM9.13759 11.5526C9.13759 11.7481 9.06247 11.9355 8.92877 12.0737C8.79506 12.2118 8.61371 12.2895 8.42462 12.2895C8.23553 12.2895 8.05419 12.2118 7.92048 12.0737C7.78678 11.9355 7.71166 11.7481 7.71166 11.5526V6.39474C7.71166 6.19931 7.78678 6.0119 7.92048 5.87371C8.05419 5.73553 8.23553 5.65789 8.42462 5.65789C8.61371 5.65789 8.79506 5.73553 8.92877 5.87371C9.06247 6.0119 9.13759 6.19931 9.13759 6.39474V11.5526Z"/>
</g>
<defs>
<clipPath id="clip0_1268_12790">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
</button>
              <button   @click="(e)=>handleAddSceneOption(e,item.id)" :class="`${openAddSceneOptionsMenu===item.id?'bg-blue-600 fill-white':'bg-gray-100 fill-gray-900'}`" class="flex justify-center items-center w-6 h-6 rounded-md cursor-pointer">
                  <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                </button>

              </div>

          </div>

            <div v-else  @click="selectedScene=item.id" :id="`scene-${item.id}`" class="scene-scroll-target flex justify-between align-center p-1.5 my-2 rounded-md relative"
          :class="`${currentScene===item.id?'bg-blue-50':''}`">
                <div @click="handleSceneClick(item.id)" class="max-w-32 cursor-pointer text-gray-900 hover:text-gray-900"><p class="overflow-hidden text-ellipsis whitespace-nowrap">{{ item.name }} </p></div>
                <button @click="selectedSceneDelete===item.id?selectedSceneDelete=null:selectedSceneDelete=item.id" class="px-1 rounded-md h-6 flex justify-center items-center" :class="selectedSceneDelete===item.id?'bg-blue-600 fill-white':'fill-gray-900'"><svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-5" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="more-vertical"><rect width="24" height="24" transform="rotate(-90 12 12)" opacity="0"/><circle cx="12" cy="12" r="2"/><circle cx="12" cy="5" r="2"/><circle cx="12" cy="19" r="2"/></g></g></svg>
                </button>

                <div v-if="selectedSceneDelete===item.id" ref="actionMenuRef"  class="absolute right-6 top-8 z-100">
                  <div class="w-28 rounded-md shadow-lg border-[1px] bg-white border-gray-200">
              <div @click="handleArchive(item.id,item.category)"  class="flex items-center gap-2 py-2 px-3 rounded-t-md cursor-pointer hover:bg-blue-50"><svg width="14" height="15" viewBox="0 0 14 15" class="fill-gray-500" xmlns="http://www.w3.org/2000/svg">
              <path d="M13.2222 1.08337H0.777778C0.348223 1.08337 0 1.42136 0 1.83828L0 3.34808C0 3.765 0.348223 4.10298 0.777778 4.10298H13.2222C13.6518 4.10298 14 3.765 14 3.34808V1.83828C14 1.42136 13.6518 1.08337 13.2222 1.08337Z"/>
              <path d="M0.777778 5.61279V12.4069C0.777778 12.8073 0.941666 13.1914 1.23339 13.4745C1.52511 13.7576 1.92077 13.9167 2.33333 13.9167H11.6667C12.0792 13.9167 12.4749 13.7576 12.7666 13.4745C13.0583 13.1914 13.2222 12.8073 13.2222 12.4069V5.61279H0.777778ZM9.33333 7.87749C9.33333 8.0777 9.25139 8.26972 9.10553 8.41129C8.95967 8.55286 8.76184 8.63239 8.55556 8.63239H5.44444C5.23817 8.63239 5.04033 8.55286 4.89447 8.41129C4.74861 8.26972 4.66667 8.0777 4.66667 7.87749V7.12259C4.66667 6.92238 4.74861 6.73037 4.89447 6.58879C5.04033 6.44722 5.23817 6.36769 5.44444 6.36769C5.65072 6.36769 5.84855 6.44722 5.99442 6.58879C6.14028 6.73037 6.22222 6.92238 6.22222 7.12259H7.77778C7.77778 6.92238 7.85972 6.73037 8.00558 6.58879C8.15145 6.44722 8.34928 6.36769 8.55556 6.36769C8.76184 6.36769 8.95967 6.44722 9.10553 6.58879C9.25139 6.73037 9.33333 6.92238 9.33333 7.12259V7.87749Z"/>
              </svg> <p class="text-sm text-gray-500">Archive</p>
              </div>
              <div>
              <div @click="()=>{openSceneSettings=openSceneSettings===item.id?openSceneSettings=null:openSceneSettings=item.id}"  class="flex items-center gap-2 py-2 px-3 cursor-pointer hover:bg-blue-50 rounded-b-md"><svg class="fill-gray-900" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3549_19313)">
<path d="M9.68525 5.8033L9.68175 5.8082L4.6856 10.8048L6.18298 12.3017L11.1833 7.30152L9.68525 5.8033Z" fill="black"/>
<path d="M2.19768 8.31667L3.69505 9.8149L8.6919 4.81825L8.6968 4.81475L7.19803 3.31582L2.19768 8.31667Z" fill="black"/>
<path d="M1.39684 9.49565L0.0359716 13.578C-0.0480325 13.8293 0.0177706 14.1072 0.20538 14.2942C0.338387 14.4279 0.517595 14.5 0.700304 14.5C0.774508 14.5 0.849412 14.4881 0.921515 14.4636L5.00342 13.1026L1.39684 9.49565Z" fill="black"/>
<path d="M13.1756 1.32332C12.0773 0.225559 10.2894 0.225559 9.19102 1.32332L8.18857 2.32587L12.1739 6.31157L13.1763 5.30902C14.2747 4.21056 14.2747 2.42179 13.1756 1.32332Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_3549_19313">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
<p class="text-sm text-gray-900">Edit</p>
</div>
<EditSceneSettingModal v-if="openSceneSettings" class="fixed left-96 top-1/2 transform -translate-y-1/2" @close="handleCloseEditSceneSettings" :sceneId="openSceneSettings"/>
            </div>

            </div>
        </div>
          </div>
          <!-- No Items present message for empty categories -->
          <div v-if="item.isCategory && (!item.linked_scenes || item.linked_scenes.length === 0)" class="ml-8 text-xs text-gray-400 cursor-">
            No Items present
          </div>

                    </template>
                </NestedReOrder>
                <Modal :open="openCategoryDeleteModal">
      <DeleteModalContent :trash="true" :loader="categoryDeleteLoader" @closeModal="(e) => openCategoryDeleteModal = false"
        @handleDelete="handleCategoryToTrash" :dataName="'Category'" />
    </Modal>
    <div class="h-16">

    </div>

      </div>
      <div class="w-full border-t-[1px] border-gray-200 mt-4"></div>
        <div class="my-2 h-6 w-full flex gap-1.5 cursor-pointer rounded-md relative" >

          <button class="flex items-center gap-2 h-full w-full" @click="openArchiveList=!openArchiveList">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-gray-500" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="archive"><rect width="24" height="24" transform="rotate(90 12 12)" opacity="0"/><path d="M18 3H6a3 3 0 0 0-2 5.22V18a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8.22A3 3 0 0 0 18 3zm-3 10.13a.87.87 0 0 1-.87.87H9.87a.87.87 0 0 1-.87-.87v-.26a.87.87 0 0 1 .87-.87h4.26a.87.87 0 0 1 .87.87zM18 7H6a1 1 0 0 1 0-2h12a1 1 0 0 1 0 2z"/></g></g></svg>

                <h2 class=" flex-1 flex items-center text-xs leading-4 text-gray-500 font-medium ml-1">ARCHIVE</h2>
              </button>
              <div ref="archiveListRef" v-if="openArchiveList" class="w-48 max-h-96 border rounded-md shadow-lg absolute z-100 left-36 bottom-0 bg-whiten bg-white overflow-y-auto ">
        <div  @click="(e)=>handleOpenUnArchiveMenu(e,item._id)"
        v-for="(item,index)  in TrashItems" :key="index" class="flex items-center gap-2 py-2 px-3 rounded-[inherit] cursor-pointer hover:bg-gray-100 relative" :class="`${openUnarchiveMenu === item._id?'bg-blue-50':''}`">
        <div  v-if="openUnarchiveMenu===item._id" :style="{top:`${unArchiveClick.y-96}px` , left:`${unArchiveClick.x}px`}" class="fixed z-100" >
            <div @click="handleArchive(scene.sceneData._id)" class="w-36 rounded-md shadow-lg border-[1px] bg-white">
              <div  @click="handleRestoreScene(item._id,Object.values(item.data)[0]._id)" class="flex items-center gap-2 py-2 px-3 rounded-md cursor-pointer hover:bg-blue-50"><svg width="14" height="15" viewBox="0 0 14 15" class="fill-gray-500" xmlns="http://www.w3.org/2000/svg">
              <path d="M13.2222 1.08337H0.777778C0.348223 1.08337 0 1.42136 0 1.83828L0 3.34808C0 3.765 0.348223 4.10298 0.777778 4.10298H13.2222C13.6518 4.10298 14 3.765 14 3.34808V1.83828C14 1.42136 13.6518 1.08337 13.2222 1.08337Z"/>
              <path d="M0.777778 5.61279V12.4069C0.777778 12.8073 0.941666 13.1914 1.23339 13.4745C1.52511 13.7576 1.92077 13.9167 2.33333 13.9167H11.6667C12.0792 13.9167 12.4749 13.7576 12.7666 13.4745C13.0583 13.1914 13.2222 12.8073 13.2222 12.4069V5.61279H0.777778ZM9.33333 7.87749C9.33333 8.0777 9.25139 8.26972 9.10553 8.41129C8.95967 8.55286 8.76184 8.63239 8.55556 8.63239H5.44444C5.23817 8.63239 5.04033 8.55286 4.89447 8.41129C4.74861 8.26972 4.66667 8.0777 4.66667 7.87749V7.12259C4.66667 6.92238 4.74861 6.73037 4.89447 6.58879C5.04033 6.44722 5.23817 6.36769 5.44444 6.36769C5.65072 6.36769 5.84855 6.44722 5.99442 6.58879C6.14028 6.73037 6.22222 6.92238 6.22222 7.12259H7.77778C7.77778 6.92238 7.85972 6.73037 8.00558 6.58879C8.15145 6.44722 8.34928 6.36769 8.55556 6.36769C8.76184 6.36769 8.95967 6.44722 9.10553 6.58879C9.25139 6.73037 9.33333 6.92238 9.33333 7.12259V7.87749Z"/>
              </svg> <p class="text-sm text-gray-500">Un Archive</p>
              </div>

            </div>
        </div>
          <h class="text-sm text-gray-900  overflow-hidden text-ellipsis whitespace-nowrap">{{Object.values(item.data)[0].name}}</h>
        </div>
          <div v-if="Object.keys(TrashItems).length ===0"  class="flex items-center gap-2 py-2 px-3 rounded-[inherit] cursor-default">
        <p class="text-gray-900 text-sm font-medium">Archive is Empty</p>
      </div>
    </div>

          </div>

      </div>

      <AddSceneOptionsMenu ref="addSceneMenuRef" :style="{top:`${createSceneClick.y}px` , left:`${createSceneClick.x}px`}" class="fixed left-36 top-8 z-100" v-if="openAddSceneOptionsMenu" :category="openAddSceneOptionsMenu==='UNCATEGORIZED' || 'pages'?'':openAddSceneOptionsMenu" @close="openAddSceneOptionsMenu=!openAddSceneOptionsMenu"/>
      <CreateSidebarModal ref="categoryModalRef" v-if="openCreateCategory" class="fixed left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 z-[100]" @close="handleCreateCategory"/>
    </div>

</template>

<style scoped>
.scene-skeleton-loader{
  background-color: #5a5757;
        background: linear-gradient(
          100deg,
          rgba(255, 255, 255, 0) 40%,
          rgba(255, 255, 255, 0.468) 50%,
          rgba(255, 255, 255, 0) 60%
        ) #e5e7eb;
        background-size: 200% 100%;
        background-position-x: 180%;
        animation: 1s loading ease-in-out infinite;
}

@keyframes loading {
        to {
          background-position-x: -20%;
        }
    }

</style>

<script setup>
import { useRoute } from 'vue-router';
import router from '../../../router';
import { ref, watch, onMounted, computed, onUnmounted, nextTick } from 'vue';
import { ProjectStore } from '../../../store/project';
import { getIcons } from '../../../api/projects/icon';
import { DownloadFile, isMasterScenePath, JsonStringifyFromReadJsonFile } from '../../../helpers/helpers.ts';
import { GetCooridnatesFromReadSvgFile } from '../../../helpers/svgConversionHelper.ts';
import { createSVG, moveSvgToTrash } from '../../../api/projects/scene/svg/index';
import { createSVG as createMasterSVG, moveSvgToTrash as moveMasterSvgToTrash} from '../../../api/masterScene/svg/index';
import { getCookie } from '../../../helpers/domhelper';
import Modal from '@/components/common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import { Org_Store } from '@/store/organization';
import DesignSceneList from '../DesignSceneList.vue';
import CreateLayers from '../CreateLayers.vue';
import CreateIcons from '../CreateIcons.vue';
import { onClickOutside } from '@vueuse/core';

const viewerBox = ref(window.viewer ? window.viewer : null); // viewer
const route = useRoute();
const projectStore = ProjectStore();
const newOrganizationStore = Org_Store();
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const sceneDetails = ref(null);
const sceneId = ref(route.params.scene_id);
const projectId = ref(route.params.project_id);
const downloadedIconFile = ref();
const listOfIcons = ref([]);
const searchQuery = ref('');
const selectedMenu = ref('pages');
const openAddLayers = ref(false);
const openAddIcons = ref(false);
const SceneName = ref('');
const addLayerModalRef = ref();
const sceneDataLoading = ref(false);

const debouncedSearch = ref({
  search: searchQuery.value,
  timeout: null,
});

if (route.path.includes('/layers')) {
  selectedMenu.value = 'layers';
} else if (route.path.includes('/icons')) {
  selectedMenu.value = 'icons';
} else {
  selectedMenu.value = 'pages';
}

watch(
  () => route.path,
  (newPath) => {
    console.log('newPath ->', newPath);
    if (newPath.includes('/layers')) {
      selectedMenu.value = 'layers';
    } else if (newPath.includes('/icons')) {
      selectedMenu.value = 'icons';
    } else {
      selectedMenu.value = 'pages';
    }
  },
  { immediate: true },
);
const iconsError = ref(null);
const openSvgDeleteModal = ref(false);
const svgToDelete = ref();
const svgDeleteLoader = ref(false);

const updateSceneDetails = (sceneInfo) => {
  if (sceneInfo){
    sceneDataLoading.value = true;
    sceneDetails.value = Object.values(sceneInfo[sceneId.value].svgData).length > 0 ? sceneInfo[sceneId.value].svgData : null;
    SceneName.value= sceneInfo[sceneId.value].sceneData.name;
    sceneDataLoading.value = false;
  }
};

const getSceneInfo = () => {
  if (isMasterScene.value){
    updateSceneDetails(newOrganizationStore.masterScenes);
  } else {
    updateSceneDetails(projectStore.scenes);
  }
};

if (sceneId.value) {
  getSceneInfo();
}

watch(() => {
  const sceneData = !isMasterScene.value ? projectStore.scenes : newOrganizationStore.masterScenes;
  return sceneData;
}, () => {
  getSceneInfo();
});

/* Watch(() => route.query.layerId,(prev,current) => {
       setTimeout(() => {
        if(current){
            layersRef.value[current].scrollIntoView({ behavior: "smooth", block: "center" });
        }
       }, 200);
}); */

// Svg Selection
const handleSvgSelection = (values) => {
  if (route.query.svgId === values._id) {
    router.push({ path: route.path, query: {} });
  } else {
    router.push({ path: route.path, query: { svgId: values._id } });
  }
};

// Layer Selection
const handleLayerSelection = (values) => {
  /*     Console.log(layersRef.value);
      console.log(layersRef.value[values.layer_id]);
   //   console.log(e.target);
     /// const element = e.target;
     // element.scrollIntoView({ behavior: "smooth", block: "center", inline: "nearest" }); */
  router.push({ path: route.path, query: { svgId: route.query.svgId, layerId: values.layer_id } });
};

watch(searchQuery, (newValue) => {
  clearTimeout(debouncedSearch.value.timeout);
  debouncedSearch.value.timeout = setTimeout(() => {
    debouncedSearch.value.search = newValue;
  }, 300);
});

const filteredIcons = computed(() => {
  return Object.values(listOfIcons.value).filter((icon) =>
    icon.category.toLowerCase().includes(debouncedSearch.value.search.toLowerCase()) || icon.name.toLowerCase().includes(debouncedSearch.value.search.toLowerCase()),
  );
});

async function handleFileConversionJson (file, src, attributesObject) {
  return new Promise((resolve) => {
    if (file.type === 'image/svg+xml') {
      console.log('image/svg+xml');
      console.log(attributesObject, src, file);

      GetCooridnatesFromReadSvgFile(file, src, attributesObject).then((res) => {
        resolve(res);
      });
    } else {
      JsonStringifyFromReadJsonFile(file).then((res) => {
        resolve(res);
      });
    }
  });
}

const findObjectByID = (id, data) => {
  return data[Object.keys(data).find((item) => item === id)] || null;
};

const refreshData = () => {
  document.dispatchEvent(new Event('refreshAddProjectSvg'));
  document.dispatchEvent(new Event('refreshGetScene'));
};

async function handleIconClick (e, src) {
  const iconObject = findObjectByID(e.srcElement.alt, filteredIcons.value);

  if (!iconObject) {
    console.error('Icon Object not found');
    return;
  }

  try {
    const downloadedFile = await DownloadFile(iconObject.iconURL, iconObject.name);
    downloadedIconFile.value = downloadedFile;

    const attributesObject = {
      width: iconObject.iconWidth,
      height: iconObject.iconHeight,
    };

    if (downloadedIconFile.value && src) {
      const formData = new FormData();
      formData.append('type', iconObject.category);
      formData.append('scene_id', sceneId.value);
      formData.append('organization_id', getCookie('organization'));
      if (!isMasterScene.value){
        formData.append('project_id', projectId.value);
      }

      formData.append('scene_type', 'deep_zoom');

      const conversionResult = await handleFileConversionJson(downloadedIconFile.value, src, attributesObject);

      formData.append('layers', conversionResult);
      if (isMasterScene.value){
        createMasterSVG(formData).then((res) => {
          const newObject = {};
          newObject[res.scene_id] = newOrganizationStore.masterScenes[res.scene_id];
          newObject[res.scene_id].svgData[res._id] = res;
          newOrganizationStore.SyncMultipleMasterScenes(newObject);
          refreshData();
        });
      } else {
        createSVG(formData).then((res) => {
          const newObject = {};
          newObject[res.scene_id] = projectStore.scenes[res.scene_id];
          newObject[res.scene_id].svgData[res._id] = res;
          projectStore.SyncMultipleScenes(newObject);
          refreshData();
        });
      }
    }
  } catch (error) {
    console.error('An error occurred during icon processing:', error);
  }
}

const handleGetIconsList = () => {
  getIcons().then((res) => {
    if (typeof res === 'object') {
      listOfIcons.value = res;
    } else {
      iconsError.value = res;
    }
  });
};

document.addEventListener('refreshIconsList', () => { // After editing scene
  handleGetIconsList();
});

watch(() => router.currentRoute.value, () => {
  if (router.options.history.state.forward !== null) {
    if (router.options.history.state.forward.includes('createicons')) {
      handleGetIconsList();
    }
  }

});

const handleViewerInfo = () => viewerBox.value = window.viewer.value;

const handleMoveSvgToTrash = () => {
  svgDeleteLoader.value = true;
  const obj = {
    svg_id: [svgToDelete.value],
    timeStamp: Date.now(),
  };

  if (isMasterScene.value){
    moveMasterSvgToTrash(obj).then(() => {
      svgDeleteLoader.value = false;
      openSvgDeleteModal.value = false;
      document.dispatchEvent(new Event('refreshDeepZoomScene'));
    }).catch((err) => {
      svgDeleteLoader.value = false;
      console.log(err);
    });
  } else {
    moveSvgToTrash(obj, projectId.value).then(() => {
      svgDeleteLoader.value = false;
      openSvgDeleteModal.value = false;
      document.dispatchEvent(new Event('refreshDeepZoomScene'));
    }).catch((err) => {
      svgDeleteLoader.value = false;
      console.log(err);
    });
  }
};

onMounted(() => {
  if (filteredIcons.value.length === 0) {
    handleGetIconsList();
  }

  window.addEventListener('dispatchViewerInfo', handleViewerInfo);
});

onUnmounted(() => {
  window.removeEventListener('dispatchViewerInfo', handleViewerInfo);
});

onClickOutside(addLayerModalRef, () => {
  openAddLayers.value = false;
});

watch(() => route.params.scene_id, () => {
  sceneId.value = route.params.scene_id;
  getSceneInfo();
});

function scrollToSection (id) {
  nextTick(() => {
    const el = document.getElementById(id);
    if (!el) {
      console.warn(`Element with id "${id}" not found.`);
      return;
    }

    el.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  });
}

if (route.query?.layerId){
  scrollToSection(`layer-${route.query.layerId}`);
}

watch(() => route.query.layerId, () => {
  if (route.query.layerId) {
    scrollToSection(`layer-${route.query.layerId}`);
  }
});

</script>

<template>
  <div class="h-full w-full bg-gray-100  flex justify-between">
    {{ sceneDetails?.svgData }}
    <DesignSceneList v-if="selectedMenu==='pages'"/>
  <div v-else-if="selectedMenu==='layers'"
    class="h-full  w-full  bg-white py-3 flex flex-col">

    <div class="flex justify-between items-center px-3">
          <h2 class=" text-gray-500 text-lg font-semibold ml-1">
            Layers
          </h2>
          <button class="relative flex justify-center items-center w-6 h-6 rounded-md cursor-pointer" :class="openAddLayers?'bg-blue-600 fill-white':'bg-gray-100 fill-gray-900'"
          @click="openAddLayers=!openAddLayers"
          >
                  <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                </button>
                <CreateLayers ref="addLayerModalRef" v-if="openAddLayers" class="fixed left-1/2 top-1/2 transform -translate-y-1/2 z-100" @close="openAddLayers=false"/>
        </div>

    <div class="w-full py-2 mt-2 px-3">

      <!-- scenen Name Heading Skeleton -->
          <div v-if="sceneDataLoading" class="flex items-center gap-2 ">
            <div class="w-5 h-5 svg-skeleton-loader rounded-md"></div>

                <div class="h-5 w-28 svg-skeleton-loader rounded-md">
                </div>
          </div>

          <div v-else class="flex items-center gap-2 ">
            <svg class="w-5 h-5 fill-gray-500" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_723_21115)">
            <path d="M16.4 2.6665H3.6C3.17565 2.6665 2.76869 2.8382 2.46863 3.14381C2.16857 3.44943 2 3.86393 2 4.29613V15.7035C2 16.1357 2.16857 16.5502 2.46863 16.8559C2.76869 17.1615 3.17565 17.3332 3.6 17.3332H16.4C16.8243 17.3332 17.2313 17.1615 17.5314 16.8559C17.8314 16.5502 18 16.1357 18 15.7035V4.29613C18 3.86393 17.8314 3.44943 17.5314 3.14381C17.2313 2.8382 16.8243 2.6665 16.4 2.6665ZM12 5.92576C12.2373 5.92576 12.4693 5.99745 12.6667 6.13174C12.864 6.26604 13.0178 6.45693 13.1087 6.68026C13.1995 6.90359 13.2232 7.14934 13.1769 7.38643C13.1306 7.62352 13.0164 7.8413 12.8485 8.01223C12.6807 8.18316 12.4669 8.29956 12.2341 8.34672C12.0013 8.39388 11.7601 8.36968 11.5408 8.27717C11.3215 8.18466 11.1341 8.02801 11.0022 7.82702C10.8704 7.62602 10.8 7.38972 10.8 7.14799C10.8 6.82383 10.9264 6.51295 11.1515 6.28374C11.3765 6.05453 11.6817 5.92576 12 5.92576ZM15.5008 14.4658C15.4319 14.5938 15.3305 14.7005 15.2072 14.7749C15.0839 14.8493 14.9433 14.8886 14.8 14.8887H5.2C5.06362 14.8888 4.92949 14.8533 4.81034 14.7858C4.6912 14.7182 4.591 14.6207 4.51925 14.5025C4.44751 14.3844 4.40662 14.2495 4.40045 14.1108C4.39428 13.972 4.42304 13.8339 4.484 13.7097L7.284 8.00599C7.34846 7.87442 7.44666 7.76307 7.56821 7.68375C7.68976 7.60442 7.83011 7.56007 7.9744 7.55539C8.11993 7.54715 8.26478 7.58103 8.3922 7.65313C8.51962 7.72523 8.62439 7.83259 8.6944 7.9628L10.9144 11.8389L12.1512 10.3013C12.2315 10.2017 12.3339 10.1228 12.45 10.0714C12.5661 10.0199 12.6925 9.99715 12.8188 10.0051C12.9452 10.0131 13.0679 10.0514 13.1769 10.1171C13.2858 10.1828 13.3779 10.2738 13.4456 10.3828L15.4784 13.6421C15.5537 13.7649 15.5955 13.9061 15.5994 14.0509C15.6033 14.1956 15.5693 14.3389 15.5008 14.4658Z"/>
            </g>
            <defs>
            <clipPath id="clip0_723_21115">
            <rect width="16" height="16" fill="white" transform="translate(2 2)"/>
            </clipPath>
            </defs>
            </svg>

                <h3 class=" text-gray-500 text-sm font-semibold">
            {{SceneName}}
          </h3>
          </div>
        </div>
        <!-- Svg Skeleton Loader -->
         <div v-if="sceneDataLoading"  class="mt-3 max-h-[inherit] overflow-y-auto mb-16 px-3">

         <div v-for="i in 6" class="my-2.5 h-6 w-full flex items-center gap-1.5" :key="i">
            <div class="h-4 w-4 rounded-full svg-skeleton-loader ">

              </div>

              <div class="flex-1 w-full rounded-md h-full svg-skeleton-loader">
              </div>
                 <div class="h-4 w-7 rounded-2xl mr-2  svg-skeleton-loader">

                </div>
                 <div class="h-full w-4 rounded-md  svg-skeleton-loader">

                </div>

          </div>
          </div>

                         <!-- No Data  -->
        <div class="py-10 px-2 flex flex-col justify-center items-center gap-3" v-if="!sceneDetails || Object.keys(sceneDetails).length === 0 && !sceneDataLoading">
          <div class="flex justify-center items-center w-full">
<svg class="w-8 h-8 fill-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="alert-circle"><rect width="24" height="24" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 18a8 8 0 1 1 8-8 8 8 0 0 1-8 8z"/><circle cx="12" cy="16" r="1"/><path d="M12 7a1 1 0 0 0-1 1v5a1 1 0 0 0 2 0V8a1 1 0 0 0-1-1z"/></g></g></svg></div>
<p class="text-center text-gray-500 text-md font-semibold ">No data available!</p>
</div>
        <!-- List -->
        <div v-else class="mt-3 overflow-y-auto pb-8">
          <div v-for="svg, svgId  in sceneDetails" :key="svgId" class="relative">
            <div  v-if="$route.query.svgId===svg._id" class="w-1 h-[95%] absolute border-l border-b border-gray-500 left-6 top-4 z-10"></div>
        <div class="h-8 w-full flex gap-2 my-1 cursor-pointer px-3">
            <div class="flex justify-center items-center" @click="handleSvgSelection(svg)">
              <svg v-if="$route.query.svgId===svg._id" width="16" height="17" class="fill-gray-500 w-4.5 h-4.5" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.99399 6.30021L7.21405 12.1912C7.42287 12.3889 7.70604 12.5 8.0013 12.5C8.29656 12.5 8.57974 12.3889 8.78855 12.1912L15.0086 6.30021C15.1643 6.15272 15.2703 5.96483 15.3133 5.76028C15.3562 5.55574 15.3341 5.34373 15.2499 5.15106C15.1656 4.95838 15.0229 4.79369 14.8399 4.6778C14.6568 4.56192 14.4416 4.50004 14.2214 4.5H1.78124C1.56104 4.50004 1.3458 4.56192 1.16273 4.6778C0.979661 4.79369 0.836976 4.95838 0.752717 5.15106C0.668458 5.34373 0.646407 5.55574 0.689352 5.76028C0.732297 5.96483 0.83831 6.15272 0.99399 6.30021Z"/>
</svg>
              <svg v-else class="fill-gray-500 w-4.5 h-4.5" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_21782)">
<path d="M5.80021 15.0085L11.6912 8.78839C11.8889 8.57958 12 8.2964 12 8.00114C12 7.70588 11.8889 7.4227 11.6912 7.21389L5.80021 0.993828C5.65272 0.838149 5.46483 0.732134 5.26028 0.689189C5.05574 0.646244 4.84373 0.668295 4.65106 0.752554C4.45838 0.836813 4.29369 0.979498 4.1778 1.16257C4.06192 1.34564 4.00004 1.56088 4 1.78108L4 14.2212C4.00004 14.4414 4.06192 14.6566 4.1778 14.8397C4.29369 15.0228 4.45838 15.1655 4.65106 15.2497C4.84373 15.334 5.05574 15.356 5.26028 15.3131C5.46483 15.2701 5.65272 15.1641 5.80021 15.0085Z"/>
</g>
<defs>
<clipPath id="clip0_723_21782">
<rect width="16" height="16" fill="white" transform="translate(0 0.000976562)"/>
</clipPath>
</defs>
</svg>

              </div>
              <div class="flex-1 flex items-center"  @click="handleSvgSelection(svg)">
                <h2 class=" max-w-16 text-sm leading-4 text-gray-900 font-semibold ml-1 overflow-hidden text-ellipsis whitespace-nowrap">{{ svg.type }}</h2>
              </div>
              <div class="w-16 flex-0 flex justify-between items-center  h-full">
                <div class="flex justify-center items-center p-1 cursor-pointer">
                  <svg class="h-5" width="24" height="21" viewBox="0 0 24 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M12.5 17.75H16C18.7614 17.75 21 15.7912 21 13.375C21 10.9588 18.7614 9 16 9H12.5M9.5 17.75H6C3.23858 17.75 1 15.7912 1 13.375C1 10.9588 3.23858 9 6 9H9.5" stroke="#1C64F2" stroke-width="1.5" stroke-linecap="round"/>
<path d="M8 13.375H14" stroke="#1C64F2" stroke-width="1.5" stroke-linecap="round"/>
<path d="M20 2L20.2211 2.59745C20.511 3.38088 20.656 3.77259 20.9417 4.05834C21.2274 4.34409 21.6191 4.48903 22.4025 4.77892L23 5L22.4025 5.22108C21.6191 5.51097 21.2274 5.65592 20.9417 5.94166C20.656 6.22741 20.511 6.61912 20.2211 7.40255L20 8L19.7789 7.40255C19.489 6.61913 19.344 6.22741 19.0583 5.94166C18.7725 5.65591 18.3809 5.51097 17.5975 5.22108L17 5L17.5975 4.77892C18.3809 4.48903 18.7725 4.34409 19.0583 4.05834C19.344 3.77259 19.489 3.38088 19.7789 2.59745L20 2Z" fill="#1C64F2" stroke="#1C64F2" stroke-linejoin="round"/>
</svg>

              </div>
              <button class="flex justify-center items-center w-6 h-6 rounded-md cursor-pointer" @click.stop="()=>{openSvgDeleteModal=true;svgToDelete=svg._id}">
                <svg class="fill-gray-500 w-4.5 h-4.5" width="14" height="15" viewBox="0 0 14 15" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1268_12790)">
<path d="M12.7024 3.44737H9.85055V1.97368C9.85055 1.58284 9.70032 1.208 9.43291 0.931632C9.16549 0.655263 8.8028 0.5 8.42462 0.5H5.57277C5.19459 0.5 4.8319 0.655263 4.56449 0.931632C4.29708 1.208 4.14685 1.58284 4.14685 1.97368V3.44737H1.29499C1.1059 3.44737 0.92456 3.525 0.790853 3.66318C0.657147 3.80137 0.582031 3.98879 0.582031 4.18421C0.582031 4.37963 0.657147 4.56705 0.790853 4.70524C0.92456 4.84342 1.1059 4.92105 1.29499 4.92105H2.00796V13.0263C2.00796 13.4172 2.15819 13.792 2.4256 14.0684C2.69301 14.3447 3.0557 14.5 3.43388 14.5H10.5635C10.9417 14.5 11.3044 14.3447 11.5718 14.0684C11.8392 13.792 11.9894 13.4172 11.9894 13.0263V4.92105H12.7024C12.8915 4.92105 13.0728 4.84342 13.2065 4.70524C13.3403 4.56705 13.4154 4.37963 13.4154 4.18421C13.4154 3.98879 13.3403 3.80137 13.2065 3.66318C13.0728 3.525 12.8915 3.44737 12.7024 3.44737ZM5.57277 1.97368H8.42462V3.44737H5.57277V1.97368ZM6.28574 11.5526C6.28574 11.7481 6.21062 11.9355 6.07691 12.0737C5.94321 12.2118 5.76186 12.2895 5.57277 12.2895C5.38368 12.2895 5.20234 12.2118 5.06863 12.0737C4.93492 11.9355 4.85981 11.7481 4.85981 11.5526V6.39474C4.85981 6.19931 4.93492 6.0119 5.06863 5.87371C5.20234 5.73553 5.38368 5.65789 5.57277 5.65789C5.76186 5.65789 5.94321 5.73553 6.07691 5.87371C6.21062 6.0119 6.28574 6.19931 6.28574 6.39474V11.5526ZM9.13759 11.5526C9.13759 11.7481 9.06247 11.9355 8.92877 12.0737C8.79506 12.2118 8.61371 12.2895 8.42462 12.2895C8.23553 12.2895 8.05419 12.2118 7.92048 12.0737C7.78678 11.9355 7.71166 11.7481 7.71166 11.5526V6.39474C7.71166 6.19931 7.78678 6.0119 7.92048 5.87371C8.05419 5.73553 8.23553 5.65789 8.42462 5.65789C8.61371 5.65789 8.79506 5.73553 8.92877 5.87371C9.06247 6.0119 9.13759 6.19931 9.13759 6.39474V11.5526Z"/>
</g>
<defs>
<clipPath id="clip0_1268_12790">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
</button>
              </div>
          </div>
          <div v-if="$route.query.svgId===svg._id" class="max-h-72 overflow-scroll">

          <div class="pl-3 my-1 rounded-md ml-3" :id="`layer-${layer.layer_id}`" :class="$route.query.layerId===layer.layer_id?'bg-blue-50':''"
           v-for="layer, layerId,index  in  sceneDetails[svg._id].layers" :key="layerId" @click="handleLayerSelection(layer)" >
          <div class="h-8 px-1 w-full flex items-center gap-1.5 cursor-pointer " >
              <div class="flex-1 flex items-center">

    <!-- <ClickToEditText  v-if="enableEditLayerName===layer.layer_id" :text="layer.name?layer.name:layer.layer_id" @submitEditedText="handleEditLayerName" /> -->
                <!-- <h2 @click="handleTextClick(layer.layer_id)"  class="text-xs leading-4 text-gray-900 font-semibold ml-1 overflow-hidden text-ellipsis whitespace-nowrap max-w-20">{{ layer.name?layer.name:layer.layer_id }}</h2> -->
                <h2  class="text-xs leading-4 text-gray-900 font-semibold ml-1 overflow-hidden text-ellipsis whitespace-nowrap max-w-20"><span>{{ index + 1 + ')' }}</span> {{ layer.name ? layer.name : layer.layer_id }}</h2>

              </div>
              <div class="w-14 flex-0 flex items-center justify-between  h-full mr-3">
                <div class="flex justify-center items-center p-1 cursor-pointer">
                  <svg v-if="layer.type" width="10" height="10" class="h-3 w-3 fill-green-500" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                   <g clip-path="url(#clip0_723_21212)">
                   <path d="M3.5777 9.1678C3.4145 9.16858 3.25752 9.09192 3.14033 8.95422L0.18872 5.48236C0.129889 5.41274 0.082947 5.32975 0.0505741 5.23814C0.0182012 5.14653 0.00103144 5.04808 4.5112e-05 4.94842C-0.00194686 4.74716 0.0621007 4.55317 0.178098 4.40915C0.294096 4.26512 0.452541 4.18285 0.618578 4.18044C0.784615 4.17803 0.944643 4.25566 1.06346 4.39627L3.5802 7.35538L8.9361 1.05014C9.05508 0.909535 9.21526 0.831977 9.38142 0.834534C9.54757 0.837091 9.70608 0.919552 9.82208 1.06378C9.93808 1.208 10.0021 1.40218 9.99995 1.60359C9.99784 1.805 9.92981 1.99714 9.81083 2.13775L4.01507 8.95422C3.89789 9.09192 3.74091 9.16858 3.5777 9.1678Z" fill="#0E9F6E"/>
                   </g>
                   <defs>
                   <clipPath id="clip0_723_21212">
                   <rect width="10" height="10" fill="white" transform="translate(0 0.000976562)"/>
                   </clipPath>
                   </defs>
                   </svg>

<svg v-else class="h-3 w-3 fill-orange-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="info"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm1 14a1 1 0 0 1-2 0v-5a1 1 0 0 1 2 0zm-1-7a1 1 0 1 1 1-1 1 1 0 0 1-1 1z"/></g></g></svg>

              </div>
                <button class="flex justify-center items-center w-6 h-6 rounded-md fill-gray-900 cursor-pointer">
                <svg class="w-4 h-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-forward"><rect width="24" height="24" transform="rotate(-90 12 12)" opacity="0"/><path d="M10 19a1 1 0 0 1-.64-.23 1 1 0 0 1-.13-1.41L13.71 12 9.39 6.63a1 1 0 0 1 .15-1.41 1 1 0 0 1 1.46.15l4.83 6a1 1 0 0 1 0 1.27l-5 6A1 1 0 0 1 10 19z"/></g></g></svg>
</button>
              </div>
          </div>
          </div>
        </div>
      </div>
        </div>

    <Modal :open="openSvgDeleteModal">
      <DeleteModalContent :trash="true" :loader="svgDeleteLoader" @closeModal="(e) => openSvgDeleteModal = false"
        @handleDelete="handleMoveSvgToTrash" :dataName="'Svg'" />
    </Modal>
  </div>

  <div v-else-if="selectedMenu==='icons'"
  class="h-full  w-full bg-white rounded-t-lg py-3 flex flex-col">

  <div class="flex justify-between items-center px-3">
          <h2 class=" text-gray-500 text-lg font-semibold ml-1">
            Icons
          </h2>
          <button class="relative flex justify-center items-center w-6 h-6 rounded-md cursor-pointer" :class="openAddIcons?'bg-blue-600 fill-white':'bg-gray-100 fill-gray-900'"
          @click="openAddIcons=!openAddIcons"
          >
                  <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="plus"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"/></g></g></svg>
                </button>
                <CreateIcons v-if="openAddIcons" class="fixed left-1/2 top-1/2 transform -translate-y-1/2 z-100" @close="openAddIcons=false"/>
        </div>

<div class="px-3 py-3 ">
<div class="flex items-center bg-gray-100 rounded-lg w-full">
  <button class="flex items-center justify-center">
  <svg class="ml-2 h-5 w-5 text-grey-500 fill-gray-500" fill="currentColor" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
    <path
      d="M16.32 14.9l5.39 5.4a1 1 0 0 1-1.42 1.4l-5.38-5.38a8 8 0 1 1 1.41-1.41zM10 16a6 6 0 1 0 0-12 6 6 0 0 0 0 12z" />
  </svg>
</button>
<input :disabled="viewerBox ? false : true" v-model="searchQuery" type="text" placeholder="Search Icon"
  class="flex w-full rounded-lg h-10 transition-all bg-gray-100 duration-300 ease-in-out px-2 py-0 border-0 focus:border-blue-500 placeholder:text-start placeholder:text-sm outline-none" />

</div>
</div>

<div class=" px-3 py-3 max-h-[inherit] overflow-y-auto">
<div v-if="iconsError" class="bg-red-600 text-sm text-white text-center p-3 rounded-md">
  {{ iconsError }}
</div>
<div v-else-if="filteredIcons.length > 0"
 :class="[viewerBox ? 'pointer-events-auto' : 'pointer-events-none']"  class="flex flex-col gap-3 overflow-y-auto rounded-md">
  <div v-for="(shape, index) in filteredIcons" :key="index"
    class="flex items-center gap-3 p-1 rounded-lg cursor-pointer"
    @click="(e) => { handleIconClick(e, 'library') }" >
    <img :src="shape.iconURL" :alt="(index)" class="w-6 h-6"  />
    <p class="text-gray-500 text-sm">{{ shape.name }}</p>
  </div>
</div>
<p v-else class="flex items-center gap-3 p-1 rounded-lg">No icons found.</p>
</div>
</div>

</div>
</template>

<style scoped></style>

<script setup>
import { ref, watch } from 'vue';

import {
  ChartPieIcon,
  GlobeAltIcon,
  PlusIcon,
} from '@heroicons/vue/24/outline';
import router from '@/router';
import { useRoute } from 'vue-router';
import { getCookie } from '../../helpers/domhelper.ts';
import RightSidebar from '../common/Modal/RightSidebar.vue';
import UpdateProjectSvgLayers from './UpdateProjectSvgLayers.vue';
import { Org_Store } from '../../store/organization';
import { ProjectStore } from '../../store/project';
import { getScene } from '../../api/projects/scene';
import { getCategories as getAmenitiesCategories} from '../../api/projects/amenties';
import DeleteModalContent from '../common/ModalContent/DeleteModalContent.vue';
import Modal from '../common/Modal/Modal.vue';
import { moveSvgToTrash } from '../../api/projects/scene/svg/index';
import { createCoordinates, deleteCoordinate, getScene as getMasterScene, updateCoordinate, updateMasterScene } from '../../api/masterScene/index';

import { moveSvgToTrash as moveMasterSvgToTrash } from '../../api/masterScene/svg/index';
import CreateCoordinates from './CreateCoordinates.vue';
import { isMasterScenePath } from '../../helpers/helpers';

const preview_domain = import.meta.env.VITE_PREVIEW_DOMAIN;
const route = useRoute();

const projectId = ref(route.params.project_id);
const sceneId = ref(route.params.scene_id);
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const selectedSvg = ref();
const sceneDetails = ref(null);
const openSvgDeleteModal = ref(false);
const svgToDelete = ref();
const svgDeleteLoader = ref(false);
const coordinatesLoader = ref(false);
const categoryList = ref();
const openCoordinatesDeleteModal = ref(false);
const coordinateDeleteLoader =  ref(false);
const positionsLoader = ref(false);
const coordinateToDelete = ref();
const earthPositionMsgRef = ref();

// Refresh projects
if (!isMasterScene.value){
  projectStore.RefreshScenes(projectId.value);
  projectStore.RefreshLandmarks(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
  projectStore.RefreshAmenities(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshCommunities(projectId.value);
}
newOrganizationStore.RefreshMasterScenes();
newOrganizationStore.RefreshProjects();

const handleGetScene = () => {
  if (isMasterScene.value){
    getMasterScene(sceneId.value).then((res) => {
      sceneDetails.value = res;
    });
  } else {
    getScene(projectId.value, sceneId.value).then((res) => {
      sceneDetails.value = res;
    });
  }
};

handleGetScene(); // Initialize

document.addEventListener('refreshGetScene', () => { // After adding new svgs
  handleGetScene();
});

if (!isMasterScene.value){
  getAmenitiesCategories(projectId.value).then((res) => { // Get list of amenities categories
    categoryList.value = res.map((elem) => {
      return {name: elem.category};
    });
  }).catch((err) => {
    console.log('output->err', err);
  });
}

// Coordinates

const getIndividualCoordniatesInfo = (id) => {
  const coordinates = sceneDetails.value?.sceneData?.coordinates;

  if (!coordinates?.length) {
    return false;
  } // no coordinates

  const targetIndex = coordinates.findIndex((item) => item._id === id); // find the coordinate index
  console.log(route.query.svgId);
  console.log(targetIndex);

  return targetIndex !== -1 ? coordinates[targetIndex] : false;

};

// Layer Selection
const handleLayerSelect = (layer) => {
  router.push({ path: route.path, query: { svgId: route.query.svgId, layerId: layer.layer_id } });
};

// Close Layer Selection
const handleCloseUpdateSidebar = () => {
  if (sceneDetails.value.sceneData.type !== 'earth'){
    //
    router.push({ path: route.path, query: { svgId: route.query.svgId } });
  } else if (sceneDetails.value.sceneData.type === 'earth' && route.query.createCorodinates){
  // create coordinates
    router.push({ path: route.path, query: {} });
  } else if (sceneDetails.value.sceneData.type === 'earth' && route.query.svgId) {
  // update coordinates
    selectedSvg.value = null;
  }
};

const handleSelectSvg = (svg) => {
  if (selectedSvg.value?._id === svg._id) {
    selectedSvg.value = null;
  } else {
    console.log('output->svg', svg);
    selectedSvg.value = svg;
  }
};

watch(() => selectedSvg.value, () => {
  if (!selectedSvg.value) {
    router.push({ path: route.path, query: {} });
  } else {
    router.push({ path: route.path, query: { svgId: selectedSvg.value._id } });
  }
});

// Parent layer removal
const handleMoveSvgToTrash = () => {
  svgDeleteLoader.value = true;
  const obj = {
    svg_id: [svgToDelete.value],
    timeStamp: Date.now(),
  };

  if (isMasterScene.value){
    moveMasterSvgToTrash(obj).then(() => {
      svgDeleteLoader.value = false;
      handleGetScene();
      document.dispatchEvent(new Event('refreshAddProjectSvg'));
      openSvgDeleteModal.value = false;
    }).catch((err) => {
      svgDeleteLoader.value = false;
      console.log(err);
    });

  } else {
    moveSvgToTrash(obj, projectId.value).then(() => {
      svgDeleteLoader.value = false;
      handleGetScene();
      document.dispatchEvent(new Event('refreshAddProjectSvg'));
      openSvgDeleteModal.value = false;
    }).catch((err) => {
      svgDeleteLoader.value = false;
      console.log(err);
    });
  }
};

// Create svg
const handleCreate = () => {
  if (isMasterScene.value){
    if ( newOrganizationStore.masterScenes?.[sceneId.value]?.sceneData.type !== 'earth'){  // Master scene
      router.push(`/masterscenes/${sceneId.value}/createsvg`);
    } else {
      router.push({ path: route.path, query: { 'createCorodinates': '0' } });
    }
  } else {
    router.push(`/projects/${projectId.value}/scenes/${sceneId.value}/createsvg`);    // Project scene
  }
};

// Edit scene
const handleEditScene = () => {
  if (isMasterScene.value){
    if (  newOrganizationStore.masterScenes?.[sceneId.value].sceneData.type !== 'earth') {
      router.push(`/masterscenes/${sceneId.value}/edit`);
    } else {
      router.push(`/masterscenes/earth/${sceneId.value}/edit`);
    }
  } else {
    router.push(`/projects/${projectId.value}/scenes/${sceneId.value}/edit`);     // Project scene
  }
};

// Create Coordinates
const handleCreateCoordinates = (data) => {
  coordinatesLoader.value = true;
  createCoordinates(data).then(() => {
    coordinatesLoader.value = false;
    //   handleGetScene();
    // document.dispatchEvent(new Event('refreshAddProjectSvg'));
    window.location = `/masterscenes/earth/${sceneId.value}`;
  }).catch(() => {
    coordinatesLoader.value = false;
  });
};

// Update Coordinates
const handleUpdateCoordinates = (data) => {
  coordinatesLoader.value = true;
  updateCoordinate(data).then(() => {
    coordinatesLoader.value = false;
    // handleGetScene();
    // document.dispatchEvent(new Event('refreshAddProjectSvg'));
    window.location = `/masterscenes/earth/${sceneId.value}`;
  }).catch(() => {
    coordinatesLoader.value = false;
  });
};

// Delete Coordinates
const handleDeleteCoordinate = () => {
  coordinateDeleteLoader.value= true;
  const obj = {
    "masterSceneId": sceneId.value,
    "coordinateId": coordinateToDelete.value,
  };
  deleteCoordinate(obj).then(() => {
    coordinateDeleteLoader.value= false;
    handleGetScene();
    document.dispatchEvent(new Event('refreshAddProjectSvg'));
    openCoordinatesDeleteModal.value = false;
  }).catch((err) => {
    coordinateDeleteLoader.value= false;
    console.log(err);
  });
};

// Update Positions
const handleUpdatePositions = ()  => {
  const messageElement = earthPositionMsgRef.value;
  console.log(messageElement);
  messageElement.classList.remove('hidden');
  messageElement.classList.add('block');
  messageElement.innerText = "Updating positions...";
  positionsLoader.value = true;
  const obj = {
    'scene_id': sceneId.value,
    'earth_position': {
      'x_axis': Number(window.camera.position.x),
      'y_axis': Number(window.camera.position.y),
      'z_axis': Number(window.camera.position.z),
    },
    "organization_id": getCookie('organization'),
  };
  updateMasterScene(obj).then(() => {
    handleGetScene();
    document.dispatchEvent(new Event('refreshAddProjectSvg'));
    messageElement.innerText = "Updated...";
  }).catch((err) => {
    positionsLoader.value = false;
    console.log(err);
  }).finally(() => {
    positionsLoader.value = false;
    setTimeout(() => {
      console.log("setTimeout");

      messageElement.classList.remove('block');
      messageElement.classList.add('hidden');
    }, 700);
  });
};

</script>

<template >
  <!-- Static sidebar for desktop -->
  <div
    class="h-full w-72 dark:bg-bg-default flex flex-col  bg-bg-1000 border-bg-900 border-r-[1px] overflow-auto relative">
    <!-- Sidebar component, swap this element with another sidebar if you like -->
    <div v-if="sceneDetails" class="flex grow flex-col gap-y-4 overflow-y-auto px-4 pb-4 mt-4">
      <div>
        <div class="flex items-center gap-2 h-12 py-1.5 mb-2">
          <button class="w-10 h-full bg-black p-1 rounded flex justify-center items-center bg-re"
            @click="() => isMasterScene ? router.push(`/masterscenes`) : router.push(`/projects/${projectId}/scenes`) ">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 fill-white" viewBox="0 0 24 24">
              <g data-name="Layer 2">
                <g data-name="arrow-back">
                  <rect width="24" height="24" transform="rotate(90 12 12)" opacity="0" />
                  <path
                    d="M19 11H7.14l3.63-4.36a1 1 0 1 0-1.54-1.28l-5 6a1.19 1.19 0 0 0-.09.15c0 .05 0 .08-.07.13A1 1 0 0 0 4 12a1 1 0 0 0 .07.36c0 .05 0 .08.07.13a1.19 1.19 0 0 0 .09.15l5 6A1 1 0 0 0 10 19a1 1 0 0 0 .64-.23 1 1 0 0 0 .13-1.41L7.14 13H19a1 1 0 0 0 0-2z" />
                </g>
              </g>
            </svg>
          </button>
          <a :href="isMasterScene ? preview_domain + `/${getCookie('organization')}/masterscene/${sceneId}` : preview_domain + `/${getCookie('organization')}/projectscene/${projectId}/${sceneId}`"
            target="_blank" type="button" @click="{ }"
            class=" w-full createButton inline-flex items-center justify-between rounded-md bg-gray-500 px-3 py-2 text-sm font-semibold text-white shadow-sm  hover:bg-gray-700">
            Preview
            <svg width="18" height="16" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g id="eye">
                <path id="Vector"
                  d="M8 2.25C3.6896 2.25 0 6.252 0 7.5C0 8.8065 2.8368 12.75 8 12.75C13.1632 12.75 16 8.8065 16 7.5C16 6.252 12.3104 2.25 8 2.25ZM8 9.75C7.52532 9.75 7.06131 9.61804 6.66663 9.37081C6.27195 9.12357 5.96434 8.77217 5.78269 8.36104C5.60104 7.9499 5.55351 7.4975 5.64612 7.06105C5.73872 6.62459 5.9673 6.22368 6.30294 5.90901C6.63859 5.59434 7.06623 5.38005 7.53178 5.29323C7.99734 5.20642 8.4799 5.25097 8.91844 5.42127C9.35698 5.59157 9.73181 5.87996 9.99553 6.24997C10.2592 6.61998 10.4 7.05499 10.4 7.5C10.4 8.09674 10.1471 8.66903 9.69706 9.09099C9.24697 9.51295 8.63652 9.75 8 9.75Z"
                  fill="white" />
              </g>
            </svg>
          </a>
        </div>
        <div class="flex gap-2">
          <button :disabled=" isMasterScene ? newOrganizationStore.masterScenes?.[sceneId]?.sceneData?.type === 'earth' && $route.query.createCoordinates ? true : false: false" type="button" @click="handleCreate"
            class="w-full createButton inline-flex items-center rounded-md  bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 px-3 py-2 text-sm font-semibold shadow-sm">
            <PlusIcon class="h-4 w-4 mr-2 text-white" aria-hidden="true" />
             {{ isMasterScene ?  newOrganizationStore.masterScenes?.[sceneId]?.sceneData?.type !== 'earth' ? 'Create New Svg' : 'Create Coordinates' : 'Create New Svg' }}
          </button>
          <button type="button" @click="handleEditScene"
            class="w-fit createButton inline-flex items-center rounded-md  bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 px-2.5 py-2 text-sm font-semibold shadow-sm fill-white">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-5 w-5">
              <g data-name="Layer 2">
                <g data-name="settings-2">
                  <rect width="24" height="24" transform="rotate(180 12 12)" opacity="0" />
                  <path
                    d="M12.94 22h-1.89a1.68 1.68 0 0 1-1.68-1.68v-1.09a.34.34 0 0 0-.22-.29.38.38 0 0 0-.41 0l-.74.8a1.67 1.67 0 0 1-2.37 0L4.26 18.4a1.66 1.66 0 0 1-.5-1.19 1.72 1.72 0 0 1 .5-1.21l.74-.74a.34.34 0 0 0 0-.37c-.06-.15-.16-.26-.3-.26H3.68A1.69 1.69 0 0 1 2 12.94v-1.89a1.68 1.68 0 0 1 1.68-1.68h1.09a.34.34 0 0 0 .29-.22.38.38 0 0 0 0-.41L4.26 8a1.67 1.67 0 0 1 0-2.37L5.6 4.26a1.65 1.65 0 0 1 1.18-.5 1.72 1.72 0 0 1 1.22.5l.74.74a.34.34 0 0 0 .37 0c.15-.06.26-.16.26-.3V3.68A1.69 1.69 0 0 1 11.06 2H13a1.68 1.68 0 0 1 1.68 1.68v1.09a.34.34 0 0 0 .22.29.38.38 0 0 0 .41 0l.69-.8a1.67 1.67 0 0 1 2.37 0l1.37 1.34a1.67 1.67 0 0 1 .5 1.19 1.63 1.63 0 0 1-.5 1.21l-.74.74a.34.34 0 0 0 0 .37c.06.15.16.26.3.26h1.09A1.69 1.69 0 0 1 22 11.06V13a1.68 1.68 0 0 1-1.68 1.68h-1.09a.34.34 0 0 0-.29.22.34.34 0 0 0 0 .37l.77.77a1.67 1.67 0 0 1 0 2.37l-1.31 1.33a1.65 1.65 0 0 1-1.18.5 1.72 1.72 0 0 1-1.19-.5l-.77-.74a.34.34 0 0 0-.37 0c-.15.06-.26.16-.26.3v1.09A1.69 1.69 0 0 1 12.94 22zm-1.57-2h1.26v-.77a2.33 2.33 0 0 1 1.46-2.14 2.36 2.36 0 0 1 2.59.47l.54.54.88-.88-.54-.55a2.34 2.34 0 0 1-.48-2.56 2.33 2.33 0 0 1 2.14-1.45H20v-1.29h-.77a2.33 2.33 0 0 1-2.14-1.46 2.36 2.36 0 0 1 .47-2.59l.54-.54-.88-.88-.55.54a2.39 2.39 0 0 1-4-1.67V4h-1.3v.77a2.33 2.33 0 0 1-1.46 2.14 2.36 2.36 0 0 1-2.59-.47l-.54-.54-.88.88.54.55a2.39 2.39 0 0 1-1.67 4H4v1.26h.77a2.33 2.33 0 0 1 2.14 1.46 2.36 2.36 0 0 1-.47 2.59l-.54.54.88.88.55-.54a2.39 2.39 0 0 1 4 1.67z"
                    data-name="&lt;Group&gt;" />
                  <path
                    d="M12 15.5a3.5 3.5 0 1 1 3.5-3.5 3.5 3.5 0 0 1-3.5 3.5zm0-5a1.5 1.5 0 1 0 1.5 1.5 1.5 1.5 0 0 0-1.5-1.5z" />
                </g>
              </g>
            </svg>
          </button>
        </div>
        <button type="button" v-if="sceneDetails?.sceneData.type === 'rotatable_image'"
            @click="() =>  isMasterScene ?  router.push(`/masterscenes/${sceneId}/createframe`) : router.push(`/projects/${projectId}/scenes/${sceneId}/createframe`)"
            class="w-full createButton inline-flex justify-center items-center rounded-md mt-2  bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 px-3 py-2 text-sm font-semibold shadow-sm">
            <PlusIcon class="h-4 w-4 mr-2 text-white" aria-hidden="true" /> Create new frame
        </button>
        <div v-if="sceneDetails?.sceneData.type === 'earth'" class="flex flex-col justify-start items-start gap-2">
          <button
          :disabled="positionsLoader"
          type="button"
          @click="handleUpdatePositions"
            class="w-full createButton inline-flex items-center rounded-md mt-2 justify-center bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 px-3 py-2 text-sm font-semibold shadow-sm">

            <GlobeAltIcon class="h-4 w-4 mr-2 text-white" aria-hidden="true"></GlobeAltIcon>   Update Positions
          </button>
          <p ref="earthPositionMsgRef" class="hidden italic text-end w-full text-green-700"></p>
          <p class="text-black text-base font-semibold p-0 "> <b> Positions : </b> </p>
          <div class="mb-0">
            <p class="text-black text-base p-0 flex justify-start items-center gap-2"> <b class="font-semibold"> x : </b>{{sceneDetails?.sceneData?.earth_position?.x_axis}}</p>
            <p class="text-black text-base p-0  flex justify-start items-center gap-2"> <b class="font-semibold"> y : </b>{{sceneDetails?.sceneData?.earth_position?.y_axis}}</p>
            <p class="text-black text-base p-0  flex justify-start items-center gap-2"> <b class="font-semibold"> z : </b>{{sceneDetails?.sceneData?.earth_position?.z_axis}}</p>
          </div>
        </div>

      </div>

      <nav v-if="sceneDetails?.sceneData.type !== 'rotatable_image'" class="overflow-scroll overflow-x-hidden hide-scroll-bar w-full">

        <div class="d-flex justify-between items-center p-0">
          <h2 class="text-black text-lg font-semibold p-0"> List of {{  sceneDetails.sceneData.type === 'earth' ? 'Coordinates' : 'layers' }} :</h2>
        </div>

        <p class="text-black" v-if="sceneDetails.sceneData.type === 'earth' ? (sceneDetails.sceneData?.coordinates.length > 0 ?  false : true) : (Object.keys(sceneDetails?.svgData).length > 0 ? false : true)" > No Data ! </p>
        <div v-else class="flex flex-col justify-start items-start gap-2 w-full">

              <!-- Other layers -->
              <div v-if="sceneDetails?.sceneData.type !== 'earth'" class="w-full">
                <div v-for="svg, svgId  in sceneDetails?.svgData" :key="svgId">
                  <div
                    class="group flex justify-between gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold cursor-pointer text-txt-100 dark:text-txt-950 hover:text-txt-1000 hover:bg-bg-100 fill-black hover:fill-white"
                    style="transition: color 0.3s;" @click="handleSelectSvg(svg)">
                    <div class="flex items-center">
                      <component :is="ChartPieIcon" class="h-6 w-6 shrink-0 mr-2" aria-hidden="true" />
                      {{ svg.type }}
                    </div>
                    <div class="w-fit h-full my-auto flex justify-center items-center gap-3">
                      <button class="p-0.5 rounded-sm">
                        <svg v-if="$route.query.svgId != svg._id" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                          class="h-5 w-5">
                          <g data-name="Layer 2">
                            <g data-name="plus">
                              <rect width="24" height="24" transform="rotate(180 12 12)" opacity="0" />
                              <path
                                d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z" />
                            </g>
                          </g>
                        </svg>

                        <svg v-else xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-5 w-5">
                          <g data-name="Layer 2">
                            <g data-name="minus">
                              <rect width="24" height="24" transform="rotate(180 12 12)" opacity="0" />
                              <path d="M19 13H5a1 1 0 0 1 0-2h14a1 1 0 0 1 0 2z" />
                            </g>
                          </g>
                        </svg>
                      </button>
                      <button class="p-0.5 rounded-sm" @click.stop="()=>{openSvgDeleteModal=true;svgToDelete=svg._id}">

                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-rose-500" viewBox="0 0 24 24">
                          <g data-name="Layer 2">
                            <g data-name="trash-2">
                              <rect width="24" height="24" opacity="0" />
                              <path
                                d="M21 6h-5V4.33A2.42 2.42 0 0 0 13.5 2h-3A2.42 2.42 0 0 0 8 4.33V6H3a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2zM10 4.33c0-.16.21-.33.5-.33h3c.29 0 .5.17.5.33V6h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V8h12z" />
                              <path d="M9 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                              <path d="M15 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                            </g>
                          </g>
                        </svg>
                      </button>

                    </div>

                  </div>
                  <div v-if="$route.query.svgId == svg._id" class="pl-2 max-h-48 overflow-auto layers-container">
                    <div v-for="layer, layerId  in  svg.layers" :key="layerId" @click="handleLayerSelect(layer)"
                      :class="$route.query.layerId===layer.layer_id?'bg-gray-400':''"
                      class="group flex gap-x-3 p-2 text-xs w-full  leading-6 font-semibold cursor-pointer text-black hover:bg-gray-200 rounded"
                      style="transition: color 0.3s;">
                      <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 fill-blue-500" viewBox="0 0 24 24">
                          <g data-name="Layer 2">
                            <g data-name="layers">
                              <rect width="24" height="24" transform="rotate(180 12 12)" opacity="0" />
                              <path
                                d="M21 11.35a1 1 0 0 0-.61-.86l-2.15-.92 2.26-1.3a1 1 0 0 0 .5-.92 1 1 0 0 0-.61-.86l-8-3.41a1 1 0 0 0-.78 0l-8 3.41a1 1 0 0 0-.61.86 1 1 0 0 0 .5.92l2.26 1.3-2.15.92a1 1 0 0 0-.61.86 1 1 0 0 0 .5.92l2.26 1.3-2.15.92a1 1 0 0 0-.61.86 1 1 0 0 0 .5.92l8 4.6a1 1 0 0 0 1 0l8-4.6a1 1 0 0 0 .5-.92 1 1 0 0 0-.61-.86l-2.15-.92 2.26-1.3a1 1 0 0 0 .5-.92zm-9-6.26l5.76 2.45L12 10.85 6.24 7.54zm-.5 7.78a1 1 0 0 0 1 0l3.57-2 1.69.72L12 14.85l-5.76-3.31 1.69-.72zm6.26 2.67L12 18.85l-5.76-3.31 1.69-.72 3.57 2.05a1 1 0 0 0 1 0l3.57-2.05z" />
                            </g>
                          </g>
                        </svg>
                      </div>
                      <p class="flex-1 truncate">{{ layer.layer_id }}</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Coordinates -->
              <div v-else class="w-full">

                  <div  v-for="coordinates, index  in sceneDetails?.sceneData?.coordinates" :key="index"
                   :class="$route.query.svgId === coordinates._id ? 'text-txt-950 !bg-txt-100' : 'text-txt-100 hover:text-txt-950 bg-white hover:!bg-txt-100'"
                    class="flex justify-between items-center border-b last:border-none  rounded-none border-gray-400 gap-x-3 py-2 px-2 text-sm leading-6 font-semibold cursor-pointer   "
                    style="transition: color 0.3s;" @click="handleSelectSvg(coordinates)">

                    <p class="flex items-center">
                        {{ coordinates._id }}
                    </p>
                    <button
                        class=" w-fit p-1 rounded-sm "
                        @click.stop="()=>{openCoordinatesDeleteModal=true;coordinateToDelete=coordinates._id;}">

                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 fill-rose-500" viewBox="0 0 24 24">
                          <g data-name="Layer 2">
                            <g data-name="trash-2">
                              <rect width="24" height="24" opacity="0" />
                              <path
                                d="M21 6h-5V4.33A2.42 2.42 0 0 0 13.5 2h-3A2.42 2.42 0 0 0 8 4.33V6H3a1 1 0 0 0 0 2h1v11a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3V8h1a1 1 0 0 0 0-2zM10 4.33c0-.16.21-.33.5-.33h3c.29 0 .5.17.5.33V6h-4zM18 19a1 1 0 0 1-1 1H7a1 1 0 0 1-1-1V8h12z" />
                              <path d="M9 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                              <path d="M15 17a1 1 0 0 0 1-1v-4a1 1 0 0 0-2 0v4a1 1 0 0 0 1 1z" />
                            </g>
                          </g>
                        </svg>
                      </button>

                  </div>

              </div>

            </div>
      </nav>
    </div>

    <!-- Update Svg - Project Scene ||  Master Scene -->
    <RightSidebar
      :open="sceneDetails && (route.query.createCorodinates || sceneDetails.sceneData.type === 'earth' ? route.query.svgId : route.query.svgId && route.query.layerId) && ( isMasterScene ?  newOrganizationStore.masterScenes && newOrganizationStore.projects  : projectStore.landmarks && projectStore.buildings &&  categoryList && projectStore.scenes  )"
      @closeModal="handleCloseUpdateSidebar" >

        <UpdateProjectSvgLayers
         v-if="sceneDetails.sceneData.type !== 'earth'"
         @closeModal="() => router.push({ path: route.path, query: { svgId: route.query.svgId } })"
         :landmarks="isMasterScene ? null : projectStore.landmarks"
         :categoryList="isMasterScene ? null : categoryList"
          />

           <!-- Create Coordinates (Earth) -->
        <CreateCoordinates
         v-if="isMasterScene && sceneDetails.sceneData.type === 'earth' && route.query.createCorodinates"
        :sceneId="sceneId"
        :dataName="'Create'"
        :loader="coordinatesLoader"
        :coordinateValues = "false"
        @handleSubmit="(val) => handleCreateCoordinates(val)"
        @closeModal="() => router.push({ path: route.path, query: {} })"
         />

          <!-- Update Coordinates (Earth) -->
        <CreateCoordinates
        v-if="isMasterScene && sceneDetails.sceneData.type === 'earth'"
        :sceneId="sceneId"
        :dataName="'Edit'"
        :loader="coordinatesLoader"
        :coordinateValues="getIndividualCoordniatesInfo(route.query.svgId)"
        @handleSubmit="(val) => handleUpdateCoordinates(val)"
        @closeModal="() => selectedSvg = null"
         />

    </RightSidebar>

     <Modal :open="openCoordinatesDeleteModal || openSvgDeleteModal ">

      <div class="w-full sm:max-w-lg">
          <!-- Delete Coodinates (Earth) -->

            <DeleteModalContent v-if="openCoordinatesDeleteModal"  :loader="coordinateDeleteLoader" @closeModal="(e) => openCoordinatesDeleteModal = false"
              @handleDelete="handleDeleteCoordinate" :dataName="`Coordinate`" />

            <!-- Svg Layer  -->

          <DeleteModalContent v-if="openSvgDeleteModal" :trash="true" :loader="svgDeleteLoader" @closeModal="(e) => openSvgDeleteModal = false"
            @handleDelete="handleMoveSvgToTrash" :dataName="`${isMasterScene ? 'Master scene' : 'Project scene'} Svg`" />

          </div>
    </Modal>

  </div>
</template>

<style scoped>
</style>

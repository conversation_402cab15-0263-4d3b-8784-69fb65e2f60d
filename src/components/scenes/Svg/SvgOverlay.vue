<script setup>
import { ref, watch, defineProps, defineEmits, defineExpose, onBeforeUnmount } from 'vue';
import IndexLayer from './IndexLayer.vue';
const emit = defineEmits([
  'clickEvent',
  'mouseEnter',
  'mouseLeave',
  'imageHigResLoaded',
  'imageLowResLoaded',
  'svgLoaded',
  'transitionEnd',
]);
const containerRef = ref(null);
const props = defineProps({
  Data: Object,
  bucketURL: String,
  replaceURL: String,
  landmarkData: Object,
  sceneId: String,
  projectId: String,
});
const svgContent = ref(null);
const layers = ref([]);
const showLoader = ref(false);

async function cdn (path) {
  // If (path != undefined && path.includes('?alt')) {
  //   Path = path.substring(0, path.lastIndexOf('?alt'));
  //   Path = path.replace('firebasestorage.googleapis.com/v0/b/' + props.bucketURL + '/o', props.replaceURL);
  //   Return path;
  // }
  // Else
  return path;
}

/* Const classList = ref({
  'project': 'areaSVGCls',
  'landmarks': 'landmarkSvgCls',
  'location': 'locationSvgCls',
  'media': 'mediaSvgCls',
  'route': 'routeSvgCls',
  'radius': 'radiusSvgCls',
  'vrTour': 'vrTourSVgCls',
  'labels': 'labelsSvgCls',
  'inventory': 'InventoryCls',
  'amenities': '',
  'boundary': 'boundarySVGCls',
  'connectivity': 'connectSVGCls',
}); */

/* Const toolBtnData=ref([{
  id: 'radius',
  activeSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="white" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2V4C7.6 4 4 7.6 4 12C4 16.4 7.6 20 12 20C16.4 20 20 16.4 20 12H22C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2ZM12 6V8C9.8 8 8 9.8 8 12C8 14.2 9.8 16 12 16C14.2 16 16 14.2 16 12H18C18 15.3 15.3 18 12 18C8.7 18 6 15.3 6 12C6 8.7 8.7 6 12 6Z"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12 14C10.9 14 10 13.1 10 12C10 10.9 10.9 10 12 10C13.1 10 14 10.9 14 12C14 13.1 13.1 14 12 14Z"/>
            </svg>`,
  inactiveSVG: `<svg width="24" height="24" viewBox="0 0 24 24" fill="#28334A"  xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2V4C7.6 4 4 7.6 4 12C4 16.4 7.6 20 12 20C16.4 20 20 16.4 20 12H22C22 17.5 17.5 22 12 22C6.5 22 2 17.5 2 12C2 6.5 6.5 2 12 2ZM12 6V8C9.8 8 8 9.8 8 12C8 14.2 9.8 16 12 16C14.2 16 16 14.2 16 12H18C18 15.3 15.3 18 12 18C8.7 18 6 15.3 6 12C6 8.7 8.7 6 12 6Z"/>
      <path fill-rule="evenodd" clip-rule="evenodd" d="M12 14C10.9 14 10 13.1 10 12C10 10.9 10.9 10 12 10C13.1 10 14 10.9 14 12C14 13.1 13.1 14 12 14Z"/>
  </svg>`,
}]); */

async function readSVGLink (data) {
  return new Promise((resolve, reject) => {
    const requestOptions = {
      method: 'GET',
      redirect: 'follow',
    };

    if (!data.svg_url) {
      reject(new Error('svg_url is required'));
      return;  // Ensure we return after rejecting
    }

    cdn(data.svg_url)
      .then((url) => fetch(url, requestOptions))
      .then((response) => response.text())
      .then((result) => {
        const tempElement = document.createElement('div');
        tempElement.innerHTML = result;

        for (let i = 0; i < tempElement.children.length; i++) {
          for (let j = 0; j < tempElement.children[i].children.length; j++) {
            console.log(tempElement.children[i].children[j].id);
            layers.value.push({
              layer_data: data.layers[tempElement.children[i].children[j].id],
              layer: tempElement.children[i].children[j],
            });
          }
        }
        console.log(layers.value);
        resolve();
      })
      .catch((error) => {
        console.log('error', error);
        reject(error);
      });
  });
}

function findOutermostParentGNode (element) {
  let parentGNode = null;
  while (element && element.tagName !== 'svg') {
    if (element.tagName === 'g') {
      parentGNode = element;
    }
    element = element.parentNode;
  }
  return parentGNode;
}

async function clickEvent (e) {
  const id = await findOutermostParentGNode(e.target).id;
  emit('clickEvent', id, e.clientX, e.clientY);
}
async function mouseEnter (e) {
  const id = await findOutermostParentGNode(e.target).id;
  emit('mouseEnter', id, e.clientX, e.clientY);
}
async function mouseLeave (e) {
  const id = await findOutermostParentGNode(e.target).id;
  emit('mouseLeave', id);
}

async function addEventListenersToSVG () {
  const elements = document.getElementsByClassName('areaSVGCls');
  const elements1 = document.getElementsByClassName('mediaSvgCls');
  const elements2 = document.getElementsByClassName('landmarkSvgCls');
  const elements3 = document.getElementsByClassName('locationSvgCls');
  const elements4 = document.getElementsByClassName('vrTourSVgCls');
  const elements5 = document.getElementsByClassName('InventoryCls');
  const elements6 = document.getElementsByClassName('labelsSvgCls');
  const elements7 = document.getElementsByClassName('boundarySVGCls');
  const elements8 = document.getElementsByClassName('connectSVGCls');
  const total_elements = Array.from(elements)
    .concat(Array.from(elements1))
    .concat(Array.from(elements2))
    .concat(Array.from(elements3))
    .concat(Array.from(elements4))
    .concat(Array.from(elements5))
    .concat(Array.from(elements6))
    .concat(Array.from(elements7))
    .concat(Array.from(elements8));
  for (let i = 0; i < total_elements.length; i++) {
    total_elements[i].addEventListener('click', clickEvent);
    total_elements[i].addEventListener('mouseenter', mouseEnter);
    total_elements[i].addEventListener('mouseleave', mouseLeave);
  }
}
const svgRenderer = () => {
  if (Object.keys(props.Data.svgData).length) {
    Promise.all([...Object.values(props.Data.svgData).map((svg) => readSVGLink(svg))]).then(() => {
      showLoader.value=true;
      addEventListenersToSVG();
      emit('svgLoaded');
    });
  }
};

svgRenderer();

watch(() => props.Data, () => {
  console.log("*****", props.Data);
  svgRenderer();
});

function scrollToCenter () {
  const container = document.getElementById('svgContent');
  const scrollTarget = document.getElementById('SVGLayer');
  const screenWidth = window.innerWidth;
  const screenHeight = window.innerHeight;
  if (screenWidth < 991) {
    const targetWidth = scrollTarget.clientWidth;
    const scrollLeft = (targetWidth - screenWidth) / 2;
    container.scrollLeft = scrollLeft;
  }
  if (screenHeight < 700) {
    const targetHeight = scrollTarget.clientHeight;
    const scrollTop = (targetHeight - screenHeight) / 2;
    container.scrollTop = scrollTop;
  }
}

// Function highResImageLoaded () {
//   If (document.getElementById('svg_highres_image')) {
//     Document.getElementById('svg_lowres_image').remove();
//   }
//   Emit('imageHigResLoaded');
// }

// Function lowResImageLoaded () {
//   Const image = document.createElementNS('http://www.w3.org/2000/svg', 'image');
//   image.setAttributeNS('http://www.w3.org/1999/xlink', 'href', props.Data.background.highRes);
//   Image.setAttribute('height', window.innerHeight);
//   Image.setAttribute('width', window.innerWidth);
//   Image.setAttribute('style', 'object-fit:contain;height:100%;width:100%');
//   Image.addEventListener('load', highResImageLoaded);
//   Image.setAttribute('id', 'svg_highres_image');
//   Document.getElementById('svg_lowres_image').parentNode.insertBefore(image, document.getElementById('svg_lowres_image'));
//   Emit('imageLowResLoaded');
//   ScrollToCenter();
// }

function transitionEnd () {
  emit('transitionEnd');
}
function startAnimation () {
  containerRef.value.style.opacity = 1;
}
onBeforeUnmount(() => {
  containerRef.value.classList.add('active');
  const elements = document.getElementsByClassName('areaSVGCls');
  const elements1 = document.getElementsByClassName('mediaSvgCls');
  const elements2 = document.getElementsByClassName('landmarkSvgCls');
  const elements3 = document.getElementsByClassName('locationSvgCls');
  const elements4 = document.getElementsByClassName('vrTourSVgCls');
  const elements5 = document.getElementsByClassName('InventoryCls');
  const elements6 = document.getElementsByClassName('labelsSvgCls');
  const elements7 = document.getElementsByClassName('boundarySVGCls');
  const elements8 = document.getElementsByClassName('connectSVGCls');
  const total_elements = Array.from(elements)
    .concat(Array.from(elements1))
    .concat(Array.from(elements2))
    .concat(Array.from(elements3))
    .concat(Array.from(elements4))
    .concat(Array.from(elements5))
    .concat(Array.from(elements6))
    .concat(Array.from(elements7))
    .concat(Array.from(elements8));
  for (let i = 0; i < total_elements.length; i++) {
    total_elements[i].removeEventListener('click', clickEvent);
    total_elements[i].removeEventListener('mouseenter', mouseEnter);
    total_elements[i].removeEventListener('mouseleave', mouseLeave);
  }
  svgContent.value = null;
});
defineExpose({
  startAnimation,
});

window.addEventListener('resize', scrollToCenter);
</script>
<template>
  <div ref="containerRef" @transitionend="transitionEnd" class="svgContainer">
    <div class="svgWrapper">
      <svg version="1.1" id="SVGLayer" xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
      viewBox="0 0 1920 1080"
      style="enable-background:new 0 0 1920 1080;"
      xml:space="preserve" preserveAspectRatio="xMidYMid slice" class="svgLayer">
        <image height="747" width="1536" id="svg_lowres_image"
        style="object-fit:contain;height:100%;width:100%"
        :xlink:href="props.Data.sceneData.background.high_resolution"></image>
        <IndexLayer v-for="layer, layerIndex in layers.filter(layer=>layer.layer.tagName=='style' || layer.layer_data)"
          :id="layer.id"
          :layer="layer"
          :key="layerIndex"
          :sceneId="sceneId"
          :projectId="projectId">
          </IndexLayer>
     </svg>
    </div>
  </div>
  <portal-target name="destination">
    <slot></slot>
</portal-target>
</template>
<style scoped>
.svgContainer {
  height: 100%;
  width: 100%;
  position: relative;
  user-select: none;
  overflow: hidden;
  transform-origin: center center;
  transition: transform 0.5s, opacity 0.5s;
}
.svgWrapper {
  height: 100%;
  width: 100%;
  overflow: hidden;
  position: relative;
}
.fade-in-overlay {
  opacity: 0;
  transition: opacity 2s ease;
}
.fade-in-animation {
  opacity: 1;
}
.svgContainer.active {
  transform: scale(1.2);
  opacity: 0;
}
.svgContent {
  width: 100%;
  height: 100%;
  overflow-x: auto;
  white-space: nowrap;
  scroll-behavior: smooth;
}
.svgContent::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}
</style>

<script setup>
import { defineProps } from 'vue';
import router from '@/router';
import { isMasterScenePath } from '@/helpers/helpers';
import { useRoute } from 'vue-router';
import { ref } from 'firebase/storage';

const route = useRoute();
const isMasterScene = ref(isMasterScenePath(route.fullPath));

const props = defineProps(['layer', 'layer_data', 'sceneId', 'projectId']);
console.log(props.layer);
function selectLayer (layer) {
  console.log('svg_id', layer.layer.getAttribute('class').substring(4));
  console.log('layer_id', layer.layer_data.layer_id);
  router.push({ path: isMasterScene ?`/masterscenes/${props.sceneId}/layers`:`/projects/${props.projectId}/design/scenes/${props.sceneId}/layers`, query: {svgId: layer.layer.getAttribute('class').substring(4), layerId: layer.layer_data.layer_id} });

}
</script>
<template>
  <component :is="'style'" v-if="!layer.layer_data && layer.layer.tagName === 'style'" v-html="layer.layer.innerHTML">
  </component>
  <g v-else @click="selectLayer(layer)"
    :class="[layer.layer.getAttribute('class') + ' ' + layer.layer_data.type + 'opacity-80 hover:opacity-100 hover:fill-white cursor-pointer', $route.query.layerId === layer.layer_data.layer_id ? 'fill-red-700' : '']"
    v-html="layer.layer.innerHTML"></g>
</template>
<style scoped></style>

<script setup>
import {ref} from 'vue';

// Props

const emits = defineEmits(['submitEditedText']);
const props = defineProps({
  text: {
    type: String,
  },
  show: {
    type: <PERSON>olean,
  },
});
// const text = ref('Click me to edit');
const tempText = ref(props.text);

const finishEditing = async () => {
  if (tempText.value !== props.text) {
    emits('submitEditedText', tempText.value);
  }
};

</script>

<template>
    <input
      v-model="tempText"
      @blur="finishEditing"
      @keydown.enter="finishEditing"
      class="text-xs leading-4 text-gray-900 font-semibold ml-1 border border-gray-200 bg-gray-100 rounded-sm w-full p-0.5"
      autofocus
    />
</template>

<style scoped>
    /* Your styles go here */
</style>

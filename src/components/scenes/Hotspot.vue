<script setup>

import Spinner from '../common/Spinner.vue';
import { useRoute } from 'vue-router';
import Multiselect from 'vue-multiselect';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { onMounted, ref, watch } from 'vue';
import projectSchema from '../../validationSchema/scene/updateProjectLayerSchema';
import masterFormSchema from '../../validationSchema/scene/updateMasterLayerSchema';
import { gsplatFieldValidatteSchema } from '../../validationSchema/scene/updateProjectLayerSchema';
import { gsplatFieldValidatteSchema as masterGsplatFieldValidatteSchema } from '../../validationSchema/scene/updateMasterLayerSchema';
import { Org_Store } from '../../store/organization';
import { ProjectStore } from '../../store/project';
import { createLayers, updatesvgLayer } from '../../api/projects/scene/svg/index';
import { createLayers as masterCreateLayers, updatesvgLayer as masterUpdatesvgLayer  } from '../../api/masterScene/svg/index';
import router from '../../router';
import { isMasterScenePath } from '../../helpers/helpers';

const emit = defineEmits(['closeModal', 'updateGsplatLayers']);
const route = useRoute();
const props = defineProps({
  type: String,
  landmarks: Object,
  svgData: Object,
  scenes: Object,
  projects: Object,
  projectId: String,
  defaultPostion: Object,
  defaultScale: Object,
  categoryList: Array,
  isPlane: Boolean,
});
const loader = ref(false);
const svgId = ref(Object.keys(props.svgData)[0]);
const sceneId = ref(route.params.scene_id);
const layerId = ref(route.query.layerId);
const newOrganizationStore = Org_Store();
const projectStore = ProjectStore();
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const formSchema = isMasterScene.value ? masterFormSchema : projectSchema;
console.log(formSchema);
const hotSpotNameRef = ref(null);
const xposition = ref(null);
const yposition = ref(null);
const zposition = ref(null);
const width = ref(null);
const height = ref(null);
const previousData = ref({});

/* Methods */

// Find the route new
const findTypeRoute = () => {
  const routeList = [];
  Object.values(props.svgData).forEach((svg) => {

    Object.values(svg.layers).forEach((layer) => {
      if (layer.type === 'route') {
        routeList.push({ _id: layer.layer_id, name: layer.name });
      }
    });
  });
  return routeList;
};

const handleChange = (val) => {

  if (val === 'project') {
    if (formSchema.project_id.options.length === 0) {
      formSchema.project_id.options = Object.values(newOrganizationStore.projects).map((project) => ({ '_id': project._id, 'name': project.name }));
    }
  }
  if (val === 'scene' || val === 'pin' || val === 'building' || val === 'floor' || val==='community') {
    if (formSchema.scene_id.options.length === 0) {
      formSchema.scene_id.options = Object.values(props.scenes).map(({ sceneData }) => ({ '_id': sceneData._id, 'name': sceneData.name }));
    }
  }

  if (!isMasterScene.value){
    if (val === 'landmark') {
      if (formSchema.landmark_id.options.length === 0) {
        formSchema.landmark_id.options = Object.values(props.landmarks).map((landmark) => ({ '_id': landmark._id, 'name': landmark.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
      // Finding layers with type route
      formSchema.route_id.options = findTypeRoute();

    }
    if (val === 'building') {

      formSchema.building_id.options = Object.values(projectStore.buildings).map((building) => ({ '_id': building._id, 'name': building.name }));
    }
    if (val === 'community') {
      console.log(projectStore.communities);

      formSchema.community_id.options = Object.values(projectStore.communities).map((community) => ({ '_id': community._id, 'name': community.name }));
    }

    if (val === 'floor') {
      if (formSchema.building_id.options.length === 0) {
        formSchema.building_id.options = Object.values(projectStore.buildings).map((building) => ({ '_id': building._id, 'name': building.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
      watch(formSchema.building_id.ref, (val) => {
        if (formSchema.floor_id.options.length === 0) {
          formSchema.floor_id.options = Object.values(projectStore.buildings[val].floors).map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
        }
      });
      if (formSchema.building_id.ref.value) {
        if (formSchema.floor_id.options.length === 0) {
          formSchema.floor_id.options = Object.values(projectStore.buildings[formSchema.building_id.ref.value].floors).map((floor) => ({ '_id': floor.floor_id, 'name': floor.floor_name }));
        }
      }

    }
    if (val === 'units') {
      if (formSchema.units.options.length === 0) {
        formSchema.units.options = Object.values(projectStore.units).map((unit) => ({ '_id': unit._id, 'name': (unit.name + ' ' + (unit.building_id?projectStore.buildings[unit.building_id].name:'') + ' ' + (unit.community_id?projectStore.communities[unit.community_id].name:'')   ) }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'amenity') {
      if (formSchema.amenity_id.options.length === 0) {
        formSchema.amenity_id.options = Object.values(projectStore.amenities).map((amenity) => ({ '_id': amenity._id, 'name': amenity.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
    if (val === 'amenitycategory') {
      if (formSchema.amenity_category.options.length === 0) {
        formSchema.amenity_category.options = Object.values(props.categoryList).map((elem) => ({ '_id': elem.name, 'name': elem.name }));
        // Console.log(`output->landmark`,props.landmarks)
      }
    }
  }

};

const handleInitialValues = () => {
  layerId.value = route.query.layerId;
  if (props.svgData[svgId.value]?.layers[layerId.value]){
    // Testing
    const layer = props.svgData[svgId.value].layers[layerId.value];
    const {position, scale, name, ...query} =  layer;
    if (!props.isPlane){
      previousData.value.name = name;
    }
    previousData.value.xposition = position.x;
    previousData.value.yposition = position.y;
    previousData.value.zposition = position.z;

    previousData.value.query = query;
    hotSpotNameRef.value = layer.name;
    xposition.value = layer.position.x;
    yposition.value = layer.position.y;
    zposition.value = layer.position.z;
    if (props.isPlane){
      previousData.value.width = scale.width;
      previousData.value.height = scale.height;
      width.value = layer.scale.width;
      height.value = layer.scale.height;
    }
    formSchema.type.ref.value = layer.type ? layer.type : null;
    formSchema.project_id.ref.value = layer.project_id ? layer.project_id : null;
    formSchema.scene_id.ref.value = layer.scene_id ? layer.scene_id : null;
    formSchema.image_id.ref.value = layer.image_id ? layer.image_id : null;

    if (!isMasterScene.value){
      formSchema.landmark_id.ref.value = layer.landmark?.landmark_id ? layer.landmark.landmark_id : null;
      formSchema.route_id.ref.value = layer.landmark?.route_id ? layer.landmark.route_id : null;
      formSchema.building_id.ref.value = layer.building_id ? layer.building_id : null;
      formSchema.community_id.ref.value = layer.community_id ? layer.community_id : null;
      formSchema.floor_id.ref.value = layer.floor_id ? layer.floor_id : null;
      formSchema.amenity_id.ref.value = layer.amenity_id ? layer.amenity_id : null;
      formSchema.units.ref.value = layer.units ? layer.units.map((elem) => {
        return { '_id': elem, 'name': projectStore.units[elem].name };
      }) : null;
      formSchema.title.ref.value = layer.title ? layer.title : null;
      formSchema.category.ref.value = layer.category ? layer.category : null;
    }

  }

  /*   If(props.defaultPostion){
        // position initial values
  } */

  handleChange(formSchema.type.ref.value);

};

const comparePreviousValues = (compareObj, sourceObj) => {
  const newObject = {}; // Return Object
  const comparisonObject = {...compareObj};
  const queryObject = compareObj.query;
  delete queryObject.layer_id;
  delete queryObject.svg_url;
  // delete queryObject.scene_id;
  const objectQuery = {};
  Object.keys(comparisonObject).forEach((key) => {
    if (key !== 'xposition' && key !== 'yposition' && key !== 'zposition' && key !== 'name' && key !== 'position' ){
      // Query object
      if (typeof comparisonObject[key] === 'object'){
        // Extract the object and get the keys and values
        Object.keys(comparisonObject[key]).forEach((secondKey) => {

          if (typeof comparisonObject[key][secondKey] === 'object'){
            Object.keys(comparisonObject[key][secondKey]).forEach((thirdkey) => {
              objectQuery[thirdkey] = comparisonObject[key][secondKey][thirdkey]; // Append
            });
          } else {
            objectQuery[secondKey] = comparisonObject[key][secondKey]; // Append
          }

        });
      }
    }
  });
  comparisonObject.query = {...objectQuery};
  const {name, xposition, yposition, zposition, width, height, svgFile, ...query} = sourceObj;
  const newSourceObj = {
    name: name,
    xposition: xposition,
    yposition: yposition,
    zposition: zposition,
    ...( props.isPlane && ({width: width,
      height: height})),
    query: {...query},
  };

  Object.keys(newSourceObj).forEach((key) => {
    if (key !== 'query'){

      if (comparisonObject[key] !== newSourceObj[key]){
        if (key ===  'xposition' || key === 'zposition' ||  key === 'yposition'  ){
          newObject.xposition = newSourceObj.xposition;
          newObject.zposition = newSourceObj.zposition;
          newObject.yposition = newSourceObj.yposition;
        } else if ( key === 'width' || key === 'height' ){
          newObject.width = newSourceObj.width;
          newObject.height = newSourceObj.height;
        } else {
          newObject[key] = newSourceObj[key];
        }
      }

    } else {
      // Type & its related
      if (comparisonObject[key].type === newSourceObj[key].type){
        let anyChange = false;
        Object.keys(newSourceObj[key]).forEach((secondKey) => {
          if (comparisonObject[key][secondKey] !== newSourceObj[key][secondKey]){
            newObject[secondKey] = newSourceObj[key][secondKey];
            anyChange = true;
          }
        });
        if (anyChange){
          newObject.type = newSourceObj[key].type;
        }
      } else {
        // Type is not same or undefined
        Object.keys(newSourceObj[key]).forEach((querykey) => {
          newObject[querykey] = newSourceObj[key][querykey];
        });
        newObject.type = newSourceObj[key].type;
      }

    }
  });

  if (svgFile){
    newObject.svgFile =  svgFile;
  }

  console.log(newObject);
  return newObject;
};

const frameParms = (values) => {
  const formData = new FormData();
  if (!isMasterScene.value){
    formData.append('project_id', props.projectId);
  }
  formData.append('svg_id', svgId.value);
  if (props.type === 'edit'){
    formData.append('layer_id', layerId.value);
  }
  const {name, xposition, yposition, zposition, width, height, svgFile, ...query} = values;
  console.log(values);

  name && formData.append('name', name);
  if (props.type === 'edit'){
    Object.keys(query).length > 0 && formData.append('query', JSON.stringify(query));
  } else {
    const newquery = Object.values(query)[0]; // create
    console.log(Object.values(query));
    Object.keys(query).length > 0 && formData.append('query', JSON.stringify(newquery));
  }
  const position = {
    ...( (xposition !== null && xposition !== undefined )&& {x: xposition}),
    ...( (yposition !== null && yposition !== undefined) && {y: yposition}),
    ...( (zposition !== null && zposition !== undefined) && {z: zposition}),
  };
  Object.keys(position).length > 0 && formData.append('position', JSON.stringify(position));
  if (props.isPlane){
    const scale = {
      ...( (width !== null && width !== undefined )&& {'width': width}),
      ...( (height !== null && height !== undefined) && {'height': height}),
    };
    console.log(scale);
    Object.keys(scale).length > 0 && formData.append('scale', JSON.stringify(scale));
  }

  svgFile && formData.append('svgFile',  svgFile);

  return formData;
};

watch(() => route.query.svgId, () => {
  svgId.value = route.query.svgId;
});

watch(() => route.query.layerId, () => {
  handleInitialValues();
});

watch(formSchema.type.ref, (val) => {
  console.log(val);
  handleChange(val);
});

// Watch position
watch(() => props.defaultPostion, () => {
  console.log(props.defaultPostion);
  if (Object.keys(props.defaultPostion).length > 0){
    xposition.value = props.defaultPostion.x;
    yposition.value = props.defaultPostion.y;
    zposition.value = props.defaultPostion.z;
  }

}, {deep: true});

watch(() => props.defaultScale, () => {

  if (Object.keys(props.defaultScale).length > 0){
    width.value = props.defaultScale.width;
    height.value = props.defaultScale.height;
  }

}, {deep: true});

const handelChangeFile = (e) => {
  const file = e.target.files[0];
  const svgFormats = ['image/svg+xml'];
  if (svgFormats.includes(file.type)){
    if (layerId.value){
      emit('updateGsplatLayers', {
        'id': layerId.value,
        'svg_url': URL.createObjectURL(file),
      });
    }
  }
};

const handelSubmit = (values) => {
  if (!isMasterScene.value){
    if (values.units && values.units.length !== 0) {
      values.units = values.units.map((elem) => elem._id);
    }
  }
  /*  Const obj = removeUndefinedAndNullInObject(values);  */
  loader.value = true;
  console.log(values);
  if (props.type === 'add'){
    // Add
    const { xposition, yposition, zposition, width, height, svgFile, ...query} = values;
    const newObj = {
      'name': name,
      'xposition': xposition,
      'yposition': yposition,
      'zposition': zposition,
      'width': width,
      'height': height,
      'svgFile': svgFile,
      'query': query,
    };
    console.log(newObj);

    if (isMasterScene.value){
      masterCreateLayers(frameParms(newObj)).then(() => {
        loader.value = false;
        window.location = `/masterscenes/${sceneId.value}/layers`;
      }).catch(() => {
      }).finally(() => {
        loader.value = false;
      });
    } else {
      createLayers(frameParms(newObj)).then(() => {
        loader.value = false;
        window.location = `/projects/${props.projectId}/design/scenes/${sceneId.value}/layers`;
      }).catch((err) => {
        console.log('output->err in updateProjectSceneLayer', err);
      }).finally(() => {
        loader.value = false;
      });
    }
  } else {
    // Edit
    console.log(previousData.value);
    console.log(comparePreviousValues(previousData.value, values));
    console.log(frameParms(comparePreviousValues(previousData.value, values)));
    for (const pair of frameParms(comparePreviousValues(previousData.value, values)).entries()) {
      console.log(pair[0], pair[1]);
    }
    if (Object.keys(comparePreviousValues(previousData.value, values)).length > 0) {

      if (isMasterScene.value){
        masterUpdatesvgLayer(frameParms(comparePreviousValues(previousData.value, values))).then(() => {
          console.log(frameParms(comparePreviousValues(previousData.value, values)));
          loader.value = false;
          window.location = `/masterscenes/${sceneId.value}/layers`;
        }).catch((err) => {
          console.log('output->err in updateProjectSceneLayer', err);
        }).finally(() => {
          loader.value = false;
        });
      } else {

        updatesvgLayer(frameParms(comparePreviousValues(previousData.value, values))).then(() => {
          console.log(frameParms(comparePreviousValues(previousData.value, values)));
          loader.value = false;
          window.location = `/projects/${props.projectId}/design/scenes/${sceneId.value}/layers`;
        }).catch((err) => {
          console.log('output->err in updateProjectSceneLayer', err);
        }).finally(() => {
          loader.value = false;
        });
      }

    } else {
      if (isMasterScene.value){
        router.push(`/masterscenes/${sceneId.value}/layers`);
      } else {
        router.push(`/projects/${props.projectId}/design/scenes/${sceneId.value}/layers`);
      }
    }
  }
};

/* Hooks */
onMounted(() => {
  if (props.type === 'edit'){
    handleInitialValues();
  }
});

// Add the new method for handling unit removal
const handleUnitRemoval = (field, index) => {
  const newValue = [...formSchema[field].ref.value];
  newValue.splice(index, 1);
  formSchema[field].ref.value = newValue;
};

</script>

<template>
  <div
    class="relative">
      <!-- <h2 class="text-black text-lg mb-4 text-capitalize"> {{ type }} {{isPlane ?  'Plane' : 'Hotspot'}} </h2> -->

      <Form @submit="handelSubmit"
        :validation-schema="isMasterScene ? masterGsplatFieldValidatteSchema(type,isPlane) : gsplatFieldValidatteSchema(type, isPlane)">
        <div class="">
          <div class="flex flex-col gap-2">

            <!-- Name -->

            <div class="mx-2" v-if="!isPlane">
              <label for=""
                class="text-xs font-medium text-gray-500 mb-0">Name</label>

              <Field v-model="hotSpotNameRef" as="input" type="text"
                    id="name"
                    class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-sm"
                    name="name">
              </Field>

              <ErrorMessage name="name"
                class="text-sm text-rose-500 mt-1" as="p" />
            </div>

            <!-- Type -->
            <div v-if="formSchema.type !== null"
              class="mx-2">
              <p
              class="text-xs font-medium text-gray-500 ">{{
                  formSchema.type.label }}</p>
              <label :for="formSchema"
                class="w-full bg-white relative flex justify-center items-center gap-2 p-2 mt-1 mb-0 rounded-md ">
                  <p class="w-full text-gray-500 text-xs font-medium">{{
                  formSchema.type.ref.value }}</p>
                  <svg class="w-4 h-4 fill-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-downward"><rect width="24" height="24" opacity="0"/><path d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15 1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16z"/></g></g></svg>

              <Field v-model="formSchema.type.ref.value"
                v-if="formSchema.type.as.toLowerCase() === 'select'"
                :as="formSchema.type.as"
                :id="formSchema.type.name"
                :name="formSchema.type.name"
                class="absolute inset-0 opacity-0 cursor-pointer w-full h-full flex rounded-lg text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                <option value="" disabled> Choose </option>
                <option value="" disabled
                  v-if="formSchema.type.options === null || formSchema.type.options.length === 0">
                  No Data found ! </option>
                <option v-else :value="option.value"
                  v-for="option, index in  formSchema.type.options"
                  :key="index" class="text-black"> {{
                    option.value }} </option>
              </Field>
            </label>
              <ErrorMessage :name="formSchema.type.name"
                class="text-sm text-rose-500 mt-1" as="p" />
            </div>
            <!-- Others -->
            <div v-if="formSchema.type.ref.value" class="flex flex-col gap-2">
              <div
                v-for="items in formSchema.type.options[formSchema.type.ref.value]?.toShow"
                :key="items.field" >

                <div
                  v-if="formSchema[items.field]?.type === 'multiselect'"
                  class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                  <label
                    class="label-primary">{{
                      formSchema[items.field].label }}</label>
                  <Field as="input"
                    v-model="formSchema[items.field].ref.value"
                    class="sr-only"
                    :name="formSchema[items.field].name">
                  </Field>
                  <Multiselect
                    v-model="formSchema[items.field].ref.value"
                    :options="formSchema[items.field].options"
                    :searchable="true" :multiple="true"
                    :taggable="false" placeholder="units name?"
                    :close-on-select="false" label="name"
                    track-by="_id" open-direction="bottom"
                    deselectLabel="remove" selectLabel="">
                     <!-- Custom Option Template (showing just selected items count) -->
                                <template #selection="{ values, isOpen }">
        <span class=""
              v-if="values.length"
              v-show="!isOpen">{{ values.length }} units selected</span>
      </template>

      <!-- No Search Results Message -->
      <template v-slot:noResult>Oops! No Units found.</template>
                  </Multiselect>
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1" />
                    <!-- Selected Units Tag -->
                                <div name="unit_lists" class="">
                        <div class="outside-tags mt-2 flex flex-wrap gap-2 max-h-24 py-1 px-2 overflow-y-auto">
      <div
        class="bg-[#10b981] py-1 px-2 rounded-md text-white text-xs w-fit h-fit flex items-center gap-2"
        v-for="(unit, index) in formSchema[items.field].ref.value"
        :key="unit._id"
      >
        {{ unit.name }}
        <button class="remove-tag" @click="() => handleUnitRemoval(items.field, index)">
          <span class="sr-only">Remove</span>
<svg class="h-4 w-4 fill-[#f6f6f6]" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg></button>
        </div>
    </div>
    </div>

                </div>
                <div
                  v-if="formSchema[items.field]?.type === 'dropdown'"
                  class="mx-2">
                  <p
              class="text-xs font-medium text-gray-500 ">{{
                  formSchema[items.field].label }}</p>
                  <label :for="formSchema[items.field].name"
                    class="w-full bg-white relative flex justify-center items-center gap-2 p-2 mt-1 rounded-md">
                    <p class="w-full text-gray-500 text-xs font-medium overflow-hidden text-ellipsis whitespace-nowrap">{{`${formSchema[items.field].ref.value?formSchema[items.field].ref.value:formSchema[items.field].label}`}}</p>
                    <svg class="w-4 h-4 fill-gray-900" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="arrow-ios-downward"><rect width="24" height="24" opacity="0"/><path d="M12 16a1 1 0 0 1-.64-.23l-6-5a1 1 0 1 1 1.28-1.54L12 13.71l5.36-4.32a1 1 0 0 1 1.41.15 1 1 0 0 1-.14 1.46l-6 4.83A1 1 0 0 1 12 16z"/></g></g></svg>

                  <Field
                    v-model="formSchema[items.field].ref.value"
                    v-if="formSchema[items.field].as.toLowerCase() === 'select'"
                    :as="formSchema[items.field].as"
                    :id="formSchema[items.field].name"
                    :name="formSchema[items.field].name"
                    class="absolute inset-0 opacity-0 cursor-pointer w-full h-full flex rounded-lg text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                    {{ formSchema[items.field].ref.value }}
                    <option value='' class="text-gray-500">
                      Choose </option>
                    <option value="" disabled
                      v-if="formSchema[items.field].options === null || formSchema[items.field].options.length === 0">
                      No Data found ! </option>
                    <option v-else
                      :value="option._id ? option._id : option"
                      v-for="option, index in formSchema[items.field].options"
                      :key="index" class="text-black"> {{
                        option.name ? option.name : option }}
                    </option>
                  </Field>
                </label>
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                </div>
                <div
                  v-if="formSchema[items.field]?.type === 'text'"
                  class="mx-2">
                  <label :for="formSchema[items.field].name"
                    class="text-xs font-medium text-gray-500 mb-0">{{
                      formSchema[items.field].label }}</label>

                  <Field
                    v-model="formSchema[items.field].ref.value"
                    :type="formSchema[items.field].ref.type"
                    v-if="formSchema[items.field].as.toLowerCase() === 'input'"
                    :name="formSchema[items.field].name"
                    :id="formSchema[items.field].name"
                    class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-xs placeholder:text-gray-500 placeholder:text-left placeholder:font-semibold"
                    :placeholder="`enter ${formSchema[items.field].label}`" />
                  <ErrorMessage
                    :name="formSchema[items.field].name"
                    class="text-sm text-rose-500 mt-1"
                    as="p" />
                </div>

              </div>
            </div>

            <!-- Positions -->
            <div class="relative mx-2">

                      <label
                    class="text-xs font-medium text-gray-500 mb-0">
                      X position
                  </label>
                  <Field
                    v-model="xposition"
                   disabled
                    as="input"
                    type="number"
                    id="xposition"
                    class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-sm"
                    name="xposition">
                  </Field>

                  <ErrorMessage
                    name="xposition"
                    as="p"
                    class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative mx-2">
                          <label
                        class="text-xs font-medium text-gray-500 mb-0">
                          y position
                      </label>
                      <Field
                      v-model="yposition"
                      disabled
                          as="input"
                          id="yposition"
                        class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-sm"
                        name="yposition">
                      </Field>

                      <ErrorMessage
                        name="yposition"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative mx-2">
                          <label
                        class="text-xs font-medium text-gray-500 mb-0">
                          z position
                      </label>
                      <Field v-model="zposition" as="input"
                      disabled
                        id="zposition"
                        class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-sm"
                        name="zposition">
                      </Field>

                      <ErrorMessage
                        name="zposition"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative mx-2" v-if="isPlane">

            <label
            class="text-xs font-medium text-gray-500 mb-0">
             Width
            </label>
            <Field
            v-model="width"
            disabled
            as="input"
            type="number"
            id="width"
            class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-sm"
            name="width">
            </Field>

            <ErrorMessage
            name="width"
            as="p"
            class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative mx-2" v-if="isPlane">
                <label
              class="text-xs font-medium text-gray-500 mb-0">
                Height
            </label>
            <Field
            v-model="height"
            disabled
                as="input"
                id="height"
              class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-sm"
              name="height">
            </Field>

            <ErrorMessage
              name="height"
              as="p"
              class="text-sm text-rose-500 mt-1" />
            </div>

            <div class="relative mx-2" v-if="!isPlane">
                  <label
                        class="text-xs font-medium text-gray-500 mb-0">
                        Icon
                      </label>
                      <Field :onchange="(e) => handelChangeFile(e)" type="file"
                                        name="svgFile"
                                        id="svgFile"
                                        autocomplete="svgFile"
                                        class="w-full bg-white flex justify-center items-center gap-2 p-2 mt-1 rounded-md text-gray-500 text-xs font-medium placeholder:text-sm"
                                        placeholder="Upload gspalt zip" />

                      <ErrorMessage
                        name="svgFile"
                        as="p"
                        class="text-sm text-rose-500 mt-1" />
            </div>

            <div
              class="text-center flex justify-end items-center mt-2">
              <button id="submit" type="submit"
               :disabled="loader"
                class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">
                {{!loader ?'Save Changes':""}}
                <Spinner class="fill-white text-gray-200" v-if="loader" />
              </button>
            </div>
          </div>
        </div>
      </Form>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
/* width */
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}
</style>

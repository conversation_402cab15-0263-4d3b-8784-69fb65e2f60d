<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../common/Spinner.vue';
import formSchema from '../../validationSchema/scene/updateProjectLayerSchema';
import { createIcon } from '../../api/projects/icon';
import { GetHeightWidthFromReadSvgFile } from "../../helpers/svgConversionHelper.ts";
import MasterSchema from '@/validationSchema/scene/updateMasterLayerSchema';
import { isMasterScenePath } from '../../helpers/helpers';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { iconSchema } from '@/validationSchema/scene';

const route = useRoute();
const emits = defineEmits(['close']);
const loader = ref(false);
const categoryList = ref([]);
const iconTypeList = ref([]);
const iconFile = ref();
const isMasterScene = ref(isMasterScenePath(route.fullPath));
const iconPreviewUrl = ref();

categoryList.value = Object.keys( isMasterScene.value ? MasterSchema.type.options : formSchema.type.options).map((elem) => {
  console.log(elem);
  return elem;
});

iconTypeList.value =  Object.keys( isMasterScene.value ? MasterSchema.iconType.options : formSchema.iconType.options).map((element) => {
  return element;
});

const HandleAddIcon = (formData) => {
  createIcon(formData).then((res) => {
    console.log(res, "Icon saved Res !!");
  }).catch(() => {
  }).finally(() => {
    loader.value = false;
    document.dispatchEvent(new Event('refreshIconsList'));
    emits('close');
  });
};

const handleFileChange = (event) => {
  iconFile.value = event.target.files[0];
  if (iconFile.value){
    iconPreviewUrl.value = URL.createObjectURL(iconFile.value);
  }
  console.log('output->svgFile.value', iconFile.value);
};

const  handleFormSubmit = async (values) => {
  loader.value = true;
  let attributesObj ;
  await GetHeightWidthFromReadSvgFile(iconFile.value).then((res) => {
    attributesObj = res;
  });
  const formData = new FormData();
  formData.append('name', values.name);
  formData.append('type', values.type);
  formData.append('category', values.category);
  formData.append('icon', iconFile.value);

  formData.append('iconheight', attributesObj.height);
  formData.append('iconwidth', attributesObj.width);

  HandleAddIcon(formData);
};
</script>

<template>
        <div class="bg-white border border-gray-200 w-[259px] rounded-lg">
           <div class="h-9  flex justify-between items-center px-2.5 border-b border-gray-200 rounded-t-lg">
                    <p class="text-sm font-medium text-gray-900">Add Icons</p>
                    <button  class="fill-gray-400" @click="$emit('close')">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
                          </button>
                </div>
                <Form :validation-schema="iconSchema" @submit="handleFormSubmit" class="px-2.5 py-3 flex flex-col justify-center gap-2 mb-0">

                        <div
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit row-span-2">
                            <label for="icon"
                                class="text-sm font-medium text-gray-900">Icon
                                File</label>
                            <div class="w-full">
                              <div class="w-full rounded-lg relative shadow-md" v-if="iconPreviewUrl">
                                <img class="h-28 w-full  object-fill rounded-lg" :src="iconPreviewUrl" alt="">
                                <div class="h-full w-full absolute left-0 top-0 flex justify-center items-center"><button @click="()=>{iconPreviewUrl=null,iconFile=null}" class="absolute h-10 px-3 rounded-lg bg-white">Change</button></div>

                            </div>
                                    <label v-show="!iconPreviewUrl" class="mb-0 w-full h-28 rounded-lg border-2 border-dashed border-gray-200 py-3 px-1 cursor-pointer">
              <div class="flex justify-center items-center gap-2">
                <svg class="h-5 w-5 fill-gray-500" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_22140)">
<path d="M10.2949 5.736C10.1636 5.60141 9.98561 5.52579 9.8 5.52579C9.61438 5.52579 9.43637 5.60141 9.3051 5.736L7.7 7.38226V1.21795C7.7 1.02754 7.62625 0.844924 7.49497 0.710282C7.3637 0.575641 7.18565 0.5 7 0.5C6.81435 0.5 6.6363 0.575641 6.50503 0.710282C6.37375 0.844924 6.3 1.02754 6.3 1.21795V7.38226L4.6949 5.736C4.63033 5.66743 4.55309 5.61273 4.46768 5.57511C4.38228 5.53748 4.29043 5.51767 4.19748 5.51685C4.10454 5.51602 4.01236 5.53418 3.92633 5.57028C3.8403 5.60638 3.76215 5.65969 3.69642 5.7271C3.6307 5.79451 3.57872 5.87467 3.54352 5.9629C3.50833 6.05114 3.49062 6.14568 3.49142 6.24101C3.49223 6.33633 3.51154 6.43054 3.54823 6.51814C3.58492 6.60573 3.63824 6.68495 3.7051 6.75118L6.5051 9.62297C6.57012 9.68983 6.64737 9.74288 6.73241 9.77907C6.81746 9.81527 6.90863 9.8339 7.0007 9.8339C7.09277 9.8339 7.18394 9.81527 7.26899 9.77907C7.35403 9.74288 7.43128 9.68983 7.4963 9.62297L10.2963 6.75118C10.4273 6.61635 10.5008 6.43367 10.5006 6.24329C10.5003 6.05292 10.4263 5.87045 10.2949 5.736Z"/>
<path d="M12.6 8.75641H10.815L8.7325 10.8923C8.50499 11.1257 8.2349 11.3108 7.93763 11.4371C7.64037 11.5634 7.32176 11.6284 7 11.6284C6.67824 11.6284 6.35963 11.5634 6.06237 11.4371C5.7651 11.3108 5.49501 11.1257 5.2675 10.8923L3.185 8.75641H1.4C1.0287 8.75641 0.672601 8.90769 0.41005 9.17698C0.1475 9.44626 0 9.81148 0 10.1923V13.0641C0 13.4449 0.1475 13.8102 0.41005 14.0794C0.672601 14.3487 1.0287 14.5 1.4 14.5H12.6C12.9713 14.5 13.3274 14.3487 13.5899 14.0794C13.8525 13.8102 14 13.4449 14 13.0641V10.1923C14 9.81148 13.8525 9.44626 13.5899 9.17698C13.3274 8.90769 12.9713 8.75641 12.6 8.75641ZM10.85 13.0641C10.6423 13.0641 10.4393 13.0009 10.2667 12.8826C10.094 12.7643 9.9594 12.5961 9.87993 12.3993C9.80046 12.2025 9.77966 11.986 9.82018 11.7771C9.86069 11.5682 9.96069 11.3763 10.1075 11.2257C10.2544 11.0751 10.4415 10.9725 10.6452 10.9309C10.8488 10.8894 11.06 10.9107 11.2518 10.9922C11.4437 11.0737 11.6077 11.2118 11.723 11.3889C11.8384 11.566 11.9 11.7742 11.9 11.9872C11.9 12.2728 11.7894 12.5467 11.5925 12.7487C11.3955 12.9506 11.1285 13.0641 10.85 13.0641Z"/>
</g>
<defs>
<clipPath id="clip0_723_22140">
<rect width="14" height="14" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
           </svg>
           <p class="text-xs font-medium text-gray-500 mt-2">Upload</p>
</div>
<div>
  <p class="text-xs font-medium text-gray-500 text-center">{{'16:9 Resolution 3840 x 2160px 4k jpeg ( < 10 mb )'}}</p>
  <Field type="file"
  @change="(e)=>handleFileChange(e)"
  v-model="iconFile"
                                    name="icon"
                                    id="icon"
                                    class="hidden" />
</div>
            </label>
                                <ErrorMessage as="p"
                                    class="ml-1 text-xs text-rose-500 mt-1"
                                    name="icon" />
                            </div>
                        </div>

                    <div
                            class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                            <label for="name"
                                class="text-sm font-medium text-gray-900">Name</label>
                            <Field
                                placeholder="Enter Icon Name"
                                 id="name"
                                name="name"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300 placeholder:text-left"/>
                            <ErrorMessage name="name"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>

                    <div
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                            <label for="type"
                                class="text-sm font-medium text-gray-900">Type</label>
                            <Field
                                as="select"
                                 id="type"
                                name="type"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                                <option value=""
                                    class="text-gray-700">
                                    Choose
                                </option>
                                <option value=""
                                    v-if="iconTypeList && iconTypeList.length === 0">
                                    No Data found ! </option>
                                <option v-else
                                    :value="option"
                                    v-for="option, index in  iconTypeList"
                                    :key="index"
                                    class="text-black">
                                    {{ option }} </option>
                            </Field>
                            <ErrorMessage name="type"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>

                    <div
                            class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2">
                            <label for="category"
                                class="text-sm font-medium text-gray-900">Icon Category</label>
                            <Field
                                as="select"
                                 id="category"
                                name="category"
                                class="flex w-full rounded-lg h-8 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300">
                                <option value=""
                                    class="text-gray-700">
                                    Choose
                                </option>
                                <option value=""
                                    v-if="categoryList && categoryList.length === 0">
                                    No Data found ! </option>
                                <option v-else
                                    :value="option"
                                    v-for="option, index in  categoryList"
                                    :key="index"
                                    class="text-black">
                                    {{ option }} </option>
                            </Field>
                            <ErrorMessage name="category"
                                class="ml-1 text-xs text-rose-500 mt-1"
                                as="p" />
                        </div>

                    <div class="">

                        <button type="submit" class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">{{loader?'':'Save'}}
                            <Spinner v-if="loader" class="text-slate-400 fill-white" />
                        </button>
                    </div>
                </Form>

        </div>
</template>

<style scoped>
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}
</style>

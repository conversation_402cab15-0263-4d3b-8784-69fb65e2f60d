<script setup>
import { <PERSON>u, MenuButton, MenuItems } from '@headlessui/vue';
import { ChevronDownIcon } from '@heroicons/vue/20/solid';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../../common/Spinner.vue';
import Modal from '../../common/Modal/Modal.vue';
import { useRouter } from 'vue-router';
import { ProjectStore } from '../../../store/project';
import { getListofUnits, updateUnit } from '../../../api/projects/units';
import { currencyList, unitStatusList } from '../../../helpers/constants';
import { updateUnitSchema } from '@/validationSchema/unit';
import { removeNullProperties } from '@/helpers/helpers';
const projectStore = ProjectStore();
const router = useRouter();

const loader = ref(false);
const unitDetails = ref();

const route = useRoute();
const projectId = ref(route.params.project_id);
const unitId = ref(route.params.unit_id);
const status = ref(); // Status

const metadata = ref([]); // Metadata

const selectedBuildingId = ref(null); // Selected Building Id

const selectedFloor = ref(null); // Floor Id

const measurementType = ref();
const balcony_measurementType = ref();
const suiteAreaType = ref();

/* Methods */

const handleUpdateUnit = (data) => {
  loader.value = true;
  updateUnit(data, unitId.value).then(() => {
    document.dispatchEvent(new Event('refreshUnits'));
    router.go(-1);
  }).catch(() => {
  }).finally(() => {
    loader.value = false;
  });
};

const handleInitialValues = () => {
  metadata.value = unitDetails.value.metadata ?Object.keys(unitDetails.value.metadata).map((key) => ({ key: key, value: unitDetails.value.metadata[key] })):[];
  measurementType.value= unitDetails.value?.measurement_type ? unitDetails.value.measurement_type : 'sqft';
  selectedBuildingId.value = unitDetails.value?.building_id ? unitDetails.value.building_id : null;
  balcony_measurementType.value = unitDetails.value?.balcony_measurement_type ? unitDetails.value.balcony_measurement_type : 'sqft';
  suiteAreaType.value = unitDetails.value?.suite_area_type ? unitDetails.value?.suite_area_type : 'sqft';
  status.value= unitDetails.value.status;
};

const getListOfAllUnits = () => {
  getListofUnits(projectId.value).then((res) => {
    unitDetails.value = removeNullProperties(res[unitId.value]);
    handleInitialValues();
    projectStore.SyncMultipleUnits(unitDetails.value);
  });
};

if (projectStore.units){
  unitDetails.value = removeNullProperties(projectStore.units[unitId.value]);
  handleInitialValues();
} else {
  getListOfAllUnits();
}

const handleForm = (values) => {  // To handle unit addition or updation
  const metadataObj = {};  // To transform metadata array into one object in key and value pair
  metadata.value.forEach((elem) => {
    if (elem.key && elem.value){
      metadataObj[elem.key] = elem.value;
    }
  });
  console.log(selectedFloor.value);

  /* Data */
  const data = {
    project_id: projectId.value,
    ...(values.unitplan_id !== unitDetails.value.unitplan_id && {unitplan_id: values.unitplan_id}),
    ...(values.name !== unitDetails.value.name && {name: values.name}),
    ...(values.status !== unitDetails.value.status && {status: values.status}),
    metadata: metadataObj,
    ...(values.building_id && values.building_id!=="" && values.building_id !== unitDetails.value.building_id && {building_id: values.building_id}),
    ...(values.building_id ?  values.floor_id && {floor_id: values.floor_id}:{floor_id: null}),
    ...(values.tour_id !== unitDetails.value.tour_id && {tour_id: values.tour_id}),
    /* Floor_id */
    ...(values.measurement && values.measurement!==   unitDetails.value?.measurement && { measurement: values.measurement }),
    ...(values.measurement_type && values.measurement_type !== unitDetails.value.measurement_type && { measurement_type: values.measurement_type }),
    ...(values.community_id &&values.building_id!=="" && values.community_id !== unitDetails.value?.community_id && { community_id: values.community_id }),
    ...(values.balcony_measurement && values.balcony_measurement !== unitDetails.value.balcony_measurement &&  { balcony_measurement: values.balcony_measurement }),
    ...(values.balcony_measurement_type && values.balcony_measurement_type !== unitDetails.value.balcony_measurement_type && { balcony_measurement_type: values.balcony_measurement_type }),
    ...(values.suite_area && values.suite_area !== unitDetails.value?.suite_area && { suite_area: values.suite_area }),
    ...(values.suite_area_type && values.suite_area_type !== unitDetails.value.suite_area_type && { suite_area_type: values.suite_area_type }),
    ...(values.cta_link ? values.cta_link !== unitDetails.value.cta_link && {cta_link: values.cta_link}:{cta_link: null}),
    ...(values.price ? values.price !== unitDetails.value.price && { price: values.price } : { price: 0 }),
    ...(values.max_price ? values.max_price !== unitDetails.value.max_price && { max_price: values.max_price } : { max_price: 0 }),
    ...(values.currency ? values.currency !== unitDetails.value.currency && {currency: values.currency.toLowerCase()}:{currency: 'inr'}),
  };

  console.log(data);

  handleUpdateUnit(data);
};

function addPair () { // Adding metadata in key and value pair
  metadata.value.push({});

}

function removePair (index) {
  metadata.value.splice(index, 1);
}

const handleMetadataChange = (e, ind) => {
  metadata.value[ind][e.target.name] = e.target.value;
};

/* Hooks */

projectStore.RefreshBuildings(projectId.value);
projectStore.RefreshCommunities(projectId.value);
projectStore.RefreshUnitplans(projectId.value);
projectStore.RefreshVirtualTours(projectId.value);

</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary">
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">update Unit</h1>
          <p class="modal-subheadinng-primary">
            fill Details to Update Unit.
          </p>
        </div>
        <Form v-if="unitDetails" @submit="handleForm" :initial-values="unitDetails"
        :validation-schema="updateUnitSchema"
         class="flex flex-col justify-center">
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">

            <div class="col-span-auto">
              <label for="unitplan_id" class="label-primary"
                >select Unitplan</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="unitplan_id"
                  id="unitplan_id"
                  autocomplete="unitplan_id"
                  class="select-primary"
                >
                  <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="!projectStore.unitplans">
                    No Type found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in projectStore.unitplans"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.name }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="unitplan_id"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="name" class="label-primary"
                >Unit Name</label
              >
              <div class="mt-2">
                <Field
                  type="text"
                  name="name"
                  id="name"
                  class="input-primary"
                  placeholder="Enter Unit Name"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="name"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="status" class="label-primary"
                >Status</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  v-model="status"
                  type="text"
                  name="status"
                  id="status"
                  autocomplete="status"
                  class="select-primary"
                >
                  <option value="" disabled>Choose</option>
                  <option
                    :value="option"
                    v-for="(option, index) in unitStatusList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="status"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="measurement" class="label-primary">
                Measurement</label
              >
              <div class="mt-2">
                <Field
                  type="text"
                  name="measurement"
                  id="measurement"
                  autocomplete="measurement"
                  class="h-11 input-primary"
                  placeholder="Measurement"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="measurement"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="measurement_type" class="label-primary"
                >Measurement Type</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="measurement_type"
                  v-model="measurementType"
                  id="measurement_type"
                  autocomplete="measurement_type"
                  class="select-primary"
                >
                  <option value="" disabled>Choose</option>
                  <option value="sqft"
                  >
                    sqft
                  </option>
                  <option value="sqmt"
                  >
                    sqmt
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="measurement_type"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="balcony_measurement" class="label-primary">
                Balcony Measurement</label
              >
              <div class="mt-2">
                <Field
                  type="text"
                  name="balcony_measurement"
                  id="balcony_measurement"
                  autocomplete="balcony_measurement"
                  class="h-11 input-primary"
                  placeholder="balcony measurement"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="balcony_measurement"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="balcony_measurement_type" class="label-primary"
                >Balcony Measurement Type</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  v-model="balcony_measurementType"
                  name="balcony_measurement_type"
                  id="balcony_measurement_type"
                  autocomplete="balcony_measurement_type"
                  class="select-primary"
                >
                  <option value="" disabled>Choose</option>
                  <option value="sqft"
                  >
                    sqft
                  </option>
                  <option value="sqmt"
                  >
                    sqmt
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="balcony_measurement_type"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="suite_area" class="label-primary">
                Suite Area</label
              >
              <div class="mt-2">
                <Field
                  type="text"
                  name="suite_area"
                  id="suite_area"
                  autocomplete="suite_area"
                  class="h-11 input-primary"
                  placeholder="Suite Area"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="suite_area"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="suite_area_type" class="label-primary"
                >Suite Area Type</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  v-model="suiteAreaType"
                  name="suite_area_type"
                  id="suite_area_type"
                  autocomplete="suite_area_type"
                  class="select-primary"
                >
                  <option value="" disabled>Choose</option>
                  <option value="sqft"
                  >
                    sqft
                  </option>
                  <option value="sqmt"
                  >
                    sqmt
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="suite_area_type"
                />
              </div>
            </div>

            <Menu
              as="div"
              class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit"
            >
              <label class="label-primary"> Metadata </label>
              <div class="w-full">
                <MenuButton
                  style="border: 1px solid #737373"
                  class="dropdown-btn"
                >
                  Options
                  <ChevronDownIcon
                    class="-mr-1 h-5 w-5 text-gray-400 ml-1.5"
                    aria-hidden="true"
                  />
                </MenuButton>
              </div>

              <transition
                enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95"
                enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75"
                leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95"
              >
                <MenuItems
                  style="width: 300px; background-color: #737373 !important"
                  class="absolute right-0 z-10 mt-2 top-[4.4rem] rounded-md p-1 bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none"
                >
                  <div
                    class="flex items-center gap-1.5 mb-1"
                    v-for="(elem, index) in metadata"
                    :key="index"
                  >
                    <input
                      class="rounded-sm w-6 p-1 text-sm flex-1"
                      type="text"
                      name="key"
                      :value="elem.key"
                      @change="(e) => handleMetadataChange(e, index)"
                      placeholder="key"
                    />
                    <input
                      class="rounded-sm w-7 p-1 text-sm flex-1"
                      type="text"
                      name="value"
                      :value="elem.value"
                      @change="(e) => handleMetadataChange(e, index)"
                      placeholder="value"
                    />
                    <button
                      class="bg-[#1C74D0] text-white rounded-sm text-sm p-1"
                      type="button"
                      @click="removePair(index)"
                    >
                      remove
                    </button>
                  </div>
                  <button
                    class="w-full bg-[#1C74D0] text-white rounded-sm text-sm p-1 mt-0.5"
                    type="button"
                    @click="addPair(event)"
                  >
                    + Add
                  </button>
                </MenuItems>
              </transition>
            </Menu>

            <div class="col-span-auto">
              <label for="building_id" class="label-primary"
                >Building Id</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  v-model="selectedBuildingId"
                  name="building_id"
                  id="building_id"
                  autocomplete="building_id"
                  class="select-primary"
                >
                  <option value="">Choose</option>
                  <option value="" disabled v-if="!projectStore.buildings">
                    No building found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in projectStore.buildings"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.name }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="building_id"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="floor_id" class="label-primary"
                >Floor Id </label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="floor_id"
                  id="floor_id"
                  autocomplete="floor_id"
                  class="select-primary"
                >
                  <option value="">Choose</option>
                  <option value="" disabled v-if="!selectedBuildingId && !projectStore.buildings?.[
                        selectedBuildingId
                      ]?.floors">
                    Select building first
                  </option>
                  <option
                    v-else
                    :value="option.floor_id"
                    v-for="(option, index) in  projectStore.buildings[
                        selectedBuildingId
                      ]?.floors"
                    :key="index"
                    class="text-black"
                  >
                  {{ option.floor_name }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="floor_id"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="community_id" class="label-primary"
                >Communities</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="community_id"
                  id="community_id"
                  autocomplete="community_id"
                  class="select-primary"
                >
                  <option value="">Choose</option>
                  <option value="" disabled v-if="! projectStore.communities">
                    No communities found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in  projectStore.communities"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.name }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="community_id"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="tour_id" class="label-primary"
                >Select virtual tour</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  type="text"
                  name="tour_id"
                  id="tour_id"
                  autocomplete="tour_id"
                  class="select-primary"
                >
                  <option value="">Choose</option>
                  <option value="" disabled v-if="! projectStore.virtualtours">
                    No Tour found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in  projectStore.virtualtours"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.tour_name }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="tour_id"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="price" class="label-primary">
                Price</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  name="price"
                  id="price"
                  autocomplete="price"
                  class="h-11 input-primary"
                  placeholder="Enter price"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="price"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="max_price" class="label-primary">
                Max Price</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  name="max_price"
                  id="max_price"
                  autocomplete="max_price"
                  class="h-11 input-primary"
                  placeholder="Enter Max price"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="max_price"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="currency" class="label-primary"
                >Currency</label
              >
              <div class="mt-2">
                <Field
                  as="select"
                  name="currency"
                  id="currency"
                  autocomplete="currency"
                  class="select-primary"
                >
                  <option value="" disabled>Choose</option>
                  <option
                    :value="option"
                    v-for="(option, index) in currencyList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
                </Field>

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="currency"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="cta_link" class="label-primary"
                >cta Link</label
              >
              <div class="mt-2">
                <Field
                  type="text"
                  name="cta_link"
                  id="cta_link"
                  autocomplete="cta_link"
                  class="input-primary"
                  placeholder="Enter cta link"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="cta_link"
                />
              </div>
            </div>
          </div>

          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="() => router.go(-1)"
              ref="cancelButtonRef"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="proceed-btn-primary"
            >
              Save
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

<style scoped>

</style>

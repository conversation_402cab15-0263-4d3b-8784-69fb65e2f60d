<script setup>
import {  ref, defineProps, defineEmits, watch, onMounted, inject, computed, nextTick } from 'vue';
import { Form, Field} from "vee-validate";
import { useRoute } from 'vue-router';
import { ProjectStore } from '@/store/project';
import { currencyList, unitStatusList } from '@/helpers/constants';
import { createUnitSchema } from '@/validationSchema/unit';
import { CreateUnit, updateUnit } from '@/api/projects/units';

const route = useRoute();
const projectStore = ProjectStore();
const isEdited = ref(false), showForm=ref(true), showLoader = ref(false), rowRef = ref(null);

const { row, index, id, customColumn, metadataKeys} = defineProps({
  row: {type: Object, default: () => ({})},
  metadataKeys: {type: Object, default: () => ({})},
  index: { type: [String, Number], default: ''},
  id: {type: String, default: ''},
  customColumn: {type: Array, default: () => ([])},
});
const initialValues = ref({...row}), selectedBuildingId = ref(initialValues.value.building_id);
const selectedCurrency = ref(initialValues.value.currency?.toUpperCase());
console.log(id);

const emit = defineEmits(['updateData', 'openDeleteModal', 'deleteRow']);

const injectedCustomColumns = inject('customColumnsRef', ref([]));
const effectiveCustomColumns = computed(() => {
  return customColumn?.length ? customColumn : injectedCustomColumns.value;
});

watch(() => effectiveCustomColumns.value, (newColumns) => {
  effectiveCustomColumns.value = newColumns;
}, { deep: true });

const metadataFields = computed(() => {
  const result = {};
  for (const key in row) {
    if (key.startsWith('metadata_')) {
      result[key] = initialValues[key];
    }
  }
  return result;
});

const trackChanges = () => {
  isEdited.value = true;
};

function getObjectDifference (previousObj, newObj) {
  const differences = {};

  for (const key in newObj) {

    const newValue = newObj[key];
    const prevValue = previousObj[key];
    if (key === 'modified'){
      continue;
    }
    // Skip if new value is falsy, but keep empty arrays and objects
    if (!newValue && !Array.isArray(newValue) && !(newValue instanceof Object)) {
      continue;
    }
    // If property is new
    if (!(key in previousObj)) {
      differences[key] = newValue;
      continue;
    }
    // Handle arrays
    if (Array.isArray(newValue)) {
      if (!Array.isArray(prevValue) || JSON.stringify(newValue) !== JSON.stringify(prevValue)) {
        differences[key] = newValue;
      }
      continue;
    }
    // Handle objects
    if (newValue instanceof Object) {
      if (!(prevValue instanceof Object) || JSON.stringify(newValue) !== JSON.stringify(prevValue)) {
        differences[key] = newValue;
      }
      continue;
    }

    // Handle primitive values
    if (prevValue !== newValue) {
      differences[key] = newValue;
    }
  }

  return differences;
}

const handleSubmit = (values) => {
  showLoader.value=true;

  if (initialValues.value.isNew) {
    const metadataObj = {};  // To transform metadata array into one object in key and value pair
    // metadata.value.forEach((elem) => {
    //   if (elem.key && elem.value){
    //     metadataObj[elem.key] = elem.value;
    //   }
    // });
    effectiveCustomColumns.value.forEach((column) => {
      const fieldId = `custom_field_${column.id}`;
      metadataObj[column.name] = values.value[fieldId];
    });

    /* Data */
    const data = {
      project_id: route.params.project_id,
      unitplan_id: values.unitplan_id,
      name: values.name,
      status: values.status,
      metadata: metadataObj,
      building_id: values.building_id,
      tour_id: values.tour_id,
      floor_id: values.floor_id, /* Floor_id */
      ...(values.community_id && { community_id: values.community_id }),
      ...(values.measurement && { measurement: values.measurement }),
      ...(values.measurement && { measurement_type: values.measurement_type }),
      ...(values.balcony_measurement && { balcony_measurement: values.balcony_measurement }),
      ...(values.balcony_measurement && values.balcony_measurementType && { balcony_measurement_type: values.balcony_measurementType }),
      ...(values.cta_link && {cta_link: values.ctaLink}),
      ...(values.max_price && {max_price: values.max_price}),
      ...(values.suite_area && {suite_area: values.suite_area}),
      ...(values.suite_area_type && {suite_area_type: values.suite_area_type}),
      ...(values.bedroom  && {bedroom: values.bedroom}),
      price: values.price ? values.price : 0,
      currency: selectedCurrency.value ? selectedCurrency.value.toLowerCase() : 'inr',
    };

    CreateUnit(data).then(() => {
      showLoader.value=false;
      isEdited.value = false;
      emit('updateData');
      showForm.value=false;
      document.dispatchEvent(new Event('refreshUnits'));
      // router.go(-1);
    }).catch(() => {
    }).finally(() => {
      // loader.value = false;
    });
  } else {
    const newObj = getObjectDifference(row, values);
    const metadataObj = {};
    const filteredObj = {};

    for (const key in newObj) {
      if (key.startsWith('custom_field')) {
        const column = effectiveCustomColumns.value.find((col) => `custom_field_${col.id}` === key);
        if (column && column.name.startsWith('metadata_')) {
          const metadataKey = column.name.replace('metadata_', '');
          metadataObj[metadataKey] = newObj[key];
        } else {
          // Custom field that doesn't start with metadata_ goes to metadata as is
          metadataObj[key] = newObj[key];
        }
      } else if (key.startsWith('metadata_')) {
        // Direct metadata fields
        const metadataKey = key.replace('metadata_', '');
        metadataObj[metadataKey] = newObj[key];
      } else {
        // Convert currency to lowercase if it's being updated
        if (key === 'currency') {
          filteredObj[key] = newObj[key] ? newObj[key].toLowerCase() : 'inr';
        } else {
          // Regular fields go to filteredObj
          filteredObj[key] = newObj[key];
        }
      }
    }

    // Final object structure
    const updatedObj = {
      project_id: route.params.project_id,
      ...filteredObj,
      metadata: JSON.stringify(metadataObj),
    };

    // loader.value = true;
    updateUnit(updatedObj, initialValues.value._id).then(() => {
      showLoader.value=false;
      isEdited.value=false;
      emit('updateData');
      document.dispatchEvent(new Event('refreshUnits'));
    }).catch(() => {
    }).finally(() => {
      // loader.value = false;
    });

  }

};

onMounted(() => {
  projectStore.RefreshUnitplans(route.params.project_id);
  projectStore.RefreshBuildings(route.params.project_id);
  projectStore.RefreshCommunities(route.params.project_id);

  if (row.isNew) {
    nextTick(() => {
      if (rowRef.value) {
        rowRef.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
        const input = rowRef.value.querySelector('input');
        if (input) {
          input.focus();
        }
      }
    });
  }
});

watch(
  () => [row, metadataKeys],
  () => {
    // Ensure metadata is always an object
    if (typeof initialValues.value.metadata === 'string') {
      try {
        initialValues.value.metadata = JSON.parse(initialValues.value.metadata || '{}');
      } catch (e) {
        initialValues.value.metadata = {};
      }
    }
    if (!initialValues.value.metadata) {
      initialValues.value.metadata = {};
    }
    if (Array.isArray(metadataKeys)) {
      metadataKeys.forEach((key) => {
        if (!(key in initialValues.value.metadata)) {
          initialValues.value.metadata[key] = '';
        }
      });
    }
  },
  { immediate: true, deep: true },
);

</script>

<template>
  <Form v-if="showForm" :validation-schema="createUnitSchema" @submit="handleSubmit" v-slot="{ errors , meta }" >
    <div ref="rowRef" class="flex w-fit h-[50px] gap-6 relative " :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-100'">
        <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-16">
            {{index + 1}}
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.unitplan_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
          <Field as="select"  type="text" name="unitplan_id" v-model="initialValues.unitplan_id" id="unitplan_id" autocomplete="unitplan_id"
                  class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.unitplan_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
                  @input="trackChanges">
                  <option value="" disabled v-if="row.isNew">Choose</option>
                  <option value="" disabled v-if="!projectStore.unitplans">
                    No Unitplan found !
                  </option>
                  <option
                    :value="option._id"
                    v-for="(option, index) in projectStore.unitplans"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.name }}
                  </option>
          </Field>
        </div>

        <div class="py-3  text-sm  flex items-center w-32" :class="errors.name ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="w-full " >
                <Field
                  name="name"
                  type="text"
                  v-model="initialValues.name"
                  placeholder="Enter name"
                  class="w-full px-2 py-2 placeholder:text-left   focus:outline-none text-sm leading-[21px]"
                  :class="[errors.name ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400', index % 2 === 0 ? 'bg-white' : '!bg-gray-100']"
                  id="name"
                  @input="trackChanges"
                />
              </div>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.status ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" v-model="initialValues.status" type="text" name="status" id="status" autocomplete="status"
             class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.status ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
             @input="trackChanges">
             <option value="" disabled class="!text-gray-400">Choose</option>
              <option
                    :value="option"
                    v-for="(option, index) in unitStatusList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
              </option>
            </Field>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.price ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="!w-[80%] flex">
                <Field
                  name="price"
                  type="text"
                  v-model="initialValues.price"
                  placeholder="price"
                  class="w-[80%] p-1 placeholder:text-left focus:outline-none text-sm leading-[21px]"
                  :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.price ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']"
                  id="name"
                  @input="trackChanges"
                />
              </div>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.currency ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="currency" v-model="selectedCurrency" id="currency" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.currency ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
            <option value="" disabled>Choose</option>
                  <option
                    :value="option"
                    v-for="(option, index) in currencyList"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                  </option>
            </Field>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.measurement ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="!w-[80%] flex">
                <Field
                  name="measurement"
                  type="text"
                  v-model="initialValues.measurement"
                  placeholder=" measurement"
                  class="w-[80%] p-1 placeholder:text-left focus:outline-none text-sm leading-[21px]"
                  :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.measurement ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']"
                  id="name"
                  @input="trackChanges"
                />
              </div>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.measurement_type ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="measurement_type" v-model="initialValues.measurement_type" id="measurementType" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.measurement_type ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
            <option value="" disabled>Choose</option>
                  <option value="sqft"
                  >
                    sqft
                  </option>
                  <option value="sqmt"
                  >
                    sqmt
                  </option>
            </Field>
        </div>

        <div class="py-3 text-sm  flex items-center w-[190px]" :class="errors.balcony_measurement ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="!w-[80%] flex">
                <Field
                  name="balcony_measurement"
                  type="text"
                  v-model="initialValues.balcony_measurement"
                  placeholder="Measurements"
                  class="w-[80%] p-1 placeholder:text-left focus:outline-none text-sm leading-[21px]"
                  :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.balcony_measurement ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']"
                  id="balcony_measurement"
                  @input="trackChanges"
                />
              </div>
        </div>

        <div class="py-3 text-sm  flex items-center w-[215px]" :class="errors.balcony_measurement_type ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="balcony_measurement_type" v-model="initialValues.balcony_measurement_type" id="balcony_measurement_type" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.balcony_measurement_type ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
            <option value="" disabled>Choose</option>
                  <option value="sqft"
                  >
                    sqft
                  </option>
                  <option value="sqmt"
                  >
                    sqmt
                  </option>
            </Field>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.building_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="building_id"  v-model="initialValues.building_id" id="building_id" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.building_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
                  <option value="">Choose</option>
                  <option value="" disabled v-if="!projectStore.buildings">
                    No building found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in projectStore.buildings"
                    :key="index"
                    class="text-black"
                  >
                  {{ option.name }}
                </option>
              </Field>
        </div>

        <!-- floor id -->
        <div class="py-3 text-sm  flex items-center w-32" :class="errors.floor_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="floor_id" v-model="initialValues.floor_id" id="floor_id" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.floor_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
           <option value="">Choose</option>
                  <option value="" disabled v-if="!selectedBuildingId && !projectStore.buildings?.[initialValues.building_id]?.floors">
                    Select building first
                  </option>
                  <option
                    v-else
                    :value="option.floor_id"
                    v-for=" (option, index) in  projectStore?.buildings?.[initialValues.building_id]?.floors"
                    :key="index"
                    class="text-black"
                  >
                  {{ option.floor_name }}
                  </option>
            </Field>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.community_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="community_id" v-model="initialValues.community_id" id="community_id" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.community_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
            <option value="">Choose</option>
                  <option value="" disabled v-if="!projectStore.communities">
                    No communities found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in  projectStore.communities"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.name }}
                  </option>
            </Field>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.tour_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="tour_id" v-model="initialValues.tour_id" id="tour_id" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.tour_id ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
            <option value="">Choose</option>
                  <option value="" disabled v-if="! projectStore.virtualtours">
                    No Tour found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in  projectStore.virtualtours"
                    :key="index"
                    class="!text-black"
                  >
                    {{ option.name ||  option.tour_name}}
                  </option>
            </Field>
        </div>

        <div class="py-3 text-sm  flex items-center w-32" :class="errors.cta_link ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="!w-[80%] flex">
                <Field
                  name="cta_link"
                  type="text"
                  v-model="initialValues.cta_link"
                  placeholder="cta link"
                  class="w-[80%] p-1 placeholder:text-left focus:outline-none text-sm leading-[21px]"
                  :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.cta_link ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']"
                  id="name"
                  @input="trackChanges"
                />
              </div>
        </div>

         <div class="py-3  text-sm  flex items-center w-32" :class="errors.max_price ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="w-full " >
                <Field
                  name="max_price"
                  type="number"
                  v-model="initialValues.max_price"
                  placeholder="Enter price"
                  class="w-full px-2 py-2 placeholder:text-left   focus:outline-none text-sm leading-[21px]"
                  :class="[errors.max_price ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400', index % 2 === 0 ? 'bg-white' : '!bg-gray-100']"
                  id="name"
                  @input="trackChanges"
                />
              </div>
        </div>

         <div class="py-3  text-sm  flex items-center w-32" :class="errors.suite_area ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
              <div class="w-full " >
                <Field
                  name="suite_area"
                  type="text"
                  v-model="initialValues.suite_area"
                  placeholder="Enter area"
                  class="w-full px-2 py-2 placeholder:text-left   focus:outline-none text-sm leading-[21px]"
                  :class="[errors.suite_area ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400', index % 2 === 0 ? 'bg-white' : '!bg-gray-100']"
                  id="name"
                  @input="trackChanges"
                />
              </div>
        </div>

        <div class="py-3 text-sm  flex items-center w-[220px]" :class="errors.suite_area_type ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
            <Field as="select" type="text" name="suite_area_type" v-model="initialValues.suite_area_type" id="balcony_measurement_type" autocomplete="type"
            class="select-primary border-none" :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.suite_area_type ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black !placeholder-gray-400']"
            @input="trackChanges">
            <option value="" disabled>Choose</option>
                  <option value="sqft"
                  >
                    sqft
                  </option>
                  <option value="sqmt"
                  >
                    sqmt
                  </option>
            </Field>
        </div>

        <!-- Always show all metadata fields, even if value is missing -->
        <div v-for="key in metadataKeys" :key="key" class="py-3 text-sm flex items-center w-32">
          <div class="w-full">
            <Field
              :name="`metadata_${key}`"
              type="text"
              v-model="initialValues.metadata[key]"
              :placeholder="`Enter ${key}`"
              class="w-full px-2 py-2 placeholder:text-left focus:outline-none text-sm leading-[21px]"
              :class="[
                errors[`metadata_${key}`] ? '!bg-red-50 !text-red-700 placeholder-red-700' : 'bg-white text-black placeholder-gray-400',
                index % 2 === 0 ? 'bg-white' : '!bg-gray-100'
              ]"
              :id="`metadata_${key}`"
              @input="trackChanges"
            />
          </div>
        </div>

        <!-- metadata fields for the new units   -->
        <div  v-for="(value, key) in metadataFields" :key="key" class="py-3 text-sm flex items-center w-32">
          <div class="w-full">
            <Field
              :name="key"
              type="text"
              v-model="initialValues[key]"
              :placeholder="`Enter ${key.replace('metadata_', '')}`"
              class="w-full px-2 py-2 placeholder:text-left focus:outline-none text-sm leading-[21px]"
              :class="[
                errors[key] ? '!bg-red-50 !text-red-700 placeholder-red-700' : 'bg-white text-black placeholder-gray-400',
                index % 2 === 0 ? 'bg-white' : '!bg-gray-100'
              ]"
              :id="key"
              @input="trackChanges"
            />
          </div>
        </div>

        <!-- custom columns  -->
        <div v-for="(item, ind) in effectiveCustomColumns" :key="ind" class="py-3 text-sm flex items-center w-32" :class="errors.cta_link ? '!bg-red-50 !text-red-700 placeholder-red-700' : ''">
          <div class="!w-[80%] flex">
              <Field
                  :name="`custom_field_${item.id}`"
                  type="text"
                  placeholder='Enter Value'
                  class="w-[80%] p-1 placeholder:text-left focus:outline-none text-sm leading-[21px]"
                  :class="[index % 2 === 0 ? 'bg-white' : '!bg-gray-100', errors.cta_link ? '!bg-red-50 !text-red-700 placeholder-red-700' : ' bg-white text-black placeholder-gray-400']"
                  :id="`field_${item.id}`"
                  value=""
                  @input="trackChanges"
              />
          </div>
        </div>

        <div class="bg-white text-sm flex gap-4 items-center justify-end w-32 sticky right-0" :class="index % 2 === 0 ? 'bg-white' : '!bg-gray-100'" >
            <div v-if="row.isNew ? meta.dirty : isEdited" class="text-gray-500 text-sm  flex items-center">
                  <button  type="submit"
                      class="py-2 px-3 text-sm font-medium text-white bg-blue-700 rounded-lg flex items-center gap-2">
                      <div v-if="showLoader" class="loader !h-5 !w-5 !border-2"></div>
                      Save
                  </button>
              </div>

              <div class="text-gray-500 text-sm  flex items-center pr-3">
                <button  v-if="initialValues.isNew" type="button" @click="emit('deleteRow',initialValues._id)" class="text-red-600">
                      <div class="w-8 h-8 flex justify-center items-center bg-gray-100 rounded-lg">
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 6h18"/><path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/><path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>
                      </div>
                  </button>
                  <button  v-else type="button" @click="emit('openDeleteModal',initialValues._id ,index)" class="text-red-600">
                      <div class="w-8 h-8 flex justify-center items-center bg-gray-100 rounded-lg">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_1362_13369)">
                            <path d="M15.1111 0.666992H0.888889C0.397969 0.666992 0 1.05326 0 1.52974L0 3.25523C0 3.73171 0.397969 4.11797 0.888889 4.11797H15.1111C15.602 4.11797 16 3.73171 16 3.25523V1.52974C16 1.05326 15.602 0.666992 15.1111 0.666992Z" fill="#6B7280"/>
                            <path d="M0.888889 5.84346V13.6082C0.888889 14.0658 1.07619 14.5047 1.40959 14.8283C1.74299 15.1519 2.19517 15.3337 2.66667 15.3337H13.3333C13.8048 15.3337 14.257 15.1519 14.5904 14.8283C14.9238 14.5047 15.1111 14.0658 15.1111 13.6082V5.84346H0.888889ZM10.6667 8.4317C10.6667 8.66051 10.573 8.87995 10.4063 9.04175C10.2396 9.20355 10.0135 9.29444 9.77778 9.29444H6.22222C5.98647 9.29444 5.76038 9.20355 5.59368 9.04175C5.42698 8.87995 5.33333 8.66051 5.33333 8.4317V7.56895C5.33333 7.34014 5.42698 7.1207 5.59368 6.9589C5.76038 6.7971 5.98647 6.70621 6.22222 6.70621C6.45797 6.70621 6.68406 6.7971 6.85076 6.9589C7.01746 7.1207 7.11111 7.34014 7.11111 7.56895H8.88889C8.88889 7.34014 8.98254 7.1207 9.14924 6.9589C9.31594 6.7971 9.54203 6.70621 9.77778 6.70621C10.0135 6.70621 10.2396 6.7971 10.4063 6.9589C10.573 7.1207 10.6667 7.34014 10.6667 7.56895V8.4317Z" fill="#6B7280"/>
                          </g>
                          <defs>
                            <clipPath id="clip0_1362_13369">
                              <rect width="16" height="16" fill="white"/>
                            </clipPath>
                          </defs>
                        </svg>
                    </div>
                  </button>
              </div>
        </div>
    </div>
  </Form>
</template>

<style scoped>
.loader{
  @apply w-20 h-20 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-8 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}
</style>

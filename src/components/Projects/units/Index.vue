<script setup>
import { <PERSON>u, <PERSON>uButton, MenuItem, MenuItems } from '@headlessui/vue';
import { onMounted, ref, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Modal from '../../common/Modal/Modal.vue';
import DeleteUnit from '../../common/ModalContent/DeleteModalContent.vue';
import ErrorModalContent from '../../common/ModalContent/ErrorModalContent.vue';
import Papa from 'papaparse';
import { ProjectStore } from '../../../store/project';
import { moveUnitToTrash, getListofUnits, updateUnit, AddBulkUnits } from '../../../api/projects/units';
import Button from '../../common/Button.vue';
import NotfoundImage from '../../common/NotfoundImage.vue';
import { GetAllTrash, RestoreTrash } from '@/api/trash';
import Spinner from '@/components/common/Spinner.vue';
import Pagination from '../../Pagination.vue';

const router = useRouter();

const route = useRoute();
const projectStore = ProjectStore();

const importModal = ref(false);
const importData = ref(null);
const importValidatedData = ref([]);
const newUnits = ref(null);
const existingUnits = ref(null);
const errorUnits = ref([]);
const projectId = ref(route.params.project_id);
const openDeleteUnitModal = ref(false);
const unitIdToDelete = ref();
const openErrorModal = ref(false);
const loader = ref([]);
const units = ref();
const showTrash = ref(false), trashData = ref([]), load = ref(null), searchQuery = ref('');
const currentPage = ref(1);
const totalPages = ref(1);

const getListOfAllUnits = () => {
  getListofUnits(projectId.value).then((res) => {
    units.value = res;
    projectStore.SyncMultipleUnits(res);
  });
};

onMounted(() => {

  getListOfAllUnits();
  projectStore.RefreshUnitplans(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
  projectStore.RefreshCommunities(projectId.value);

});

document.addEventListener('refreshUnits', () => {  // To refresh ref after adding, updating or deleting unit
  getListOfAllUnits();
});

async function fetchTrashData () {
  await GetAllTrash(projectId.value, 'units', currentPage.value, 5).then((res) => {
    trashData.value = res.items;
    totalPages.value = res.total;
  });
}
fetchTrashData();

const handleMoveToTrash = () => {
  const obj = {
    unit_id: [unitIdToDelete.value],
    timeStamp: Date.now(),
  };
  moveUnitToTrash(obj, projectId.value).then(() => {
    getListOfAllUnits();
    fetchTrashData();
    openDeleteUnitModal.value = false;
    unitIdToDelete.value = null;
  }).catch((err) => {
    console.log('output->', err);
  });
};
function processFile (event){
  const file = event.target.files[0];
  Papa.parse(file, {
    header: true, // Set to false if your data does not have a header
    complete: (result) => {
      importModal.value = true;
      importData.value = result;
      // Separate arrays for modified units and units with undefined keys
      importData.value.data.forEach((unit) => {
        // Find the organization key where name matches the unit's unitplan_id
        const unitplanKey = Object.keys(projectStore.unitplans).find((key) => projectStore.unitplans[key]._id === unit.unitplan_id);
        const buildingKey = Object.keys(projectStore.buildings).find((key) => projectStore.buildings[key]._id === unit.building_id);
        const balcony_measurement_type = !unit.balcony_measurement_type ? "sqft" : unit.balcony_measurement_type;
        const suite_area_type = !unit.suite_area_type ? "sqft" : unit.suite_area_type;
        const floorKey = Object.keys(projectStore.buildings).find((key) => {
          const building = projectStore.buildings[key];
          // Find the floor in the building that matches
          return Object.values(building.floors).some((floor) => {
            return floor.floor_id === unit.floor_id;
          });
        });

        const metadata = {};

        for (const key in unit) {
          if (key.startsWith('metadata_')) {
            const newKey = key.replace('metadata_', '');
            metadata[newKey] = unit[key];
          }
        }

        // If both unitplanKey and buildingKey are defined, modify the unit
        if (unitplanKey !== undefined && buildingKey !== undefined && floorKey !== undefined) {
          importValidatedData.value.push({
            ...unit,
            building_id: buildingKey,
            unitplan_id: unitplanKey,
            floor_id: unit.floor_id,
            balcony_measurement_type: balcony_measurement_type,
            suite_area_measurement_type: suite_area_type,
            metadata: metadata,
          });
          console.log("importValidatedData.value - ", importValidatedData.value);
        } else {
          // If either unitplanKey or buildingKey is undefined, push the entire unit to the undefinedKeysUnits array
          errorUnits.value.push(unit);
        }
      });
      newUnits.value = importValidatedData.value.filter((unit) => {
        return unit._id.length < 1;
      });

      existingUnits.value = importValidatedData.value.filter((unit) => {
        console.log(unit._id.length); return unit._id.length > 1;
      });
    },
  });
}

function RenderLabel (param, _id) {

  switch (param) {
    case 'unitplan_id':
      return projectStore.unitplans[_id] ? projectStore.unitplans[_id].name : _id;
    case 'building_id':
      return projectStore.buildings[_id] ? projectStore.buildings[_id].name : _id;
    case 'metadata':
      return null;
    default:
      return _id;
  }
}

// const AddBulkUnits= (payload) => {
//   return new Promise((outerResolve, outerReject) => {

//     Promise.all(Object.values(payload).map(async (data) => {
//       await CreateUnit(data);
//     })).then(() => {
//       projectStore.ForceRefreshUnits(projectId.value);
//       outerResolve();

//     }).catch((err) => {
//       outerReject();
//     });
//   });

// };

const UpdateBulkUnits =(payload) => {
  return new Promise((outerResolve, outerReject) => {
    Promise.all(Object.values(payload).map(async (data) => {
      console.log(data);
      await updateUnit(data, data._id);
    })).then(() => {
      projectStore.ForceRefreshUnits(projectId.value);
      outerResolve();
    }).catch(() => {
      outerReject();
    });
  });

};

const chunkArray = (array) => {
  const chunks = [];
  const chunkSize = 500;  // /maximum units to be sended to Backend (500)
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};
function CreateBulkUnits () {
  if (Object.keys(newUnits.value).length >= 500){                        // /maximum units to be sended to Backend (500)
    const chunksArray =  chunkArray(newUnits.value);
    Promise.all(chunksArray.map((batch) => AddBulkUnits(batch))).then(() => {
      console.log("Batch uploaded successfully");
    }).catch((err) => {
      console.log('Error creating Units', err);
    }).finally(() => {
      importModal.value = false;
      projectStore.ForceRefreshUnits(projectId.value);
    });
  } else {
    AddBulkUnits(newUnits.value).then(() => {
      importModal.value = false;
    }).then(() => {
      projectStore.ForceRefreshUnits(projectId.value);
    }).catch((err) => {
      console.log('Error creating Units', err);
    });
  }
}

function handleUpdateBulkUnits () {
  UpdateBulkUnits(existingUnits.value).then(() => {
    importModal.value = false;
  });
}

function objectToCsv (data) {
  const csvData = [];

  const title = ['_id', 'building_id', 'community_id', 'currency', 'floor_id', 'name', 'price', 'max_price', 'measurement', 'measurement_type', 'balcony_measurement', 'balcony_measurement_type', 'suite_area', 'suite_area_type',  'project_id', 'status', 'unitplan_id', 'tour_id', 'cta_link'];
  csvData.push(title);

  console.log("****", data);
  for (const key in data) {
    const entry = data[key];
    const csvEntry = [];
    const { ...cleanedEntry } = entry;
    console.log(entry);

    csvEntry.push(cleanedEntry._id);
    csvEntry.push(cleanedEntry.building_id);
    csvEntry.push(cleanedEntry.community_id);
    csvEntry.push(cleanedEntry.currency);
    csvEntry.push(cleanedEntry.floor_id);
    csvEntry.push(cleanedEntry.name);
    csvEntry.push(cleanedEntry.price);
    csvEntry.push(cleanedEntry.max_price);
    csvEntry.push(cleanedEntry.measurement);
    csvEntry.push(cleanedEntry.measurement_type);
    csvEntry.push(cleanedEntry.balcony_measurement);
    csvEntry.push(cleanedEntry.balcony_measurement_type);
    csvEntry.push(cleanedEntry.suite_area);
    csvEntry.push(cleanedEntry.suite_area_type);
    csvEntry.push(cleanedEntry.project_id);
    csvEntry.push(cleanedEntry.status);
    csvEntry.push(cleanedEntry.unitplan_id);
    csvEntry.push(cleanedEntry.tour_id);
    if (cleanedEntry.metadata!==undefined){
      Object.keys(cleanedEntry.metadata).forEach((metaKey) => {
        if (!title.includes('metadata_'+metaKey)){
          title.push('metadata_'+metaKey);
        }
        csvEntry.push(cleanedEntry.metadata[metaKey]);
      });
    }
    csvEntry.push(cleanedEntry.cta_link);

    // If(Array.isArray(cleanedEntry)){
    //     CsvEntry.push(cleanedEntry.unitplan_id.join(','));
    // } else {
    //     CsvEntry.push(cleanedEntry.unitplan_id);
    // }

    // For (let field in entry) {
    //   If (typeof entry[field] === 'object' && entry[field] !== null) {
    //     // If the field is an object, stringify it

    //     CsvEntry.push(JSON.stringify(entry[field]));
    //   } else if (Array.isArray(entry[field])) {
    //     // If the field is an array, join its elements
    //     CsvEntry.push(entry[field].join(','));
    //   } else {
    //     // Otherwise, directly push the value
    //     CsvEntry.push(entry[field]);
    //   }
    // }

    console.log(csvEntry);
    csvData.push(csvEntry);
  }

  return csvData;
}

function exportCsv () {
  console.log("**", projectStore.units);
  const csvData = objectToCsv(projectStore.units);
  console.log(csvData);
  const csv = Papa.unparse(csvData);

  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: 'text/csv' });

  // Create a URL for the Blob
  const url = window.URL.createObjectURL(blob);

  // Create a link element and trigger the download
  const a = document.createElement('a');
  a.href = url;
  a.download = 'data.csv';
  document.body.appendChild(a);
  a.click();

  // Clean up
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

async function restoreFunc (item) {
  const payload = {
    trash_id: item._id,
  };
  load.value = item._id;
  try {
    await RestoreTrash(payload, projectId.value, 'unit', 'restoreUnit');
    await fetchTrashData();
    if (Object.keys(trashData.value).length === 0 && currentPage.value > 1) {
      currentPage.value = currentPage.value - 1;
      await fetchTrashData();
    }
    getListOfAllUnits();
  } catch (error) {
    console.error("Error during restore or fetching trash:", error);
  }
}

const filteredTrashData = computed(() => {
  if (!searchQuery.value) {
    return trashData.value;
  }
  return Object.entries(trashData.value).reduce((result, [key, item]) => {
    const filteredUnitPlans = Object.entries(item.data).filter(([_, unitplan]) => {
      console.log(_);

      const queryLower = searchQuery.value.toLowerCase();
      return (
        (unitplan.name && unitplan.name.toLowerCase().includes(queryLower)) ||
                (unitplan.tour_name && unitplan.tour_name.toLowerCase().includes(queryLower))
      );
    });
    if (filteredUnitPlans.length > 0) {
      result[key] = { ...item, data: Object.fromEntries(filteredUnitPlans) };
    }
    return result;
  }, {});
});

const totalPagesProps = computed(() => {
  return Math.ceil(totalPages.value / 5);
});

const updateCurrentPage = (page) => {
  currentPage.value = page;
  fetchTrashData();
};

watch(() => trashData.value, (newVal) => {
  trashData.value = newVal;
});
watch(() => currentPage.value, (newVal) => {
  currentPage.value = newVal;
});

</script>
<template>
  <div class="relative bg-transparent pb-5">
        <!-- Header -->
        <div class="mb-6">
                           <!-- Title -->
                           <div class="mb-4 px-8 pt-6 flex justify-between">
                                  <div class="flex flex-col justify-start items-start gap-3">
                                            <h3 class="text-txt-50 dark:text-txt-1000 text text-2xl font-semibold"> Units </h3>
                                            <!-- <p class="text-gray-600 dark:text-txt-650 text-base font-normal mb-0"> Lorem, ipsum dolor sit amet consectetur adipisicing elit. </p> -->
                                            <input type="file" name="importcsv" class="input-primary"
                                        @change="processFile" />
                                  </div>
                                  <div class="flex gap-6 items-center">
                                    <div>
                                        <Button title="Export CSV" theme="primary"  @click="exportCsv">
                                            <template v-slot:svg>
                                                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <g clip-path="url(#clip0_306_21007)">
                                                        <path class="fill-txt-1000 dark:fill-txt-default" d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z"/>
                                                        <path class="fill-txt-1000 dark:fill-txt-default" d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z"/>
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_306_21007">
                                                            <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)"/>
                                                        </clipPath>
                                                    </defs>
                                                </svg>
                                            </template>
                                        </Button>
                                    </div>
                                    <div>
                                        <Button title="Create new Unit" theme="primary" @click="router.push(`/projects/${projectId}/units/create`)">
                                            <template v-slot:svg>
                                                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <g clip-path="url(#clip0_306_21007)">
                                                        <path class="fill-txt-1000 dark:fill-txt-default" d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z"/>
                                                        <path class="fill-txt-1000 dark:fill-txt-default" d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z"/>
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_306_21007">
                                                            <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)"/>
                                                        </clipPath>
                                                    </defs>
                                                </svg>
                                            </template>
                                        </Button>
                                    </div>
                                    <div>
                                      <Button title="Trashed Units" theme="primary" @click="showTrash = !showTrash"></Button>
                                    </div>
                                  </div>
                           </div>
                           <!-- <BreadCrumb/> -->
        </div>
        <div v-if="units && Object.keys(units).length !== 0" class="h-fit px-8">
                <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">name</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">unitplan</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">status</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">building</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">floor</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">price</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">max price</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">currency</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">measurement</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">measurement_type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize"> balcony measurement</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize"> balcony measurement Type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">Suite Area</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">Suite Area measurement type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">community</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">tour_id</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">cta link</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="unit, index in units"
                                :key="index" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                              <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                {{ unit.name }}
                              </td>
                              <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                                {{
                                 projectStore.unitplans?.[unit.unitplan_id]?.name
                                 }}
                                </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.status ?? '' }}
                                </td>
                                <td  class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.building_id? projectStore.buildings?.[unit.building_id]?.name:"-" }}
                                </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.name }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.max_price ? unit.max_price : "-" }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.currency }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.measurement? unit.measurement : "-" }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.measurement_type? unit.measurement_type : "-" }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.balcony_measurement? unit.balcony_measurement : "-" }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.balcony_measurement_type? unit.balcony_measurement_type : "-" }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.suite_area ? unit.suite_area : "-" }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{ unit.suite_area_type? unit.suite_area_type : "-" }}
                                </td>
                                 <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  {{
                                  projectStore.unitplans?.[unit.unitplan_id]?.name
                                  }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.status ?? '' }}
                                  </td>
                                  <td  class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.building_id? projectStore.buildings?.[unit.building_id]?.name:"-" }}
                                  </td>

                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{
                                      unit.building_id?projectStore.buildings?.[unit.building_id]?.floors?.[unit.floor_id]?.floor_name :"-"
                                    }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.price }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.max_price ? unit.max_price : "-" }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.currency }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.measurement? unit.measurement : "-" }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.measurement_type? unit.measurement_type : "-" }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.balcony_measurement? unit.balcony_measurement : "-" }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{ unit.balcony_measurement_type? unit.balcony_measurement_type : "-" }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{
                                    unit.community_id ?projectStore.communities?.[unit.community_id]?.name :"-"
                                    }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{
                                      unit.tour_id ? unit.tour_id :"-"
                                    }}
                                  </td>
                                  <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    {{
                                      unit.cta_link? unit.cta_link :"-"
                                    }}
                                  </td>
                                  <td class="p-3 flex justify-center">
                                    <Menu as="div"
                            class="relative whitespace-nowrap flex justify-center items-center">
                            <div>
                              <MenuButton as="div"
                                class="inline-flex w-full mr-1.5 rounded-md bg-inherit py-0 text-xs text ring-gray-300 cursor-pointer">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  viewBox="0 0 24 24"
                                  fill="currentColor"
                                  class="w-6 h-6 fill-black">
                                  <path fillRule="evenodd"
                                    d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"
                                    clipRule="evenodd" />
                                </svg>
                              </MenuButton>
                            </div>

                            <transition
                              enter-active-class="transition ease-out duration-100"
                              enter-from-class="transform opacity-0 scale-95"
                              enter-to-class="transform opacity-100 scale-100"
                              leave-active-class="transition ease-in duration-75"
                              leave-from-class="transform opacity-100 scale-100"
                              leave-to-class="transform opacity-0 scale-95">
                              <MenuItems
                                class="absolute -top-2 right-6 z-80 mt-2 w-fit origin-top-right rounded-md bg-neutral-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                <div class="py-2 flex flex-col">
                                  <MenuItem>
                                  <p
                                    @click="router.push(`/projects/${projectId}/units/${ unit._id}/edit`)"
                                    class="text-gray-300 block px-3 py-1 text-xs hover:text-white cursor-pointer">Edit
                                    Unit</p>
                                  </MenuItem>
                                  <MenuItem>
                                  <a href="#"
                                    @click="() => { openDeleteUnitModal = !openDeleteUnitModal; unitIdToDelete = unit._id }"
                                    class="text-gray-300 block px-3 py-1 text-xs hover:text-white">Delete
                                    Unit</a>
                                  </MenuItem>
                                </div>
                              </MenuItems>
                            </transition>
                          </Menu>
                                  </td>
                              </tr>
                          </tbody>
                      </table>
                  </div>
          </div>
          <div v-else class="w-full">
                  <div class="w-fit m-auto">
                      <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                          <rect width="300" height="286" fill="url(#pattern0)"/>
                          <NotfoundImage/>
                      </svg>
                  </div>
                  <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">No Units Available</div>
                  <Button class="mt-8 mx-auto" title="Create new Unit" @click="router.push(`/projects/${projectId}/units/create`)" theme="primary">
                          <template v-slot:svg>
                              <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <g clip-path="url(#clip0_306_21007)">
                                      <path class="fill-txt-1000 dark:fill-txt-default" d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z"/>
                                      <path class="fill-txt-1000 dark:fill-txt-default" d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z"/>
                                  </g>
                                  <defs>
                                      <clipPath id="clip0_306_21007">
                                          <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)"/>
                                      </clipPath>
                                  </defs>
                              </svg>
                          </template>
                  </Button>
              </div>
        </div>
        <div class="mt-3 text-black px-8" v-if="showTrash">
          <span class="flex pb-4 font-bold">Trashed Units</span>
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search"
            class="border px-2 py-1 mb-3 rounded text-black"
          />
          <div v-if="filteredTrashData && Object.keys(filteredTrashData).length !== 0" class="h-fit">
            <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
              <table class="w-full rounded-lg bg-transparent">
                <thead>
                  <tr class="bg-gray-50 dark:bg-bg-150">
                    <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">name</th>
                    <th class="p-3 text-left text-sm font-semibold text-gray-900 capitalize">status</th>
                  </tr>
                </thead>
                <tbody>
                  <template v-for="(item, index) in filteredTrashData" :key="index">
                    <tr v-for="unit, itemIndex in item.data" :key="itemIndex" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                      <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                        {{ unit.name }}
                      </td>
                      <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                        {{ unit.status ?? '' }}
                      </td>
                      <td class="flex items-center gap-2 p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap cursor-pointer" @click="restoreFunc(item)">
                        Restore
                        <Spinner v-if="load === item._id"/>
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
            <Pagination v-if="totalPagesProps > 1" :totalPages="totalPagesProps" :currentPage="currentPage" @currentPageSync="updateCurrentPage"/>
          </div>
          <div v-else class="w-full">
            <div class="w-fit m-auto">
              <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <rect width="300" height="286" fill="url(#pattern0)"/>
                <NotfoundImage/>
              </svg>
            </div>
            <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">No Trashed Units Available</div>
          </div>
        </div>
    <Modal :open="openDeleteUnitModal">
      <DeleteUnit
        :trash="true"
        @closeModal="(e) => openDeleteUnitModal = false"
        @handleDelete="handleMoveToTrash" :dataName="'Unit'" />
    </Modal>
    <Modal :open="openErrorModal">
      <ErrorModalContent
        @closeModal="(e) => openErrorModal = false"
        :dataName="'Error'" :loader="loader" />
    </Modal>

    <div class="relative z-50" aria-labelledby="modal-title"
    role="dialog" aria-modal="true" v-if="importModal">
    <div
      class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity">
    </div>
    <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
      <div
        class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div
          class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:p-6 ">
          <div
            class="sm:flex sm:items-start max-h-screen overflow-auto">
            <div class="w-full px-4 sm:px-6 lg:px-8 py-2">
              <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                  <h1
                    class="text-base font-semibold leading-6 text-gray-900">
                    New Units</h1>
                </div>
              </div>
              <div class="mt-1 flow-root">

                <div class="h-fit">
                <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th v-for="field, fieldIndex in importData.meta.fields" :key="fieldIndex"
                                 class="p-3 text-left text-sm font-semibold text-gray-900">{{ field }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="data, dataIndex in newUnits"
                          :key="dataIndex" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                              <td v-for="datafield, datafieldIndex in data"
                            :key="datafieldIndex"
                             class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                             {{ RenderLabel(datafieldIndex,
                              datafield) }}
                              </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
        </div>
                <div
                  class="mt-1 sm:ml-10 sm:mt-4 sm:flex justify-end gap-3">
                  <button type="button"
                    class="proceed-btn-primary"
                    @click="CreateBulkUnits">Create
                    Units</button>
                  <button type="button"
                    class="cancel-btn-primary">Cancel</button>
                </div>
              </div>
            </div>
          </div>
          <div
            class="sm:flex sm:items-start max-h-screen overflow-auto">
            <div class="w-full px-4 sm:px-6 lg:px-8 py-2 border-t-[1px] border-gray-40s0">
              <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                  <h1
                    class="text-base font-semibold leading-6 text-gray-900">
                    Existing Units</h1>
                </div>
              </div>
              <div class="mt-1 flow-root">

                <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th v-for="field, fieldIndex in importData.meta.fields" :key="fieldIndex"
                                 class="p-3 text-left text-sm font-semibold text-gray-900">{{ field }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="data, dataIndex in existingUnits"
                          :key="dataIndex" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                              <td v-for="datafield, datafieldIndex in data"
                            :key="datafieldIndex"
                             class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                             {{ RenderLabel(datafieldIndex,
                              datafield) }}
                              </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div
                  class="mt-1 sm:ml-10 sm:mt-4 sm:flex justify-end gap-3">
                  <button type="button"
                    class="proceed-btn-primary"
                    @click="handleUpdateBulkUnits">Update
                    units</button>
                  <button type="button"
                    class="cancel-btn-primary">Cancel</button>
                </div>
              </div>
            </div>
          </div>
          <div
            class="sm:flex sm:items-start max-h-screen overflow-auto">
            <div class="w-full px-4 sm:px-6 lg:px-8 py-2 border-t-[1px] border-gray-400">
              <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                  <h1
                    class="text-base font-semibold leading-6 text-gray-900">
                    Error Units</h1>
                </div>
              </div>
              <div class="mt-1 flow-root">

                <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th v-for="field, fieldIndex in importData.meta.fields" :key="fieldIndex"
                                 class="p-3 text-left text-sm font-semibold text-gray-900">{{ field }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="data, dataIndex in errorUnits"
                          :key="dataIndex" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                              <td v-for="datafield, datafieldIndex in data"
                            :key="datafieldIndex"
                             class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                             {{ RenderLabel(datafieldIndex,
                              datafield) }}
                              </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
              </div>
              <div class="mt-1 sm:ml-10 sm:mt-4 sm:flex justify-end gap-3">
            <button type="button"
              class="proceed-btn-primary">Deactivate</button>
            <button type="button"
              class="cancel-btn-primary">Cancel</button>
          </div>
        </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* width */
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}
</style>

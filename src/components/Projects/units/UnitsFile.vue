<script setup>
import { useRoute } from 'vue-router';
import { ProjectStore } from '@/store/project';
import { onMounted, ref, nextTick, watch, provide } from 'vue';
import { AddBulkUnits, getListofUnits, moveUnitToTrash, updateUnit } from '@/api/projects/units';
import SwitchButtons from '@/components/common/SwitchButtons.vue';
import UnitsTable from './UnitsTable.vue';
import router from '@/router';
import Modal from '@/components/common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import { GetAllTrash, RestoreTrash } from '@/api/trash';
import { getCookie } from '@/helpers/domhelper';
import Papa from 'papaparse';
import Button from '@/components/common/Button.vue';

const route = useRoute();
const projectStore = ProjectStore();

const unitsData = ref(null), newUnitData= ref({}), type = ref(route.query.type || 'Units'), unitHeaders=ref([]), loader = ref(false);
const openUnitDeleteModal = ref(false), trashUnitLoader =ref(null), trashUnitId = ref({id: null, index: null}), existingUnits = ref(null), errorUnits = ref([]), newUnits = ref(null);
const archived = ref(null), unarchiveLoader = ref({}), showImportModal=ref(false), importData=ref({}), importValidatedData = ref([]);
const customColumns = ref([]), idCounter = ref(0), metadataKeys = ref([]);
const extractedMetadata = ref({
  data: {},
  count: 0,
});
const defaultUnitHeaders=ref(['sno', 'unitplan', 'name', 'status', 'price', 'currency', 'measurement', 'measurement type', 'balconcy measurement', 'balcony measurement type', 'building', 'floor', 'community', 'tour', 'cta link', 'max price', 'bedroom', 'suite area', 'suite area measurement type']);

projectStore.RefreshVirtualTours(route.params.project_id);
projectStore.RefreshCommunities(route.params.project_id);

const extractHeaders = (data) => {
  const uniqueKeys = new Set();
  const metadataKeys = new Set();
  const metadataDetails = {
    data: {},
    count: 0,
  };

  Object.values(data).forEach((item) => {
    Object.keys(item).forEach((key) => {
      if (key !== "__v" && key !== 'project_id'  && key !== 'type' && key !== 'bedroom') {
        uniqueKeys.add(key);
        if (key === "metadata" && item[key]) {
          try {
            const value = typeof item[key] === 'string'
              ? JSON.parse(item[key])
              : item[key]; // already an object

            metadataDetails.data = value;
            metadataDetails.count = Object.keys(value).length;

            Object.keys(value).forEach((metaKey) => {
              metadataKeys.add(metaKey);
            });

          } catch (e) {
            console.error('Parsing error:', e.message);
          }
        }
      }
    });
  });

  // Convert Set to Array and transform keys
  let transformedKeys = Array.from(uniqueKeys).map((key) => {
    if (key === "_id") {
      return "sno";
    }

    if (key.endsWith("_id")) {
      const baseName = key.slice(0, -3);
      return baseName;
    }

    if (key === "suite_area_type") {
      return "suite area measurement type";
    }
    // Replace underscores with spaces
    return key.replace(/_/g, " ");
  });
  transformedKeys = transformedKeys.filter((key) => key !== 'metadata');

  metadataKeys.forEach((metaKey) => {
    transformedKeys.push(`metadata ${metaKey}`);
  });

  // Define the desired order for the first set of headers
  const priorityOrder = [
    'sno',
    'unitplan',
    'name',
    'status',
    'price',
    'currency',
    'measurement',
    'measurement type',
    'balcony measurement',
    'balcony measurement type',
    'building',
    'floor',
    'community',
    'tour',
    'cta link',
    'max price',
    'suite area',
    'suite area measurement type',
  ];

  const orderedKeys = [
    ...priorityOrder,
    ...transformedKeys.filter((header) => !priorityOrder.includes(header)),
  ];

  return { orderedKeys, metadataDetails, metadataKeys };
};

const getListOfAllUnits = () => {
  getListofUnits(route.params.project_id).then((res) => {
    unitsData.value = res;
    const result = extractHeaders(unitsData.value);
    unitHeaders.value = result.orderedKeys;
    extractedMetadata.value = result.metadataDetails;
    metadataKeys.value = result.metadataKeys;
    projectStore.SyncMultipleUnits(res);
  });
};

async function getArchivedItems () {
  try {
    const response = await GetAllTrash(route.params.project_id, getCookie('organization'), 'units', 1);
    archived.value = response.items;
  } catch (error) {
    console.error('Err', error);
  }
}

const addNewUnit = () => {
  const id = crypto.randomUUID();

  const newFile = {
    _id: id,
    unitplan_id: '',
    project_id: '',
    name: '',
    status: '',
    price: '',
    currency: '',
    measurement: null,
    measurement_type: '',
    balcony_measurement: null,
    balcony_measurement_type: '',
    building_id: '',
    floor_id: '',
    community_id: '',
    tour_id: '',
    max_price: '',
    suite_area: '',
    suite_area_type: '',
    isNew: true,

  };
  for (const key in extractedMetadata.value.data) {
    newFile[`metadata_${key}`] = '';
  }
  newUnitData.value[id] = newFile;
};

async function handleButtonClick (e) {
  type.value = e;
  try {
    await router.push({ path: route.path, query: { ...route.query, type: e } });
    if (e === 'Units') {
      await handleListOfUnitplans(); // This will fetch fresh gallery data
    } else if (e === 'Archive') {
      await getArchivedItems(); // This will fetch archived items
    }
  } catch (e) {
    console.log(e);
  }
}

const handleUpdateData = () => {
  getListOfAllUnits();
};

const deleteUnitRow=(ind) => {
  delete newUnitData.value[ind];
};

const openUnitTrashModal=(id, ind) => {
  openUnitDeleteModal.value=true;
  trashUnitId.value.id =id;
  trashUnitId.value.index=ind;
};

const handleMoveUnitToTrash = () => {
  trashUnitLoader.value = true;
  projectStore.ForceRefreshBuildings(route.params.project_id);
  if ( route.params.project_id && trashUnitId.value.id ) {
    const newObj = {
      unit_id: [trashUnitId.value.id],
      timeStamp: Date.now(),
    };
    moveUnitToTrash(newObj, route.params.project_id).then(async () => {
      document.dispatchEvent(new Event('refreshUnits'));
      trashUnitLoader.value = false;
      openUnitDeleteModal.value = false;
      trashUnitId.value = {id: null, index: null};
      location.reload();
    });
  }
};

async function restoreFunc (item) {
  unarchiveLoader.value[item._id] = true;
  const payload = {
    trash_id: item._id,
  };
  // load.value = item._id;
  try {
    await RestoreTrash(payload, route.params.project_id, 'unit', 'restoreUnit');
    await getArchivedItems();
    getListOfAllUnits();
    delete unarchiveLoader.value[item._id];
  } catch (error) {
    console.error("Error during restore or fetching trash:", error);
  }
}

// export csv functions
function objectToCsv (data) {
  const csvData = [];

  const title = ['_id', 'building_id', 'community_id', 'currency', 'floor_id', 'name', 'price', 'measurement', 'measurement_type', 'balcony_measurement', 'balcony_measurement_type', 'project_id', 'status', 'unitplan_id', 'tour_id', 'cta_link'];
  csvData.push(title);

  for (const key in data) {
    const entry = data[key];
    const csvEntry = [];
    const { ...cleanedEntry } = entry;
    csvEntry.push(cleanedEntry._id);
    csvEntry.push(cleanedEntry.building_id);
    csvEntry.push(cleanedEntry.community_id);
    csvEntry.push(cleanedEntry.currency);
    csvEntry.push(cleanedEntry.floor_id);
    csvEntry.push(cleanedEntry.name);
    csvEntry.push(cleanedEntry.price);
    csvEntry.push(cleanedEntry.measurement);
    csvEntry.push(cleanedEntry.measurement_type);
    csvEntry.push(cleanedEntry.balcony_measurement);
    csvEntry.push(cleanedEntry.balcony_measurement_type);
    csvEntry.push(cleanedEntry.project_id);
    csvEntry.push(cleanedEntry.status);
    csvEntry.push(cleanedEntry.unitplan_id);
    csvEntry.push(cleanedEntry.tour_id);
    if (cleanedEntry.metadata!==undefined){
      Object.keys(cleanedEntry.metadata).forEach((metaKey) => {
        if (!title.includes('metadata_'+metaKey)){
          title.push('metadata_'+metaKey);
        }
        csvEntry.push(cleanedEntry.metadata[metaKey]);
      });
    }
    csvEntry.push(cleanedEntry.cta_link);

    // If(Array.isArray(cleanedEntry)){
    //     CsvEntry.push(cleanedEntry.unitplan_id.join(','));
    // } else {
    //     CsvEntry.push(cleanedEntry.unitplan_id);
    // }

    // For (let field in entry) {
    //   If (typeof entry[field] === 'object' && entry[field] !== null) {
    //     // If the field is an object, stringify it

    //     CsvEntry.push(JSON.stringify(entry[field]));
    //   } else if (Array.isArray(entry[field])) {
    //     // If the field is an array, join its elements
    //     CsvEntry.push(entry[field].join(','));
    //   } else {
    //     // Otherwise, directly push the value
    //     CsvEntry.push(entry[field]);
    //   }
    // }
    csvData.push(csvEntry);
  }
  return csvData;
}

function exportCsv () {
  const csvData = objectToCsv(projectStore.units);
  const csv = Papa.unparse(csvData);
  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: 'text/csv' });
  // Create a URL for the Blob
  const url = window.URL.createObjectURL(blob);
  // Create a link element and trigger the download
  const a = document.createElement('a');
  a.href = url;
  a.download = 'data.csv';
  document.body.appendChild(a);
  a.click();

  // Clean up
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

// import csv function
const fileInput = ref(null);

// Function to trigger the file input click
const triggerFileInput = () => {
  fileInput.value.click();
};

function processFile (event){
  const file = event.target.files[0];
  Papa.parse(file, {
    header: true, // Set to false if your data does not have a header
    complete: (result) => {
      showImportModal.value = true;
      importData.value = result;
      // Separate arrays for modified units and units with undefined keys
      importData.value.data.forEach((unit) => {
        // Find the organization key where name matches the unit's unitplan_id
        const unitplanKey = Object.keys(projectStore.unitplans).find((key) => projectStore.unitplans[key]._id === unit.unitplan_id);
        const buildingKey = Object.keys(projectStore.buildings).find((key) => projectStore.buildings[key]._id === unit.building_id);
        const balcony_measurement_type = !unit.balcony_measurement_type ? "sqft" : unit.balcony_measurement_type;
        const floorKey = Object.keys(projectStore.buildings).find((key) => {
          const building = projectStore.buildings[key];
          // Find the floor in the building that matches
          return Object.values(building.floors).some((floor) => {
            return floor.floor_id === unit.floor_id;
          });
        });

        const metadata = {};

        for (const key in unit) {
          if (key.startsWith('metadata_')) {
            const newKey = key.replace('metadata_', '');
            metadata[newKey] = unit[key];
          }
        }

        // If both unitplanKey and buildingKey are defined, modify the unit
        if (unitplanKey !== undefined && buildingKey !== undefined && floorKey !== undefined) {
          importValidatedData.value.push({
            ...unit,
            building_id: buildingKey,
            unitplan_id: unitplanKey,
            floor_id: unit.floor_id,
            balcony_measurement_type: balcony_measurement_type,
            metadata: metadata,

          });
        } else {
          // If either unitplanKey or buildingKey is undefined, push the entire unit to the undefinedKeysUnits array
          errorUnits.value.push(unit);
        }
      });
      newUnits.value = importValidatedData.value.filter((unit) => {
        return unit._id.length < 1;
      });

      existingUnits.value = importValidatedData.value.filter((unit) => {
        return unit._id.length > 1;
      });
    },
  });
}

function RenderLabel (param, _id) {

  switch (param) {
    case 'unitplan_id':
      return projectStore.unitplans[_id] ? projectStore.unitplans[_id].name : _id;
    case 'building_id':
      return projectStore.buildings[_id] ? projectStore.buildings[_id].name : _id;
    case 'metadata':
      return null;
    default:
      return _id;
  }
}
const chunkArray = (array) => {
  const chunks = [];
  const chunkSize = 500;  // /maximum units to be sended to Backend (500)
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize));
  }
  return chunks;
};

const CreateBulkUnits= () => {
  if (Object.keys(newUnits.value).length >= 500){ // /maximum units to be sended to Backend (500)
    const chunksArray =  chunkArray(newUnits.value);
    Promise.all(chunksArray.map((batch) => AddBulkUnits(batch))).then(() => {
      console.log("Batch uploaded successfully");
    }).catch((err) => {
      console.log('Error creating Units', err);
    }).finally(() => {
      showImportModal.value = false;
      projectStore.ForceRefreshUnits(route.params.project_id);
    });
  } else {
    AddBulkUnits(newUnits.value).then(() => {
      showImportModal.value = false;
    }).then(() => {
      projectStore.ForceRefreshUnits(route.params.project_id);
    }).catch((err) => {
      console.log('Error creating Units', err);
    });
  }
};

const handleUpdateBulkUnits = () => {
  new Promise((resolve, reject) => {
    Promise.all(
      Object.values(existingUnits.value).map(async (data) => {
        await updateUnit(data, data._id);
      }),
    )
      .then(() => {
        projectStore.ForceRefreshUnits(route.params.project_id);
        resolve();
      })
      .catch((error) => {
        console.error("Error updating units:", error);
        reject(error);
      });
  })
    .then(() => {
      showImportModal.value = false;
    })
    .catch((error) => {
      console.error("Failed to update units:", error);
    });
};

const addNewColumn = () => {
  const newColumn = {
    id: `col_${Date.now()}_${idCounter.value}`,
    name: 'metadata_',
    type: 'text',
    isEditing: true,
  };

  idCounter.value++;
  customColumns.value.push(newColumn);

  const headerObj = {
    id: newColumn.id,
    name: 'metadata_',
    isCustom: true,
  };

  unitHeaders.value.push(headerObj);
  setTimeout(() => {
    const inputElement = document.getElementById(`input-${newColumn.id}`);
    if (inputElement) {
      inputElement.focus();
    }
  }, 0);

  nextTick(() => {
    const tableContainer = document.querySelector('.table-container'); // or use ref
    if (tableContainer) {
      tableContainer.scrollLeft = tableContainer.scrollWidth;
    }
  });
};

const updateColumnName = (columnId, newName) => {
  const column = customColumns.value.find((col) => col.id === columnId);
  if (!column) {
    return;
  }
  // Convert input to string and trim whitespace
  let processedName = String(newName).trim();

  // ensure the name starts with "metadata_"
  if (!processedName.startsWith('metadata_')) {
    processedName = `metadata_${processedName}`;
  }
  if (column) {
    column.name = processedName;

    // update the name in unitHeaders
    const headerIndex = unitHeaders.value.findIndex((header) =>
      typeof header === 'object' && header.id === columnId,
    );

    if (headerIndex !== -1) {
      unitHeaders.value[headerIndex].name = processedName;
    }
  }
};

const getCustomColumnById = (header) => {
  if (typeof header === 'object' && header.id) {
    return customColumns.value.find((col) => col.id === header.id);
  }
  if (typeof header === 'string' && header.startsWith('custom-col-')) {
    const id = header.replace('custom-col-', '');
    return customColumns.value.find((col) => col.id === id);
  }

  return null;
};

const removeCustomColumn = (columnId) => {
  customColumns.value = customColumns.value.filter((col) => col.id !== columnId);
  unitHeaders.value = unitHeaders.value.filter((header) => {
    if (typeof header === 'object' && header.id) {
      return header.id !== columnId;
    }
    if (typeof header === 'string' && header.startsWith('custom-col-')) {
      const id = header.replace('custom-col-', '');
      return id !== columnId;
    }
    return true;
  });
};

watch(customColumns.value, (newVal) => {
  customColumns.value = newVal;
  provide('customColumnsRef', customColumns.value);
});

onMounted(async () => {

  getListOfAllUnits();
  projectStore.RefreshUnitplans(route.params.project_id);
  projectStore.RefreshBuildings(route.params.project_id);
  projectStore.RefreshCommunities(route.params.project_id);

  if (type.value === 'Units') {
    getListOfAllUnits();
  } else if (type.value === 'Archive') {
    await getArchivedItems();
  }
});
</script>

<template>
    <div class=" dynamic-header">
      <div class="dynamic-heading ">
          <p class="dynamic-topic"> Enter Unit Details</p>
          <p class="dynamic-sub-topic">Details provided here will be used to to create the project's Unitplan Screens. <span class="text-[#1c64f2] underline underline-offset-4">View Sample</span>.</p>
      </div>

      <div class="flex gap-3">
        <!-- export button  -->
        <Button :disabled="loader" @click="exportCsv" title="Export" class="!rounded-lg !px- !py-3 !bg-bg-1000 border border-gray-200 !text-[#111928]">
            <template v-slot:svg>
               <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                 <path d="M3 8.37494H0.0780001C0.158987 8.67991 0.313718 8.95814 0.5274 9.18304L2.2242 10.9505C2.44011 11.1731 2.70722 11.3342 3 11.4186V8.37494Z" fill="#1F2A37"/>
                 <path d="M9.285 2.89511C9.3943 3.01299 9.45477 3.17086 9.45341 3.33472C9.45204 3.49859 9.38894 3.65534 9.27769 3.77122C9.16645 3.8871 9.01596 3.95283 8.85864 3.95425C8.70132 3.95567 8.54976 3.89268 8.4366 3.77883L7.8 3.11636L7.8 7.12498C7.8 7.29074 7.73679 7.4497 7.62426 7.56691C7.51174 7.68411 7.35913 7.74996 7.2 7.74996C7.04087 7.74996 6.88826 7.68411 6.77574 7.56691C6.66321 7.4497 6.6 7.29074 6.6 7.12498L6.6 3.11636L5.9634 3.77883C5.90805 3.83853 5.84185 3.88614 5.76864 3.91889C5.69544 3.95165 5.61671 3.96889 5.53704 3.96961C5.45737 3.97033 5.37837 3.95452 5.30463 3.92309C5.23089 3.89167 5.1639 3.84526 5.10756 3.78658C5.05123 3.7279 5.00668 3.65812 4.97651 3.58131C4.94634 3.5045 4.93116 3.42221 4.93185 3.33922C4.93254 3.25624 4.94909 3.17423 4.98054 3.09798C5.01198 3.02173 5.05769 2.95277 5.115 2.89511L6.4542 1.50016H1.2C0.887173 1.49497 0.585146 1.61925 0.360179 1.84573C0.135213 2.07222 0.00568067 2.3824 0 2.70824L0 7.12498H3C3.31826 7.12498 3.62348 7.25667 3.84853 7.49109C4.07357 7.7255 4.2 8.04343 4.2 8.37494V11.4998H10.8C11.1128 11.505 11.4149 11.3807 11.6398 11.1543C11.8648 10.9278 11.9943 10.6176 12 10.2918V2.75012C12 2.41861 11.8736 2.10067 11.6485 1.86626C11.4235 1.63185 11.1183 1.50016 10.8 1.50016H7.9458L9.285 2.89511Z" fill="#1F2A37"/>
               </svg>
            </template>
        </Button>

        <!-- import button  -->
        <div>
          <input
            type="file"
            ref="fileInput"
            @change="processFile"
            style="display: none;"
            accept=".csv"
          />
          <Button
            title="Import"
            @click="triggerFileInput"
            class="!rounded-lg !px-3 !py-3 !bg-bg-1000 border border-gray-200 !text-[#111928]"
          >
            <template v-slot:svg>
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
                <path d="M3 4.62506H0.0780001C0.158987 4.32009 0.313718 4.04186 0.5274 3.81696L2.2242 2.04951C2.44011 1.82693 2.70722 1.66576 3 1.5814L3 4.62506Z" fill="#111928"/>
                <path d="M9.285 10.1049C9.3943 9.98701 9.45477 9.82914 9.45341 9.66528C9.45204 9.50141 9.38894 9.34466 9.27769 9.22878C9.16645 9.1129 9.01596 9.04717 8.85864 9.04575C8.70132 9.04433 8.54976 9.10732 8.4366 9.22117L7.8 9.88364L7.8 5.87502C7.8 5.70926 7.73679 5.5503 7.62426 5.43309C7.51174 5.31589 7.35913 5.25004 7.2 5.25004C7.04087 5.25004 6.88826 5.31589 6.77574 5.43309C6.66321 5.5503 6.6 5.70926 6.6 5.87502L6.6 9.88364L5.9634 9.22117C5.90805 9.16147 5.84185 9.11386 5.76864 9.08111C5.69544 9.04835 5.61671 9.03111 5.53704 9.03039C5.45737 9.02967 5.37837 9.04548 5.30463 9.07691C5.23089 9.10833 5.1639 9.15474 5.10756 9.21342C5.05123 9.2721 5.00668 9.34188 4.97651 9.41869C4.94634 9.4955 4.93116 9.57779 4.93185 9.66078C4.93254 9.74376 4.94909 9.82577 4.98054 9.90202C5.01198 9.97827 5.05769 10.0472 5.115 10.1049L6.4542 11.4998H1.2C0.887173 11.505 0.585146 11.3807 0.360179 11.1543C0.135213 10.9278 0.00568067 10.6176 0 10.2918L0 5.87502H3C3.31826 5.87502 3.62348 5.74333 3.84853 5.50891C4.07357 5.2745 4.2 4.95657 4.2 4.62506V1.50016L10.8 1.50016C11.1128 1.49497 11.4149 1.61925 11.6398 1.84574C11.8648 2.07222 11.9943 2.3824 12 2.70824V10.2499C12 10.5814 11.8736 10.8993 11.6485 11.1337C11.4235 11.3682 11.1183 11.4998 10.8 11.4998L7.9458 11.4998L9.285 10.1049Z" fill="#111928"/>
              </svg>
            </template>
          </Button>
        </div>

        <button @click="addNewUnit" type="button"  class="!py-1 !px-3 text-base font-medium text-blue-700  bg-white rounded-lg flex gap-2 !border border-blue-700 items-center justify-center whitespace-nowrap">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="13" viewBox="0 0 12 13" fill="none">
             <path
               d="M10.2659 5.96686H6.53255V2.23353C6.53255 2.09208 6.47636 1.95642 6.37634 1.85641C6.27632 1.75639 6.14067 1.7002 5.99922 1.7002C5.85777 1.7002 5.72211 1.75639 5.6221 1.85641C5.52208 1.95642 5.46589 2.09208 5.46589 2.23353V5.96686H1.73255C1.5911 5.96686 1.45545 6.02305 1.35543 6.12307C1.25541 6.22309 1.19922 6.35875 1.19922 6.5002C1.19922 6.64164 1.25541 6.7773 1.35543 6.87732C1.45545 6.97734 1.5911 7.03353 1.73255 7.03353H5.46589V10.7669C5.46589 10.9083 5.52208 11.044 5.6221 11.144C5.72211 11.244 5.85777 11.3002 5.99922 11.3002C6.14067 11.3002 6.27632 11.244 6.37634 11.144C6.47636 11.044 6.53255 10.9083 6.53255 10.7669V7.03353H10.2659C10.4073 7.03353 10.543 6.97734 10.643 6.87732C10.743 6.7773 10.7992 6.64164 10.7992 6.5002C10.7992 6.35875 10.743 6.22309 10.643 6.12307C10.543 6.02305 10.4073 5.96686 10.2659 5.96686Z"
               fill="#1C64F2" />
           </svg>
           Add Unit
        </button>
      </div>

    </div>

    <div class="w-full rounded-lg shadow-[0px_1px_2px_-1px_rgba(0,0,0,0.10)] border border-gray-200 justify-start items-start">
        <div class="w-full">
          <div class="border-b border-gray-200 w-full">
          <nav class="flex">
            <SwitchButtons :disabled="loader" :active="type" :array="['Units', 'Archive']" @click="(e) => handleButtonClick(e)" class="w-full p-0.5"/>
          </nav>
        </div>
        </div>
        <!-- table -->
        <div  class="w-full overflow-x-auto h-fit relative">
          <div v-if="unitHeaders.length > 0"  class="flex w-fit h-[35px] gap-6 bg-gray-50 table-container">
            <div v-for="(item, index) in unitHeaders" :key="index"
              class="rounded-lg  text-gray-500 text-xs font-semibold !text-left uppercase leading-[18px] text-nowrap flex items-center"
              :class="[item === 'sno'? '!w-16':'w-32', item === 'balcony measurement' ? 'w-[190px]':'w-32',item === 'balcony measurement type' ? 'w-[215px]':'w-32',item === 'suite area measurement type'?'w-[220px]':'w-32']"
            >
            <template v-if="typeof item === 'object' && item.isCustom">
            <template v-if="getCustomColumnById(item)?.isEditing">
              <div class="w-32 flex gap-1">
                <input
                  :id="`input-${item.id}`"
                  v-model="getCustomColumnById(item).name"
                  @input="updateColumnName(item.id, getCustomColumnById(item).name)"
                  class="column-input w-24"
                />
                <button
                    @click="removeCustomColumn(item.id)"
                    class="text-sm flex !justify-end items-center  w-5 mt-2"
                    title="Remove column"
                  >
                   <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                      <path d="M17.6132 10L23.6517 3.96146C23.7607 3.85622 23.8476 3.73033 23.9074 3.59114C23.9672 3.45195 23.9986 3.30225 24 3.15077C24.0013 2.99929 23.9724 2.84906 23.915 2.70885C23.8577 2.56864 23.773 2.44127 23.6659 2.33415C23.5587 2.22703 23.4314 2.14232 23.2911 2.08496C23.1509 2.02759 23.0007 1.99873 22.8492 2.00004C22.6978 2.00136 22.548 2.03283 22.4089 2.09262C22.2697 2.15241 22.1438 2.23933 22.0385 2.34829L16 8.38683L9.96146 2.34829C9.74629 2.14047 9.45811 2.02548 9.15898 2.02808C8.85985 2.03068 8.57371 2.15066 8.36219 2.36219C8.15066 2.57371 8.03068 2.85985 8.02808 3.15898C8.02548 3.45811 8.14047 3.74629 8.34829 3.96146L14.3868 10L8.34829 16.0385C8.23933 16.1438 8.15241 16.2697 8.09262 16.4089C8.03283 16.548 8.00136 16.6978 8.00004 16.8492C7.99873 17.0007 8.02759 17.1509 8.08496 17.2911C8.14232 17.4314 8.22703 17.5587 8.33415 17.6659C8.44127 17.773 8.56864 17.8577 8.70885 17.915C8.84906 17.9724 8.99929 18.0013 9.15077 18C9.30225 17.9986 9.45195 17.9672 9.59114 17.9074C9.73033 17.8476 9.85622 17.7607 9.96146 17.6517L16 11.6132L22.0385 17.6517C22.2537 17.8595 22.5419 17.9745 22.841 17.9719C23.1401 17.9693 23.4263 17.8493 23.6378 17.6378C23.8493 17.4263 23.9693 17.1401 23.9719 16.841C23.9745 16.5419 23.8595 16.2537 23.6517 16.0385L17.6132 10Z" fill="#9CA3AF"/>
                    </svg>
                  </button>
              </div>
            </template>
            <template v-else>
              {{ getCustomColumnById(item)?.name || item.name }}
            </template>
          </template>

          <template v-else>
            <div
              class="rounded-lg p-2 text-gray-500 text-xs font-semibold !text-center uppercase leading-[18px] text-nowrap flex items-center"
              :class="[item === 'sno'? '!w-16':'w-32', item === 'balcony measurement' ? 'w-[190px]':'w-32',(item === 'balcony measurement type') ? 'w-[215px]':'w-32']"
            >
              {{ item }}
            </div>
          </template>

          </div>

          <!-- Add Column Button -->
          <div @click="addNewColumn" class="w-32 flex items-center justify-end !sticky !right-0 bg-gray-50 pr-[17px]">
              <button type="button" >
                  <div class="w-7 h-7 flex justify-center items-center bg-white rounded-lg !border !border-blue-500">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                      <path d="M10.2659 5.46589H6.53255V1.73255C6.53255 1.5911 6.47636 1.45545 6.37634 1.35543C6.27632 1.25541 6.14067 1.19922 5.99922 1.19922C5.85777 1.19922 5.72212 1.25541 5.6221 1.35543C5.52208 1.45545 5.46589 1.5911 5.46589 1.73255V5.46589H1.73255C1.5911 5.46589 1.45545 5.52208 1.35543 5.6221C1.25541 5.72212 1.19922 5.85777 1.19922 5.99922C1.19922 6.14067 1.25541 6.27632 1.35543 6.37634C1.45545 6.47636 1.5911 6.53255 1.73255 6.53255H5.46589V10.2659C5.46589 10.4073 5.52208 10.543 5.6221 10.643C5.72212 10.743 5.85777 10.7992 5.99922 10.7992C6.14067 10.7992 6.27632 10.743 6.37634 10.643C6.47636 10.543 6.53255 10.4073 6.53255 10.2659V6.53255H10.2659C10.4073 6.53255 10.543 6.47636 10.643 6.37634C10.743 6.27632 10.7992 6.14067 10.7992 5.99922C10.7992 5.85777 10.743 5.72212 10.643 5.6221C10.543 5.52208 10.4073 5.46589 10.2659 5.46589Z" fill="#1C64F2"/>
                    </svg>
                  </div>
              </button>
          </div>
          </div>
          <div v-else class="flex w-fit h-[35px] gap-6 bg-gray-50 table-container">
            <div v-for="(item, index) in defaultUnitHeaders" :key="index"
              class="rounded-lg  text-gray-500 text-xs font-semibold !text-left uppercase leading-[18px] text-nowrap flex items-center"
              :class="[item === 'sno'? '!w-16':'w-32', item === 'balconcy measurement' ? 'w-[190px]':'w-32',item === 'balcony measurement type' ? 'w-[215px]':'w-32', item === 'suite area measurement type'?'w-[220px]':'w-32']"
            >
            <template v-if="typeof item === 'object' && item.isCustom">
            <template v-if="getCustomColumnById(item)?.isEditing">
              <div class="w-32 flex gap-1">
                <input
                  :id="`input-${item.id}`"
                  v-model="getCustomColumnById(item).name"
                  @input="updateColumnName(item.id, getCustomColumnById(item).name)"
                  class="column-input w-24"
                />
                <button
                    @click="removeCustomColumn(item.id)"
                    class="text-sm flex !justify-end items-center  w-5 mt-2"
                    title="Remove column"
                  >
                   <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
                      <path d="M17.6132 10L23.6517 3.96146C23.7607 3.85622 23.8476 3.73033 23.9074 3.59114C23.9672 3.45195 23.9986 3.30225 24 3.15077C24.0013 2.99929 23.9724 2.84906 23.915 2.70885C23.8577 2.56864 23.773 2.44127 23.6659 2.33415C23.5587 2.22703 23.4314 2.14232 23.2911 2.08496C23.1509 2.02759 23.0007 1.99873 22.8492 2.00004C22.6978 2.00136 22.548 2.03283 22.4089 2.09262C22.2697 2.15241 22.1438 2.23933 22.0385 2.34829L16 8.38683L9.96146 2.34829C9.74629 2.14047 9.45811 2.02548 9.15898 2.02808C8.85985 2.03068 8.57371 2.15066 8.36219 2.36219C8.15066 2.57371 8.03068 2.85985 8.02808 3.15898C8.02548 3.45811 8.14047 3.74629 8.34829 3.96146L14.3868 10L8.34829 16.0385C8.23933 16.1438 8.15241 16.2697 8.09262 16.4089C8.03283 16.548 8.00136 16.6978 8.00004 16.8492C7.99873 17.0007 8.02759 17.1509 8.08496 17.2911C8.14232 17.4314 8.22703 17.5587 8.33415 17.6659C8.44127 17.773 8.56864 17.8577 8.70885 17.915C8.84906 17.9724 8.99929 18.0013 9.15077 18C9.30225 17.9986 9.45195 17.9672 9.59114 17.9074C9.73033 17.8476 9.85622 17.7607 9.96146 17.6517L16 11.6132L22.0385 17.6517C22.2537 17.8595 22.5419 17.9745 22.841 17.9719C23.1401 17.9693 23.4263 17.8493 23.6378 17.6378C23.8493 17.4263 23.9693 17.1401 23.9719 16.841C23.9745 16.5419 23.8595 16.2537 23.6517 16.0385L17.6132 10Z" fill="#9CA3AF"/>
                    </svg>
                  </button>
              </div>
            </template>
            <template v-else>
              {{ getCustomColumnById(item)?.name || item.name }}
            </template>
          </template>

          <template v-else>
            <div
              class="rounded-lg p-2 text-gray-500 text-xs font-semibold !text-center uppercase leading-[18px] text-nowrap flex items-center"
              :class="[item === 'sno'? '!w-16':'w-32', item === 'balcony measurement' ? 'w-[190px]':'w-32',(item === 'balcony measurement type' || item === 'meta data') ? 'w-[215px]':'w-32']"
            >
              {{ item }}
            </div>
          </template>

          </div>

          <!-- Add Column Button -->
          <div @click="addNewColumn" class="w-32 flex items-center justify-end !sticky !right-0 bg-gray-50 pr-[17px]">
              <button type="button" >
                  <div class="w-7 h-7 flex justify-center items-center bg-white rounded-lg !border !border-blue-500">
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                      <path d="M10.2659 5.46589H6.53255V1.73255C6.53255 1.5911 6.47636 1.45545 6.37634 1.35543C6.27632 1.25541 6.14067 1.19922 5.99922 1.19922C5.85777 1.19922 5.72212 1.25541 5.6221 1.35543C5.52208 1.45545 5.46589 1.5911 5.46589 1.73255V5.46589H1.73255C1.5911 5.46589 1.45545 5.52208 1.35543 5.6221C1.25541 5.72212 1.19922 5.85777 1.19922 5.99922C1.19922 6.14067 1.25541 6.27632 1.35543 6.37634C1.45545 6.47636 1.5911 6.53255 1.73255 6.53255H5.46589V10.2659C5.46589 10.4073 5.52208 10.543 5.6221 10.643C5.72212 10.743 5.85777 10.7992 5.99922 10.7992C6.14067 10.7992 6.27632 10.743 6.37634 10.643C6.47636 10.543 6.53255 10.4073 6.53255 10.2659V6.53255H10.2659C10.4073 6.53255 10.543 6.47636 10.643 6.37634C10.743 6.27632 10.7992 6.14067 10.7992 5.99922C10.7992 5.85777 10.743 5.72212 10.643 5.6221C10.543 5.52208 10.4073 5.46589 10.2659 5.46589Z" fill="#1C64F2"/>
                    </svg>
                  </div>
              </button>
          </div>
          </div>

          <div v-if="type==='Units'" class="">
            <div v-if="unitsData === null" class="flex flex-col items-center justify-center p-4">
              <div class="loader !h-6 !w-6 !border-3 !border-[#1C64F2] !border-t-gray-200"></div>
              <p class="text-black text-xs font-normal pt-1">Loading...</p>
            </div>
            <div v-else-if="unitsData && Object.values(unitsData).length === 0" class="flex flex-col items-center justify-center p-4 text-gray-500 text-lg">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                <g clip-path="url(#clip0_4982_18313)">
                  <path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z" fill="#6B7280"/>
                </g>
                <defs>
                  <clipPath id="clip0_4982_18313">
                    <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                  </clipPath>
                </defs>
              </svg>
              <p class="text-black text-xs font-normal pt-1">Empty</p>
            </div>

            <div v-else>
              <UnitsTable v-for="(data,id,index) in unitsData" :key="data._id"  :customColumn="customColumns" :metadataKeys="metadataKeys"
              :row="data"  :index="index"  @updateData="handleUpdateData" @openDeleteModal="openUnitTrashModal" :metadataDetails="extractedMetadata" />
              <UnitsTable v-for="(data,id,index) in newUnitData" :key="data._id"  :customColumn="customColumns" :metadataKeys="metadataKeys"
              :row="data"  :index="index" @updateData="handleUpdateData"  @deleteRow="deleteUnitRow"  />
            </div>
          </div>

          <!-- archive modal  -->
          <div v-else>
            <div v-if="archived == null" class="flex flex-col items-center justify-center p-4">
              <div class="loader !h-6 !w-6 !border-3 !border-[#1C64F2] !border-t-gray-200"></div>
              <p class="text-black text-xs font-normal pt-1">Loading...</p>
            </div>
            <div v-if="archived && Object.values(archived)?.length === 0" class="flex gap-3  items-center justify-center p-4 text-gray-500 text-lg">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                <g clip-path="url(#clip0_4982_18313)">
                  <path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z" fill="#6B7280"/>
                </g>
                <defs>
                  <clipPath id="clip0_4982_18313">
                    <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                  </clipPath>
                </defs>
              </svg>
              <p class="text-black text-sm font-normal ">Empty</p>
            </div>
            <div v-for="(data,id,index) in archived" :key="id" class="flex w-fit h-[50px] gap-6 relative " :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-100'">
              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-16">
                  {{index + 1}}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                  {{ data.data[Object.keys(data.data)[0]].name }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                {{projectStore.unitplans?.[data.data[Object.keys(data.data)[0]].unitplan_id].name}}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                  {{ data.data[Object.keys(data.data)[0]].status }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                {{ data.data[Object.keys(data.data)[0]].building_id? projectStore.buildings?.[data.data[Object.keys(data.data)[0]].building_id].name:"-" }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                {{ data.data[Object.keys(data.data)[0]].building_id?projectStore.buildings?.[data.data[Object.keys(data.data)[0]].building_id]?.floors?.[data.data[Object.keys(data.data)[0]].floor_id]?.floor_name :"-" }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                  {{ data.data[Object.keys(data.data)[0]].price }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                  {{ data.data[Object.keys(data.data)[0]].currency}}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                  {{ data.data[Object.keys(data.data)[0]].measurement }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                  {{ data.data[Object.keys(data.data)[0]].measurement_type }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-[190px]">
                  {{ data.data[Object.keys(data.data)[0]].balcony_measurement }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-[215px]">
                  {{data.data[Object.keys(data.data)[0]].balcony_measurement_type }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-[215px]">
                  {{data.data[Object.keys(data.data)[0]].balcony_measurement_type }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                {{ data.data[Object.keys(data.data)[0]].community_id ?projectStore.communities?.[data.data[Object.keys(data.data)[0]].community_id]?.name :"-"       }}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                  {{  data.data[Object.keys(data.data)[0]].tour_id? data.data[Object.keys(data.data)[0]].tour_id : '-'}}
              </div>

              <div class="p-3 text-gray-500 text-sm  flex items-center !text-left !w-32">
                  {{   data.data[Object.keys(data.data)[0]].cta_link ? data.data[Object.keys(data.data)[0]].cta_link :'-'}}
              </div>

              <div class="text-gray-500 text-sm  flex items-center !text-left !w-32 sticky right-0 pr-3"  :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
                <Button title="Unarchive" class="relative !px-3 !py-1 border !border-blue-600 !bg-bg-1000 !justify-center !items-center !gap-2 !text-[#1c64f2]"
                  @click="restoreFunc(data)" :disabled="unarchiveLoader[data.data[Object.keys(data.data)[0]]._id]"
                >
                  <template v-slot:svg>
                    <div v-if="unarchiveLoader[data.data[Object.keys(data.data)[0]]._id]" class="loader !h-5 !w-5 !border-2"></div>
                    <svg   width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M0.666667 9.95961C0.666667 10.3415 0.807142 10.7078 1.05719 10.9778C1.30724 11.2479 1.64638 11.3996 2 11.3996H10C10.3536 11.3996 10.6928 11.2479 10.9428 10.9778C11.1929 10.7078 11.3333 10.3415 11.3333 9.95961V4.91961H0.666667V9.95961ZM4.19533 7.29057C4.32035 7.15559 4.48989 7.07976 4.66667 7.07976C4.84344 7.07976 5.01298 7.15559 5.138 7.29057L5.33333 7.50153V6.35961C5.33333 6.16865 5.40357 5.98552 5.5286 5.85049C5.65362 5.71547 5.82319 5.63961 6 5.63961C6.17681 5.63961 6.34638 5.71547 6.4714 5.85049C6.59643 5.98552 6.66667 6.16865 6.66667 6.35961V7.50153L6.862 7.29057C6.98774 7.15942 7.15614 7.08684 7.33093 7.08848C7.50573 7.09012 7.67294 7.16585 7.79655 7.29934C7.92015 7.43283 7.99026 7.61342 7.99178 7.8022C7.9933 7.99098 7.92611 8.17286 7.80467 8.30865L6.47133 9.74865C6.40941 9.8157 6.33584 9.8689 6.25484 9.9052C6.17385 9.94149 6.08702 9.96018 5.99933 9.96018C5.91164 9.96018 5.82482 9.94149 5.74382 9.9052C5.66283 9.8689 5.58926 9.8157 5.52733 9.74865L4.194 8.30865C4.0692 8.17344 3.99923 7.99023 3.99948 7.79931C3.99973 7.60839 4.07018 7.4254 4.19533 7.29057Z" fill="#1C64F2"/>
                      <path d="M11.3333 0.599609H0.666667C0.298477 0.599609 0 0.921964 0 1.31961L0 2.75961C0 3.15725 0.298477 3.47961 0.666667 3.47961H11.3333C11.7015 3.47961 12 3.15725 12 2.75961V1.31961C12 0.921964 11.7015 0.599609 11.3333 0.599609Z" fill="#1C64F2"/>
                    </svg>
                  </template>
                </Button>
              </div>
            </div>
          </div>
        </div>
    </div>

    <!-- import preview -->
    <div class="relative z-50" aria-labelledby="modal-title" role="dialog" aria-modal="true" v-if="showImportModal">
      <div
        class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity">
      </div>
      <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <div  class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:p-6 ">
            <div class="sm:flex sm:items-start max-h-screen overflow-auto">
              <div class="w-full px-4 sm:px-6 lg:px-8 py-2">
                <div class="sm:flex sm:items-center">
                  <div class="sm:flex-auto">
                    <h1
                      class="text-base font-semibold leading-6 text-gray-900">
                      New Units</h1>
                  </div>
                </div>
                <div class="mt-1 flow-root">
                  <div class="h-fit">
                    <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
                        <table class="w-full rounded-lg bg-transparent">
                            <thead>
                                <tr class="bg-gray-50 dark:bg-bg-150">
                                    <th v-for="field, fieldIndex in importData.meta.fields" :key="fieldIndex"
                                    class="p-3 text-left text-sm font-semibold text-gray-900">{{ field }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="data, dataIndex in newUnits"
                              :key="dataIndex" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                                  <td v-for="datafield, datafieldIndex in data"
                                :key="datafieldIndex"
                                class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                {{ RenderLabel(datafieldIndex,
                                  datafield) }}
                                  </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                  </div>
                  <div class="mt-1 sm:ml-10 sm:mt-4 sm:flex justify-end gap-3">
                    <button type="button" class="proceed-btn-primary" @click="CreateBulkUnits">
                      Create Units
                    </button>
                    <button type="button" class="cancel-btn-primary" @click="()=>{showImportModal=false}">
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="sm:flex sm:items-start max-h-screen overflow-auto">
              <div class="w-full px-4 sm:px-6 lg:px-8 py-2 border-t-[1px] border-gray-40s0">
                <div class="sm:flex sm:items-center">
                  <div class="sm:flex-auto">
                    <h1
                      class="text-base font-semibold leading-6 text-gray-900">
                      Existing Units</h1>
                  </div>
                </div>
                <div class="mt-1 flow-root">
                  <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
                      <table class="w-full rounded-lg bg-transparent">
                          <thead>
                              <tr class="bg-gray-50 dark:bg-bg-150">
                                  <th v-for="field, fieldIndex in importData.meta.fields" :key="fieldIndex"
                                  class="p-3 text-left text-sm font-semibold text-gray-900">{{ field }}</th>
                              </tr>
                          </thead>
                          <tbody>
                              <tr v-for="data, dataIndex in existingUnits"
                            :key="dataIndex" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                                <td v-for="datafield, datafieldIndex in data"
                              :key="datafieldIndex"
                              class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                              {{ RenderLabel(datafieldIndex,
                                datafield) }}
                                </td>
                              </tr>
                          </tbody>
                      </table>
                  </div>
                  <div
                    class="mt-1 sm:ml-10 sm:mt-4 sm:flex justify-end gap-3">
                    <button type="button"
                      class="proceed-btn-primary"
                      @click="handleUpdateBulkUnits">Update
                      units</button>
                    <button type="button" class="cancel-btn-primary" @click="()=>{showImportModal=false}">
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div class="sm:flex sm:items-start max-h-screen overflow-auto">
              <div class="w-full px-4 sm:px-6 lg:px-8 py-2 border-t-[1px] border-gray-400">
                <div class="sm:flex sm:items-center">
                  <div class="sm:flex-auto">
                    <h1
                      class="text-base font-semibold leading-6 text-gray-900">
                      Error Units</h1>
                  </div>
                </div>
                <div class="mt-1 flow-root">
                  <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
                      <table class="w-full rounded-lg bg-transparent">
                          <thead>
                              <tr class="bg-gray-50 dark:bg-bg-150">
                                  <th v-for="field, fieldIndex in importData.meta.fields" :key="fieldIndex"
                                  class="p-3 text-left text-sm font-semibold text-gray-900">{{ field }}</th>
                              </tr>
                          </thead>
                          <tbody>
                              <tr v-for="data, dataIndex in errorUnits" :key="dataIndex" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                                <td v-for="datafield, datafieldIndex in data" :key="datafieldIndex" class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                   {{ RenderLabel(datafieldIndex,datafield) }}
                                </td>
                              </tr>
                          </tbody>
                      </table>
                  </div>
                </div>
                <div class="mt-1 sm:ml-10 sm:mt-4 sm:flex justify-end gap-3">
              <button type="button" class="proceed-btn-primary">Deactivate</button>
              <button type="button" class="cancel-btn-primary" @click="()=>{showImportModal=false}">Cancel</button>
            </div>
          </div>
          </div>
          </div>
        </div>
      </div>
    </div>

      <Modal :open="openUnitDeleteModal">
        <DeleteModalContent
            :trash="true"
            @closeModal="(e) => openUnitDeleteModal = false"
            :loader="trashTowerLoader"
            @handleDelete="handleMoveUnitToTrash"
            :dataName="'Unit'"
        />
      </Modal>
</template>

<style scoped>

.loader{
  @apply w-20 h-20 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-8 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}

</style>

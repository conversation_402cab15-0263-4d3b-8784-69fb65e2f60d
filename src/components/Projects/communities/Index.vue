<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, computed, watch } from 'vue';
import {getListOfCommunities, moveCommunityToTrash} from '../../../api/projects/communities/index';
import { ProjectStore } from '../../../store/project';
import NotfoundImage from '../../common/NotfoundImage.vue';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue';
import Button from '../../common/Button.vue';
import Modal from '../../common/Modal/Modal.vue';
import DeleteModalContent from '../../common/ModalContent/DeleteModalContent.vue';
import Spinner from '@/components/common/Spinner.vue';
import { GetAllTrash, RestoreTrash } from '@/api/trash';
import Pagination from  '../../Pagination.vue';

/* Props , State and Emits */
const route = useRoute();
const router = useRouter();
const projectStore = ProjectStore();
const projectId = ref(route.params.project_id);
const communities = ref(null);
const openDeleteModal = ref(false);
const communityToDelete = ref();
const deleteLoader = ref(false);
const showTrash = ref(false), trashData = ref([]), load = ref(null), searchQuery = ref('');
const currentPage = ref(1);
const totalPages = ref(1);

const handleGetListOfCommunities = () => {
  getListOfCommunities(projectId.value).then((res) => {
    communities.value = res;
    projectStore.SyncMultipleCommunities(res);
  });
};
handleGetListOfCommunities();

document.addEventListener('refreshcommunities', () => {
  handleGetListOfCommunities();
});

async function fetchTrashData () {
  await GetAllTrash(projectId.value, 'communitys', currentPage.value, 5).then((res) => {
    trashData.value = res.items;
    totalPages.value = res.total;
  });
}
fetchTrashData();

const handleMoveToTrash = () => {
  deleteLoader.value = true;
  const obj = {
    community_id: [communityToDelete.value],
    timeStamp: Date.now(),
  };
  moveCommunityToTrash(obj, projectId.value).then(() => {
    deleteLoader.value = false;
    handleGetListOfCommunities();
    fetchTrashData();
    openDeleteModal.value = false;
    communityToDelete.value = null;
  }).catch(() => {
    deleteLoader.value = false;
  });
};

async function restoreFunc (item) {
  const payload = {
    trash_id: item._id,
  };
  load.value = item._id;
  try {
    await RestoreTrash(payload, projectId.value, 'community', 'restoreCommunity');
    await fetchTrashData();
    if (Object.keys(trashData.value).length === 0 && currentPage.value > 1) {
      currentPage.value = currentPage.value - 1;
      await fetchTrashData();
    }
    handleGetListOfCommunities();
  } catch (error) {
    console.error("Error during restore or fetching trash:", error);
  }
}

const filteredTrashData = computed(() => {
  if (!searchQuery.value) {
    return trashData.value;
  }
  return Object.entries(trashData.value).reduce((result, [key, item]) => {
    const filteredUnitPlans = Object.entries(item.data).filter(([_, unitplan]) => {
      console.log(_);
      const queryLower = searchQuery.value.toLowerCase();
      return (
        (unitplan.name && unitplan.name.toLowerCase().includes(queryLower)) ||
                (unitplan.tour_name && unitplan.tour_name.toLowerCase().includes(queryLower))
      );
    });
    if (filteredUnitPlans.length > 0) {
      result[key] = { ...item, data: Object.fromEntries(filteredUnitPlans) };
    }
    return result;
  }, {});
});

const totalPagesProps = computed(() => {
  return Math.ceil(totalPages.value / 5);
});

const updateCurrentPage = (page) => {
  currentPage.value = page;
  fetchTrashData();
};

watch(() => trashData.value, (newVal) => {
  trashData.value = newVal;
});
watch(() => currentPage.value, (newVal) => {
  currentPage.value = newVal;
});

</script>

<template>
  <div class="relative bg-transparent pb-5">
    <!-- Header -->
    <div class="mb-6">
      <!-- Title -->
      <div class="mb-4 px-8 pt-6 flex justify-between">
        <div class="flex flex-col justify-start items-start gap-3">
          <h3 class="text-txt-50 dark:text-txt-1000 text text-2xl font-semibold"> Communities </h3>
          <p class="text-gray-600 dark:text-txt-650 text-base font-normal mb-0"> Lorem, ipsum dolor sit amet consectetur
            adipisicing elit. </p>
        </div>
        <div class="flex items-end gap-4">
            <Button title="Create Community" theme="primary"
              @click="() => router.push(`/projects/${projectId}/communities/create`)">
              <template v-slot:svg>
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_306_21007)">
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_306_21007">
                      <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                    </clipPath>
                  </defs>
                </svg>
              </template>
            </Button>
            <Button title="Trashed Communities" @click="showTrash = !showTrash" theme="primary"></Button>
        </div>
      </div>
      <!-- <BreadCrumb/> -->
    </div>
    <div v-if="!showTrash">
      <div v-if="communities && Object.keys(communities).length !== 0" class="h-fit px-8">
        <div
          :class="[' overflow-x-auto w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ']">
          <table class="w-full rounded-lg bg-transparent">
            <thead>
              <tr class="bg-gray-50 dark:bg-bg-150">
                <th class="p-3 text-left text-sm font-semibold text-gray-900">S.No</th>
                <th class="p-3 text-left text-sm font-semibold text-gray-900">name</th>
                <th class="p-3 text-left text-sm font-semibold text-gray-900"></th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item, _id, index in communities" :key="index"
                class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                  {{ index + 1 }}
                </td>
                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                  {{ item.name }}
                </td>
                <td class="p-3 flex justify-center">
                  <Menu as="div" class="relative whitespace-nowrap flex justify-center items-center">
                    <div>
                      <MenuButton as="div"
                        class="inline-flex w-full mr-1.5 rounded-md bg-inherit py-0 text-xs text ring-gray-300 cursor-pointer">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                          class="w-6 h-6 fill-black">
                          <path fillRule="evenodd"
                            d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"
                            clipRule="evenodd" />
                        </svg>
                      </MenuButton>
                    </div>

                    <transition enter-active-class="transition ease-out duration-100"
                      enter-from-class="transform opacity-0 scale-95" enter-to-class="transform opacity-100 scale-100"
                      leave-active-class="transition ease-in duration-75"
                      leave-from-class="transform opacity-100 scale-100" leave-to-class="transform opacity-0 scale-95">
                      <MenuItems
                        class="absolute -top-2 right-10 z-80 mt-2 w-fit origin-top-right rounded-md bg-neutral-700 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                        <div class="py-2 flex flex-col">
                          <MenuItem>
                          <h @click="router.push(`/projects/${projectId}/communities/${item._id}/edit`)"
                            class="text-gray-300 block px-3 py-1 text-xs hover:text-white">Edit</h>
                          </MenuItem>
                          <MenuItem>
                          <a href="#" @click="()=>{communityToDelete= item._id,openDeleteModal=true}"
                            class="text-gray-300 block px-3 py-1 text-xs hover:text-white">Delete</a>
                          </MenuItem>
                        </div>
                      </MenuItems>
                    </transition>
                  </Menu>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div v-else class="w-full">
        <div class="w-fit m-auto">
          <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg"
            xmlns:xlink="http://www.w3.org/1999/xlink">
            <rect width="300" height="286" fill="url(#pattern0)" />
            <NotfoundImage />
          </svg>
        </div>
        <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">No Tower/Building Created</div>
        <Button class="mt-8 mx-auto" title="Create Community"
          @click="() => router.push(`/projects/${projectId}/communities/create`)" theme="primary">
          <template v-slot:svg>
            <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_306_21007)">
                <path class="fill-txt-1000 dark:fill-txt-default"
                  d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                <path class="fill-txt-1000 dark:fill-txt-default"
                  d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
              </g>
              <defs>
                <clipPath id="clip0_306_21007">
                  <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                </clipPath>
              </defs>
            </svg>
          </template>
        </Button>
      </div>
    </div>
    <div class="mt-3 text-white px-8" v-if="showTrash">
      <span class="flex pb-4 font-bold text-black">Trashed Communities</span>
      <input
        type="text"
        v-model="searchQuery"
        placeholder="Search"
        class="border px-2 py-1 mb-3 rounded text-black"
      />
      <div v-if="filteredTrashData && Object.keys(filteredTrashData).length !== 0" class="h-fit">
        <div class="overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden">
          <table class="w-full rounded-lg bg-transparent">
            <thead>
              <tr class="bg-gray-50 dark:bg-bg-150">
                <th class="p-3 text-left text-sm font-semibold text-gray-900">Name</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in filteredTrashData" :key="index">
                <tr v-for="community, communityId in item.data"
                                :key="communityId"
                class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                  <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                    {{ community.name }}
                  </td>
                  <td class="flex items-center gap-2 p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap cursor-pointer" @click="restoreFunc(item)">
                    Restore
                    <Spinner v-if="load === item._id"/>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
        <Pagination v-if="totalPagesProps > 1" :totalPages="totalPagesProps" :currentPage="currentPage" @currentPageSync="updateCurrentPage"/>
      </div>
      <div v-else class="w-full">
        <div class="w-fit m-auto">
            <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <rect width="300" height="286" fill="url(#pattern0)"/>
                <NotfoundImage/>
            </svg>
        </div>
        <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">No Trashed Communities Available</div>
    </div>
    </div>
            <Modal :open="openDeleteModal">
          <DeleteModalContent
            :trash="true"
            :loader="deleteLoader"
            @closeModal="(e) => openDeleteModal = false"
            @handleDelete="handleMoveToTrash"
            :dataName="'Community'" />
        </Modal>
  </div>
</template>

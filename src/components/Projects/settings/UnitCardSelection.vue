<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import { UpdateProjectSettings } from '@/api/projects/settings';
import Multiselect from 'vue-multiselect';
import { CardDetailsSchema } from '../../../validationSchema/project/settings';

const route = useRoute();
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const initialData = ref(null);
const selected_unitCard = ref();
const cardCustomizationTypes = ['default', 'custom'];
const project_Id = ref(route.params.project_id); // Project id

const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const setupDataCallBack = (values) => {
  console.log('setupDataCallBack', values);

  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      customize_type: data.projectSettings?.ale?.unit_card_customize_type ? data.projectSettings?.ale?.unit_card_customize_type : 'default',
      type: data.projectSettings?.ale?.unitcard_config?.type ? data.projectSettings?.ale?.unitcard_config?.type : false,
      measurement: data.projectSettings?.ale?.unitcard_config?.measurement ? data.projectSettings?.ale?.unitcard_config?.measurement : false,
      bedrooms: data.projectSettings?.ale?.unitcard_config?.bedrooms ? data.projectSettings?.ale?.unitcard_config?.bedrooms : false,
      bathrooms: data.projectSettings?.ale?.unitcard_config?.bathrooms ? data.projectSettings?.ale?.unitcard_config?.bathrooms : false,
      status: data.projectSettings?.ale?.unitcard_config?.status ? data.projectSettings?.ale?.unitcard_config?.status : false,
      price: data.projectSettings?.ale?.unitcard_config?.price ? data.projectSettings?.ale?.unitcard_config?.price : false,
      maid: data.projectSettings?.ale?.unitcard_config?.maid ? data.projectSettings?.ale?.unitcard_config?.maid : false,
      view: data.projectSettings?.ale?.unitcard_config?.view ? data.projectSettings?.ale?.unitcard_config?.view : false,
      floor_id: data.projectSettings?.ale?.unitcard_config?.floor_id ? data.projectSettings?.ale?.unitcard_config?.floor_id : false,
      building_id: data.projectSettings?.ale?.unitcard_config?.building_id ? data.projectSettings?.ale?.unitcard_config?.building_id : false,
      style: data.projectSettings?.ale?.unitcard_config?.style ? data.projectSettings?.ale?.unitcard_config?.style : false,
      units: data.projectSettings?.ale?.unitcard_config?.units ? data.projectSettings?.ale?.unitcard_config?.units : false,
      favIcon: data.projectSettings?.ale?.unitcard_config?.favIcon ? data.projectSettings?.ale?.unitcard_config?.favIcon : false,
    };

    // Form Initial Values
    initialData.value = {
      type: data.projectSettings?.ale?.unitcard_config?.type ? data.projectSettings?.ale?.unitcard_config?.type : false,
      measurement: data.projectSettings?.ale?.unitcard_config?.measurement ? data.projectSettings?.ale?.unitcard_config?.measurement : false,
      bedrooms: data.projectSettings?.ale?.unitcard_config?.bedrooms ? data.projectSettings?.ale?.unitcard_config?.bedrooms : false,
      bathrooms: data.projectSettings?.ale?.unitcard_config?.bathrooms ? data.projectSettings?.ale?.unitcard_config?.bathrooms : false,
      status: data.projectSettings?.ale?.unitcard_config?.status ? data.projectSettings?.ale?.unitcard_config?.status : false,
      price: data.projectSettings?.ale?.unitcard_config?.price ? data.projectSettings?.ale?.unitcard_config?.price : false,
      maid: data.projectSettings?.ale?.unitcard_config?.maid ? data.projectSettings?.ale?.unitcard_config?.maid : false,
      view: data.projectSettings?.ale?.unitcard_config?.view ? data.projectSettings?.ale?.unitcard_config?.view : false,
      floor_id: data.projectSettings?.ale?.unitcard_config?.floor_id ? data.projectSettings?.ale?.unitcard_config?.floor_id : false,
      building_id: data.projectSettings?.ale?.unitcard_config?.building_id ? data.projectSettings?.ale?.unitcard_config?.building_id : false,
      style: data.projectSettings?.ale?.unitcard_config?.style ? data.projectSettings?.ale?.unitcard_config?.style : false,
      units: data.projectSettings?.ale?.unitcard_config?.units ? data.projectSettings?.ale?.unitcard_config?.units : false,
      favIcon: data.projectSettings?.ale?.unitcard_config?.favIcon ? data.projectSettings?.ale?.unitcard_config?.favIcon : false,
    };

    selected_unitCard.value = data.projectSettings?.ale?.unit_card_customize_type ? data.projectSettings?.ale?.unit_card_customize_type : 'default';

  }
};
const handleSubmit = async (val) => {
  console.log('val', val);

  return new Promise((resolve) => {
    const prevData = previousData.value; // prevData track source
    const newCompareObj = { ...val }; // form values
    console.log('newCompareObj', newCompareObj);

    if (Object.keys(frameParms(prevData, newCompareObj)).length > 0) {
      const oldValues = Object.fromEntries(
        Object.entries(prevData).filter(([key]) => key !== 'customize_type'),
      );
      const newValues = Object.fromEntries(
        Object.entries(newCompareObj).filter(([key]) => key !== 'customize_type'),
      );

      const payload = {
        project_id: project_Id.value,
        query: {
          [projectSettingsFormTypes.ALE]: {
            unitcard_config: frameParms(oldValues, newValues),
          },
        },
      };

      const params = frameParms(prevData, newCompareObj);
      if (params.customize_type){
        payload.query[projectSettingsFormTypes.ALE].unit_card_customize_type = params.customize_type;
      } else {
        payload.query[projectSettingsFormTypes.ALE].unit_card_customize_type = previousData.value.customize_type;
      }

      UpdateProjectSettings(payload).then((res) => {
        if (res){
          projectStore.settings.projectSettings[projectSettingsFormTypes.UNITCARDREQUIREMENTS] = res.projectSettings[projectSettingsFormTypes.UNITCARDREQUIREMENTS]; // update to store
          setupDataCallBack(res); // update the values
          resolve(res);
        }
      });
    } else {
      resolve();
    }
  });
};

if (projectStore.settings){
  console.log('projectStore.settings', projectStore.settings);
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
    <div class="flex flex-col justify-start items-start my-3">

        <!-- Headers -->
        <div class="flex justify-between items-center w-full mb-4">

        <p class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> Card Customization: </p>

        <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
            @handle-click="() => isEdit = !isEdit">
            <template v-slot:svg>
            <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                fill="white" />
            </svg>
            </template>
        </Button>

        <div v-if="isEdit" class="flex justify-start items-center gap-3">
            <Button title="Reset" type="button" theme="secondary" @handle-click="() => isEdit = !isEdit">
            </Button>
            <label for="editCardDetails"
            :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer']">
            Save </label>
        </div>

        </div>

        <!-- View -->
        <div v-if="!isEdit" class="grid grid-cols-3 gap-8 w-full mt-3 mb-5">

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Unit Card Customization: </label>
                <p class="font-medium text-sm text-txt-default capitalize"> {{
                    previousData?.customize_type ?
                        previousData?.customize_type : '-' }} </p>
            </div>

            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Unit Variant: </label>
                <p v-if="previousData?.type"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.type }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Measurement: </label>
                <p v-if="previousData?.measurement"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.measurement }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Bedrooms: </label>
                <p v-if="previousData?.bedrooms"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.bedrooms }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Bathrooms: </label>
                <p v-if="previousData?.bathrooms"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.bathrooms }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Status: </label>
                <p v-if="previousData?.status"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.status }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>

            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Price: </label>
                <p v-if="previousData?.price"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.price }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Style: </label>
                <p v-if="previousData?.style"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.style }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Floor Name: </label>
                <p v-if="previousData?.floor_id"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.floor_id }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Building Name: </label>
                <p v-if="previousData?.building_id"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.building_id }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50">Maid: </label>
                <p v-if="previousData?.maid"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.maid }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> View: </label>
                <p v-if="previousData?.view"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.view }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Units: </label>
                <p v-if="previousData?.units"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.units }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
            <div v-if="previousData?.customize_type !== 'default'" class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Favorites Icon: {{  previousData?.favIcon }} </label>
                <p v-if="previousData?.favIcon"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.favIcon }} </p>
                <p v-else class="font-medium text-sm text-txt-default">
                -
                </p>
            </div>
        </div>

        <!-- Form -->
        <Form v-else class="grid grid-cols-3 gap-3 mt-3 w-full "
        @submit="(val) => handleSubmit(val).then(() => isEdit = false)"
        :initial-values="initialData" :validation-schema="CardDetailsSchema">

            <div class="relative col-span-3 w-[300px]">
                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Unit Card Customization
                <strong>*</strong>
                </label>
                <Field name="customize_type" :model-value="selected_unitCard" v-slot="{ field }">
                <Multiselect :allow-empty="false" v-bind="field" v-model="selected_unitCard" :searchable="false"
                    :close-on-select="true" :show-labels="false" placeholder="Choose" :options="cardCustomizationTypes" maxHeight="200">
                </Multiselect>
                </Field>

                <ErrorMessage name="customize_type" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>
        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="type" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="type" v-bind="field" :value="true" />
                Unit Variant <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="type" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>

        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="measurement" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="measurement" v-bind="field" :value="true" />
                Measurement <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="measurement" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>

        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="bedrooms" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="bedrooms" v-bind="field" :value="true" />
                Bedrooms <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="bedrooms" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>
        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="bathrooms" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="bathrooms" v-bind="field" :value="true" />
                Bathrooms <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="bathrooms" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>

        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="status" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="status" v-bind="field" :value="true" />
                Status <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="status" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>

        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="price" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="price" v-bind="field" :value="true" />
                Price <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="price" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>

        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="style" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="style" v-bind="field" :value="true" />
                Style <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="style" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>

        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="floor_id" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="floor_id" v-bind="field" :value="true" />
                Floor Name <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="floor_id" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>

        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="building_id" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="building_id" v-bind="field" :value="true" />
                Building Name <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="building_id" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>
        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="maid" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="maid" v-bind="field" :value="true" />
                Maid <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="maid" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>
        <div class="relative" v-if="selected_unitCard === 'custom'">

            <Field v-slot="{ field }" name="view" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="view" v-bind="field" :value="true" />
                View <strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="view" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

        </div>

        <div class="relative" v-if="selected_unitCard === 'custom'">

        <Field v-slot="{ field }" name="units" type="checkbox" :value="true" :unchecked-value="false">
        <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
            <input type="checkbox" name="units" v-bind="field" :value="true" />
            Units <strong>*</strong>
        </label>
        </Field>

        <ErrorMessage name="units" as="p" v-slot="{ message }"
        class="flex justify-start items-center gap-2 ml-3 mb-2 ">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fill-rule="evenodd" clip-rule="evenodd"
            d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
            fill="#B3261E" />
        </svg>
        <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
        </ErrorMessage>

        </div>
        <div class="relative" v-if="selected_unitCard === 'custom'">
            <Field v-slot="{ field }" name="favIcon" type="checkbox" :value="true" :unchecked-value="false">
            <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="favIcon" v-bind="field" :value="true" />
                Favorites Icon<strong>*</strong>
            </label>
            </Field>

            <ErrorMessage name="favIcon" as="p" v-slot="{ message }"
            class="flex justify-start items-center gap-2 ml-3 mb-2 ">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                fill="#B3261E" />
            </svg>
            <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>
        </div>

        <Button id="editCardDetails" class="hidden" title="Submit" type="submit" theme="primary"> </Button>

        </Form>

    </div>
</template>

<style scoped>

</style>

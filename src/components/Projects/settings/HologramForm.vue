<script setup>
import { ref } from 'vue';
import { PlusIcon, XMarkIcon } from '@heroicons/vue/20/solid';
import { UpdateProjectSettings } from '../../../api/projects/settings/index.ts';
import { updateSettingsFile } from '../../../api/projects/assets/index.ts';
import { hologramSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { projectSettingsFormTypes, assetsType } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import { getUploadedStorageLink, ShortenUrl } from '../../../helpers/helpers.ts';
import Multiselect from 'vue-multiselect';
import { getCookie } from '../../../helpers/domhelper';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const initialData = ref(null);
const shortFileUrl = ref(false);
const type = ref();
const hologramFile = ref(null);
const holoLogo = ref({
  preview: null,
  fileData: null,
});
const holoThumbnail = ref({
  preview: null,
  fileData: null,
});
const typeRef = ref();
const projectType = ref([]);
const brRef = ref([]);
const br = ref([]);
const tagNames = ref([]);
const tagEnter = ref("");
const color = ref('#000000');
const hologramSettingsLoader = ref(false);

/* Methods */

const addType = (value) => {
  typeRef.value = value;
  projectType.value.push(value);
};

const addBR = (value) => {
  console.log(value);
  brRef.value = [...brRef.value, value];
  br.value.push(value);
  console.log(brRef.value, br.value);
};

const hexToRgb = (hex) => {
  // Remove the hash if it's there
  hex = hex.replace(/^#/, '');
  // Parse the hex values
  const bigint = parseInt(hex, 16);
  const r = (bigint >> 16) & 255;
  const g = (bigint >> 8) & 255;
  const b = bigint & 255;
  return `${r},${g},${b}`;
};

function handleTagAddtion (tag, clr){
  console.log(tag, clr);
  if (!tag || !clr){
    return;
  }
  tagEnter.value='';
  color.value='#000000';
  tagNames.value.push({name: tag, color: hexToRgb(clr)});
}

const handleUploadImage = (e, type) => {
  const file = e.target.files[0];
  if (type==='thumbnail'){
    if (!file){
      holoThumbnail.value.fileData = null;
      holoThumbnail.value.preview = null;
    }
    holoThumbnail.value.fileData = e.target.files[0];
    holoThumbnail.value.preview = URL.createObjectURL(file);
  } else if (type==='logo'){
    if (!file){
      holoLogo.value.fileData = null;
      holoLogo.value.preview = null;
    }
    holoLogo.value.fileData = e.target.files[0];
    holoLogo.value.preview = URL.createObjectURL(file);
  }
};

async function generateShortUrl (url, type) {
  console.log(url);
  await ShortenUrl(url).then((res) => {
    if (type === 'file'){
      shortFileUrl.value = res;
    }
  });
  return;
}

const setupDataCallBack = (values) => {
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      project_location: (data.projectSettings?.hologram?.project_location ? data.projectSettings?.hologram?.project_location : null),
      amount: (data.projectSettings?.hologram?.amount ? data.projectSettings?.hologram?.amount : null),
      project_type: (data.projectSettings?.hologram?.project_type ? data.projectSettings?.hologram?.project_type : null),
      bedrooms: (data.projectSettings?.hologram?.bedrooms ? data.projectSettings?.hologram?.bedrooms : null),
      thumbnail: (data.projectSettings?.hologram?.thumbnail ? data.projectSettings?.hologram?.thumbnail : null),
      file: (data.projectSettings?.hologram?.file ? data.projectSettings?.hologram?.file : null),
      tags: (data.projectSettings?.hologram?.tags ? data.projectSettings?.hologram?.tags : null),
      project_logo: (data.projectSettings?.hologram?.project_logo ? data.projectSettings?.hologram?.project_logo : null),
    };

    // Form Initial Values
    initialData.value = {
      project_location: (data.projectSettings?.hologram?.project_location ? data.projectSettings?.hologram?.project_location : null),
      amount: (data.projectSettings?.hologram?.amount ? data.projectSettings?.hologram?.amount : null),
      project_type: (data.projectSettings?.hologram?.project_type ? data.projectSettings?.hologram?.project_type : null),
      bedrooms: (data.projectSettings?.hologram?.bedrooms ? data.projectSettings?.hologram?.bedrooms : null),
      thumbnail: (data.projectSettings?.hologram?.thumbnail ? data.projectSettings?.hologram?.thumbnail : null),
      file: (data.projectSettings?.hologram?.file ? data.projectSettings?.hologram?.file : null),
      tags: (data.projectSettings?.hologram?.tags ? data.projectSettings?.hologram?.tags : null),
      project_logo: (data.projectSettings?.hologram?.project_logo ? data.projectSettings?.hologram?.project_logo : null),
    };

    hologramFile.value = data.projectSettings?.hologram?.file ? data.projectSettings?.hologram?.file : null;
    holoLogo.value.preview = data.projectSettings?.hologram?.project_logo ? data.projectSettings?.hologram?.project_logo : null;
    holoLogo.value.fileData = data.projectSettings?.hologram?.project_logo ? data.projectSettings?.hologram?.project_logo : null;
    holoThumbnail.value.preview = data.projectSettings?.hologram?.thumbnail ? data.projectSettings?.hologram?.thumbnail : null;
    holoThumbnail.value.fileData = data.projectSettings?.hologram?.thumbnail ? data.projectSettings?.hologram?.thumbnail : null;

    if (data.projectSettings?.hologram?.tags) {
      tagNames.value = [...data.projectSettings.hologram.tags];
    }

    if (data.projectSettings?.hologram?.file){
      generateShortUrl(data.projectSettings?.hologram?.file, 'file');
    }

    if (data.projectSettings?.hologram?.bedrooms){
      br.value = [...data.projectSettings.hologram.bedrooms];
      brRef.value = [...data.projectSettings.hologram.bedrooms];
    }

    if (data.projectSettings?.hologram?.project_type){
      typeRef.value = data.projectSettings?.hologram?.project_type;
      projectType.value = [ data.projectSettings?.hologram?.project_type];
    }

  }
};

const updateValuesCallback = (res) => {
  if (res){
    projectStore.settings.projectSettings[projectSettingsFormTypes.HOLOGRAM] = res.projectSettings[projectSettingsFormTypes.HOLOGRAM]; // update to store
    setupDataCallBack(res); // update the values
  }
};

/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const handleSubmit = (val) => {

  console.log(val);

  const prevData = previousData.value; // prevData track source
  const newCompareObj = { ...val }; // form values
  if (type.value) {
    delete type.value.newCompareObj;
  }
  const parms = frameParms(prevData, newCompareObj);

  if (Object.keys(parms).length === 0){
    isEdit.value = false;
    return;
  }

  hologramSettingsLoader.value = true; // loader
  const payload1 = new FormData();
  // Form data
  payload1.append('project_id', project_Id.value);
  parms.thumbnail && payload1.append('thumbnail', parms.thumbnail);
  parms.project_logo && payload1.append('hologram_project_logo', parms.project_logo);

  if (parms.project_logo || parms.thumbnail || parms.file){

    const organization = getCookie('organization');
    let filepath;
    if (parms.file){
      const stampFileName = parms.file.name;
      filepath = `CreationtoolAssets/${organization}/projects/${project_Id.value}/${projectSettingsFormTypes.HOLOGRAM}/${stampFileName}`; // File path formation
    }
    const payload2 = {
      project_id: project_Id.value,
      query: {
        [projectSettingsFormTypes.HOLOGRAM]: {},
      },
    };

    // Add all form data to hologram object
    Object.keys(parms).forEach((key) => {
      payload2.query.hologram[key] = parms[key];
    });
    payload2.query[projectSettingsFormTypes.HOLOGRAM].bedrooms = br.value;

    if ( parms.file){
      getUploadedStorageLink(parms.file, filepath).then((res) => {
        if (parms.thumbnail || parms.project_logo){
          updateSettingsFile(payload1).then((data) => {
            payload2.query[projectSettingsFormTypes.HOLOGRAM].thumbnail = data.thumbnail;
            payload2.query[projectSettingsFormTypes.HOLOGRAM].hologram_project_logo = data.hologram_project_logo;
            payload2.query[projectSettingsFormTypes.HOLOGRAM].file = res;
            UpdateProjectSettings(payload2).then((res) => {
              updateValuesCallback(res);
            }).catch((err) => {
              console.log(err);
            }).finally(() => {
              hologramSettingsLoader.value = false;
              isEdit.value = false;
            });

          });
        } else {
          payload2.query[projectSettingsFormTypes.HOLOGRAM].file = res;
          UpdateProjectSettings(payload2).then((res) => {
            updateValuesCallback(res);
          }).catch((err) => {
            console.log(err);
          }).finally(() => {
            hologramSettingsLoader.value = false;
            isEdit.value = false;
          });
        }
      });
    } else {
      updateSettingsFile(payload1).then((data) => {
        data.thumbnail && (payload2.query[projectSettingsFormTypes.HOLOGRAM].thumbnail = data.thumbnail);
        data.hologram_project_logo = (payload2.query[projectSettingsFormTypes.HOLOGRAM].hologram_project_logo = data.hologram_project_logo);
        delete payload2.query[projectSettingsFormTypes.HOLOGRAM].project_logo;

        UpdateProjectSettings(payload2).then((res) => {
          updateValuesCallback(res);
        }).catch((err) => {
          console.log(err);
        });
      }).finally(() => {
        hologramSettingsLoader.value = false;
        isEdit.value = false;
      });
    }

  } else {

    const payload = {
      project_id: project_Id.value,
      query: {
        [projectSettingsFormTypes.HOLOGRAM]: {},
      },
    };

    // Add all form data to [ projectSettingsFormTypes.HOLOGRAM] object
    Object.keys(parms).forEach((key) => {
      payload.query[projectSettingsFormTypes.HOLOGRAM][key] = parms[key];
    });
    payload.query[projectSettingsFormTypes.HOLOGRAM].bedrooms = br.value;

    UpdateProjectSettings(payload).then((res) => {
      updateValuesCallback(res);
    }).catch((err) => {
      console.log(err);
    }).finally(() => {
      hologramSettingsLoader.value = false;
      isEdit.value = false;
    });
  }

};

// Initialize
if (projectStore.settings){
  setupDataCallBack(projectStore.settings);
}

</script>

<template>
   <div class="flex flex-col justify-start items-start my-3">

    <!-- Headers -->
    <div class="flex justify-between items-center w-full mb-4">

    <p class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> Hologram: </p>

    <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
        @handle-click="() => isEdit = !isEdit">
        <template v-slot:svg>
        <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
            d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
            fill="white" />
        </svg>
        </template>
    </Button>

    <div v-if="isEdit" class="flex justify-start items-center gap-3">
        <Button title="Reset" type="button" theme="secondary" @handle-click="() => isEdit = !isEdit" :disabled="hologramSettingsLoader">
        </Button>
        <label for="editHologramSettings"
        :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer',(hologramSettingsLoader && `pointer-events-none opacity-50`)]">
        <div v-if="hologramSettingsLoader" class="loader"></div>
        Save </label>
    </div>

    </div>

    <!-- View -->
    <div v-if="!isEdit" class="w-full mt-3 mb-5 ">
    <div class="grid grid-cols-3 gap-8">
    <div class="flex flex-col justify-start items-start gap-2 w-full">
        <label class="font-semibold text-sm text-txt-50"> Thumbnail: </label>
        <div v-if="previousData?.thumbnail" class="w-52 h-52 flex justify-center items-center">
        <img :src="previousData?.thumbnail" class="h-full w-full" alt="">
        </div>
        <p v-else> - </p>
    </div>

    <div class="flex flex-col justify-start items-start gap-2 w-full">
        <label class="font-semibold text-sm text-txt-50"> Logo: </label>
        <div v-if="previousData?.project_logo" class="w-52 h-52 flex justify-center items-center">
          <img :src="previousData?.project_logo" class="h-full w-full" alt="">
        </div>
        <p v-else> - </p>
    </div>

    <div class="flex flex-col justify-start items-start gap-2 w-full">
        <label class="font-semibold text-sm text-txt-50"> Amount: </label>
        <p class="font-medium text-sm text-txt-default"> {{
        previousData?.amount ?
            previousData.amount : '-' }} </p>
    </div>

    <div class="flex flex-col justify-start items-start gap-2 w-full">
        <label class="font-semibold text-sm text-txt-50"> Bedroom: </label>
        <div class="flex-wrap" v-if="brRef">
        <span v-for="item,index in brRef" :key="index" class="font-medium text-sm text-txt-default">{{ item }}{{ index === brRef.length-1 ? "" : ", "}}</span>
      </div>
    </div>

    <div class="flex flex-col justify-start items-start gap-2 w-full">
        <label class="font-semibold text-sm text-txt-50"> Project Location: </label>
        <p class="font-medium text-sm text-txt-default"> {{
        previousData?.project_location ?
            previousData.project_location : '-' }} </p>
    </div>

    <div class="flex flex-col justify-start items-start gap-2 w-full">
        <label class="font-semibold text-sm text-txt-50"> Project Type: </label>
        <p class="font-medium text-sm text-txt-default"> {{
        previousData?.project_type ?
            previousData.project_type : '-' }} </p>
    </div>

    <div class="flex flex-col justify-start items-start gap-2 w-full">
        <label class="font-semibold text-sm text-txt-50"> File: </label>
        <a v-if="shortFileUrl" :href="shortFileUrl" class="text-blue-500">{{ shortFileUrl }}</a>
    </div>

    <div class="flex flex-col justify-start items-start gap-2 mt-3">
    <label class="font-semibold text-sm text-txt-50"> Tags: </label>
    <div class="justify-start items-center gap-2 inline-flex flex-wrap" >
        <template v-if="previousData?.tags?.length > 0">
            <span
            class="font-medium text-sm text-txt-default bg-transparent border-[1px] p-2 rounded-3xl border-[grey]  flex justify-start items-center gap-2"
            v-for="item, index in previousData?.tags"
            :key="index">
                <span class="w-4 h-4" :style="`background:rgb(${item.color})`"></span>
                {{ item.name }}
            </span>
        </template>
    </div>
    </div>

</div>

    </div>

    <!-- Form -->
    <Form v-else class="grid grid-cols-3 gap-3 mt-3 w-full"
    @submit="val => handleSubmit(val)"
    :initial-values="initialData" :validation-schema="hologramSettingsSchema">

        <div class="relative">
                        <label for="type"
                            class="label-primary">
                            Type</label>
                        <Field
                            v-model="type"
                            as="select" type="text"
                            name="type" id="type"
                            autocomplete="type"
                            class="select-primary"
                            :placeholder="`Seclect Type`">
                            <option value="" disabled>
                                Choose
                            </option>
                            <option value="" disabled
                                v-if="!assetsType">
                                No Type found ! </option>
                            <option v-else
                                :value="option"
                                v-for="option, index in  assetsType"
                                :key="index"
                                class="text-black">
                                {{
                                    option }} </option>
                        </Field>
                        <ErrorMessage as="p"
                            class="text-sm text-rose-500 mt-1"
                            name="type" />
        </div>
        <div class="relative">
                        <label for="file"
                            class="label-primary">Upload {{ type }}
                            File</label>
                        <div class="mt-2">
                            <Field type="file"
                                :model-value="hologramFile"
                                name="file"
                                id="file"
                                autocomplete="highRes"
                                class="input-primary"
                                placeholder="Upload High Resulation Image" />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="file" />
                        </div>
        </div>
        <div class="relative">
                        <label for="project_location"
                            class="label-primary">
                            location</label>
                        <Field as="input" type="project_location"
                            name="project_location" autocomplete
                            id="project_location"
                            class="input-primary"
                            :placeholder="`Enter Location`" />
                        <ErrorMessage as="p"
                            class="text-sm text-rose-500 mt-1"
                            name="project_location" />
        </div>
        <div class="relative">
                        <label for="amount"
                            class="label-primary">
                            Amount</label>
                        <Field as="input" type="text"
                            name="amount" autocomplete
                            id="amount"
                            class="input-primary"
                            :placeholder="`Enter amount`" />
                        <ErrorMessage as="p"
                            class="text-sm text-rose-500 mt-1"
                            name="amount" />
        </div>
        <div class="relative">
                        <label for="project_type"
                            class="label-primary">Project Type</label>
                        <div class="mt-2">
                        <Field  name="project_type" v-slot="{ field }" :model-value="typeRef">

                                <Multiselect
                                class="mmulti"
                                v-bind="field"
                                v-model="typeRef"
                                tag-placeholder="Add this as new type"
                                placeholder="Search or add type"
                                :options="projectType"
                                :multiple="false"
                                :taggable="true"
                                @tag="addType"
                                :maxHeight="200" >
                                </Multiselect>

                                </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="project_type" />
                        </div>
        </div>
        <div class="relative">
                        <label for="bedrooms"
                            class="label-primary">BR</label>
                        <div class="mt-2">
                        <Field  name="bedrooms" v-slot="{ field }" :model-value="br">

                                <Multiselect
                                v-bind="field"
                                v-model="br"
                                tag-placeholder="Add this as new BR"
                                placeholder="Search or add BR"
                                :multiple="true"
                                :taggable="true"
                                @tag="addBR"
                                :options="brRef" :maxHeight="250" >
                                </Multiselect>
                                </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="bedrooms" />
                        </div>
        </div>
        <div class="relative">
                        <label for="thumbnail" class="label-primary">Upload Thumbnail</label>
                            <div v-if="holoThumbnail.preview" class="h-24 w-24 flex justify-center items-center">
                                <img :src="holoThumbnail.preview" class="h-full w-full"/>
                            </div>
                        <div class="mt-2">
                            <Field type="file" :model-value="holoThumbnail.fileData"
                                name="thumbnail"
                                id="thumbnail"
                                class="input-primary"
                                placeholder="Upload Low Resulation Image"
                                @change="(e)=>{handleUploadImage(e,'thumbnail')}"
                            />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="thumbnail" />
                        </div>
        </div>
        <div class="relative">
                        <label for="project_logo" class="label-primary">Upload Logo</label>
                            <div v-if="holoLogo.preview" class="h-24 w-24 flex justify-center items-center">
                                <img :src="holoLogo.preview" class="h-full w-full"/>
                            </div>
                        <div class="mt-2">
                            <Field type="file" :model-value="holoLogo.fileData"
                                name="project_logo"
                                id="project_logo"
                                class="input-primary"
                                placeholder="Upload Low Resulation Image"
                                @change="(e)=>{handleUploadImage(e,'logo')}"
                                />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="project_logo" />
                        </div>
        </div>

      <div class="col-span-2">
                    <label for="video" class="label-primary"><b>Tags :</b></label>
                    <Field name="tags" :model-value="tagNames" v-slot="{ field }">
                        <div class="flex flex-col justify-start items-start gap-2 w-full">
                        <div v-if="tagNames.length > 0" v-bind="field">
                                <div class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-24 overflow-y-auto " >
                                        <template v-if="tagNames.length > 0">
                                        <span
                                            class="font-medium text-sm text-txt-default bg-transparent border-[1px] p-2 rounded-3xl border-[grey] flex justify-start items-center gap-1"
                                            v-for="slots, index in tagNames"
                                            :key="index">
                                            <span class="h-4 w-4" :style="`background:rgb(${slots.color})`"></span>
                                            {{ slots.name }}
                                            <XMarkIcon class="text-red-600 w-5 cursor-pointer" @click="() => tagNames.splice(index,1)" />
                                        </span>
                                        </template>
                                </div>
                        </div>
                        <div class="flex justify-start items-end gap-2 w-full">

                        <div class="w-full">
                                <label for="tag"
                                    class="label-primary">
                                    Tag Name</label>
                                <Field as="input" type="text" v-model="tagEnter"
                                    autocomplete
                                    name="tag"
                                    id="tag"
                                    class="input-primary"
                                    :placeholder="`Enter tag`" />
                        </div>

                        <div class="w-full">
                            <label for="color"
                                class="label-primary">
                                Tag Color</label>
                            <Field as="input" type="color" v-model="color"
                                name="color" autocomplete
                                id="color"
                                class="input-primary"/>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="color" />
                        </div>

                        <Button type="button" class="w-fit h-full" title="Add Tag" @handle-click="handleTagAddtion(tagEnter,color)" >
                            <template v-slot:svg>
                                <PlusIcon class="w-5" />
                            </template>
                        </Button>
                        </div>
                        </div>
                    </Field>
                    <ErrorMessage name="tags"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 mt-2">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                        </svg>
                        <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>
                </div>

             <Button id="editHologramSettings" class="hidden" title="Submit" type="submit" theme="primary"> </Button>

    </Form>

    </div>

</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}

.loader{
  @apply w-6 h-6 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-4 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
  }

  @-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

</style>

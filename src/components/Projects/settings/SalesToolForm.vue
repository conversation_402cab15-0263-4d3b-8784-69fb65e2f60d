<script setup>
import { ref } from 'vue';
import { PlusIcon, XMarkIcon } from '@heroicons/vue/20/solid';
import { UpdateProjectSettings } from '../../../api/projects/settings/index.ts';
import { SalesToolSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import Multiselect from 'vue-multiselect';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const SalestoolList = ['default', 'pixel_streaming', 'ale'];
const initialData = ref(null);
const default_experience = ref(null);
const enterTag = ref('');
const projectTags = ref([]);

/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const handleAddTag = (tag) => {
  if (tag === '') {
    return;
  }
  projectTags.value.push(tag);
  enterTag.value = '';
};

const setupDataCallBack = (values) => {
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      is_enabled: data.projectSettings?.salestool?.is_enabled ? data.projectSettings?.salestool?.is_enabled : false,
      default_experience: (data.projectSettings?.salestool?.default_experience ? data.projectSettings.salestool.default_experience : null),
      tags: data.projectSettings?.salestool?.tags ? data.projectSettings?.salestool?.tags : null,
    };

    // Form Initial Values
    initialData.value = {
      is_enabled: (data.projectSettings?.salestool?.is_enabled ? data.projectSettings?.salestool?.is_enabled : false),
    };

    if (data.projectSettings?.salestool?.default_experience) {
      default_experience.value = (data.projectSettings.salestool.default_experience);
    }
    if (data.projectSettings?.salestool?.tags) {
      projectTags.value = [...data.projectSettings.salestool.tags];
    }

  }
};

const handleSubmit = async (val) => {
  return new Promise((resolve) => {
    const prevData = previousData.value; // prevData track source
    const newCompareObj = { ...val }; // form values

    if (Object.keys(frameParms(prevData, newCompareObj)).length > 0) {
      const parms = frameParms(prevData, newCompareObj);

      const payload = {
        project_id: project_Id.value,
        query: {
          [projectSettingsFormTypes.SALESTOOL]: {},
        },
      };

      // Add all fields to the salestool object
      Object.keys(parms).forEach((key) => {
        payload.query.salestool[key] = parms[key];
      });

      UpdateProjectSettings(payload).then((res) => {
        if (res){
          projectStore.settings.projectSettings[projectSettingsFormTypes.SALESTOOL] = res.projectSettings[projectSettingsFormTypes.SALESTOOL]; // update to store
          setupDataCallBack(res); // update the values
          resolve(res);
        }
      });
    } else {
      resolve();
    }

  });
};

// Initialize
if (projectStore.settings){
  setupDataCallBack(projectStore.settings);
}

</script>

<template>

            <div class="flex flex-col justify-start items-start my-3">

                <!-- Headers -->
                <div class="flex justify-between items-center w-full mb-4">

                <p class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> Sales Tool Settings: </p>

                <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
                    @handle-click="() => isEdit = !isEdit">
                    <template v-slot:svg>
                    <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                        d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                        fill="white" />
                    </svg>
                    </template>
                </Button>

                <div v-if="isEdit" class="flex justify-start items-center gap-3">
                    <Button title="Reset" type="button" theme="secondary"
                    @handle-click="() => isEdit = !isEdit"> </Button>
                    <label for="editSalesToolSettings"
                    :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer']">
                    Save </label>
                </div>

                </div>

                <!-- View -->
                <div v-if="!isEdit" class="grid grid-cols-3 gap-8 w-full mt-3 mb-5">

                <div class="flex flex-col justify-start items-start gap-2 w-full">
                    <label class="font-semibold text-sm text-txt-50"> Enabled: </label>
                    <p class="font-medium text-sm text-txt-default"
                    v-if="previousData?.is_enabled"> {{
                        previousData?.is_enabled }} </p>
                    <p v-else class="font-medium text-sm text-txt-default">
                    -
                    </p>
                </div>

                <div class="flex flex-col justify-start items-start gap-2 w-full">
                    <label class="font-semibold text-sm text-txt-50"> Initial Experience Type: </label>
                    <p class="font-medium text-sm text-txt-default capitalize"> {{
                    previousData?.default_experience ?
                       previousData.default_experience : '-' }} </p>
                </div>

                <div class="flex flex-col justify-start items-start gap-2 w-full">
                    <label class="font-semibold text-sm text-txt-50"> Tags: </label>
                    <div class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-40 overflow-y-auto ">
                    <template v-if="previousData?.tags.length > 0">
                        <span
                        class="font-medium text-sm text-txt-default bg-transparent border-[1px] p-2 rounded-3xl border-[grey]  flex justify-start items-center gap-2"
                        v-for="slots, index in previousData?.tags" :key="index">
                        {{ slots }}
                        </span>
                    </template>
                    <span v-else>
                        -
                    </span>
                    </div>
                </div>

                </div>

                <!-- Form -->
                <Form v-else class="grid grid-cols-3 gap-3 mt-3 w-full"
                @submit="(val) => handleSubmit(val).then(() => isEdit = false)"
                :initial-values="initialData" :validation-schema="SalesToolSettingsSchema">

                <div class="relative">

                    <Field v-slot="{ field }" name="is_enabled" type="checkbox" :value="true" :unchecked-value="false">
                    <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                        <input type="checkbox" name="is_enabled" v-bind="field" :value="true" />
                        Enable <strong>*</strong>
                    </label>
                    </Field>

                    <ErrorMessage name="is_enabled" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>

                </div>
                <div class="relative">

                    <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2"> Initial Experience Type
                    <strong>*</strong> </label>

                    <Field name="default_experience" :model-value="default_experience" v-slot="{ field }">

                    <Multiselect :allow-empty="false" v-bind="field" v-model="default_experience" :searchable="false"
                        :close-on-select="true" :show-labels="false" placeholder="Choose" :options="SalestoolList"
                        maxHeight="250">
                    </Multiselect>

                    </Field>

                    <ErrorMessage name="default_experience" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                        fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>

                </div>

                <div class="relative">
                    <label  class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2">Tags <strong>*</strong> </label>
                    <Field name="tags" :model-value="projectTags" v-slot="{ field }">
                                            <div v-if="projectTags.length > 0" v-bind="field">
                                                <div class="justify-start items-center gap-2 inline-flex flex-wrap min-h-[auto] max-h-40 overflow-y-auto" >
                                                    <template v-if="projectTags.length > 0">
                                                        <span
                                                        class="font-medium text-sm text-txt-default bg-transparent border-[1px] p-2 rounded-3xl border-[grey]  flex justify-start items-center gap-2"
                                                        v-for="slots, index in projectTags"
                                                        :key="index">
                                                            {{ slots }}
                                                        <XMarkIcon class="text-red-600 w-5 cursor-pointer" @click="() => projectTags.splice(index,1)" />
                                                        </span>
                                                    </template>
                                                </div>
                                            </div>
                                                <div class="flex flex-wrap gap-1 items-center mt-2">
                                                <Field  as="input" type="text" v-model="enterTag" name="tag" class="customField flex rounded-lg h-10 transition-all duration-[0.3s] ease-in-out px-3 py-0 border-[1px] border-bg-700 focus:border-bg-default"></Field>
                                                <Button type="button" class="w-fit" title="Add Tag" @handle-click="handleAddTag(enterTag)" >
                                                    <template v-slot:svg>
                                                        <PlusIcon class="w-5" />
                                                    </template>
                                                </Button>
                                                </div>
                    </Field>
                    <ErrorMessage name="tags"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 ">
                                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                            </svg>
                                            <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                    </ErrorMessage>
                </div>

                <Button id="editSalesToolSettings" class="hidden" title="Submit" type="submit" theme="primary"> </Button>

                </Form>

            </div>
</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}
</style>

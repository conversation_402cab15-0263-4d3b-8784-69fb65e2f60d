<script setup>
import { onMounted, ref } from 'vue';
import { UpdateProjectSettings, GetApplicationList} from '../../../api/projects/settings/index.ts';
import { ClipboardDocumentCheckIcon } from '@heroicons/vue/24/outline';
import { projectPixelStreamingSettingsSchema } from '../../../validationSchema/project/settings';
import { useRoute } from 'vue-router';
import Button from '../../common/Button.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { projectSettingsFormTypes } from '../../../enum.ts';
import { ProjectStore } from '../../../store/project.ts';
import FormField from '../../common/FormField.vue';
import Multiselect from 'vue-multiselect';

const route = useRoute();
const project_Id = ref(route.params.project_id); // Project id
const projectStore = ProjectStore();
const previousData = ref(null); // Previous data
const isEdit = ref(false);
const initialData = ref(null);
const autoScale = ref(false), records = ref(null), arrayOfRecords=ref(null), selected_applicationId=ref(null);

/* Methods */
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const handleClipBoardCopy = (id, copyMsgId, type) => {

  if (type === 'edit') {
    if (document.getElementById(id).value) {
      navigator.clipboard.writeText(document.getElementById(id).value);
    }
  } else {
    if (document.getElementById(id).innerHTML) {
      navigator.clipboard.writeText(document.getElementById(id).innerHTML);
    }
  }
  const messageDOM = document.getElementById(copyMsgId);
  messageDOM.style.visibility = 'visible';
  setTimeout(() => {
    messageDOM.style.visibility = 'hidden';
  }, 1000);

};

const setupDataCallBack = async (values) => {
  if (values) {
    const data = values;

    // Previous Data
    previousData.value =  {
      is_enabled: data.projectSettings?.pixelstreaming?.is_enabled ? data.projectSettings?.pixelstreaming?.is_enabled : false,
      pixel_streaming_endpoint: (data.projectSettings?.pixelstreaming?.pixel_streaming_endpoint ? data.projectSettings?.pixelstreaming?.pixel_streaming_endpoint : null),
      max_concurrent_sessions: (data.projectSettings?.pixelstreaming?.max_concurrent_sessions ? data.projectSettings?.pixelstreaming?.max_concurrent_sessions : null),
      session_duration: (data.projectSettings?.pixelstreaming?.session_duration ? data.projectSettings?.pixelstreaming?.session_duration : null),
      auto_scale: (data.projectSettings?.pixelstreaming?.auto_scale ? data.projectSettings?.pixelstreaming?.auto_scale : false),
      resource_group: (data.projectSettings?.pixelstreaming?.resource_group ? data.projectSettings?.pixelstreaming?.resource_group : ''),
      vm_scaleset_name: (data.projectSettings?.pixelstreaming?.vm_scaleset_name ? data.projectSettings?.pixelstreaming?.vm_scaleset_name : ''),
      min_instances: (data.projectSettings?.pixelstreaming?.min_instances ? data.projectSettings?.pixelstreaming?.min_instances : null),
      application_id: (data.projectSettings?.pixelstreaming?.application_id ? data.projectSettings?.pixelstreaming?.application_id : null),
    };

    // Form Initial Values
    initialData.value = {
      is_enabled: (data.projectSettings?.pixelstreaming?.is_enabled ? data.projectSettings?.pixelstreaming?.is_enabled : false),
      pixel_streaming_endpoint: data.projectSettings?.pixelstreaming?.pixel_streaming_endpoint,
      max_concurrent_sessions: data.projectSettings?.pixelstreaming?.max_concurrent_sessions,
      session_duration: data.projectSettings?.pixelstreaming?.session_duration,
      auto_scale: data.projectSettings?.pixelstreaming?.auto_scale ? data.projectSettings?.pixelstreaming?.auto_scale : false,
      resource_group: data.projectSettings?.pixelstreaming?.resource_group,
      vm_scaleset_name: data.projectSettings?.pixelstreaming?.vm_scaleset_name,
      min_instances: data.projectSettings?.pixelstreaming?.min_instances,
      application_id: data.projectSettings?.pixelstreaming?.application_id,
    };
    if (data.projectSettings?.pixelstreaming?.application_id){
      selected_applicationId.value = arrayOfRecords.value.find((elem) => elem.appliId === data.projectSettings?.pixelstreaming?.application_id);
    }
  }
};

const handleSubmit = async (val) => {
  return new Promise((resolve) => {
    const prevData = previousData.value; // prevData track source
    const newCompareObj = { ...val }; // form values
    newCompareObj.application_id = newCompareObj.application_id?.appliId ? newCompareObj.application_id.appliId : null;

    if (Object.keys(frameParms(prevData, newCompareObj)).length > 0) {
      const parms = frameParms(prevData, newCompareObj);

      const payload = {
        project_id: project_Id.value,
        query: {
          [projectSettingsFormTypes.PIXELSTREAMING]: {},
        },
      };

      // Add all form data to pixelstreaming object
      Object.keys(parms).forEach((key) => {
        payload.query.pixelstreaming[key] = parms[key];
      });

      UpdateProjectSettings(payload).then((res) => {
        if (res){
          projectStore.settings.projectSettings[projectSettingsFormTypes.PIXELSTREAMING] = res.projectSettings[projectSettingsFormTypes.PIXELSTREAMING]; // update to store
          setupDataCallBack(res);  // update the data value
          resolve(res);
        }
      });
    } else {
      resolve();
    }
  });
};

// Initialize
onMounted(async () => {
  await GetApplicationList().then((res) => {
    records.value = res.result.records;
    arrayOfRecords.value = records.value.map((item) => ({
      appliId: item.appliId,
      appliName: item.appliName,
    }));
    if (projectStore.settings){
      setupDataCallBack(projectStore.settings);
    }
  });
});
</script>

<template>

        <div class="flex flex-col justify-start items-start my-3">

            <!-- Headers -->
            <div class="flex justify-between items-center w-full mb-4">

            <p class="text-txt-100 dark:text-txt-650 text-xl font-semibold mb-0"> Pixel Streaming Settings: </p>

            <Button v-if="!isEdit" type="button" title="Edit Settings" theme="primary"
                @handle-click="() => isEdit = !isEdit">
                <template v-slot:svg>
                <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                    d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                    fill="white" />
                </svg>
                </template>
            </Button>

            <div v-if="isEdit" class="flex justify-start items-center gap-3">
                <Button title="Reset" type="button" theme="secondary"
                @handle-click="() => isEdit = !isEdit"> </Button>
                <label for="editPixelStreamingSettings"
                :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 h-10 m-0 cursor-pointer']">
                Save </label>
            </div>

            </div>

            <!-- View -->
            <div v-if="!isEdit" class="grid grid-cols-3 gap-8 w-full mt-3">
            <div class="flex flex-col justify-start items-start gap-2 w-full">
            <label class="font-semibold text-sm text-txt-50"> Enabled: </label>
            <p v-if="previousData?.is_enabled"
                class="font-medium text-sm text-txt-default">
                {{ previousData?.is_enabled }}
            </p>
            <p v-else class="font-medium text-sm text-txt-default">
                -
            </p>
            </div>

            <div class="flex flex-col justify-end items-start gap-2 w-full">
            <label class="font-semibold text-sm text-txt-50"> Pixel Streaming Endpoint: </label>
            <div class="flex justify-start items-center gap-2 relative">
                <p class="font-medium text-sm text-txt-default" id="pixel_streaming_endpoint_View">
                {{ previousData?.pixel_streaming_endpoint || '-' }}
                </p>
                <div class="relative">
                <ClipboardDocumentCheckIcon class="w-8 cursor-pointer text-zinc-900 p-[4px] border-l border-zinc-400"
                    @click="handleClipBoardCopy('pixel_streaming_endpoint_View', 'copyMsg_ViewPixelStream', 'view')" />
                <span id="copyMsg_ViewPixelStream" class="copyMsgBox -left-3" style="visibility: hidden;">
                    Copied
                </span>
                </div>
            </div>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
            <label class="font-semibold text-sm text-txt-50"> Max Concurrent Sessions: </label>
            <p class="font-medium text-sm text-txt-default">
                {{ previousData?.max_concurrent_sessions || '-' }}
            </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
            <label class="font-semibold text-sm text-txt-50"> Session Duration: </label>
            <p class="font-medium text-sm text-txt-default">
                {{ previousData?.session_duration || '-' }}
            </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
            <label class="font-semibold text-sm text-txt-50"> Auto Scale: </label>
            <p class="font-medium text-sm text-txt-default">
                {{ previousData?.auto_scale ? 'Yes' : 'No' }}
            </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
            <label class="font-semibold text-sm text-txt-50"> Resource Group: </label>
            <p class="font-medium text-sm text-txt-default">
                {{ previousData?.resource_group || '-' }}
            </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
            <label class="font-semibold text-sm text-txt-50"> Scaleset Name: </label>
            <p class="font-medium text-sm text-txt-default">
                {{ previousData?.vm_scaleset_name || '-' }}
            </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
            <label class="font-semibold text-sm text-txt-50"> Minimum Instances: </label>
            <p class="font-medium text-sm text-txt-default">
                {{ previousData?.min_instances || '-' }}
            </p>
            </div>

            <div class="flex flex-col justify-start items-start gap-2 w-full">
                <label class="font-semibold text-sm text-txt-50"> Application Id</label>
                    <p class="font-medium text-sm text-txt-default">
                        {{ previousData?.application_id ?previousData?.application_id  : '-' }}
                    </p>
            </div>
            </div>

            <!-- Form -->
            <Form v-else class="grid grid-cols-3 gap-3 mt-3 w-full"
            @submit="(val) => handleSubmit(val).then(() => isEdit = false)"
            :initial-values="initialData" :validation-schema="projectPixelStreamingSettingsSchema">

            <div class="relative">
            <Field v-slot="{ field }" name="is_enabled" type="checkbox" :value="true" :unchecked-value="false">
                <label class="bg-white cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                <input type="checkbox" name="is_enabled" v-bind="field" :value="true" />
                Enable <strong>*</strong>
                </label>
            </Field>
            <ErrorMessage name="is_enabled" v-slot="{ message }">
                <p class="text-xs text-red-600">{{ message }}</p>
            </ErrorMessage>
            </div>

            <div class="relative flex justify-start items-center gap-2">
            <FormField name="pixel_streaming_endpoint" label="Pixel Streaming Endpoint" required="true" class="w-[80%]" />
            <div class="relative">
                <ClipboardDocumentCheckIcon class="w-8 cursor-pointer text-zinc-900 p-[4px]"
                @click="handleClipBoardCopy('pixel_streaming_endpoint', 'copyMsg_EditPixelStream', 'edit')" />
                <span id="copyMsg_EditPixelStream" class="copyMsgBox -left-3" style="visibility: hidden;">Copied</span>
            </div>
            </div>

            <FormField name="max_concurrent_sessions" type="number" label="Max Concurrent Sessions" required="true" />

            <div class="w-full">
                <FormField name="session_duration" label="Session Duration" type="number" required="true" />
                <span class="italic text-xs text-txt-50 font-medium text-end block">(Only in Minutes)</span>
            </div>

            <div class="relative">
                <Field v-slot="{ field }" name="auto_scale" v-model="autoScale" type="checkbox" :value="true" :unchecked-value="false">
                    <label class="bg-white cursor-text text-sm text-bg-50 font-semibold px-2.5 py-0">
                    <input type="checkbox" name="auto_scale" v-bind="field" :value="true" />
                      Auto Scale
                    </label>
                </Field>
                <ErrorMessage name="auto_scale" v-slot="{ message }">
                    <p class="text-xs text-red-600">{{ message }}</p>
                </ErrorMessage>
            </div>

            <FormField name="resource_group" type="text" label="Resource group" :required="autoScale" />

            <FormField name="vm_scaleset_name" type="text" label="Vm scaleset name group"  :required="autoScale"  />

            <FormField name="min_instances" type="number" label="Min instances" :required="autoScale"  />

            <div class="flex flex-col justify-start items-start gap-1 relative">

                <label class="bg-white  cursor-text text-sm text-bg-50 font-semibold py-0 mb-2 text-nowrap"> Application Id
                </label>

                <Field name="application_id" v-slot="{ field }" :model-value="selected_applicationId">
                    <Multiselect class="multi" v-bind="field" v-model="selected_applicationId"
                        placeholder="Search" :options="arrayOfRecords" :multiple="false" :maxHeight="200" label="appliName" track-by="appliId"
                        :custom-label="appliId" >
                    </Multiselect>
                </Field>

                <ErrorMessage name="application_id" as="p" v-slot="{ message }"
                    class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fill-rule="evenodd" clip-rule="evenodd"
                            d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                            fill="#B3261E" />
                    </svg>
                    <span class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>

            </div>

            <Button id="editPixelStreamingSettings" class="hidden" title="Submit" type="submit" theme="primary">
            </Button>

            </Form>

        </div>

</template>

<style>
.multiselect__content .multiselect__element .multiselect__option {
  text-transform: capitalize;
}

.multiselect__single {
  text-transform: capitalize;
}

/* Copy */
.copyMsgBox {
  background-color: #323232;
  color: white;
  position: absolute;
  margin-top: 10px;
  padding: 10px;
  font-size: 14px;
  border-radius: 30px;
  transition: all 900ms linear;

}

.copyMsgBox::before {
  content: '';
  position: absolute;
  left: 22px;
  top: -5px;
  display: block;
  width: 15px;
  height: 15px;
  background-color: #323232;
  border-top-left-radius: 4px;
  transform: rotate(45deg);
}

</style>

<script setup>
import { onMounted, ref, computed } from 'vue';
import { tableViewColumns } from '../../../enum.ts';
import { Org_Store } from '../../../store/organization';
import { formatTimestamp } from '@/helpers/helpers';

const Organizationstore = Org_Store();
const emits = defineEmits(['pageChange']);
const props = defineProps({
  data: Object,
  totalSession: Number,
  limit: Number,
  currentPage: Number,
});

const columnData = ref(null);
const rowData = ref([]);
const accordionState = ref({}); // Store accordion toggle state for each row
console.log(props.currentPage, props.limit);
const toggleAccordion = (id) => {
  // Toggle accordion state for the clicked row ID
  accordionState.value[id] = !accordionState.value[id];
};

const isAccordionOpen = (id) => {
  // Check if the accordion is open for the given row ID
  return accordionState.value[id];
};
// Computed property to check if a column should be shown
const shouldShowColumn = (col) => {
  const excludedColumns = ['_id', 'description', '__v', 'end_time', 'schedule_time', 'is_scheduled', 'last_interaction_time', 'organization_id', 'code', 'status', 'start', 'invite_link', 'source', 'is_pixelstreaming_active'];
  return !excludedColumns.includes(col.toLowerCase());
};

const formatDuration = (minutes) => {
  const hrs = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  const secs = Math.round((minutes % 1) * 60);

  let result = '';
  if (hrs > 0) {
    result += `${hrs}h `;
  }
  if (mins > 0 || hrs > 0) {
    result += `${mins}m `;
  }
  result += `${secs}s`;

  return result.trim();
};

const totalPages = computed(() => Math.ceil(props.totalSession / props.limit));

const changePage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    emits('pageChange', page);
  }
};

const visiblePages = computed(() => {
  const total = totalPages.value;
  const current = props.currentPage;
  const pages = [];

  // Always show first page
  pages.push(1);

  // Add "..." if current > 4
  if (current > 4) {
    pages.push('...');
  }

  // Add neighbors: current -1, current, current +1
  for (let i = current - 1; i <= current + 1; i++) {
    if (i > 1 && i < total) {
      pages.push(i);
    }
  }

  // Add "..." if current < total - 3
  if (current < total - 3) {
    pages.push('...');
  }

  // Always show last page (only if more than 1 page)
  if (total > 1) {
    pages.push(total);
  }

  return pages;
});

onMounted(() => {
  if (props.data) {
    if (Object.keys(props.data).length > 0) {
      const DTOData = Object.keys(props.data)[0];
      const columns = Object.getOwnPropertyNames(props.data[DTOData]);
      if (!columns.includes('tag')) {
        columns.push('tag');
      }
      // Replace columnData with modified column names
      // Rename columns and add 'Date' and 'Time' columns
      const updatedColumns = ['Date & Time', ...columns.map((column) => {
        switch (column) {
          case 'user_id':
            return 'Sales Executive';
          case 'duration_minutes':
            return 'Duration';
          case 'type':
            return 'Session Type';
          case 'project_id':
            return 'Project';
          case 'tag':
            return 'Tag';
          default:
            return column;
        }
      })];

      columnData.value = updatedColumns;
      rowData.value = Object.values(props.data);
      rowData.value.forEach((row) => {
        const startDateTime = new Date(row.start); // Assuming 'start' is your ISO string field
        row['Date & Time'] = formatTimestamp(startDateTime);
        // Map values to updated column names
        updatedColumns.forEach((column) => {
          switch (column) {
            case 'Sales Executive': {
              const userId = row.user_id;
              const userName = Organizationstore.users[userId]
                ? Organizationstore.users[userId].first_name
                  ? Organizationstore.users[userId].first_name
                  : Organizationstore.users[userId].email
                : userId;
              row[column] = userName;
              break;
            }
            case 'Session Type': {
              const type = row.type.replace(/_/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
              row[column] = type;
              break;
            }
            case 'Project': {
              const projectId = row.project_id;
              const projectName = Organizationstore?.projects?.projectsObj[projectId]
                ? Organizationstore?.projects?.projectsObj[projectId]?.name
                : projectId;
              row[column] = projectName;
              break;
            }
            case 'Tag': {
              row[column] = row.tag || ' -';
              break;
            }
            case 'Duration': {
              row[column] = row?.duration_minutes ? formatDuration(row.duration_minutes) : '0s';
              break;
            }
            case 'scheduled_end_time': {
              console.log(row?.scheduled_end_time);
              row[column] = row?.scheduled_end_time ? formatTimestamp(row?.scheduled_end_time) : '';
              break;
            }
          }
        });
        return row;
      });
    }
  }
});
console.log(props.data);
</script>

<template>

  <div
    class="w-full bg-bg-1000 dark:bg-bg-150 rounded-tl-2xl rounded-tr-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden overflow-x-auto mt-4">
    <table class="w-full rounded-2xl bg-transparent">
      <thead>
        <tr class="bg-gray-100 dark:bg-bg-150">
          <th v-bind:key="item" v-for="item in columnData" v-show="shouldShowColumn(item)"
        class="bg-transparent p-[16px] text-txt-500 dark:text-txt-650 text-xs uppercase font-semibold whitespace-nowrap h-12">
        {{ item }}
      </th>
        </tr>
      </thead>
      <tbody>
        <tr :key="id" v-for="(item, id) in rowData"
          class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50 relative border-b-[1px] h-12">
          <template v-if="Object.keys(item).some(col => shouldShowColumn(col))">
          <td :key="col" class="bg-transparent px-[24px] py-[16px] align-baseline" v-for="col in columnData"
           v-show="shouldShowColumn(col)">
            <span class="flex justify-start items-center">
              <!-- Your existing content -->
              <!-- Check if the current column is 'participants' -->
              <template v-if="col.toLowerCase() === 'participants'">
                <button @click="toggleAccordion(id)" v-if="item[col].length > 0"
                  class="w-max text-blue-500 dark:text-blue-400 text-sm">View Participants</button>
                <p class="w-max" v-else>No participants</p>
                <!-- Render accordion content if the toggle state matches the current row ID -->
                <div v-if="isAccordionOpen(id)"
                  class="fixed inset-0 z-50 flex justify-center items-center">
                  <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                    <div class="absolute inset-0 bg-black opacity-75"></div>
                  </div>
                  <div
                    class="relative z-50 h-fit max-h-[80vh] bg-white dark:bg-bg-1000 rounded-lg w-full sm:max-w-3xl mx-auto shadow-xl flex overflow-hidden">
                    <div class="p-4 max-h-full flex flex-col w-full">
                      <div class="flex justify-between items-center">
                        <p class="font-bold text-lg">Participants</p>
                        <button @click="toggleAccordion(id)"
                          class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none">
                          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M6 18L18 6M6 6l12 12"></path>
                          </svg>
                        </button>
                      </div>
                      <div class="mt-4 overflow-x-auto overflow-y-auto h-full divide-y border border-black rounded-2xl">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-bg-200">
                          <!-- Table headers -->
                          <thead>
                            <tr class="bg-gray-50 dark:bg-bg-700">
                              <th scope="col"
                                class="px-4 py-3 text-nowrap text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Participant Name</th>
                              <th scope="col"
                                class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Email</th>
                              <th scope="col"
                                class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Phone</th>
                              <th scope="col"
                                class="px-4 py-3 text-nowrap text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Duration in minutes</th>
                            </tr>
                          </thead>
                          <!-- Table body -->
                          <tbody class="bg-white divide-y divide-gray-200 dark:bg-bg-800 dark:divide-bg-200">
                            <tr v-for="(participant, index) in item[col]" :key="index"
                              class="hover:bg-gray-100 dark:hover:bg-bg-700">
                              <td
                                class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-200">
                                {{ participant.name }}</td>
                              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{
            participant.email }}</td>
                              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{
            participant.phone_number }}</td>
                              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{
           formatDuration(participant.duration_minutes) }}</td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <!-- Render other columns normally -->
              <template v-else-if="col.toLowerCase() === 'tag'">
                <p class="bg-yellow-100 text-yellow-800 text-sm font-medium me-2 px-2.5 py-0.5 rounded-sm dark:bg-yellow-900 dark:text-yellow-300">
                  {{ item[col] }}
                </p>
              </template>
              <template v-else>
                <!-- Your existing content for other columns -->
                <span class="flex justify-start items-center gap-1">
                  <p class="text-sm font-normal text-txt-50 dark:text-txt-950 whitespace-nowrap"
                    v-if="col.toLowerCase() !== tableViewColumns[3] && col.toLowerCase() !== tableViewColumns[2]">
                    {{ item[col] }}
                  </p>

                </span>
              </template>
            </span>
          </td>
        </template>
        </tr>

      </tbody>
    </table>
  </div>
  <div v-if="Array.isArray(data) && data.length"
    class="w-full rounded-bl-2xl rounded-br-2xl sm:border-[1px] flex items-center justify-center sm:justify-end md:justify-between gap-0 sm:gap-y-5 md:gap-0 p-4 sm:flex-col md:flex-row"
  >
    <div class="sm:block hidden w-fit text-nowrap">
      <span class="text-gray-500 text-sm font-normal leading-tight">Showing </span>
      <span class="text-gray-900 text-sm font-semibold leading-tight">
        {{ (currentPage - 1) * limit + 1 }} - {{ (currentPage - 1) * limit + rowData.length }}
      </span>
      <span class="text-gray-500 text-sm font-normal leading-tight"> of </span>
      <span class="text-gray-900 text-sm font-semibold leading-tight">{{totalSession}}</span>
    </div>
    <div class="flex items-center w-fit">
      <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1"
        class="px-2.5 sm:px-3 py-2.5 rounded-tl-lg rounded-bl-lg border-2 border-gray-200 disabled:opacity-50">
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7.98974 11.4C7.72508 11.3999 7.47128 11.2862 7.28416 11.0839L3.2922 6.76923C3.1051 6.56694 3 6.29263 3 6.0066C3 5.72058 3.1051 5.44626 3.2922 5.24398L7.28416 0.929282C7.37623 0.826258 7.48635 0.744082 7.60811 0.68755C7.72987 0.631018 7.86082 0.601261 7.99334 0.600016C8.12585 0.598772 8.25726 0.626064 8.37991 0.6803C8.50256 0.734537 8.61399 0.814632 8.70769 0.915912C8.8014 1.01719 8.8755 1.13763 8.92568 1.27019C8.97586 1.40276 9.00111 1.5448 8.99996 1.68802C8.99881 1.83125 8.97128 1.97279 8.91898 2.10439C8.86667 2.236 8.79064 2.35502 8.69532 2.45453L5.40894 6.0066L8.69532 9.55868C8.83485 9.70953 8.92987 9.90171 8.96836 10.1109C9.00685 10.3201 8.98709 10.537 8.91157 10.7341C8.83605 10.9311 8.70817 11.0996 8.54409 11.2181C8.38001 11.3366 8.1871 11.3999 7.98974 11.4Z" fill="#6B7280"/>
        </svg>
      </button>
      <template v-for="page in visiblePages" :key="page">
        <button v-if="page === '...'" disabled class="px-2 text-gray-500">...</button>
        <button
          v-else
          @click="changePage(page)"
          :class="[
            'px-2.5 sm:px-3 py-1 border-2 border-gray-200 tx-sm font-medium',
            page === currentPage ? 'bg-[#E1EFFE] text-[#1A56DB]' : 'text-[#6B7280]',
          ]">
          {{ page }}
        </button>
      </template>
      <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages"
        class="px-2.5 sm:px-3 py-2.5 rounded-tr-lg rounded-br-lg border-2 border-gray-200 disabled:opacity-50">
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M4.01026 11.4C3.8129 11.3999 3.61999 11.3366 3.45591 11.2181C3.29183 11.0996 3.16395 10.9311 3.08843 10.7341C3.01291 10.537 2.99315 10.3201 3.03164 10.1109C3.07013 9.90171 3.16515 9.70953 3.30468 9.55868L6.59106 6.0066L3.30468 2.45453C3.20936 2.35502 3.13333 2.236 3.08102 2.10439C3.02872 1.97279 3.00119 1.83125 3.00004 1.68802C2.99889 1.5448 3.02414 1.40276 3.07432 1.27019C3.1245 1.13763 3.1986 1.01719 3.29231 0.915912C3.38601 0.814632 3.49744 0.734537 3.62009 0.6803C3.74274 0.626064 3.87415 0.598772 4.00666 0.600016C4.13918 0.601261 4.27013 0.631018 4.39189 0.68755C4.51365 0.744082 4.62377 0.826258 4.71584 0.929282L8.7078 5.24398C8.8949 5.44626 9 5.72058 9 6.0066C9 6.29263 8.8949 6.56694 8.7078 6.76923L4.71584 11.0839C4.52872 11.2862 4.27492 11.3999 4.01026 11.4Z" fill="#6B7280"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<style scoped>
/* Your CSS styles */
</style>

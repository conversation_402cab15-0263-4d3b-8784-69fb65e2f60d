<script setup>
import {ref, watch, onMounted, computed} from 'vue';
// import CountShowCaseCard from '../../common/ShowCaseCards/CountShowCaseCard.vue';
// import TimeShowCaseCard from '../../common/ShowCaseCards/TimeShowCaseCard.vue';
import AnalyticsTable from '../analytics/analyticsTable.vue';
import { GetSessionAnalytics } from '../../../api/analytics';
import { Org_Store } from '../../../store/organization';
import { sessionTypes } from '../../../enum';
import { useRouter, useRoute } from 'vue-router';
import LoaderCompVue from '../../common/LoaderComp.vue';
import Button from '../../common/Button.vue';
import CardComponent from '../../../components/UIElements/CardComponent.vue';
import { analytics_store } from '../../../store/analytics';
// import BottomSheet from '../../common/BottomSheet.vue';
// import ToggleButton from '../../common/ToggleButton.vue';
import LabelDropdown from '../../common/LabelDropdown.vue';
import { convertToArray } from '../../../helpers/helpers';
import { UserStore } from '../../../../src/store/index.ts';

const Organizationstore = Org_Store();
Organizationstore.RefreshUsers();
Organizationstore.RefreshProjects();
const router = useRouter();
const route = useRoute();
/* Dump Table View Data */
const analytics = ref(false);
const usagetime = {
  hours: ref(0),
  minutes: ref(0),
  seconds: ref(0),
};

const userOptions = ref([]);
const projectOptions = ref([]);
const sessionOptions = ref([]);
const tagOptions = ref([]);
const selectedProject = ref(null);
const selectedUser = ref(null);
const selectedType = ref(null);
const selectedTag = ref(null);
const Store = analytics_store();
const session_fields = ref(['Date', 'Time', 'Duration', 'Project', 'Sales Executive', 'Session Type', 'Tag', 'Status', 'participants']);
// const showBottomSheet = ref(false);
// const showparticipantslist = ref(false);
// const participantslist = ref([]);
const viewType = ref('table');
const store = UserStore();
const tableViewData = ref([]);
const totalLeads = ref(0);
const totalViews = ref(0);
const total_session = ref(0);
const currentPage = ref(1);
const pageLimit = ref(10);

const formatDuration = (minutes) => {
  if (!minutes) {
    return 'N/A';
  }
  const hrs = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  const secs = Math.round((minutes % 1) * 60);

  let result = '';
  if (hrs > 0) {
    result += `${hrs}h `;
  }
  if (mins > 0 || hrs > 0) {
    result += `${mins}m `;
  }
  result += `${secs}s`;

  return result.trim();
};

const sessionFields = computed(() => [
  { key: 'project_id', label: 'Project' },
  { key: 'participants',
    label: 'participants',
  },
  {
    key: 'schedule_time',
    label: 'Date & Time',
    format: (value) => (value ? `${formatDate(value)} ${formatTime(value)}` : '-'),
  },
  {
    key: 'duration_minutes',
    label: 'Duration',
    format: (minutes) => (minutes ? formatDuration(minutes) : '-'),
  },
  { key: 'status', label: 'Status' },
  {
    key: 'user_id',
    label: 'Agent',
    format: (userId) => (Organizationstore.users && Organizationstore.users[userId]
      ? Organizationstore.users[userId].name || Organizationstore.users[userId].email
      : userId || '-'),
  },
]);

// const selectedFilterValues = ref({
//   users: [],
//   tags: [],
//   project: [],
// });

function formatLabel (option) {
  if (!option) {
    return '';
  }

  if (option.first_name) {
    if (option.role === 'admin') {
      return `${option.first_name} (admin)`;
    }
    return `${option.first_name}`;
  } else if (option.email) {
    if (option.role === 'admin') {
      return `${option.email} (admin)`;
    }
    return `${option.email}`;
  }
  return 'Unknown';
}

function getFilteredUsers (users) {
  if (!users) {
    return [{ first_name: 'Users', label: 'All' }];
  }
  const filteredUsers = Object.values(users || {});
  for (let i=0; i<filteredUsers.length; i++){
    filteredUsers[i].label = formatLabel(filteredUsers[i]);
  }

  return [{ first_name: 'Users', label: 'All' }, ...filteredUsers];
}

// const filterOptions = computed(() => ({
//   users: Organizationstore.users ? getFilteredUsers(Organizationstore.users).map((user) => ({
//     id: user.user_id || user.email || 'all',
//     label: user.first_name === 'All' ? 'All' : formatLabel(user),
//     value: user,
//   })) : [],
//   tags: tagOptions.value ? tagOptions.value.map((tag) => ({
//     id: tag.value,
//     label: tag.value,
//     value: tag,
//   })) : [],
//   project: Organizationstore.projects
//     ? [{ id: 'all', label: 'All ', value: { name: 'All', label: 'All' } },
//       ...Object.values(Organizationstore.projects).map((project) => ({
//         id: project._id || '',
//         label: project.name || 'Unnamed Project',
//         value: project,
//       })),
//     ] : [],
// }));

// const filterTabs = ref([
//   {
//     id: 'users',
//     label: 'By user',
//     icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
//       <path d="M8 8C10.21 8 12 6.21 12 4C12 1.79 10.21 0 8 0C5.79 0 4 1.79 4 4C4 6.21 5.79 8 8 8ZM8 10C5.33 10 0 11.34 0 14V16H16V14C16 11.34 10.67 10 8 10Z" fill="currentColor"/>
//     </svg>`,
//   },
//   {
//     id: 'project',
//     label: 'Project',
//     icon: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
//         <g id="building-01"> <g id="Group 1000005187">
//         <path id="Vector" d="M3.3335 17H14.5335" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
//         <path id="Vector_2" d="M12.4332 7.90002H10.3333M13.1332 10.7H10.3333M13.1332 13.5H10.3333" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
//         <path id="Vector_3" d="M4.7334 17V3.84C4.7334 3.29669 5.06308 3 5.5734 3C6.74394 3 7.32922 3 7.81892 3.07756C10.5146 3.50452 12.6289 5.61874 13.0558 8.31446C13.1334 8.80418 13.1334 9.38945 13.1334 10.56V17" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g></g></svg>`,
//   },
//   {
//     id: 'tags',
//     label: 'By Tag',
//     icon: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
//             <g id="Frame 1707479938"><path id="Vector" d="M15 8H6M12.1875 13H8.8125" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></g></svg>`,
//   },
// ]);

// const handlefilter = () => {
//   showBottomSheet.value = true;
// };

// const handleParticipantList = (val) => {
//   showparticipantslist.value = true;
//   participantslist.value = val;
// };

// const handleFilterApply = (val) => {
//   const tagValue = val.tags[0]?.id === 'All' ? null : val.tags[0]?.id;
//   const userValue = val.users[0]?.id === 'all' ? null : val.users[0]?.id;
//   const projectValue = val.project[0]?.id === 'all' ? null : val.project[0]?.id;

//   // Construct the query object and filter out null values
//   const query = {
//     ...route.query,
//     user_id: userValue,
//     project_id: projectValue,
//     tag: tagValue,
//   };

//   const filteredQuery = Object.fromEntries(Object.entries(query).filter(([, value]) => value !== null));

//   router.push({
//     path: '/analytics',
//     query: filteredQuery,
//   });
// };

// const handleFilterClear = () => {
//   selectedFilterValues.value = {};
// };

const extractTime = (totalSeconds) => {
  let seconds = totalSeconds;
  usagetime.hours.value = Math.floor(seconds / 3600);
  seconds %= 3600;
  usagetime.minutes.value = Math.floor(seconds / 60);
  seconds %= 60;
  usagetime.seconds.value = Math.round(seconds);
};

// watch(()=>{
//     GetSessionAnalytics(route.query);
// })

async function counteleads (data) {
  totalLeads.value = 0;
  data.forEach((e) => {
    if (e.participants.length){
      e.participants.forEach((e) => {
        if (e.session_id){
          totalLeads.value = totalLeads.value + 1;
        }
      });
    }
  });
  console.log(totalLeads.value);
}

function getAnalyticsData (params){
  // tableData.value = [];
  analytics.value = false;
  Store.analyticsData.sessions = null;
  GetSessionAnalytics(params, currentPage.value, pageLimit.value).then(() => {
    analytics.value = true;
    if (Store.analyticsData) {
      tableViewData.value = Store.analyticsData.sessions;
      extractTime(Store.analyticsData.analytics.total_duration);
      total_session.value = Store.analyticsData.analytics.num_sessions;
      // tableData.value = tableData;
      const uniqueTags = new Set();
      counteleads(tableViewData.value);

      Object.values(Organizationstore.projects.projectsObj).forEach((project) => {
        if (project && project.projectSettings && project.projectSettings.salestool && project.projectSettings.salestool.tags.length){
          project.projectSettings.salestool.tags.forEach((tag) => {
            uniqueTags.add(tag);
          });
        }
      });
      tagOptions.value = [ ...Array.from(uniqueTags).map((tag) => ({ value: tag }))];
    }
  });
}

if (Organizationstore.users){
  userOptions.value.push({ _id: 'all', email: 'All' });
  userOptions.value = Object.values(Organizationstore.users);
  selectedType.value = {'value': route.query.type || 'All'};
  selectedTag.value = {'value': route.query.tag || 'All'};
  selectedUser.value =  Organizationstore.users[route.query.user_id] || { id: 'all', email: 'All' };
  getAnalyticsData(route.query);

}
watch(() => Organizationstore.users, () => {
  userOptions.value.push({ _id: 'all', email: 'All' });
  Object.values(Organizationstore.users).forEach((user) => {
    userOptions.value.push(user);
  });
  getAnalyticsData(route.query);
  selectedType.value = {'value': route.query.type || 'All'};
  selectedUser.value =  Organizationstore.users[route.query.user_id] || { id: 'all', email: 'All' };
});
if (Organizationstore.projects){
  projectOptions.value.push({ id: 'all', name: 'All' });
  projectOptions.value = Object.values(Organizationstore.projects);
  selectedProject.value = Organizationstore.projects[route.query.project_id] || { id: 'all', name: 'All' } ;
}

watch(() => Organizationstore.projects, () => {
  projectOptions.value.push({ id: 'all', name: 'All' });
  if (Organizationstore.projects) {
    Object.values(Organizationstore.projects).forEach((project) => {
      projectOptions.value.push(project);
    });
    selectedProject.value = Organizationstore.projects[route.query.project_id] || { id: 'all', name: 'All' } ;
  }
});
watch(() => route.query, () => {
  // GetSessionAnalytics(route.query);
  if (Organizationstore.users) {
    getAnalyticsData(route.query);
  }
});

function handleProjectSelection (project){
  router.push({ path: '/analytics', query: { ...route.query, project_id: project.value._id} });
  selectedProject.value = project.value;
}
function handleUserSelection (user) {
  router.push({ path: '/analytics', query: { ...route.query, user_id: user.user_id} });
  selectedUser.value = user;
}

function handleTagSelection (tag) {
  selectedTag.value = tag.value.value;
  if (tag.value.value === 'All') {
    router.push({ path: '/analytics', query: { ...route.query, tag: null } });
  } else {
    router.push({ path: '/analytics', query: { ...route.query, tag: tag.value.value } });
  }
}

const downloadCSV = () => {
  if (!Store.analyticsData?.sessions?.length) {
    console.warn("No session data available.");
    return;
  }

  const fields = session_fields.value || session_fields;

  let csvContent = 'data:text/csv;charset=utf-8,';
  csvContent += fields.join(',') + '\n';

  // Process each session row
  Store.analyticsData.sessions.forEach((session) => {
    const rowDataArray = fields.map((field) => {
      switch (field) {
        case 'Sales Executive': {
          const userId = session.user_id;
          const user = Organizationstore.users[userId];
          const userName = user
            ? user.first_name || user.email
            : userId;
          return userName;
        }

        case 'Session Type': {
          const type = session.type
            ? session.type.replace(/_/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase())
            : '';
          return type;
        }

        case 'Date': {
          return session.start
            ? new Date(session.start).toLocaleDateString()
            : '';
        }

        case 'Time': {
          return session.start
            ? new Date(session.start).toLocaleTimeString()
            : '';
        }

        case 'Duration': {
          return formatDuration(session.duration_minutes || 0);
        }

        case 'Project': {
          return session.Project || session.project_id || '';
        }

        case 'Tag': {
          return session.tag || '';
        }

        case 'Status': {
          return session.status || '';
        }

        case 'participants': {
          return session.participants
            ? session.participants.map((participant) => `${participant.name} (${participant.email})`).join(', ')
            : '';
        }

        default: {
          const cellData = session[field] !== undefined ? session[field] : '';
          return cellData.includes(',') ? `"${cellData}"` : cellData;
        }
      }
    });

    // Join row data with commas and add to csvContent
    csvContent += rowDataArray.join(',') + '\n';
  });

  // Create an encoded URI for download
  const encodedUri = encodeURI(csvContent);
  const link = document.createElement('a');
  link.setAttribute('href', encodedUri);
  link.setAttribute('download', 'analytics_data.csv');
  document.body.appendChild(link);

  link.click();
  document.body.removeChild(link);
};

// const toggleViewType = () => {
//   viewType.value = viewType.value === 'table' ? 'grid' : 'table';
// };

onMounted(() => {
  sessionOptions.value.push({ _id: 'all', value: 'All' });
  sessionTypes.forEach((val) => {
    sessionOptions.value.push({'value': val});
  });
  getAnalyticsData(route.query);
});
selectedTag.value = { value: route.query.tag || 'All' };

store.callbackFunctionMonitorChanges();

</script>
<template>
    <div class="block pb-10">
            <div class="mb-7">
                <div
                    class="flex gap-3">
                    <div class="w-full flex justify-between gap-10">
                      <div>
                        <div>
                          <p
                              class="text-txt-50 dark:text-txt-1000 text-lg relative font-semibold analyticsTitleActiveBorder pb-2 after:bg-bg-50 after:dark:bg-bg-1000 cursor-pointer">
                              Analytics
                          </p>
                          <p class="text-400 text-xs text-gray-500 text-nowrap">
                            Explore Analytics.
                          </p>
                        </div>
                      </div>
                      <div class="hidden relative gap-x-6 gap-y-3 flex-wrap justify-end sm:flex">
                        <div>
                                      <div v-if="Organizationstore.users" class="flex label sessionAnalytics">
                                          <LabelDropdown :options="getFilteredUsers(Organizationstore.users)" @handleSelect="handleUserSelection"
                                          value="label" width="200px" :selectedOption="route?.query?.user_id ? getFilteredUsers(Organizationstore.users).find(item => item.user_id === route?.query?.user_id) : { first_name: 'All', label:'All' }" />
                                      </div>

                        </div>
                        <div>
                                              <div v-if="Organizationstore.projects" class="flex label sessionAnalytics">
                                                <LabelDropdown :options="convertToArray(Organizationstore.projects.projectsObj,'project')" @handleSelect="handleProjectSelection"
                                                value="label" width="200px" :selectedOption="route?.query?.project_id ? convertToArray(Organizationstore.projects,'project').find(item => item.value._id === route?.query?.project_id) : {id:'all', label:'All'}"/>
                                            </div>
                        </div>
                        <div>
                                                  <div v-if="Object.values(tagOptions).length" class="flex label sessionAnalytics">
                                                      <LabelDropdown :options="convertToArray(tagOptions,'tag')" @handleSelect="handleTagSelection"
                                                      value="label" width="200px" :selectedOption="{label:selectedTag.value}"/>
                                                  </div>
                        </div>
                      </div>
                    </div>
                    <!-- <p
                        class="text-txt-500 text-xl relative font-semibold pb-2 cursor-pointer">
                        Project Analytics </p> -->
                </div>
            </div>

            <div class="w-full bg-white shadow-md p-6 rounded-lg mt-4 lg:flex-row flex-col flex gap-6 lg:gap-0">
              <div class="flex justify-between">
                <div class="h-20 lg:w-auto w-[176px] flex">
                  <div>
                    <div class="justify-start text-gray-500 text-sm font-normal leading-tight text-nowrap">No. of Sessions Hosted</div>
                    <div class="flex gap-2 items-center">
                      <div class="justify-start text-gray-900 text-2xl font-bold leading-loose">{{ Store?.analyticsData?.analytics?.num_sessions }}</div>
                      <div class="text-center justify-center text-green-800 text-xs font-medium leading-none flex gap-1 h-fit items-center bg-green-100 rounded-md px-2.5 py-1">
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5.19488 0.570862L5.19524 0.571205L8.19063 3.42456C8.23191 3.46638 8.25042 3.51682 8.24999 3.56422C8.24955 3.61249 8.22939 3.66382 8.18551 3.70563C8.14083 3.74818 8.07554 3.77626 8.00253 3.77686C7.93073 3.77745 7.86568 3.75136 7.82007 3.7105L6.10468 2.07645L5.25981 1.27165V2.43848V9.28571C5.25981 9.33449 5.2397 9.38649 5.19532 9.42876C5.15012 9.47181 5.08385 9.5 5.00996 9.5C4.93607 9.5 4.8698 9.47181 4.82461 9.42876C4.78023 9.38649 4.76011 9.33449 4.76011 9.28571V2.43848V1.27235L3.91541 2.0763L2.1945 3.71418L2.19445 3.71412L2.1881 3.72037C2.16619 3.74199 2.13865 3.7605 2.10645 3.77368C2.07422 3.78687 2.03872 3.79411 2.00223 3.79442C1.96573 3.79472 1.93005 3.78806 1.89747 3.77536C1.86493 3.76268 1.83692 3.74455 1.81449 3.72318C1.79211 3.70186 1.77581 3.67787 1.76526 3.65329C1.75473 3.62878 1.74978 3.60343 1.75001 3.57849C1.75024 3.55354 1.75565 3.5282 1.76668 3.50375C1.77774 3.47922 1.79456 3.45537 1.81746 3.43431L1.81751 3.43436L1.82379 3.42838L4.82319 0.571205L4.82355 0.570861C4.84531 0.550089 4.87255 0.532424 4.90426 0.520068L4.90426 0.520071L4.90596 0.519405C4.97167 0.493532 5.04676 0.493532 5.11247 0.519405L5.11247 0.519408L5.11416 0.520068C5.14588 0.532424 5.17312 0.55009 5.19488 0.570862Z" fill="#03543F" stroke="#03543F"/>
                        </svg>
                        1.4%
                      </div>
                    </div>
                    <div class="flex items-center gap-1">
                      <div>
                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_1071_20649)">
                            <path d="M6.4778 2.63594L6.20002 3.05168L6.4778 2.63594C6.24757 2.48211 5.9769 2.4 5.7 2.4C5.3287 2.4 4.9726 2.5475 4.71005 2.81005C4.4475 3.0726 4.3 3.4287 4.3 3.8C4.3 4.07689 4.38211 4.34757 4.53594 4.5778C4.59053 4.6595 4.65329 4.7348 4.72297 4.8027C4.45938 4.82119 4.21024 4.93412 4.02219 5.12218C3.81589 5.32847 3.7 5.60826 3.7 5.9C3.7 6.19174 3.8159 6.47153 4.02218 6.67782C4.22847 6.88411 4.50826 7 4.8 7H4.9V7.8H4.8C4.50826 7.8 4.22847 7.91589 4.02218 8.12218C3.8159 8.32847 3.7 8.60826 3.7 8.9C3.7 9.19174 3.81589 9.47153 4.02218 9.67782C4.22848 9.88411 4.50826 10 4.8 10H7.2C7.49174 10 7.77153 9.88411 7.97782 9.67782C8.18411 9.47153 8.3 9.19174 8.3 8.9C8.3 8.60826 8.18411 8.32847 7.97782 8.12218C7.77153 7.91589 7.49174 7.8 7.2 7.8H7.1V5.9C7.1 5.60826 6.98411 5.32847 6.77782 5.12218C6.70189 5.04625 6.61599 4.98256 6.52321 4.9324C6.58218 4.88953 6.63796 4.84194 6.68995 4.78995C6.88575 4.59415 7.01908 4.3447 7.0731 4.07313L6.58271 3.97558L7.0731 4.07313C7.12712 3.80155 7.0994 3.52006 6.99343 3.26424L6.53149 3.45558L6.99343 3.26424C6.88747 3.00843 6.70803 2.78978 6.4778 2.63594ZM2.94437 1.92692C3.84876 1.32262 4.91203 1.00005 5.99973 1C7.45797 1.00167 8.85602 1.5817 9.88716 2.61284C10.9184 3.64405 11.4984 5.0422 11.5 6.50055C11.4999 7.58815 11.1773 8.65132 10.5731 9.55563C9.96874 10.4601 9.10975 11.1651 8.10476 11.5813C7.09977 11.9976 5.9939 12.1065 4.92701 11.8943C3.86011 11.6821 2.8801 11.1583 2.11092 10.3891C1.34173 9.6199 0.817902 8.63989 0.605684 7.57299C0.393465 6.5061 0.502383 5.40023 0.918665 4.39524C1.33495 3.39025 2.0399 2.53126 2.94437 1.92692Z" fill="#9CA3AF" stroke="#9CA3AF"/>
                          </g>
                            <defs>
                              <clipPath id="clip0_1071_20649">
                                <rect width="12" height="12" fill="white" transform="translate(0 0.5)"/>
                              </clipPath>
                            </defs>
                          </svg>
                      </div>
                      <div class="justify-start text-gray-500 text-sm font-normal leading-tight">vs last day</div>
                    </div>
                  </div>
                  <div class="hidden lg:block mx-12 h-20 border border-gray-600"></div>
                </div>
                <div class="h-20 lg:w-auto w-[176px] flex">
                  <div>
                    <div class="justify-start text-gray-500 text-sm font-normal leading-tight">Leads Generated</div>
                    <div class="flex gap-2 items-center">
                      <div class="justify-start text-gray-900 text-2xl font-bold leading-loose">{{ totalLeads }}</div>
                      <div class="text-center justify-center text-green-800 text-xs font-medium leading-none flex gap-1 h-fit items-center bg-green-100 rounded-md px-2.5 py-1">
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5.19488 0.570862L5.19524 0.571205L8.19063 3.42456C8.23191 3.46638 8.25042 3.51682 8.24999 3.56422C8.24955 3.61249 8.22939 3.66382 8.18551 3.70563C8.14083 3.74818 8.07554 3.77626 8.00253 3.77686C7.93073 3.77745 7.86568 3.75136 7.82007 3.7105L6.10468 2.07645L5.25981 1.27165V2.43848V9.28571C5.25981 9.33449 5.2397 9.38649 5.19532 9.42876C5.15012 9.47181 5.08385 9.5 5.00996 9.5C4.93607 9.5 4.8698 9.47181 4.82461 9.42876C4.78023 9.38649 4.76011 9.33449 4.76011 9.28571V2.43848V1.27235L3.91541 2.0763L2.1945 3.71418L2.19445 3.71412L2.1881 3.72037C2.16619 3.74199 2.13865 3.7605 2.10645 3.77368C2.07422 3.78687 2.03872 3.79411 2.00223 3.79442C1.96573 3.79472 1.93005 3.78806 1.89747 3.77536C1.86493 3.76268 1.83692 3.74455 1.81449 3.72318C1.79211 3.70186 1.77581 3.67787 1.76526 3.65329C1.75473 3.62878 1.74978 3.60343 1.75001 3.57849C1.75024 3.55354 1.75565 3.5282 1.76668 3.50375C1.77774 3.47922 1.79456 3.45537 1.81746 3.43431L1.81751 3.43436L1.82379 3.42838L4.82319 0.571205L4.82355 0.570861C4.84531 0.550089 4.87255 0.532424 4.90426 0.520068L4.90426 0.520071L4.90596 0.519405C4.97167 0.493532 5.04676 0.493532 5.11247 0.519405L5.11247 0.519408L5.11416 0.520068C5.14588 0.532424 5.17312 0.55009 5.19488 0.570862Z" fill="#03543F" stroke="#03543F"/>
                        </svg>
                        1.4%
                      </div>
                    </div>
                    <div class="flex items-center gap-1">
                      <div>
                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_1071_20649)">
                            <path d="M6.4778 2.63594L6.20002 3.05168L6.4778 2.63594C6.24757 2.48211 5.9769 2.4 5.7 2.4C5.3287 2.4 4.9726 2.5475 4.71005 2.81005C4.4475 3.0726 4.3 3.4287 4.3 3.8C4.3 4.07689 4.38211 4.34757 4.53594 4.5778C4.59053 4.6595 4.65329 4.7348 4.72297 4.8027C4.45938 4.82119 4.21024 4.93412 4.02219 5.12218C3.81589 5.32847 3.7 5.60826 3.7 5.9C3.7 6.19174 3.8159 6.47153 4.02218 6.67782C4.22847 6.88411 4.50826 7 4.8 7H4.9V7.8H4.8C4.50826 7.8 4.22847 7.91589 4.02218 8.12218C3.8159 8.32847 3.7 8.60826 3.7 8.9C3.7 9.19174 3.81589 9.47153 4.02218 9.67782C4.22848 9.88411 4.50826 10 4.8 10H7.2C7.49174 10 7.77153 9.88411 7.97782 9.67782C8.18411 9.47153 8.3 9.19174 8.3 8.9C8.3 8.60826 8.18411 8.32847 7.97782 8.12218C7.77153 7.91589 7.49174 7.8 7.2 7.8H7.1V5.9C7.1 5.60826 6.98411 5.32847 6.77782 5.12218C6.70189 5.04625 6.61599 4.98256 6.52321 4.9324C6.58218 4.88953 6.63796 4.84194 6.68995 4.78995C6.88575 4.59415 7.01908 4.3447 7.0731 4.07313L6.58271 3.97558L7.0731 4.07313C7.12712 3.80155 7.0994 3.52006 6.99343 3.26424L6.53149 3.45558L6.99343 3.26424C6.88747 3.00843 6.70803 2.78978 6.4778 2.63594ZM2.94437 1.92692C3.84876 1.32262 4.91203 1.00005 5.99973 1C7.45797 1.00167 8.85602 1.5817 9.88716 2.61284C10.9184 3.64405 11.4984 5.0422 11.5 6.50055C11.4999 7.58815 11.1773 8.65132 10.5731 9.55563C9.96874 10.4601 9.10975 11.1651 8.10476 11.5813C7.09977 11.9976 5.9939 12.1065 4.92701 11.8943C3.86011 11.6821 2.8801 11.1583 2.11092 10.3891C1.34173 9.6199 0.817902 8.63989 0.605684 7.57299C0.393465 6.5061 0.502383 5.40023 0.918665 4.39524C1.33495 3.39025 2.0399 2.53126 2.94437 1.92692Z" fill="#9CA3AF" stroke="#9CA3AF"/>
                          </g>
                            <defs>
                              <clipPath id="clip0_1071_20649">
                                <rect width="12" height="12" fill="white" transform="translate(0 0.5)"/>
                              </clipPath>
                            </defs>
                          </svg>
                      </div>
                      <div class="justify-start text-gray-500 text-sm font-normal leading-tight">vs last month</div>
                    </div>
                  </div>
                  <div class="hidden lg:block h-20 mx-12 border border-gray-600"></div>
                </div>
              </div>
              <div class="flex justify-between">
                <div class="h-20 lg:w-auto w-[176px] flex">
                  <div>
                    <div class="justify-start text-gray-500 text-sm font-normal leading-tight">Views</div>
                    <div class="flex gap-2 items-center">
                      <div class="justify-start text-gray-900 text-2xl font-bold leading-loose">{{totalViews}}</div>
                      <div class="text-center justify-center text-green-800 text-xs font-medium leading-none flex gap-1 h-fit items-center bg-green-100 rounded-md px-2.5 py-1">
                        <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M5.19488 0.570862L5.19524 0.571205L8.19063 3.42456C8.23191 3.46638 8.25042 3.51682 8.24999 3.56422C8.24955 3.61249 8.22939 3.66382 8.18551 3.70563C8.14083 3.74818 8.07554 3.77626 8.00253 3.77686C7.93073 3.77745 7.86568 3.75136 7.82007 3.7105L6.10468 2.07645L5.25981 1.27165V2.43848V9.28571C5.25981 9.33449 5.2397 9.38649 5.19532 9.42876C5.15012 9.47181 5.08385 9.5 5.00996 9.5C4.93607 9.5 4.8698 9.47181 4.82461 9.42876C4.78023 9.38649 4.76011 9.33449 4.76011 9.28571V2.43848V1.27235L3.91541 2.0763L2.1945 3.71418L2.19445 3.71412L2.1881 3.72037C2.16619 3.74199 2.13865 3.7605 2.10645 3.77368C2.07422 3.78687 2.03872 3.79411 2.00223 3.79442C1.96573 3.79472 1.93005 3.78806 1.89747 3.77536C1.86493 3.76268 1.83692 3.74455 1.81449 3.72318C1.79211 3.70186 1.77581 3.67787 1.76526 3.65329C1.75473 3.62878 1.74978 3.60343 1.75001 3.57849C1.75024 3.55354 1.75565 3.5282 1.76668 3.50375C1.77774 3.47922 1.79456 3.45537 1.81746 3.43431L1.81751 3.43436L1.82379 3.42838L4.82319 0.571205L4.82355 0.570861C4.84531 0.550089 4.87255 0.532424 4.90426 0.520068L4.90426 0.520071L4.90596 0.519405C4.97167 0.493532 5.04676 0.493532 5.11247 0.519405L5.11247 0.519408L5.11416 0.520068C5.14588 0.532424 5.17312 0.55009 5.19488 0.570862Z" fill="#03543F" stroke="#03543F"/>
                        </svg>
                        1.4%
                      </div>
                    </div>
                    <div class="flex items-center gap-1">
                      <div>
                        <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g clip-path="url(#clip0_1071_20649)">
                            <path d="M6.4778 2.63594L6.20002 3.05168L6.4778 2.63594C6.24757 2.48211 5.9769 2.4 5.7 2.4C5.3287 2.4 4.9726 2.5475 4.71005 2.81005C4.4475 3.0726 4.3 3.4287 4.3 3.8C4.3 4.07689 4.38211 4.34757 4.53594 4.5778C4.59053 4.6595 4.65329 4.7348 4.72297 4.8027C4.45938 4.82119 4.21024 4.93412 4.02219 5.12218C3.81589 5.32847 3.7 5.60826 3.7 5.9C3.7 6.19174 3.8159 6.47153 4.02218 6.67782C4.22847 6.88411 4.50826 7 4.8 7H4.9V7.8H4.8C4.50826 7.8 4.22847 7.91589 4.02218 8.12218C3.8159 8.32847 3.7 8.60826 3.7 8.9C3.7 9.19174 3.81589 9.47153 4.02218 9.67782C4.22848 9.88411 4.50826 10 4.8 10H7.2C7.49174 10 7.77153 9.88411 7.97782 9.67782C8.18411 9.47153 8.3 9.19174 8.3 8.9C8.3 8.60826 8.18411 8.32847 7.97782 8.12218C7.77153 7.91589 7.49174 7.8 7.2 7.8H7.1V5.9C7.1 5.60826 6.98411 5.32847 6.77782 5.12218C6.70189 5.04625 6.61599 4.98256 6.52321 4.9324C6.58218 4.88953 6.63796 4.84194 6.68995 4.78995C6.88575 4.59415 7.01908 4.3447 7.0731 4.07313L6.58271 3.97558L7.0731 4.07313C7.12712 3.80155 7.0994 3.52006 6.99343 3.26424L6.53149 3.45558L6.99343 3.26424C6.88747 3.00843 6.70803 2.78978 6.4778 2.63594ZM2.94437 1.92692C3.84876 1.32262 4.91203 1.00005 5.99973 1C7.45797 1.00167 8.85602 1.5817 9.88716 2.61284C10.9184 3.64405 11.4984 5.0422 11.5 6.50055C11.4999 7.58815 11.1773 8.65132 10.5731 9.55563C9.96874 10.4601 9.10975 11.1651 8.10476 11.5813C7.09977 11.9976 5.9939 12.1065 4.92701 11.8943C3.86011 11.6821 2.8801 11.1583 2.11092 10.3891C1.34173 9.6199 0.817902 8.63989 0.605684 7.57299C0.393465 6.5061 0.502383 5.40023 0.918665 4.39524C1.33495 3.39025 2.0399 2.53126 2.94437 1.92692Z" fill="#9CA3AF" stroke="#9CA3AF"/>
                          </g>
                            <defs>
                              <clipPath id="clip0_1071_20649">
                                <rect width="12" height="12" fill="white" transform="translate(0 0.5)"/>
                              </clipPath>
                            </defs>
                          </svg>
                      </div>
                      <div class="justify-start text-gray-500 text-sm font-normal leading-tight">vs last month</div>
                    </div>
                  </div>
                  <div class="hidden lg:block h-20 mx-12 border border-gray-600"></div>
                </div>
                <div class="h-20 lg:w-auto w-[176px] ">
                  <div class="justify-start text-gray-500 text-sm font-normal leading-tight">Usage Time</div>
                  <div class="flex gap-2 items-center">
                    <div class="justify-start text-gray-900 text-2xl text-nowrap font-bold leading-loose">
                      {{ usagetime.hours }} : {{ usagetime.minutes }} : {{ usagetime.seconds }}
                    </div>
                    <div class="text-center justify-center text-green-800 text-xs font-medium leading-none flex gap-1 h-fit items-center bg-green-100 rounded-md px-2.5 py-1">
                      <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5.19488 0.570862L5.19524 0.571205L8.19063 3.42456C8.23191 3.46638 8.25042 3.51682 8.24999 3.56422C8.24955 3.61249 8.22939 3.66382 8.18551 3.70563C8.14083 3.74818 8.07554 3.77626 8.00253 3.77686C7.93073 3.77745 7.86568 3.75136 7.82007 3.7105L6.10468 2.07645L5.25981 1.27165V2.43848V9.28571C5.25981 9.33449 5.2397 9.38649 5.19532 9.42876C5.15012 9.47181 5.08385 9.5 5.00996 9.5C4.93607 9.5 4.8698 9.47181 4.82461 9.42876C4.78023 9.38649 4.76011 9.33449 4.76011 9.28571V2.43848V1.27235L3.91541 2.0763L2.1945 3.71418L2.19445 3.71412L2.1881 3.72037C2.16619 3.74199 2.13865 3.7605 2.10645 3.77368C2.07422 3.78687 2.03872 3.79411 2.00223 3.79442C1.96573 3.79472 1.93005 3.78806 1.89747 3.77536C1.86493 3.76268 1.83692 3.74455 1.81449 3.72318C1.79211 3.70186 1.77581 3.67787 1.76526 3.65329C1.75473 3.62878 1.74978 3.60343 1.75001 3.57849C1.75024 3.55354 1.75565 3.5282 1.76668 3.50375C1.77774 3.47922 1.79456 3.45537 1.81746 3.43431L1.81751 3.43436L1.82379 3.42838L4.82319 0.571205L4.82355 0.570861C4.84531 0.550089 4.87255 0.532424 4.90426 0.520068L4.90426 0.520071L4.90596 0.519405C4.97167 0.493532 5.04676 0.493532 5.11247 0.519405L5.11247 0.519408L5.11416 0.520068C5.14588 0.532424 5.17312 0.55009 5.19488 0.570862Z" fill="#03543F" stroke="#03543F"/>
                      </svg>
                      1.4%
                    </div>
                  </div>
                  <div class="flex items-center gap-1">
                    <div>
                      <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_1071_20649)">
                          <path d="M6.4778 2.63594L6.20002 3.05168L6.4778 2.63594C6.24757 2.48211 5.9769 2.4 5.7 2.4C5.3287 2.4 4.9726 2.5475 4.71005 2.81005C4.4475 3.0726 4.3 3.4287 4.3 3.8C4.3 4.07689 4.38211 4.34757 4.53594 4.5778C4.59053 4.6595 4.65329 4.7348 4.72297 4.8027C4.45938 4.82119 4.21024 4.93412 4.02219 5.12218C3.81589 5.32847 3.7 5.60826 3.7 5.9C3.7 6.19174 3.8159 6.47153 4.02218 6.67782C4.22847 6.88411 4.50826 7 4.8 7H4.9V7.8H4.8C4.50826 7.8 4.22847 7.91589 4.02218 8.12218C3.8159 8.32847 3.7 8.60826 3.7 8.9C3.7 9.19174 3.81589 9.47153 4.02218 9.67782C4.22848 9.88411 4.50826 10 4.8 10H7.2C7.49174 10 7.77153 9.88411 7.97782 9.67782C8.18411 9.47153 8.3 9.19174 8.3 8.9C8.3 8.60826 8.18411 8.32847 7.97782 8.12218C7.77153 7.91589 7.49174 7.8 7.2 7.8H7.1V5.9C7.1 5.60826 6.98411 5.32847 6.77782 5.12218C6.70189 5.04625 6.61599 4.98256 6.52321 4.9324C6.58218 4.88953 6.63796 4.84194 6.68995 4.78995C6.88575 4.59415 7.01908 4.3447 7.0731 4.07313L6.58271 3.97558L7.0731 4.07313C7.12712 3.80155 7.0994 3.52006 6.99343 3.26424L6.53149 3.45558L6.99343 3.26424C6.88747 3.00843 6.70803 2.78978 6.4778 2.63594ZM2.94437 1.92692C3.84876 1.32262 4.91203 1.00005 5.99973 1C7.45797 1.00167 8.85602 1.5817 9.88716 2.61284C10.9184 3.64405 11.4984 5.0422 11.5 6.50055C11.4999 7.58815 11.1773 8.65132 10.5731 9.55563C9.96874 10.4601 9.10975 11.1651 8.10476 11.5813C7.09977 11.9976 5.9939 12.1065 4.92701 11.8943C3.86011 11.6821 2.8801 11.1583 2.11092 10.3891C1.34173 9.6199 0.817902 8.63989 0.605684 7.57299C0.393465 6.5061 0.502383 5.40023 0.918665 4.39524C1.33495 3.39025 2.0399 2.53126 2.94437 1.92692Z" fill="#9CA3AF" stroke="#9CA3AF"/>
                        </g>
                          <defs>
                            <clipPath id="clip0_1071_20649">
                              <rect width="12" height="12" fill="white" transform="translate(0 0.5)"/>
                            </clipPath>
                          </defs>
                        </svg>
                    </div>
                    <div class="justify-start text-gray-500 text-sm font-normal leading-tight">vs last month</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Data Collected -->
            <div
                class="flex justify-between items-center w-full mt-4">
                <div
                    class="flex justify-start items-center gap-2">
                    <h5
                        class="text-gray-900 text-base font-medium">
                        Data Collected
                    </h5>
                    <svg class="w-6 h-6"
                        viewBox="0 0 24 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M18.4166 3H5.58332C3.60833 3 2 4.35736 2 6.03121V13.1838C2 14.8506 3.60833 16.2079 5.58332 16.2079L8.40767 16.2334C8.5893 16.235 8.7569 16.3162 8.85028 16.4477L10.1583 18.2897C10.475 18.7327 11.0333 19 11.65 19C12.2666 19 12.8167 18.7327 13.1333 18.2897L14.5052 16.417C14.6004 16.287 14.7686 16.2079 14.9496 16.2079H18.4167C20.3957 16.2079 22 14.854 22 13.1838V6.03121C22 4.35736 20.3916 3 18.4166 3ZM11.9471 5.73086C12.4353 5.73086 12.7804 6.06781 12.7804 6.43415C12.7804 6.81539 12.4407 7.12843 11.9512 7.12843C11.4542 7.12844 11.1138 6.79946 11.1138 6.43415C11.1138 6.06525 11.4747 5.73086 11.9471 5.73086ZM12.7821 12.7897C12.7821 13.1781 12.4093 13.493 11.9487 13.493C11.4881 13.493 11.1154 13.1781 11.1154 12.7897V9.18149C11.1154 8.7931 11.4881 8.47819 11.9487 8.47819C12.4093 8.47819 12.7821 8.7931 12.7821 9.18149V12.7897Z"
                            fill="#5B616E" />
                    </svg>
                </div>

                <div class="flex flex-row gap-6">
                  <Button theme="secondary" class="!bg-white !border !border-gray-200 !px-2"  title="Refresh" @click="getAnalyticsData(route.query)" >
                        <template v-slot:svg >
                          <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_6160_4993)">
                              <path d="M3.01228 9H1.76712L2.66648 9.86115C3.54744 10.7047 4.73953 11.2 6 11.2C8.62568 11.2 10.7778 9.1008 10.7778 6.5C10.7778 6.45323 10.8187 6.4 10.8889 6.4C10.9591 6.4 11 6.45323 11 6.5C11 9.19248 8.76545 11.4 6 11.4C4.75385 11.4 3.57168 10.9317 2.66365 10.1326L1.83333 9.40195V10.508V11.9C1.83333 11.9468 1.79238 12 1.72222 12C1.65206 12 1.61111 11.9468 1.61111 11.9V8.9C1.61111 8.88894 1.61362 8.87949 1.62045 8.86838C1.62826 8.85567 1.64336 8.83881 1.66831 8.8237L1.6684 8.82385L1.67944 8.81676C1.68512 8.81312 1.69056 8.80948 1.69578 8.80587L1.69767 8.80515L1.70785 8.80073C1.74034 8.78665 1.76286 8.78706 1.77722 8.78998L1.82652 8.8H1.87683H4.77778C4.84794 8.8 4.88889 8.85323 4.88889 8.9C4.88889 8.94677 4.84794 9 4.77778 9H3.01228ZM1.56969 8.84521L1.57528 8.84393C1.57125 8.84488 1.56794 8.84559 1.56969 8.84521ZM8.98772 4H10.2329L9.33352 3.13886C8.45256 2.29534 7.26047 1.8 6 1.8C3.37432 1.8 1.22222 3.8992 1.22222 6.5C1.22222 6.54677 1.18127 6.6 1.11111 6.6C1.04095 6.6 1 6.54677 1 6.5C1 3.80752 3.23455 1.6 6 1.6C7.24615 1.6 8.42832 2.06827 9.33635 2.86735L10.1667 3.59805V2.492V1.1C10.1667 1.05323 10.2076 1 10.2778 1C10.3479 1 10.3889 1.05323 10.3889 1.1V4.1C10.3889 4.11111 10.3864 4.1206 10.3796 4.13166C10.3718 4.1443 10.3569 4.16081 10.3327 4.17545L10.3326 4.17529L10.3212 4.18264C10.3155 4.18628 10.31 4.18992 10.3048 4.19353L10.302 4.19462L10.2908 4.1995C10.2585 4.21371 10.2361 4.21304 10.2234 4.2104L10.1732 4.2H10.1219H7.22222C7.15206 4.2 7.11111 4.14677 7.11111 4.1C7.11111 4.05323 7.15206 4 7.22222 4H8.98772Z" fill="#1F2A37" stroke="#1F2A37"/>
                            </g>
                            <defs>
                              <clipPath id="clip0_6160_4993">
                                <rect width="12" height="12" fill="white" transform="translate(0 0.5)"/>
                              </clipPath>
                            </defs>
                          </svg>
                        </template>
                </Button>
                  <Button theme="secondary" class="!bg-white !border !border-gray-200 !px-2"  title="Export" @click="downloadCSV()" >
                        <template v-slot:svg >
                          <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_6160_4999)">
                              <path d="M9.94485 10.1516L9.9449 10.1517L9.95116 10.1457L10.4998 9.61893V11.3C10.4998 11.4789 10.426 11.6548 10.2875 11.7878C10.1482 11.9215 9.95522 12 9.74988 12H2.21312C2.01473 11.9956 1.8301 11.9162 1.6981 11.7851C1.56594 11.6538 1.49721 11.482 1.50009 11.3083L1.50016 11.3083V11.3V5.2H4.12506C4.58275 5.2 5.02559 5.02564 5.35519 4.70922C5.68548 4.39212 5.87502 3.9576 5.87502 3.5V1H9.78686C9.98526 1.00443 10.1699 1.0838 10.3019 1.21492C10.4341 1.34619 10.5028 1.51802 10.4999 1.69172L10.4998 1.69171V1.7V5.78107L9.95382 5.25687C9.84983 5.15443 9.72693 5.07368 9.59265 5.0183C9.45686 4.96231 9.31152 4.93309 9.16495 4.93187C9.01838 4.93064 8.87258 4.95743 8.73589 5.01112C8.59918 5.06482 8.47365 5.14463 8.36715 5.24687C8.2606 5.34917 8.17518 5.47194 8.11693 5.6086C8.05867 5.74532 8.02906 5.89258 8.03041 6.04157C8.03176 6.19055 8.06402 6.33723 8.12471 6.47286C8.14438 6.51682 8.16688 6.55928 8.192 6.6H5.37502C5.08309 6.6 4.79921 6.71114 4.58682 6.91505C4.37373 7.11962 4.25004 7.40153 4.25004 7.7C4.25004 7.99847 4.37373 8.28038 4.58682 8.48495C4.79921 8.68886 5.08309 8.8 5.37502 8.8H8.19844C8.09823 8.96834 8.04395 9.16231 8.04577 9.36317C8.04844 9.6582 8.17185 9.93614 8.38251 10.1384C8.5925 10.34 8.87246 10.451 9.1611 10.4534C9.44973 10.4558 9.73146 10.3495 9.94485 10.1516ZM1.98421 3L3.62506 1.42473V3H1.98421Z" fill="#1F2A37" stroke="#1F2A37"/>
                            </g>
                            <defs>
                              <clipPath id="clip0_6160_4999">
                                <rect width="12" height="12" fill="white" transform="translate(0 0.5)"/>
                              </clipPath>
                            </defs>
                          </svg>
                        </template>
                </Button>
                </div>

            </div>

            <div v-if="Store.analyticsData.sessions && Object.keys(Store.analyticsData.sessions).length>0"
            class="flex flex-col justify-start items-start mb-6">

            <AnalyticsTable v-if="viewType === 'table' && Store.analyticsData.sessions && Object.keys(Store.analyticsData.sessions).length>0" :data="tableViewData" :key="tableViewData" :totalSession="total_session" class="mb-4" @page-change="(n)=>{currentPage=n; getAnalyticsData(route.query)}" :limit="pageLimit" :currentPage="currentPage"/>
            <div v-else class="grid-container ">
                <div class="hidden sm:grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-6">
                  <CardComponent v-for="item in Store.analyticsData.sessions" :key="item._id" :item="item"
                    :fields="sessionFields" :highlight="highlight"  sceneType="analytics"
                    :organization-store="Organizationstore" :is-mobile="false" :session-type="item.type" @action="handleCardAction"
                    @field-click="handleFieldClick" />
                </div>
            </div>

            </div>

            <LoaderCompVue v-else-if="analytics == false && !Store.analyticsData.sessions"></LoaderCompVue>
            <div v-else class="w-full flex flex-col items-center justify-center">
                <img src="https://firebasestorage.googleapis.com/v0/b/realvr-eb62c.appspot.com/o/CreationtoolAssets%2Fsiteassets%2Femptystate.svg?alt=media"/>
                <p class="font-bold"> No Data found </p>
            </div>
    </div>
</template>
<style>

/* Ellipsis & Positions */

.filterByUser , .filterByProject, .filterBySessionType {
    position: static;
}

.filterByUser .multiselect__tags , .filterByProject .multiselect__tags{
   width:200px
}

.filterBySessionType .multiselect__tags{
 width:150px
}

.sessionAnalytics .multiselect__content-wrapper {
  position: absolute !important;
}
</style>

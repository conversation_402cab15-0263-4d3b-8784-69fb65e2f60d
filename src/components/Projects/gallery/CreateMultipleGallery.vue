<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../../common/Spinner.vue';
import { Form, Field, ErrorMessage, FieldArray } from 'vee-validate';
import { multipleGallerySchema } from '../../../validationSchema/gallery';
import Modal from '../../common/Modal/Modal.vue';
import { useRouter } from 'vue-router';
import { addGalleryItem, galleryDataSyncUp, getCategories } from '../../../api/projects/gallery';
import Multiselect from 'vue-multiselect';
import { apiMediaTypes } from '../../../enum';
import { resizeImage } from '../../../helpers/helpers';
import { ProjectStore } from '../../../store/project';

const router = useRouter();
const projectStore = ProjectStore();
const categoryList = ref([]);
const route = useRoute();
const projectId = ref(route.params.project_id);
const loader = ref(false);
const catRef = ref();

projectStore.RefreshVirtualTours(projectId.value);

getCategories(projectId.value).then((res) => { // Get list of categories
  categoryList.value = res.map((elem) => {
    return {name: elem};
  });
}).catch((err) => {
  console.log('output->err', err);
});

const addTag = (newTag) => { // To add new category if it is not in the
  const tag = {
    name: newTag,
  };
  categoryList.value.push(tag); // Adding to list
  catRef.value = tag; // Selecting same new tag
};

const uploadedFiles = ref();
const initialData = ref();
// Const initialData = {
//       GalleryItems: [{ file: "", name: "", type: "", category: "" }],
//     };

watch(uploadedFiles, async (values) => {
  console.log(values);
  const galleryItems = await Promise.all(values.map(async (file, index) => {
    console.log(index);
    if (!file) {
      return null;
    }

    // Finding the file type and name
    let fileType = file.type.split('/').slice(0, -1).join('');
    const fileName = file.name.split('.').slice(0, -1).join('.');

    fileType = fileType==='image' || fileType==='video' ? fileType :'pdf';

    let resizedThumbnail = null;
    // Image resize on type image
    if (fileType==='image'){
      resizedThumbnail = await resizeImage(file, 720, 720);
    }
    // Function to read file and return a promise that resolves to a data URL for preview image
    const readFileAsDataURL = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsDataURL(file);
      });
    };
    const preview = await readFileAsDataURL(resizedThumbnail?resizedThumbnail:file);

    return { file: file, thumbnail: resizedThumbnail, name: fileName, type: fileType, category: null, preview: preview };
  }));

  const obj = { GalleryItems: galleryItems.filter((item) => !!item) }; // Filter out null results if any
  initialData.value = obj;
});

const onSubmit = (values) => {
  return new Promise((outerResolve, outerReject) => {
    loader.value = true;
    Promise.all(values.GalleryItems.map(async (elem) => {
      if (elem.type!=='image' || elem.type!=='360_image'){

        let resizeMaxWidth = 1280;
        let resizeMaxHeight = 720;

        if (elem.type === '360_image'){
          resizeMaxWidth = 1000;
          resizeMaxHeight = 1000;
        }

        const resizedThumbnail = await resizeImage(elem.thumbnail, resizeMaxWidth, resizeMaxHeight);
        elem.thumbnail = resizedThumbnail;
      }
      const formData = new FormData();
      formData.append('project_id', projectId.value);
      formData.append('name', elem.name);
      formData.append('type', elem.type);
      formData.append('category', elem.category.name);
      formData.append('thumbnail', elem.thumbnail);
      elem.link ? formData.append('link', elem.link) : elem.tour_id ? formData.append('tour_id', elem.tour_id) : formData.append('file', elem.file);
      await addGalleryItem(formData);
    })).then(async () => {
      loader.value = false;
      await galleryDataSyncUp(projectId.value);
      document.dispatchEvent(new Event('refreshGalleryItemsListByCategory'));
      router.go(-1);
      outerResolve();
    }).catch((err) => {
      loader.value = false;
      console.log('Error gallery item', err);
      outerReject();
    });
  });
};

</script>

<template>
    <Modal :open="true">
        <div
            class="modal-content-primary sm:max-w-6xl h-screen sm:max-h-[80vh]">
            <div class="p-3 sm:p-6 h-full overflow-y-scroll">
                <div class="mb-2">
                    <h1
                        class="modal-heading-primary">
                        Add Gallery Items</h1>
                    <p class="modal-subheading-primary">Fill details
                        below
                        to Add Gallery Items.</p>
                </div>
                     <div v-if="!initialData"
                            class="col-span-auto">
                            <label for="file"
                                class="label-primary">Upload multiple
                                Files</label>
                            <div class="mt-2">
                                <Field type="file"
                                v-model="uploadedFiles"
                                   multiple
                                    name="files"
                                    id="files"
                                    autocomplete="files"
                                    class="input-primary"
                                    placeholder="Upload multiple files" />
                            </div>
                            <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                        <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.go(-1)"
                            ref="cancelButtonRef">Cancel</button>
                    </div>
                        </div>
        <div>

            <Form v-if="initialData?.GalleryItems"
              @submit="onSubmit"
              :initial-values="initialData"
              :validation-schema="multipleGallerySchema"
            >
            <div class="h-fit px-8">
                <div :class="[' overflow-x-auto mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden ' ]">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">sr</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">name</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">file</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">thumbnail</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">category</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <FieldArray name="GalleryItems" v-slot="{ fields, remove }">
                                <tr  v-for="(field, idx) in fields"
                                :key="field.key" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">{{ idx }}</td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <Field :id="`name_${idx}`" :name="`GalleryItems[${idx}].name`" class="input-primary" />
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`GalleryItems[${idx}].name`" />
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  <div v-if="initialData?.GalleryItems[idx].type!=='embed_link' && initialData?.GalleryItems[idx].type!=='virtual_tour'">
                                <p v-if="initialData?.GalleryItems[idx].type==='pdf'">{{initialData.GalleryItems[idx].name}}</p>
                                <img v-else-if="initialData?.GalleryItems[idx].type==='image' || initialData?.GalleryItems[idx].type==='360_image' && initialData?.GalleryItems?.[idx]?.preview" :src="initialData.GalleryItems[idx].preview" :alt="`file_${idx}`" width="200" height="200"/>
                                <video v-else-if="initialData?.GalleryItems[idx].type==='video' && initialData?.GalleryItems?.[idx]?.preview" :src="initialData.GalleryItems[idx].preview" :alt="`file_${idx}`" width="200" height="200"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`GalleryItems[${idx}].file`" />
                              </div>
                              <div v-else-if="initialData?.GalleryItems[idx].type==='virtual_tour'">
                                <Field
                                as="select" type="text"
                                :name="`GalleryItems[${idx}].tour_id`" :id="`tour_id_${idx}`"
                                class="select-primary"
                                :placeholder="`Seclect Tour`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!projectStore.virtualtours">
                                    No Tour found ! </option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.virtualtours"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.tour_name }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                :name="`GalleryItems[${idx}].tour_id`" />
                              </div>
                              <div v-else >
                                <Field :id="`link_${idx}`" type="url" :name="`GalleryItems[${idx}].link`" class="input-primary" />
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`GalleryItems[${idx}].link`" />
                              </div>

                                </td>

                                <td  class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <p v-if="initialData?.GalleryItems[idx].type==='image' || initialData?.GalleryItems[idx].type==='360_image'">--------</p>
                                <Field v-else v-model="initialData.GalleryItems[idx].thumbnail" type="file" :id="`thumbnail_${idx}`" :name="`GalleryItems[${idx}].thumbnail`" class="input-primary" />
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`GalleryItems[${idx}].thumbnail`" />
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">

                                <div class="col-span-auto">
                                    <Field
                                        v-model="initialData.GalleryItems[idx].type"
                                        as="select" type="text"
                                        :name="`GalleryItems[${idx}].type`"
                                        :id="`type_${idx}`"
                                        autocomplete="type"
                                        class="select-primary"
                                        :placeholder="`Seclect Type`">
                                        <option value="" disabled>
                                            Choose
                                        </option>
                                        <option value="" disabled
                                            v-if="!apiMediaTypes">
                                            No Type found ! </option>
                                        <option v-else
                                            :value="option"
                                            v-for="option, index in  apiMediaTypes"
                                            :key="index"
                                            class="text-black">
                                            {{
                                                option }} </option>
                                    </Field>
                                    <ErrorMessage as="p"
                                        class="text-sm text-rose-500 mt-1"
                                        :name="`GalleryItems[${idx}].type`" />
                                </div>

                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <!-- <Field :id="`category_${idx}`" :name="`GalleryItems[${idx}].category`" class="input-primary"/>
                                <ErrorMessage :name="`GalleryItems[${idx}].category`" /> -->

                                <div
                                    class="col-span-auto">
                                    <!-- <label for="category"
                                        class="label-primary">Category</label> -->
                                    <div class="mt-2">
                                       <Field :id="`category_${idx}`" :name="`GalleryItems[${idx}].category`" :model-value="initialData.GalleryItems[idx].category" v-slot="{ category }">

                                              <Multiselect
                                              v-bind="category"
                                              v-model="initialData.GalleryItems[idx].category"
                                              tag-placeholder="Add this as new category"
                                              placeholder="Search or add Category"
                                              label="name"
                                              track-by="name"
                                              :multiple="false"
                                              :taggable="true"
                                              @tag="addTag"
                                              :options="categoryList" maxHeight="250" >
                                              </Multiselect>

                                            </Field>
                                        <ErrorMessage as="p"
                                            class="text-sm text-rose-500 mt-1"
                                            :name="`GalleryItems[${idx}].category`" />
                                    </div>
                                </div>

                            </td>

                                <button type="button" @click="remove(idx)" class="p-1 bg-gray-200 rounded-lg">
                                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
                                </button>
                                               </tr>
                                <!-- <button type="button" @click="push({ file: '', name: '', type: '', category: '' })">
                                Add Item +
                                </button> -->
                            </FieldArray>

                        </tbody>
                    </table>
                </div>
            </div>
              <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                        <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.go(-1)"
                            ref="cancelButtonRef">Cancel</button>
                        <button type="submit"
                        :disabled="loader"
                            class="proceed-btn-primary">Save
                            <Spinner v-if="loader" />
                        </button>
                    </div>
            </Form>
            </div>
            </div>
        </div>
    </Modal>
</template>

<style>

</style>

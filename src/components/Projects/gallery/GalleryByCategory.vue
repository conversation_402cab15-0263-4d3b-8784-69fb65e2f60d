<script setup>
import { ref } from 'vue';
import noDataFound from '../../../assets/noDataFound.png';
import { useRoute, useRouter } from 'vue-router';

import Button from '../../common/Button.vue';
import { moveGalleryItemToTrash, getListofGalleryItemsByCategory, galleryDataSyncUp } from '../../../api/projects/gallery/index';
import EditDeleteButton from '../../UIElements/EditDeleteButton.vue';
import Modal from '../../common/Modal/Modal.vue';
import DeleteModalContent from '../../common/ModalContent/DeleteModalContent.vue';
import { ProjectStore } from '../../../store/project';
import TrashedData from '../trash/Index.vue';

const router = useRouter();
const route = useRoute();
const projectStore = ProjectStore();
const projectId = ref(route.params.project_id);
const category_id = ref(route.params.category_id);
const galleryItems = ref([]);
const selectedItem = ref();
const openDeleteModal = ref(false);
const deleteLoader = ref(false);
const trashedDataRef = ref(null);
const showTrash = ref(false);

const handleGetListofGalleryItems = () => {
  galleryItems.value = [];
  getListofGalleryItemsByCategory(projectId.value, category_id.value).then((res) => {
    projectStore.SyncMultipleGalleryItems(res);
    if (Object.keys(res)){
      Object.values(res).sort((a, b) => {
        return a.order - b.order;
      }).forEach((item) => {
        galleryItems.value.push(item);
      });
    }
  });
};

handleGetListofGalleryItems();
document.addEventListener('refreshGalleryItemsListByCategory', () => {
  handleGetListofGalleryItems();
});

document.addEventListener('refreshGalleryItemsList', () => {
  handleGetListofGalleryItems();
});

const handleMenuSelected = (galleryItemId) => {
  if (selectedItem.value=== galleryItemId){
    selectedItem.value = null;
  } else {
    selectedItem.value = galleryItemId;
  }
};

const handleEditClick = () => { // For opening edit modal
  router.push(`/projects/${projectId.value}/gallery/${category_id.value}/${selectedItem.value}/edit`);
  selectedItem.value = null;
};
const handleMoveToTrash = () => {
  deleteLoader.value = true;
  const obj = {
    gallery_id: [selectedItem.value],
    timeStamp: Date.now(),
  };
  moveGalleryItemToTrash(obj, projectId.value)
    .then(async () => {
      deleteLoader.value = false;
      openDeleteModal.value = false;
      selectedItem.value = false;
      await galleryDataSyncUp(projectId.value);
      handleGetListofGalleryItems();
      trashedDataRef.value?.fetchTrashData();
    })
    .catch(() => {
      deleteLoader.value = false;

    });
};

</script>

<template>
    <div class="">
        <main
            class="h-[92.8vh] w-full bg-bg-1000 dark:bg-bg-default relative">
            <div
                class="flex items-center justify-between py-3 px-8">
                <div class="min-w-0 flex-1">
                    <h2
                        class="font-bold -tracking-2 leading-7 text-txt-100 dark:text-txt-1000 sm:truncate sm:text-3xl sm:tracking-tight mb-3">
                        {{category_id}} Galary
                    </h2>
                    <p class="text-txt-600 dark:text-txt-650">
                        Lorem ipsum dolor sit amet consectetur
                        adipisicing
                        elit.
                    </p>
                </div>
                <div class="flex flex-wrap gap-3">
                    <Button v-if="galleryItems && Object.keys(galleryItems).length > 1" title="ReOrder Items"
                                            theme="secondary" class="h-10"
                                            @click="router.push(`/projects/${projectId}/gallery/${category_id}/reorder`)">

                                            <template v-slot:svg>
                                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M8.85746 12.5061C6.36901 10.6456 4.59564 8.59915 3.62734 7.44867C3.3276 7.09253 3.22938 6.8319 3.17033 6.3728C2.96811 4.8008 2.86701 4.0148 3.32795 3.5074C3.7889 3 4.60404 3 6.23433 3H17.7657C19.396 3 20.2111 3 20.672 3.5074C21.133 4.0148 21.0319 4.8008 20.8297 6.37281C20.7706 6.83191 20.6724 7.09254 20.3726 7.44867C19.403 8.60062 17.6261 10.6507 15.1326 12.5135C14.907 12.6821 14.7583 12.9567 14.7307 13.2614C14.4837 15.992 14.2559 17.4876 14.1141 18.2442C13.8853 19.4657 12.1532 20.2006 11.226 20.8563C10.6741 21.2466 10.0043 20.782 9.93278 20.1778C9.79643 19.0261 9.53961 16.6864 9.25927 13.2614C9.23409 12.9539 9.08486 12.6761 8.85746 12.5061Z" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>

                                            </template>

                                            </Button>
                    <Button class="h-10" title="Trashed Gallery" @click="showTrash = !showTrash" theme="primary"></Button>
                </div>
                <!-- <Button title="Create New Gallery Item"
                    theme="primary" class="h-10 mr-3"
                    @click="router.push(`/projects/${projectId}/gallery/create`)">
                    <template v-slot:svg>
                        <svg xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            class="h-6 w-6 fill-txt-1000 dark:fill-txt-50">
                            <g data-name="Layer 2">
                                <g data-name="plus">
                                    <rect width="24" height="24"
                                        transform="rotate(180 12 12)"
                                        opacity="0" />
                                    <path
                                        d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z" />
                                </g>
                            </g>
                        </svg>
                    </template>
                </Button>
                <Button title="Create multiple Gallery Item"
                    theme="primary" class="h-10 mr-3"
                    @click="router.push(`/projects/${projectId}/gallery/createmultiple`)">
                    <template v-slot:svg>
                        <svg xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            class="h-6 w-6 fill-txt-1000 dark:fill-txt-50">
                            <g data-name="Layer 2">
                                <g data-name="plus">
                                    <rect width="24" height="24"
                                        transform="rotate(180 12 12)"
                                        opacity="0" />
                                    <path
                                        d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z" />
                                </g>
                            </g>
                        </svg>
                    </template>
                </Button> -->
            </div>
            <!-- <BreadCrumb /> -->
            <!-- list of Gallery items -->
            <div class="mt-3 text-blue-900 px-8 " v-if="!showTrash">

                <div :class="[
                    galleryItems && Object.values(galleryItems).length !== 0 &&
                    'grid sm:grid-cols-4 gap-x-3 gap-y-3 max-h-[68vh] h-inherit overflow-y-auto grid-container pb-10 px-1',
                    'w-full text-white',
                ]">
                    <div v-if="!galleryItems || Object.values(galleryItems).length === 0"
                        class="flex w-full m-auto justify-center p-4">
                        <div class="w-full">
                            <div>{{galleryItems}}</div>
                            <img class="w-72 m-auto"
                                :src="noDataFound" alt="" />
                            <p
                                class="text-xs text-center text-neutral-100 mt-2">
                                Oops! No Data Found,
                                Contact admin to add
                                gallery Items
                            </p>
                        </div>
                    </div>

                    <div v-else
                        v-for="elem, index in galleryItems"
                        :key="index"
                        class="max-sm:rounded-sm rounded-md cursor-pointer max-w-[420px] border-[1px] border-bg-900 relative">
                        <div>
                            <img v-if="elem.thumbnail !== ''"
                                :src="elem.thumbnail"
                                alt="img"
                                class="rounded-t-md w-full"
                                style="height: 200px; object-fit: cover" />
                            <div
                                class="flex justify-between items-center">
                                <p
                                    class="text-txt-default dark:text-txt-1000 p-2">
                                    {{ elem.name }}
                                </p>
                                <button @click="handleMenuSelected(elem._id)">
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        strokeWidth="{1.5}"
                                        stroke="currentColor"
                                        class="w-6 h-6  stroke-bg-default dark:stroke-bg-1000">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                    </svg>
                                </button>
                                <EditDeleteButton @handleDelete="openDeleteModal= true" @handleEdit="handleEditClick" v-if="selectedItem===elem._id" class="right-1 bottom-10"/>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <TrashedData
                ref="trashedDataRef"
                v-if="showTrash"
                :type="'gallerys'"
                :entity="'gallery'"
                :action="'restoreGallery'"
                @refreshDataList="handleGetListofGalleryItems"
            />
        </main>
       <Modal :open="openDeleteModal">
        <DeleteModalContent :trash="true" :loader="deleteLoader" :dataName="'Gallery item'" @handleDelete="handleMoveToTrash" @closeModal="openDeleteModal=false"/>
        </Modal>
    </div>
</template>

<style scoped></style>

<script setup>
import { ref } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../../common/Spinner.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { GallerySchema } from '../../../validationSchema/gallery';
import { uiOperations } from '../../../store/uiOperations';
import { addGalleryItem, galleryDataSyncUp, getCategories } from '../../../api/projects/gallery';
import Multiselect from 'vue-multiselect';
import { apiMediaTypes } from '../../../enum';
import { resizeImage } from '../../../helpers/helpers';
import { ProjectStore } from '../../../store/project';

const uiStore = uiOperations();
const categoryList = ref([]);
const route = useRoute();
const projectId = ref(route.params.project_id);
const loader = ref(false);
const catRef = ref();
const type = ref();
const projectStore = ProjectStore();

const emit = defineEmits(['close']);

getCategories(projectId.value).then((res) => { // Get list of categories
  categoryList.value = res;
  console.log(categoryList.value);
}).catch((err) => {
  console.log('output->err', err);
});

projectStore.RefreshVirtualTours(projectId.value);

const addTag = (newTag) => { // To add new category if it is not in the
  categoryList.value.push(newTag); // Adding to list
  catRef.value = newTag; // Selecting same new tag

};

const handleAddGalleryItem = (formData) => { // Create gallery item
  loader.value = true;
  addGalleryItem(formData).then(async () => {
    await galleryDataSyncUp(projectId.value);
    document.dispatchEvent(new Event('refreshGalleryItemsList'));
    emit('close', true);
  }).catch((err) => {
    console.log(err);
    uiStore.handleApiErrorMessage(err.message._message);
  }).finally(() => {
    loader.value = false;
  });
};

const handleSubmit = async (values) => {
  console.log(values);
  const formData = new FormData();
  const { url, name, type, category, link, tour_id} = values;
  console.log(values);

  formData.append('project_id', projectId.value);
  formData.append('name', name);
  formData.append('type', type);
  formData.append('category', category);
  link ? formData.append('link', link) : tour_id ? formData.append('tour_id', tour_id) : formData.append('file', url);

  // Determine if the type is 'image' or '360_image'
  const isImage = type === 'image' || type === '360_image';
  // Choose the correct file to resize based on type
  const thumbnailFile = isImage ? url : values.thumbnail;
  // Set resize dimensions based on type
  let resizeMaxWidth = 1280;
  let resizeMaxHeight = 720;

  if (type=== '360_image'){
    resizeMaxWidth = 1000;
    resizeMaxHeight = 1000;
  }

  // Resize the image or thumbnail
  if (values.thumbnail){
    const resizedThumbnail = await resizeImage(thumbnailFile, resizeMaxWidth, resizeMaxHeight);
    formData.append('thumbnail', resizedThumbnail);
  }
  handleAddGalleryItem(formData);
};

</script>

<template>
        <div
            class="modal-content-primary">
            <div>
                <div class="flex justify-between items-center px-3 py-2 border-b border-gray-200">
                    <p class="text-sm font-medium text-gray-900">Create Gallery</p>
                    <button  class="fill-gray-400" @click="$emit('close',false)">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
                    </button>
                </div>
                <Form :validation-schema="GallerySchema"
                    @submit="handleSubmit" class="mb-0">

                    <div
                        class="grid grid-cols-2 gap-x-4 gap-y-3 px-3 py-2">

                        <div class="col-span-auto">
                            <label for="name"
                                class="label-primary">
                                Name</label>
                            <Field as="input" type="text"
                                name="name" autocomplete
                                id="name"
                                class="input-primary w-full"
                                :placeholder="`Enter Name`" />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="name" />
                        </div>

                        <div class="col-span-auto">
                            <label for="type"
                                class="label-primary">
                                Type</label>
                            <Field name="type" v-slot="{ field }" model-value="type">
                                <Multiselect v-bind="field" v-model="type"
                                    :options="apiMediaTypes" :allow-empty="false"
                                    placeholder="Select" :searchable="false" :close-on-select="true" :show-labels="false"
                                    :multiple="false" class="tabSelect h-fit !capitalize" :maxHeight="150"
                                />
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="type" />
                        </div>

                         <div
                            class="col-span-auto relative">
                            <label for="category"
                                class="label-primary">Category</label>
                            <div class="mt-2">
                               <Field  name="category" :model-value="catRef" v-slot="{ category }">

                                      <Multiselect
                                      class="!capitalize tabSelect"
                                      v-bind="category"
                                      v-model="catRef"
                                      tag-placeholder="+ Add"
                                      placeholder="Select or search a category"
                                      :multiple="false"
                                      :taggable="true"
                                      @tag="addTag"
                                      :allow-empty="false"
                                      :show-labels="false"
                                      :options="categoryList" maxHeight="130" >
                                      </Multiselect>

                                    </Field>
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="category" />
                            </div>
                        </div>

                        <div
                            class="col-span-auto" v-if="type!=='embed_link' && type!=='virtual_tour'">
                            <label for="url"
                                class="label-primary">Upload {{ type }}
                                File</label>
                            <div class="mt-2">
                                <Field type="file"
                                    name="url"
                                    id="url"
                                    autocomplete="highRes"
                                    class="input-primary w-full"
                                    placeholder="Upload High Resulation Image" />
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="url" />
                            </div>
                        </div>

                        <div class="col-span-auto"  v-if="type ==='virtual_tour'">
                            <label for="tour_id"
                                class="label-primary">
                                select virtual Tour</label>
                            <Field
                                as="select" type="text"
                                name="tour_id" id="tour_id"
                                autocomplete="tour_id"
                                class="select-primary"
                                :placeholder="`Seclect Tour`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!projectStore.virtualtours">
                                    No Tour found ! </option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.virtualtours"
                                    :key="index"
                                    class="text-black">
                                    {{
                                        option.tour_name }} </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="tour_id" />
                        </div>

                        <div class="col-span-auto" v-if="type==='embed_link'">
                            <label for="link"
                                class="label-primary">
                                Embed Link</label>
                            <Field as="input" type="url"
                                name="link" autocomplete
                                id="link"
                                class="input-primary w-full"
                                :placeholder="`Enter link`" />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="link" />
                        </div>

                        <div
                            class="col-span-auto" v-if="type !== 'image' && type !== '360_image'">
                            <label for="thumbnail"
                                class="label-primary">Upload
                                Thumbnail</label>
                            <div class="mt-2">
                                <Field type="file"
                                    name="thumbnail"
                                    id="thumbnail"
                                    class="input-primary w-full"
                                    placeholder="Upload Low Resulation Image" />
                                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="thumbnail" />
                            </div>
                        </div>

                    </div>
                    <div
                        class="py-2 px-3 flex justify-center gap-x-3">
                        <button type="submit"
                            :disabled="loader"
                            class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">Save
                            <Spinner v-if="loader" />
                        </button>
                    </div>
                </Form>

            </div>
        </div>
</template>

<style>
::-webkit-scrollbar {
    width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
    background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #737373;
    border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
.tabSelect .multiselect__content-wrapper{
    position: absolute !important;
}
.tabSelect .multiselect__option , .tabSelect .multiselect__option--selected, .tabSelect .multiselect__option:hover, .tabSelect .multiselect__option--selected:hover{
  background-color: transparent !important;
  color: #6b7280 !important;
  font-weight: 100 !important;
}
.tabSelect .multiselect__option--selected, .tabSelect .multiselect__option--selected:hover{
  color: black !important;
}
.tabSelect>.multiselect__select{
  background: none !important;
}
.tabSelect .multiselect__placeholder{
  color: black !important;
}
.tabSelect.selected-field .multiselect__single {
  background-color: #EBF5FF !important;
}
.tabSelect.selected-field .multiselect__input {
  background-color: #EBF5FF !important;
}
.tabSelect>input{
  background-color: #EBF5FF !important;
}
</style>

<script setup>
import { ref, computed, watch } from 'vue';
import NotfoundImage from '@/components/common/NotfoundImage.vue';
import Spinner from '@/components/common/Spinner.vue';
import { GetAllTrash, RestoreTrash } from '@/api/trash';
import { useRoute } from 'vue-router';
import Pagination from '../../Pagination.vue';

const props = defineProps({
  type: {
    type: String,
    required: true,
  },
  entity: {
    type: String,
    required: true,
  },
  action: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['refreshDataList', 'fetchTrashData']);
const route = useRoute();
const load = ref(null), searchQuery = ref(''), trashData = ref([]);
const projectId = ref(route.params.project_id);
const currentPage = ref(1);
const totalPages = ref(1);

async function fetchTrashData () {
  await GetAllTrash(projectId.value, props.type, currentPage.value, 5).then((res) => {
    trashData.value = res.items;
    totalPages.value = res.total;
  });
}
fetchTrashData();

async function restoreFunc (item) {
  const payload = {
    trash_id: item._id,
  };
  load.value = item._id;
  try {
    await RestoreTrash(payload, projectId.value, props.entity, props.action);
    await fetchTrashData();

    if (Object.keys(trashData.value).length === 0 && currentPage.value > 1) {

      currentPage.value = currentPage.value - 1;
      await fetchTrashData();
    }

    emit('refreshDataList');
  } catch (error) {
    console.error("Error during restore or fetching trash:", error);
  }
}

const filteredTrashData = computed(() => {
  if (!searchQuery.value) {
    return trashData.value;
  }
  return Object.entries(trashData.value).reduce((result, [key, item]) => {
    const filteredUnitPlans = Object.entries(item.data).filter(([_, unitplan]) => {
      console.log(_);
      const queryLower = searchQuery.value.toLowerCase();
      return (
        (unitplan.name && unitplan.name.toLowerCase().includes(queryLower)) ||
                (unitplan.tour_name && unitplan.tour_name.toLowerCase().includes(queryLower))
      );
    });
    if (filteredUnitPlans.length > 0) {
      result[key] = { ...item, data: Object.fromEntries(filteredUnitPlans) };
    }
    return result;
  }, {});
});

const totalPagesProps = computed(() => {
  return Math.ceil(totalPages.value / 5);
});

const updateCurrentPage = (page) => {
  currentPage.value = page;
  fetchTrashData();
};

defineExpose({
  fetchTrashData,
});

watch(() => trashData.value, (newVal) => {
  trashData.value = newVal;
});
watch(() => currentPage.value, (newVal) => {
  currentPage.value = newVal;
});

</script>
<template>
    <div class="mt-3 text-black px-8">
        <span class="flex pb-4 font-bold capitalize">Trashed {{ type }}</span>
        <input
            type="text"
            v-model="searchQuery"
            placeholder="Search"
            class="border px-2 py-1 mb-3 rounded text-black"
        />
        <div v-if="filteredTrashData && Object.keys(filteredTrashData).length !== 0">
            <div class="grid sm:grid-cols-4 gap-x-3 gap-y-3  overflow-y-auto grid-container pb-10">
                <div
                    v-for="(item, index) in filteredTrashData"
                    :key="index"
                >
                    <div
                        v-for="(data, id) in item.data"
                        :key="id"
                        class="max-sm:rounded-sm rounded-md cursor-pointer border-[1px] border-bg-900"
                    >
                        <div>
                            <img
                                :src="type === 'scenes' ? data.background ? data.background.low_resolution : data.info_icon : data.thumbnail"
                                alt="img"
                                class="rounded-t-md w-full"
                                style="height: 150px; object-fit: cover"
                            />
                            <div class="flex justify-between items-center">
                                <p class="text-txt-default dark:text-txt-1000 p-2">
                                    {{ type === 'virtualTour' ? data.tour_name : data.name }}
                                </p>
                                <p class="flex items-center gap-2 cursor-pointer text-txt-default dark:text-txt-1000 p-2" @click="restoreFunc(item)">
                                    Restore
                                    <Spinner v-if="load === item._id"/>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <Pagination v-if="totalPagesProps > 1" :totalPages="totalPagesProps" :currentPage="currentPage" @currentPageSync="updateCurrentPage"/>
        </div>
        <div v-else class="w-full">
            <div class="w-fit m-auto">
                <svg width="300" height="286" viewBox="0 0 300 286" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <rect width="300" height="286" fill="url(#pattern0)"/>
                <NotfoundImage/>
                </svg>
            </div>
            <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit capitalize">No Trashed {{ type }} Available</div>
        </div>
    </div>
</template>

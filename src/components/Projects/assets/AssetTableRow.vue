<script setup>
import { ref, defineProps, defineEmits, onMounted, nextTick } from 'vue';
import { Form, Field } from 'vee-validate';
import { useRoute } from 'vue-router';
import { getStorage, ref as referenece, getDownloadURL, uploadBytesResumable, deleteObject } from 'firebase/storage';
import Button from '../../common/Button.vue';
import Multiselect from 'vue-multiselect';
import Modal from '@/components/common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import { truncateString, formatTimestamp } from '@/helpers/helpers';
import { assetSchema } from '@/validationSchema/asset';
import { AssetType } from '@/enum';
import { getCookie } from '@/helpers/domhelper';
import { createAsset, updateAsset, deleteAsset } from '@/api/projects/assets';
import { ProjectStore } from '@/store/project';
import { onClickOutside } from '@vueuse/core';

const props = defineProps({
  index: Number,
  data: Object,
});
const initialValues = ref({ ...props.data });
const route = useRoute();
const loader = ref(false);
const deleteLoader = ref(null);
const isOpenDeleteModal = ref(false);
const assetIdToDelete = ref({ id: null, index: null });
const project_Id = ref(route.params.project_id);
const projectStore = ProjectStore();
const previewFileName = ref(null);
const newUploadFile = ref({ file: null, progress: 0, currentId: null });
const isNewFile = props.data.isNew;
const selectedFields = ref({});
const fieldRefs = ref();
const isEdited = ref(false), rowRef = ref(null);
const emits = defineEmits(['openPreviewModal', 'deleteAssetModal', 'deleteRow', 'addNewFileRow']);

const organization = getCookie('organization');

function assetFileTypeConversion (fileType) {
  return fileType.split('_').join(" ");
}

let uploadTask = null;

// File Upload Methods
// Create
const getUploadedStorageLink = async ({ file, storagePath, metadata }) => {
  return new Promise((resolve, reject) => {
    const storage = getStorage();
    const storageRef = referenece(storage, storagePath);

    uploadTask = uploadBytesResumable(storageRef, file, metadata);

    uploadTask.on('state_changed',
      (snapshot) => {
        const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
        newUploadFile.value.progress = Math.round(progress);
      },
      (error) => {
        reject(error);
      },
      () => {
        getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
          resolve(downloadURL);
        });
      },
    );
  });
};
// Deleted
const deleteFileFromStorage = async (path) => {
  try {
    const storage = getStorage();
    const fileRef = ref(storage, path);

    await deleteObject(fileRef);
    console.log(`File at path ${path} deleted successfully`);
  } catch (error) {
    if (error.code === 'storage/object-not-found') {
      console.warn(`No file found at path: ${path}`);
    } else {
      console.error('Error deleting file:', error);
      throw error;
    }
  }
};

// Extraction of FileName
function extractFirebaseStoragePath (url, key) {
  if (url.includes("firebasestorage.googleapis.com")) {
    // Existing logic for Firebase Storage URLs
    const pathMatch = url.match(/\/o\/(.+)\?/);
    if (!pathMatch || !pathMatch[1]) {
      console.warn("Invalid Firebase Storage URL format:", url);
      return null;
    }

    const fullPath = decodeURIComponent(pathMatch[1]);
    const lastSlashIndex = fullPath.lastIndexOf('/');

    return key === 'filename'
      ? lastSlashIndex === -1 ? fullPath : fullPath.substring(lastSlashIndex + 1)
      : lastSlashIndex === -1 ? '' : fullPath.substring(0, lastSlashIndex);
  }

  if (url.includes("storage.googleapis.com")) {
    // Handling non-standard Firebase Storage URLs
    const urlParts = url.split('/');
    return key === 'filename' ? urlParts[urlParts.length - 1] : urlParts.slice(3, -1).join('/');
  }

  throw new Error('Invalid Firebase Storage URL');
}
// MediaType Mapping
function getSpecificFileType (mimeType) {
  const mimeTypeMapping = {
    "image/jpeg": "JPEG",
    "image/webp": "WEBP",
    "image/png": "PNG",
    "image/svg+xml": "SVG",
    "text/csv": "CSV",
    "application/pdf": "PDF",
    "application/x-zip-compressed": "ZIP",

  };
  return mimeTypeMapping[mimeType] || null;
}

const toggleSelect = (field) => {
  selectedFields.value = { [field]: true };
};

onClickOutside(fieldRefs, () => {
  selectedFields.value = {};
});

const trackChanges = () => {
  isEdited.value = true;
};

// Tooltip popUp
const templateElementForTooltip = (left, top, msg) => {
  const div = document.createElement("div");
  div.setAttribute('data-name', 'assetToolTip');
  div.classList.add(...['block', 'absolute', 'text-xs', 'bg-[#111111]', 'text-white', 'text-center', 'z-20', 'px-2', 'py-[6px]', 'rounded-[5px]', 'whitespace-normal', '-translate-x-[50%]', 'min-w-[auto]', 'max-w-[250px]', 'mt-1.5', 'custom_shadow']);
  div.style.left = `${left}px`;
  div.style.top = `${top}px`;
  div.innerText = msg; // message
  return div;
};

const handleOpenTooltip = (e, msg) => {
  document.body.appendChild(templateElementForTooltip(e.clientX, e.clientY + 15, msg));
};
const handleCloseTooltip = () => {
  const toolTipElement = document.querySelector("[data-name='assetToolTip']");
  toolTipElement.remove(); // remove directly from dom
};

// Method to handle file upload and update modified timestamp
const uploadFile = async (event, index) => {
  event.preventDefault(); // Prevent default form submission
  event.stopPropagation();
  const file = event.target.files[0];
  console.log("inside uploadFile", index);

  if (file) {
    newUploadFile.value = {
      currentId: index,
      file: file,
      progress: 0,
    };
    const now = new Date();
    const filepath = `CreationtoolAssets/${organization}/projects/${project_Id.value}/assets/${file.name}`;
    console.log(filepath);

    const downloadLink = getUploadedStorageLink({
      storagePath: filepath,
      file: file,
      metadata: { contentType: file.type },
    });

    initialValues.value.updated_at = now.toISOString();
    initialValues.value.media_type = getSpecificFileType(file.type);
    const URl = await downloadLink;
    if (URl) {
      console.log(URl, "Download Link");
      initialValues.value.file_url = URl;
      previewFileName.value = extractFirebaseStoragePath(URl, 'filename');
      isEdited.value = true;
    }
  }
};
// Method to Cancel Upload
const cancelCurrentUpload = () => {
  if (uploadTask) {
    uploadTask.cancel();
    newUploadFile.value.progress = 0;
    uploadTask = null;
  }
  initialValues.value.media_type = '';
};

// Delete Handler
function handleDeleteFile (previewFileUrl) {
  const deletePath = extractFirebaseStoragePath(previewFileUrl, 'path');
  deleteFileFromStorage(deletePath);
  initialValues.value.file_type = '';
  initialValues.value.media_type = '';
  initialValues.value.file_url = '';
  newUploadFile.value.progress = 0;
}

// Download Handler
function handleDownloadFile (previewFileUrl, Fname) {
  const xhr = new XMLHttpRequest();
  xhr.open("GET", previewFileUrl, true);
  xhr.responseType = "blob"; // Get the response as a Blob

  xhr.onload = function () {
    if (xhr.status === 200) {
      const blob = new Blob([xhr.response]);
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = Fname || "downloaded-file";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href); // Clean up memory
    }
  };

  xhr.onerror = function () {
    console.error("Download failed.");
  };

  xhr.send();
}

// Function to return with modified fields for each object
function getModifiedFieldsWithMeta (sourceItem, updatedItem) {
  const modifiedFields = {};

  for (const key in sourceItem) {
    if (Object.prototype.hasOwnProperty.call(sourceItem, key) &&
          Object.prototype.hasOwnProperty.call(updatedItem, key) &&
          sourceItem[key] !== updatedItem[key]) {
      modifiedFields[key] = updatedItem[key]; // Store only the new value
    }
  }

  return {
    asset_id: sourceItem._id,
    project_id: project_Id.value,
    ...modifiedFields,
  };
}

// Handle Create Assets
async function handleCreateAssets (createAssetObject) {
  const {_id} = createAssetObject;
  const newData = { ...createAssetObject, project_id: project_Id.value };
  const resultAsset = await createAsset(newData);
  if (resultAsset && resultAsset._id) {
    emits('addNewFileRow', resultAsset, _id);
    initialValues.value = { ...resultAsset };
    loader.value = false;
  }
}
// Handle Edit Assets
async function UpdateAssets (updateAssetPayload) {
  const updatedAssetResult = await updateAsset(updateAssetPayload);
  if (updatedAssetResult && updatedAssetResult._id) {
    emits('addNewFileRow', updatedAssetResult);
    initialValues.value = { ...updatedAssetResult };
    loader.value = false;
  }
}

function handleUpdateAssets (updateAssetFiles) {
  console.log(updateAssetFiles, "Inside HandleUpdateAssets", props.data);
  const sourceObj = props.data;
  const updatedFile = getModifiedFieldsWithMeta(sourceObj, updateAssetFiles);
  console.log(updatedFile, "modified Fields");
  if (Object.keys(updatedFile).length > 2) {
    UpdateAssets(updatedFile);
  }

}

// Handle Delete Assets
const handleDeleteAssets = () => {
  let deleteAssetObj = {};

  if (assetIdToDelete.value.id) {
    console.log(assetIdToDelete.value.index, "inisde");

    deleteAssetObj = {
      asset_id: assetIdToDelete.value.id,
      project_id: project_Id.value,
    };
    deleteLoader.value = true;
  }

  if (deleteAssetObj) {
    deleteAsset(deleteAssetObj).then(() => {
      deleteLoader.value = false;
      isOpenDeleteModal.value = false;
      delete projectStore.assets[assetIdToDelete.value.id];
      emits('deleteAssetModal');
    }).catch((err) => {
      deleteLoader.value = false;
      console.log(err);
    });
  }
};

function handleSubmit (values) {
  console.log("FormValues", values);
  if (isNewFile) {
    loader.value = true;
    console.log("Removed Files:", initialValues.value);
    handleCreateAssets(initialValues.value);
  } else {
    loader.value = true;
    console.log("Existing Files:", initialValues.value);
    handleUpdateAssets(initialValues.value);
  }

}

onMounted(() => {
  if (props?.data?.isNew) {
    nextTick(() => {
      if (rowRef.value) {
        rowRef.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
        const input = rowRef.value.querySelector('input');
        if (input) {
          input.focus();
        }
      }
    });
  }
});
</script>

<template>
    <Form :validationSchema="assetSchema" :initial-values="initialValues" @submit=handleSubmit v-slot="{ errors }">
        <div ref="rowRef" class="w-full grid grid-cols-7 gap-6 h-14" :class="index % 2 === 0 ? 'bg-white' : 'bg-[#F9FAFB]' ">
            <div class="py-1 text-gray-500 text-sm relative flex justify-start items-center px-5">
                {{ props.index + 1 }}
            </div>
        <div class="py-1 text-black text-sm relative flex items-center px-2"
        :class="{ 'bg-[#EBF5FF]': selectedFields['file_name'], 'bg-red-50': errors.file_name }"
        @click="toggleSelect('file_name')" ref="fieldRefs">
              <div class="w-full" :class="{ 'tableSelect': selectedFields['file_name'], 'error-tableSelect': errors?.file_name }">
                <Field
                    type="text"
                    name="file_name"
                    id="file_name"
                    v-model="initialValues.file_name"
                    class="w-full placeholder:text-left bg-inherit"
                    placeholder="Enter Filename"
                    :class="{ 'selected-field': selectedFields.name, 'placeholder:text-red-700': errors?.file_name }"
                    @input="trackChanges"
                />
              </div>
        </div>
        <div class="py-3 text-sm relative flex flex-col items-center px-2"
        :class="{ 'bg-[#EBF5FF]': selectedFields['file_type'], 'bg-red-50': errors.file_type }"
        @click="toggleSelect('file_type')"
        ref="fieldRefs"
        >
          <div class="w-full">
             <Field name="file_type" v-slot="{ field }" class="select-primary1">
              <Multiselect
                v-bind="field"
                v-model="initialValues.file_type"
                :options="Object.values(AssetType)"
                :custom-label="val => assetFileTypeConversion(val)"
                :searchable="false"
                placeholder="Pick a value"
                class="tableSelect h-fit !capitalize placeholder:text-left typebold"
                :show-labels="false"
                :class="{ 'selected-field': selectedFields['file_type'], 'error-placeholder error-dropdown': errors?.file_type }"
                @update:modelValue="trackChanges"
              >
              </Multiselect>
            </Field>
          </div>
        </div>
        <div class="py-2 text-black text-sm relative flex items-center px-2"
        :class="{ 'bg-[#EBF5FF]': selectedFields['media_type'], 'bg-red-50': errors.media_type }"
        @click="toggleSelect('file_name')" ref="fieldRefs">
              <div class="w-full" :class="{ 'tableSelect': selectedFields['media_type'], 'error-tableSelect': errors?.media_type }">
                <Field
                    type="text"
                    name="media_type"
                    id="media_type"
                    v-model="initialValues.media_type"
                    class="w-full bg-inherit"
                    :class="{ 'selected-field': selectedFields.media_type }"
                    readonly
                />
              </div>
        </div>
            <div class="py-3 text-black font-medium relative flex items-center justify-start">
                <div class="w-full">
                    <div v-if="initialValues.file_url ?? ''">
                        <div class="flex items-center justify-start gap-3">
                            <div @mouseover="(e) => { handleOpenTooltip(e, extractFirebaseStoragePath(initialValues.file_url, 'filename')) }"
                                @mouseleave="handleCloseTooltip" class="w-[40%] flex-shrink-0 overflow-hidden">
                                <p>{{
                                    truncateString(extractFirebaseStoragePath(initialValues.file_url,
                                    'filename'),20)}}...
                                </p>
                            </div>
                            <div class="flex items-center gap-2">
                                <div>
                                    <button class="p-1 hover:bg-gray-100 rounded-full" @click.prevent="()=>$emit('openPreviewModal',true , initialValues.file_url ,initialValues.media_type,
                                        )">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <g id="eye">
                                                <path id="Vector"
                                                    d="M8 2C3.6896 2 0 6.57371 0 8C0 9.49314 2.8368 14 8 14C13.1632 14 16 9.49314 16 8C16 6.57371 12.3104 2 8 2ZM8 10.5714C7.52532 10.5714 7.06131 10.4206 6.66663 10.1381C6.27195 9.85551 5.96434 9.45391 5.78269 8.98404C5.60104 8.51418 5.55351 7.99715 5.64612 7.49834C5.73872 6.99953 5.9673 6.54135 6.30294 6.18173C6.63859 5.8221 7.06623 5.5772 7.53178 5.47798C7.99734 5.37876 8.4799 5.42968 8.91844 5.62431C9.35698 5.81893 9.73181 6.14852 9.99553 6.57139C10.2592 6.99426 10.4 7.49142 10.4 8C10.4 8.68199 10.1471 9.33604 9.69706 9.81827C9.24697 10.3005 8.63652 10.5714 8 10.5714Z"
                                                    fill="#6B7280" />
                                            </g>
                                        </svg>
                                    </button>
                                </div>
                                <div>
                                    <button class="p-1 hover:bg-gray-100 rounded-full"
                                        @click.prevent="handleDownloadFile(initialValues.file_url, previewFileName ?? (extractFirebaseStoragePath(initialValues?.file_url, 'filename')))">
                                        <svg width="16" height="16" viewBox="0 0 14 14" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <g id="download" clip-path="url(#clip0_716_9964)">
                                                <g id="Vector">
                                                    <path
                                                        d="M10.2949 5.236C10.1636 5.10141 9.98561 5.02579 9.8 5.02579C9.61438 5.02579 9.43637 5.10141 9.3051 5.236L7.7 6.88226V0.717949C7.7 0.527537 7.62625 0.344924 7.49497 0.210282C7.3637 0.0756408 7.18565 0 7 0C6.81435 0 6.6363 0.0756408 6.50503 0.210282C6.37375 0.344924 6.3 0.527537 6.3 0.717949V6.88226L4.6949 5.236C4.63033 5.16743 4.55309 5.11273 4.46768 5.07511C4.38228 5.03748 4.29043 5.01767 4.19748 5.01685C4.10454 5.01602 4.01236 5.03418 3.92633 5.07028C3.8403 5.10638 3.76215 5.15969 3.69642 5.2271C3.6307 5.29451 3.57872 5.37467 3.54352 5.4629C3.50833 5.55114 3.49062 5.64568 3.49142 5.74101C3.49223 5.83633 3.51154 5.93054 3.54823 6.01814C3.58492 6.10573 3.63824 6.18495 3.7051 6.25118L6.5051 9.12297C6.57012 9.18983 6.64737 9.24288 6.73241 9.27907C6.81746 9.31527 6.90863 9.3339 7.0007 9.3339C7.09277 9.3339 7.18394 9.31527 7.26899 9.27907C7.35403 9.24288 7.43128 9.18983 7.4963 9.12297L10.2963 6.25118C10.4273 6.11635 10.5008 5.93367 10.5006 5.74329C10.5003 5.55292 10.4263 5.37045 10.2949 5.236Z"
                                                        fill="#6B7280" />
                                                    <path
                                                        d="M12.6 8.25641H10.815L8.7325 10.3923C8.50499 10.6257 8.2349 10.8108 7.93763 10.9371C7.64037 11.0634 7.32176 11.1284 7 11.1284C6.67824 11.1284 6.35963 11.0634 6.06237 10.9371C5.7651 10.8108 5.49501 10.6257 5.2675 10.3923L3.185 8.25641H1.4C1.0287 8.25641 0.672601 8.40769 0.41005 8.67698C0.1475 8.94626 0 9.31148 0 9.69231V12.5641C0 12.9449 0.1475 13.3102 0.41005 13.5794C0.672601 13.8487 1.0287 14 1.4 14H12.6C12.9713 14 13.3274 13.8487 13.5899 13.5794C13.8525 13.3102 14 12.9449 14 12.5641V9.69231C14 9.31148 13.8525 8.94626 13.5899 8.67698C13.3274 8.40769 12.9713 8.25641 12.6 8.25641Z"
                                                        fill="#6B7280" />
                                                </g>
                                            </g>
                                        </svg>
                                    </button>
                                </div>
                                <button class="p-1 hover:bg-gray-100 rounded-full"
                                    @click.prevent="handleDeleteFile(initialValues.file_url, props.index)">
                                    <svg width="16" height="16" viewBox="0 0 18 20" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M14 20H4C2.89543 20 2 19.1046 2 18V5H0V3H4V2C4 0.89543 4.89543 0 6 0H12C13.1046 0 14 0.89543 14 2V3H18V5H16V18C16 19.1046 15.1046 20 14 20ZM4 5V18H14V5H4ZM6 2V3H12V2H6ZM12 16H10V7H12V16ZM8 16H6V7H8V16Z"
                                            class="fill-gray-500" />
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div v-else-if="(newUploadFile.currentId === index) && newUploadFile.file && newUploadFile.progress"
                        class="w-full">
                        <span class="text-md text-gray-600">{{ newUploadFile.file.name }}</span>
                        <div class="mt-2">
                            <div class="relative">
                                <button class="absolute -right-6 -top-5 text-gray-500 hover:text-gray-700"
                                    @click.prevent="cancelCurrentUpload">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <g id="x-circle" clip-path="url(#clip0_716_12829)">
                                            <path id="Vector"
                                                d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM10.9656 9.8344C11.042 9.90819 11.103 9.99647 11.1449 10.0941C11.1868 10.1917 11.2089 10.2967 11.2098 10.4029C11.2107 10.5091 11.1905 10.6144 11.1503 10.7128C11.11 10.8111 11.0506 10.9004 10.9755 10.9755C10.9004 11.0506 10.8111 11.11 10.7128 11.1503C10.6144 11.1905 10.5091 11.2107 10.4029 11.2098C10.2967 11.2089 10.1917 11.1868 10.0941 11.1449C9.99648 11.103 9.9082 11.042 9.8344 10.9656L8 9.1312L6.1656 10.9656C6.01472 11.1113 5.81264 11.192 5.60288 11.1901C5.39312 11.1883 5.19247 11.1042 5.04415 10.9559C4.89582 10.8075 4.81169 10.6069 4.80986 10.3971C4.80804 10.1874 4.88868 9.98528 5.0344 9.8344L6.8688 8L5.0344 6.1656C4.88868 6.01472 4.80804 5.81263 4.80986 5.60288C4.81169 5.39312 4.89582 5.19247 5.04415 5.04414C5.19247 4.89582 5.39312 4.81168 5.60288 4.80986C5.81264 4.80804 6.01472 4.88867 6.1656 5.0344L8 6.8688L9.8344 5.0344C9.98528 4.88867 10.1874 4.80804 10.3971 4.80986C10.6069 4.81168 10.8075 4.89582 10.9559 5.04414C11.1042 5.19247 11.1883 5.39312 11.1901 5.60288C11.192 5.81263 11.1113 6.01472 10.9656 6.1656L9.1312 8L10.9656 9.8344Z"
                                                fill="#6B7280" />
                                        </g>
                                        <defs>
                                            <clipPath id="clip0_716_12829">
                                                <rect width="16" height="16" fill="white" />
                                            </clipPath>
                                        </defs>
                                    </svg>
                                </button>
                                <span class="absolute right-0 -top-6 text-sm">{{ newUploadFile.progress }}%</span>
                                <div class="w-full h-2 overflow-hidden bg-slate-300 rounded-md">
                                    <div class="h-full text-[10.5px] text-white bg-[#36f] hover:bg-[#4572fc] relative progress-bar"
                                        :style="{ width: newUploadFile.progress + '%' }">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="upload-btn-wrapper">
                        <label for="file-upload" class="btn">
                            <svg width="14" height="14" viewBox="0 0 14 14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <g id="download" clip-path="url(#clip0_716_9964)">
                                    <g id="Vector">
                                        <path
                                            d="M10.2949 5.236C10.1636 5.10141 9.98561 5.02579 9.8 5.02579C9.61438 5.02579 9.43637 5.10141 9.3051 5.236L7.7 6.88226V0.717949C7.7 0.527537 7.62625 0.344924 7.49497 0.210282C7.3637 0.0756408 7.18565 0 7 0C6.81435 0 6.6363 0.0756408 6.50503 0.210282C6.37375 0.344924 6.3 0.527537 6.3 0.717949V6.88226L4.6949 5.236C4.63033 5.16743 4.55309 5.11273 4.46768 5.07511C4.38228 5.03748 4.29043 5.01767 4.19748 5.01685C4.10454 5.01602 4.01236 5.03418 3.92633 5.07028C3.8403 5.10638 3.76215 5.15969 3.69642 5.2271C3.6307 5.29451 3.57872 5.37467 3.54352 5.4629C3.50833 5.55114 3.49062 5.64568 3.49142 5.74101C3.49223 5.83633 3.51154 5.93054 3.54823 6.01814C3.58492 6.10573 3.63824 6.18495 3.7051 6.25118L6.5051 9.12297C6.57012 9.18983 6.64737 9.24288 6.73241 9.27907C6.81746 9.31527 6.90863 9.3339 7.0007 9.3339C7.09277 9.3339 7.18394 9.31527 7.26899 9.27907C7.35403 9.24288 7.43128 9.18983 7.4963 9.12297L10.2963 6.25118C10.4273 6.11635 10.5008 5.93367 10.5006 5.74329C10.5003 5.55292 10.4263 5.37045 10.2949 5.236Z"
                                            fill="#6B7280" />
                                        <path
                                            d="M12.6 8.25641H10.815L8.7325 10.3923C8.50499 10.6257 8.2349 10.8108 7.93763 10.9371C7.64037 11.0634 7.32176 11.1284 7 11.1284C6.67824 11.1284 6.35963 11.0634 6.06237 10.9371C5.7651 10.8108 5.49501 10.6257 5.2675 10.3923L3.185 8.25641H1.4C1.0287 8.25641 0.672601 8.40769 0.41005 8.67698C0.1475 8.94626 0 9.31148 0 9.69231V12.5641C0 12.9449 0.1475 13.3102 0.41005 13.5794C0.672601 13.8487 1.0287 14 1.4 14H12.6C12.9713 14 13.3274 13.8487 13.5899 13.5794C13.8525 13.3102 14 12.9449 14 12.5641V9.69231C14 9.31148 13.8525 8.94626 13.5899 8.67698C13.3274 8.40769 12.9713 8.25641 12.6 8.25641ZM10.85 12.5641C10.6423 12.5641 10.4393 12.5009 10.2667 12.3826C10.094 12.2643 9.9594 12.0961 9.87993 11.8993C9.80046 11.7025 9.77966 11.486 9.82018 11.2771C9.86069 11.0682 9.96069 10.8763 10.1075 10.7257C10.2544 10.5751 10.4415 10.4725 10.6452 10.4309C10.8488 10.3894 11.06 10.4107 11.2518 10.4922C11.4437 10.5737 11.6077 10.7118 11.723 10.8889C11.8384 11.066 11.9 11.2742 11.9 11.4872C11.9 11.7728 11.7894 12.0467 11.5925 12.2487C11.3955 12.4506 11.1285 12.5641 10.85 12.5641Z"
                                            fill="#6B7280" />
                                    </g>
                                </g>
                                <defs>
                                    <clipPath id="clip0_716_9964">
                                        <rect width="14" height="14" fill="white" />
                                    </clipPath>
                                </defs>
                            </svg>
                            Upload
                        </label>
                        <input id="file-upload" type="file" @change="uploadFile($event,index)" />
                    </div>
                </div>
            </div>
            <div class="py-1 text-black font-medium relative text-nowrap flex justify-start items-center">
                {{ initialValues.updated_at ? formatTimestamp(initialValues.updated_at) : 'Not Modified' }}
            </div>
            <div class="py-1 text-gray-500 text-sm flex items-center justify-evenly">
                <!-- Left Icon -->
                <div v-if="isEdited">
                    <Button id="submit" type="submit" title="Save">
                        <template v-slot:svg>
                            <div v-if="loader" class="loader !h-5 !w-5 !border-3"></div>
                        </template>
                    </Button>
                </div>

                <!-- Right Icons -->
                <div class="h-7 flex justify-center items-center px-1.5 bg-gray-100 rounded-lg hover:cursor-pointer">
                    <span @click.prevent="$emit('deleteRow', initialValues._id)" v-if="isNewFile">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_716_13540)">
                                <path
                                    d="M14.5179 3.36842H11.2586V1.68421C11.2586 1.23753 11.0869 0.809144 10.7813 0.493294C10.4757 0.177443 10.0612 0 9.62898 0L6.36972 0C5.93751 0 5.52301 0.177443 5.2174 0.493294C4.91178 0.809144 4.74009 1.23753 4.74009 1.68421V3.36842H1.48083C1.26473 3.36842 1.05748 3.45714 0.904669 3.61507C0.751862 3.77299 0.666016 3.98719 0.666016 4.21053C0.666016 4.43387 0.751862 4.64806 0.904669 4.80598C1.05748 4.96391 1.26473 5.05263 1.48083 5.05263H2.29565V14.3158C2.29565 14.7625 2.46734 15.1909 2.77295 15.5067C3.07857 15.8226 3.49307 16 3.92527 16H12.0734C12.5056 16 12.9201 15.8226 13.2257 15.5067C13.5314 15.1909 13.7031 14.7625 13.7031 14.3158V5.05263H14.5179C14.734 5.05263 14.9412 4.96391 15.094 4.80598C15.2468 4.64806 15.3327 4.43387 15.3327 4.21053C15.3327 3.98719 15.2468 3.77299 15.094 3.61507C14.9412 3.45714 14.734 3.36842 14.5179 3.36842ZM6.36972 1.68421H9.62898V3.36842H6.36972V1.68421ZM7.18453 12.6316C7.18453 12.8549 7.09869 13.0691 6.94588 13.227C6.79307 13.385 6.58582 13.4737 6.36972 13.4737C6.15362 13.4737 5.94637 13.385 5.79356 13.227C5.64075 13.0691 5.5549 12.8549 5.5549 12.6316V6.73684C5.5549 6.5135 5.64075 6.29931 5.79356 6.14138C5.94637 5.98346 6.15362 5.89474 6.36972 5.89474C6.58582 5.89474 6.79307 5.98346 6.94588 6.14138C7.09869 6.29931 7.18453 6.5135 7.18453 6.73684V12.6316ZM10.4438 12.6316C10.4438 12.8549 10.3579 13.0691 10.2051 13.227C10.0523 13.385 9.84508 13.4737 9.62898 13.4737C9.41288 13.4737 9.20562 13.385 9.05282 13.227C8.90001 13.0691 8.81416 12.8549 8.81416 12.6316V6.73684C8.81416 6.5135 8.90001 6.29931 9.05282 6.14138C9.20562 5.98346 9.41288 5.89474 9.62898 5.89474C9.84508 5.89474 10.0523 5.98346 10.2051 6.14138C10.3579 6.29931 10.4438 6.5135 10.4438 6.73684V12.6316Z"
                                    fill="#6B7280" />
                            </g>
                            <defs>
                                <clipPath id="clip0_716_13540">
                                    <rect width="16" height="16" fill="white" />
                                </clipPath>
                            </defs>
                        </svg>
                    </span>
                    <span v-else @click.prevent="() => { isOpenDeleteModal = true; assetIdToDelete.id = initialValues._id; assetIdToDelete.index = index }">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_1362_13369)">
                                <path
                                    d="M15.1111 0.666992H0.888889C0.397969 0.666992 0 1.05326 0 1.52974L0 3.25523C0 3.73171 0.397969 4.11797 0.888889 4.11797H15.1111C15.602 4.11797 16 3.73171 16 3.25523V1.52974C16 1.05326 15.602 0.666992 15.1111 0.666992Z"
                                    fill="#6B7280" />
                                <path
                                    d="M0.888889 5.84346V13.6082C0.888889 14.0658 1.07619 14.5047 1.40959 14.8283C1.74299 15.1519 2.19517 15.3337 2.66667 15.3337H13.3333C13.8048 15.3337 14.257 15.1519 14.5904 14.8283C14.9238 14.5047 15.1111 14.0658 15.1111 13.6082V5.84346H0.888889ZM10.6667 8.4317C10.6667 8.66051 10.573 8.87995 10.4063 9.04175C10.2396 9.20355 10.0135 9.29444 9.77778 9.29444H6.22222C5.98647 9.29444 5.76038 9.20355 5.59368 9.04175C5.42698 8.87995 5.33333 8.66051 5.33333 8.4317V7.56895C5.33333 7.34014 5.42698 7.1207 5.59368 6.9589C5.76038 6.7971 5.98647 6.70621 6.22222 6.70621C6.45797 6.70621 6.68406 6.7971 6.85076 6.9589C7.01746 7.1207 7.11111 7.34014 7.11111 7.56895H8.88889C8.88889 7.34014 8.98254 7.1207 9.14924 6.9589C9.31594 6.7971 9.54203 6.70621 9.77778 6.70621C10.0135 6.70621 10.2396 6.7971 10.4063 6.9589C10.573 7.1207 10.6667 7.34014 10.6667 7.56895V8.4317Z"
                                    fill="#6B7280" />
                            </g>
                            <defs>
                                <clipPath id="clip0_1362_13369">
                                    <rect width="16" height="16" fill="white" />
                                </clipPath>
                            </defs>
                        </svg>
                    </span>
                </div>
            </div>

        </div>
    </Form>

    <!-- Modal for Delete Row -->
    <Modal :open="isOpenDeleteModal" v-if="isOpenDeleteModal">
        <DeleteModalContent :trash="true" @closeModal="(e) => isOpenDeleteModal = false" :loader="deleteLoader"
            @handleDelete="handleDeleteAssets" :dataName="'Asset'" />
    </Modal>
</template>

<style scoped>

.upload-btn-wrapper {
    position: relative;
    overflow: hidden;
}

.btn,.btn:focus {
    color: #9E9E9E;
    background: white !important;
    display: flex;
    justify-items: center;
    gap: 10px;
    box-shadow: none !important;
}

.upload-btn-wrapper input[type=file] {
    font-size: 100px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0;
}

.tableSelect .multiselect__tags {
  border: none;
  padding-right: 30px;
  /* space for the arrow */
  max-width: 80%;
}

.tableSelect .multiselect__option,
.tableSelect .multiselect__option--selected,
.tableSelect .multiselect__option:hover,
.tableSelect .multiselect__option--selected:hover {
  background-color: transparent !important;
  color: #6B7280 !important;
  font-weight: 100 !important;
  text-transform: capitalize !important;
}

.tableSelect>.multiselect__select {
  background: none !important;
  right: 20px;
}

.tableSelect .multiselect__single{
  background-color: inherit;
  font-size: 14px;
}

.tabselect .multiselect__option--highlight .multiselect__option::after {
  background: red !important;
}

.tableSelect .multiselect__content-wrapper {
  width: 12rem;
  max-height: 175px !important;
}

.tableSelect .multiselect__element:hover {
  background-color: #EBF5FF;
}

.tableSelect.selected-field span .multiselect__single {
  background-color: #EBF5FF;
}

.tableSelect.selected-field .multiselect__input {
  background-color: #EBF5FF;
}

.tableSelect>input {
  background-color: #EBF5FF;
}

.tableSelect .multiselect__tags .multiselect__tags-wrap .multiselect__tag {
  background-color: #1c64f2;
}

.tableSelect .multiselect__select::before {
  width: 7px;
  height: 7px;
}

.tableSelect .multiselect__content-wrapper .multiselect__content .multiselect__element .multiselect__option--highlight::after {
  background-color: #1c64f2;
}

.error-placeholder .multiselect__tags .multiselect__placeholder {
  color: #C81E1E;
}

.error-dropdown .multiselect__select::before {
  border-left-color: #C81E1E;
  border-bottom-color: #C81E1E;
}

.error-tableSelect.selected-field .multiselect__single {
  background-color: #FDF2F2;
}

.error-tableSelect.selected-field .multiselect__input {
  background-color: #FDF2F2;
}

.error-tableSelect>input {
  background-color: #FDF2F2;
}

.typebold .multiselect__tags .multiselect__single {
  font-weight: 500;
}
</style>

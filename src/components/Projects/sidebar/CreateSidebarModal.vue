<script setup>
import Spinner from '../../common/Spinner.vue';
import { Field, Form, ErrorMessage } from 'vee-validate';
import { sideBarSchema } from '../../../validationSchema/sidebar';
import { createSidebarOption } from '../../../api/projects/sidebar/index';
import { ref, watch } from 'vue';
import { ProjectStore } from '../../../store/project';
import { Org_Store } from '../../../store/organization';
import {sidebar_icons} from '../../../helpers/icons.ts';
import { sidebarTypeList } from '../../../helpers/constants';
import { useRoute } from 'vue-router';
import Multiselect from 'vue-multiselect';

const projectStore = ProjectStore();
const organizationStore = Org_Store();

const loader = ref(false);
const route = useRoute();
const selectedType = ref();
const sceneId = ref(null);
const link = ref(null);
const projectId = ref(route.params.project_id);
const NameList = ref(Object.values(sidebar_icons).map((elem) => {
  return {id: elem.name, name: elem.name};
}));

projectStore.RefreshScenes(projectId.value);
organizationStore.RefreshMasterScenes();

const emits = defineEmits(['close']);

const handleCreateSidebarOption = (data) => {
  loader.value = true;
  createSidebarOption(data)
    .then((res) => {
      const newObject = {};
      newObject[res._id] = res;
      projectStore.SyncMultipleSidebarOptions(newObject);
      emits('close', true);
    })
    .catch((err) => {
      console.log('output->err create sidebar : ', err);
    })
    .finally(() => {
      loader.value = false;
    });
};
const sidebarNames = Object.values(sidebar_icons).map((elem) => elem.name);
const handleSubmitForm = (values) => {
  console.log('output->values', values);
  const obj = {
    name: values.name.name,
    ...(sidebarNames.includes(values.name) ? {icon_id: Object.values(sidebar_icons).find((elem) => elem.name === values.name)?.id}: {icon_id: values.icon_id}  ),
    ...((values.scene_id) && {scene_id: values.scene_id}),
    ...(values.link && {link: values.link}),
    type: values.type,
    project_id: projectId.value,
  };
  console.log(obj);
  handleCreateSidebarOption(obj);
};

const isOpen = ref(null);
const selected = ref(null);
const selectedName = ref(null);
const nameRef = ref(null);
const isIconRequired = ref(false);
function selectOption (val){
  isOpen.value = false;
  const id = Object.keys(sidebar_icons).find((key) => {
    return sidebar_icons[key].name === val.name;
  });
  const obj = {id: id, ...val};
  selected.value = obj;
}

watch(selectedName, (newVal) => {
  if (sidebarNames.includes(newVal)){
    isIconRequired.value = false;
  } else {
    isIconRequired.value = true;
  }
});

const HandleAddName = (newValue) => {
  const newValObj = {id: newValue, name: newValue};
  selectedName.value = newValObj;
  NameList.value.push(newValObj);
};
</script>

<template>
        <div
            class="bg-white border border-gray-200 w-[536px] rounded-lg"
            >
            <div class="">
                <div class="h-9  flex justify-between items-center px-2.5 border-b border-gray-200 rounded-t-lg">
                    <p class="text-sm font-medium text-gray-900">
                        Create Category</p>
                        <button  class="fill-gray-400" @click="$emit('close',false)">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg></button>
                </div>
                <Form @submit="handleSubmitForm"
                    :validation-schema="sideBarSchema"
                    class="flex flex-col justify-center mb-0">
                    <div
                        class="p-2.5  grid grid-cols-2 gap-4">

                        <div class="col-span-auto relative">
                            <label for="type"
                                class="text-sm font-medium text-gray-900">
                                Type</label>
                            <div class="">
                                <Field as="select" type="text"
                                    name="type"
                                    id="type"
                                    v-model="selectedType"
                                    class="flex w-full rounded-lg h-10 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300"
                                    placeholder="Type">
                                    <option value="" disabled>
                                        Choose </option>
                                    <option value="" disabled
                                        v-if="!sidebarTypeList">
                                        no type yet! </option>
                                    <option v-else
                                        :value="option"
                                        v-for="option, index in  sidebarTypeList"
                                        :key="index"
                                        class="text-black"> {{
                                            option }} </option>
                                </Field>
                                <ErrorMessage as="p"
                                    class="absolute text-sm text-rose-500 mt-1"
                                    name="type" />
                            </div>
                        </div>

        <div  class="w-full relative">
            <label class="text-sm font-medium text-gray-900">Name</label>
            <div ref="nameRef" class="relative">
                <Field  name="name" :model-value="selectedName" v-slot="{ field }">

                                      <Multiselect class="h-8"   v-bind="field" :taggable="true" @tag="(val)=>HandleAddName(val)" :allow-empty="false" v-model="selectedName" :searchable="true" :show-labels="false" :custom-label="(val) => val.name" placeholder="Name"   @select="handleProjectSelection" :options="NameList" track-by="id" maxHeight="250" >
                                      </Multiselect>

                                    </Field>
                <ErrorMessage as="p"
                                    class="absolute text-sm text-rose-500 mt-1"
                                    name="name" />
            </div>
        </div>
                        <div class="col-span-auto relative" v-if="selectedType === 'projectscene' || selectedType === 'masterscene'">
                            <label for="scene_id"
                                class="text-sm font-medium text-gray-900">
                                scene</label>
                            <div class="">
                                <Field as="select" type="text"
                                   v-model="sceneId"
                                   :disabled="link!==null && link!==''"
                                    name="scene_id"
                                    id="scene_id"
                                    class="flex w-full rounded-lg h-10 text-gray-500 text-sm transition-all duration-[0.3s] ease-in-out px-3 py-0 border border-gray-300"
                                    placeholder="scene">
                                    <option value="">
                                        Choose </option>
                                    <option value="" disabled
                                        v-if="selectedType=='masterscene'?!organizationStore.masterScenes: !projectStore.scenes">
                                        no type yet! </option>
                                    <option v-else
                                        :value="option.sceneData._id"
                                        v-for="option, index in  selectedType=='masterscene'?organizationStore.masterScenes:projectStore.scenes"
                                        :key="index"
                                        class="text-black"> {{
                                            option.sceneData.name }} </option>
                                </Field>
                                <ErrorMessage as="p"
                                    class="absolute text-sm text-rose-500 mt-1"
                                    name="scene_id" />
                            </div>
                        </div>
                        <div class="col-span-auto" v-if="selectedType === 'custom'">
                <label for="link" class="text-sm font-medium text-gray-900"
                  >link</label
                >
                <Field
                v-model="link"
                :disabled="sceneId!==null && sceneId!==''"
                  name="link"
                  class="flex w-full rounded-lg h-10 transition-all duration-[0.3s] text-sm ease-in-out px-3 py-0 border border-gray-300 placeholder:text-start placeholder:text-gray-500 placeholder:text-sm placeholder:font-normal"
                  placeholder="Enter Link"
                />
                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="link" />
              </div>
<!--
              <div class="col-span-auto" v-if="selectedType === 'custom'">
                <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    name="link_or_scene" />
              </div> -->
              <div v-if="isIconRequired" class="w-full relative pb-2.5">
  <label class="text-sm font-medium text-gray-900">Icon Type</label>
  <div class="relative">
    <button
      @click="isOpen = !isOpen"
      type="button"
      class="w-full bg-white border border-gray-300 rounded-md px-3 py-2 text-left shadow-sm"
    >
      <div v-if="selected" class="flex h-full items-center space-x-2">
        <div v-html="selected.active"></div>
      </div>
      <span v-else class="text-gray-500">Choose an icon</span>
      <svg class="absolute right-2 top-3 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <div
      v-if="isOpen"
      class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-50 overflow-auto"
    >
      <div class="grid grid-cols-3 gap-1 p-2">
        <button
          v-for="option in sidebar_icons"
          :key="option.id"
          @click="selectOption(option)"
          type="button"
          class="flex flex-col items-center justify-center p-3 rounded-md hover:bg-gray-100 focus:bg-gray-100 focus:outline-none transition-colors duration-150"
          :class="{ 'bg-blue-50 border border-blue-200': selected?.name === option.name }"
        >
          <div v-html="option.active" class="w-6 h-6 mb-1"></div>
        </button>
      </div>
    </div>
  </div>

  <div v-if="selected" class="mt-3 hidden">
    <label class="text-sm font-medium text-gray-900">Icon Label</label>
    <Field
      v-model="selected.id"
      name="icon_id"
      type="text"
      class="w-full mt-1 rounded-md border border-gray-300 px-3 py-1 text-sm placeholder:text-gray-400"
    />
  </div>
  <ErrorMessage  name="icon_id" class="absolute text-sm text-rose-500 mt-1" />
</div>
                    </div>

                    <div
                        class="p-2.5 mt-2">
                        <button type="submit"
                            :disabled="loader"
                            class="h-8 w-full text-sm font-medium rounded-lg text-white flex justify-center items-center bg-blue-700">
                            {{!loader ?'Save':""}}
                            <Spinner class="fill-white text-gray-200" v-if="loader" />
                        </button>
                    </div>
                </Form>
            </div>
        </div>
</template>

<style>

</style>

<script setup>
import Button from '../common/Button.vue';
import { useRouter, useRoute } from 'vue-router';
import { UserStore } from '../../store/index';
import { computed, onMounted, ref, watch } from 'vue';
import { FilterProjectsFromOrganization } from '../../api/organization';
import { Org_Store } from '../../store/organization';
import Modal from '../common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import { MoveProjectToTrash, RestoreProject } from '@/api/projects';
import {countryCity, createProjectSVG} from '@/helpers/constants';
import NewDropDown from '@/components/common/NewDropDown.vue';
import ToggleSliderButton from '@/components/common/ToggleSliderButton.vue';
import ProjectTableView from './ProjectTableView.vue';
import ProjectCardView from './ProjectCardView.vue';
import { uiOperations } from '@/store/uiOperations';
import { plusIcon } from '@/helpers/icons';
import { DeleteTrash, GetAllTrash } from '@/api/trash';
import { getCookie } from '@/helpers/domhelper';
import RestoreModelContent from '@/components/common/ModalContent/RestoreModelContent.vue';

const OrganizationStore = Org_Store();
const uistore = uiOperations();
const route = useRoute();
const router = useRouter();
const Store = UserStore();
const allProjects = ref([]);
const openDeleteModal = ref(false);
const openTrashDeleteModal = ref(false);
const showRestoreProject = ref(false);
const projectToDelete = ref(null);
const trashToDelete = ref(null);
const projectToRestore = ref(null);
const deleteLoader = ref(false);
const restoreLoader = ref(false);
const totalProjectsCount = ref(0);
const filteredProjectsCount = ref(0);
const projectsTrashCount = ref(0);
const filterData = ref([]);
const TrashData = ref([]);
const AllTrashData = ref([]);
const projectTrashData = ref([]);
const isLoading = ref(true);
const isTrashLoading = ref(true);
const routeQuery = ref();
const loading = ref(false);
const pageView = ref('card');
const current_organization = ref();
const activePage = ref('Published');
const count = ref(1);
const countTrash = ref(1);

watch(() => {
  Store.callbackFunctionMonitorChanges();
});
const getListOfProjects = async (routeQuery, page) => {
  if (!isLoading.value) {
    return;
  }
  isLoading.value = false;
  loading.value = true;
  console.log("getListOfProjects called");
  const query = {};
  FilterProjectsFromOrganization(Object.values(route.query).length > 0 ? routeQuery:query, page).then((projects_list) => {
    if (Object.values(projects_list.projectsObj).length === 0){
      uistore.showToast('Failed to load projects', 'error');
    }
    if (Object.values(route.query).length > 0){

      console.log("previos page data", filterData.value);
      if (filterData.value.length > 0){
        console.log("already upating");
        filterData.value = [...filterData.value, ...Object.values(projects_list.projectsObj)];
        OrganizationStore.SyncMultipleProjects(projects_list.projectsObj);
      } else {
        console.log("new updating");
        filterData.value = Object.values(projects_list.projectsObj);
        OrganizationStore.SyncMultipleProjects(filterData.value);
      }
    } else {
      if (allProjects.value.length > 0){
        allProjects.value = [...allProjects.value, ... Object.values(projects_list.projectsObj)];
        OrganizationStore.SyncMultipleProjects(projects_list.projectsObj);
      } else {
        totalProjectsCount.value = projects_list.total;
        allProjects.value = Object.values(projects_list.projectsObj);
        OrganizationStore.SyncMultipleProjects(projects_list.projectsObj);
      }
    }
    isLoading.value = true;
    loading.value = false;
  });
};
const getListOfProjectsTrash = async (page) => {
  if (!isTrashLoading.value) {
    return;
  }
  isTrashLoading.value = false;
  loading.value = true;
  console.log("GetAllTrash called");
  GetAllTrash(null, current_organization.value, 'project', page).then((projects_trashList) => {
    // if (Object.values(projects_trashList.items).length === 0){
    //   uistore.showToast('ProjectsTrash is Empty', 'error');
    // }
    // if (Object.values(route.query).length > 0){

    console.log("previos page data", TrashData.value);
    if (TrashData.value.length > 0){
      console.log("already upating");
      Object.values(projects_trashList.items).forEach((item) => {
        AllTrashData.value.push(Object.values(item.data)[0]);
        TrashData.value.push(Object.values(item.data)[0]);
        projectTrashData.value.push(item);
      });

    } else {
      console.log("new updating");
      projectsTrashCount.value = projects_trashList.total;
      Object.values(projects_trashList.items).forEach((item) => {
        AllTrashData.value.push(Object.values(item.data)[0]);
        TrashData.value.push(Object.values(item.data)[0]);
        projectTrashData.value.push(item);
      });
    }
    isTrashLoading.value = true;
    loading.value = false;
  });
};
onMounted(async () => {
  current_organization.value = getCookie('organization').toLocaleLowerCase();
  console.log("current_organization", current_organization);

  await getListOfProjects({}, 1);
  TrashData.value = [];
  AllTrashData.value = [];
  await getListOfProjectsTrash(1);
  router.replace({
    query: {},
  });
});

function handleFilterExperience (val){
  console.log("handleFilterExperience", val);
  if (val.length > 0){
    router.push({
      query: {
        ...router.currentRoute.value.query,
        experience: val.map((item) => item.toLowerCase().replace(' ', '_')),
      },
    });
  } else {
    router.push({
      query: {
        ...router.currentRoute.value.query,
        experience: undefined,
      },
    });
  }
}
function handleFilterLocation (val){
  console.log("handleFilterLocation", val);

  if (val.length > 0){
    console.log("added to query");
    router.push({
      query: {
        ...router.currentRoute.value.query,
        country: val.map((item) => item),
      },
    });
  } else {
    router.push({
      query: {
        ...router.currentRoute.value.query,
        country: undefined,
      },
    });
  }
}
function resetFilters (){
  router.push({
    path: router.currentRoute.value.path,
    query: {},
  });

}
function handleFilterPropertyType (val){
  if (val.length > 0){
    router.push({
      query: {
        ...router.currentRoute.value.query,
        property_type: val.map((item) => item.toLowerCase()),
      },
    });
  } else {
    router.push({
      query: {
        ...router.currentRoute.value.query,
        property_type: undefined,
      },
    });
  }
}

watch(
  () => route.query,
  async (newQuery) => {
    if (newQuery.experience || newQuery.country || newQuery.property_type) {
      loading.value = true;
      FilterProjectsFromOrganization(newQuery, 1).then(async (projects_list) => {
        if (Object.values(projects_list.projectsObj).length === 0){
          filterData.value = [];
          allProjects.value = [];
          uistore.showToast('No Projects Found with this Filter', 'error');
          // await getListOfProjects({}, 1);
          // count.value = 1;
          // allProjects.value = [];

        } else {
          console.log("result", projects_list.projectsObj);
          filteredProjectsCount.value = projects_list.total;
          filterData.value = Object.values(projects_list.projectsObj);
          console.log("First filter update", filterData.value);
          count.value = 1;
        }
        loading.value = false;
      });
      routeQuery.value = newQuery;
    } else {
      console.log("Should not happen");
      allProjects.value = [];
      filterData.value = [];
      await getListOfProjects({}, 1);
      TrashData.value = [];
      AllTrashData.value = [];
      await getListOfProjectsTrash(1);
      count.value = 1;
      countTrash.value = 1;
    }
  },
  { deep: true },
);

function handleMultipleAPICall (isBottom){
  console.log("route Query", Object.values(route.query).length);
  if (isBottom && isLoading.value){
    console.log("routequery", route.query);
    console.log("routeQuery.value", routeQuery.value);
    count.value++;
    if (count.value <= Math.ceil(totalProjectsCount.value/10)){
      if (Object.values(route.query).length > 0){
        if (count.value <= Math.ceil(filteredProjectsCount.value/10)){
          getListOfProjects(routeQuery.value, count.value);
        }
      } else {
        getListOfProjects({}, count.value);
      }
    }
  }
}
function handleMultipleTrashAPI (isBottom){
  console.log("handleMultipleTrashAPI Called", Object.values(route.query).length);
  if (isBottom && isTrashLoading.value){
    console.log("isBottom", isBottom);
    countTrash.value++;
    if (countTrash.value <= Math.ceil(projectsTrashCount.value/10)){
      getListOfProjectsTrash(countTrash.value);
      // if (Object.values(route.query).length > 0){
      //   if (count.value <= Math.ceil(filteredProjectsCount.value/10)){
      //     getListOfProjectsTrash(count.value);
      //   }
      // } else {
      //   getListOfProjectsTrash(count.value);
      // }
    }
  }
}

const locationOptions = Object.keys(countryCity);

function handleToggleData (val){
  if (val === 'card'){
    pageView.value = 'card';
  } else {
    pageView.value = 'list';
  }
}

const filterOptions = {
  experience: ['PropVR 360', 'RT Application', 'Embed V1'],
  location: locationOptions,
};
const filteredData = computed(() => {
  if (filterData.value.length > 0){
    console.log("data sent to component");
    return filterData.value;
  }
  // If no filters are applied, return all projects
  return allProjects.value;

});
const filteredTrashData = computed(() => {
  if (TrashData.value.length > 0){
    console.log("data sent to component");
    return TrashData.value;
  }
  console.log("returnded all trash");

  return AllTrashData.value;

});

const handleMoveToTrash = (projectId) => {
  deleteLoader.value = true;
  const obj = {
    ...({project_id: [projectToDelete.value]}),
    timeStamp: Date.now(),
  };

  MoveProjectToTrash(projectId, obj).then(() => {
    console.log("obj to delete");

    FilterProjectsFromOrganization({}, 1).then(async (projects_list) => {
      filterData.value = [];
      totalProjectsCount.value = projects_list.total;
      allProjects.value = Object.values(projects_list.projectsObj);
      OrganizationStore.SyncMultipleProjects(projects_list.projectsObj);
      count.value = 1;
      deleteLoader.value = false;
      openDeleteModal.value = false;

      TrashData.value = [];
      AllTrashData.value = [];
      countTrash.value = 1;
      await getListOfProjectsTrash(1);
    });

  }).catch((err) => {
    deleteLoader.value = false;
    console.log(err);
  });
};
const trashId = ref({});
const handleRestoreProject = (id) => {
  restoreLoader.value = true;
  console.log(" trashDataObj", projectTrashData.value);
  trashId.value = projectTrashData.value.filter((item) => Object.keys(item.data)[0] === id);
  RestoreProject(trashId.value[0]._id).then(async () => {
    showRestoreProject.value = false;
    restoreLoader.value = false;
    FilterProjectsFromOrganization({}, 1).then((projects_list) => {
      filterData.value = [];
      totalProjectsCount.value = projects_list.total;
      allProjects.value = Object.values(projects_list.projectsObj);
      OrganizationStore.SyncMultipleProjects(projects_list.projectsObj);
      count.value = 1;
      deleteLoader.value = false;
      openDeleteModal.value = false;
    });
    TrashData.value = [];
    countTrash.value = 1;
    await getListOfProjectsTrash(1);
  }).catch((err) => {
    console.log("error", err);
  }).finally(() => {
    showRestoreProject.value = false;
    restoreLoader.value = false;
  });
};

const handleDeleteTrash = (id) => {
  const trashId = projectTrashData.value.filter((item) => Object.keys(item.data)[0] === id);
  const trashArray = [trashId[0]._id];
  deleteLoader.value = true;
  DeleteTrash(trashArray).then(async () => {
    deleteLoader.value = false;
    openTrashDeleteModal.value = false;
    TrashData.value = [];
    countTrash.value = 1;
    await getListOfProjectsTrash(1);
  }).catch((err) => {
    console.log("error", err);
  }).finally(() => {
    deleteLoader.value = false;
    openTrashDeleteModal.value = false;
  });
};
const isAdmin = computed(() => {
  return Store.user_role && Store.user_role.userrole?.role === 'admin';
});

</script>

<template>
  <div v-if="isAdmin" class="h-full">
    <div v-if="!Store.isMobile" class="select-none">
      <div class="dynamic-header">
        <div class="dynamic-heading">
          <p class="dynamic-topic">Projects</p>
          <p class="dynamic-sub-topic">Create and manage projects.</p>
        </div>
            <div class="lg:flex lg:justify-end lg:gap-7 md:grid md:grid-cols-3 md:w-[60%] md:gap-x-1 md:gap-y-1 select-none">
          <div class="md:w-fit">
                    <NewDropDown
                    v-show="activePage === 'Published'"
                    title="Experience"
                    type="select"
                    inputType="checkbox"
                    :options="['PropVR 360','RT Application','Embed V1']"
                    @optionSelected="handleFilterExperience"/>
          </div>
          <div class="md:w-fit">
                    <NewDropDown
                    v-show="activePage === 'Published'"
                    title="Location"
                    type="select"
                    :options="locationOptions"
                    inputType="checkbox"
                    searchBar="true"
                    width="w-44"
                     @optionSelected="handleFilterLocation"
                    />
          </div>
          <div class="md:w-fit">
                    <NewDropDown
                    v-show="activePage === 'Published'"
                    title="Property"
                    type="select"
                    inputType="checkbox"
                    :options="['Villa','Tower','Both']"
                    @optionSelected="handleFilterPropertyType"/>
          </div>
          <div>
                    <ToggleSliderButton :title="['Card','List']" @toggleData="handleToggleData"/>
          </div>
          <div>
                    <Button
                    title="Add Project"
                    theme="primary" class="h-10  hover:bg-[#1a56db] active:bg-[#1e429f]"
                    @click="()=>router.push(`/projects/add`)">
            </Button>
          </div>

        </div>
      </div>
      <div class="h-[6%] w-full  border-b-2 mb-2 flex gap-8">
                <span class="h-full mt-[2px] cursor-pointer  w-[6rem] flex justify-center items-center " :class="activePage === 'Published'?'border-b-2 border-b-[#1a56db]':''" @click="()=>activePage = 'Published'">
                    <p class=" text-sm font-medium" :class="activePage === 'Published' ? 'text-[#1a56db] ':'text-gray-500'">Published</p>
        </span>
                <span class="h-full mt-[2px] cursor-pointer w-[6rem] flex justify-center items-center" :class="activePage === 'Archive'?'border-b-2 border-b-[#1a56db]':''" @click="()=>activePage = 'Archive'">
                    <p class="text-sm font-medium" :class="activePage === 'Archive' ? 'text-[#1a56db]':'text-gray-500'">Archive</p>
        </span>
      </div>
        <div  v-if="Object.keys(filteredData).length === 0  && activePage === 'Published'" class="h-[70vh] w-full flex justify-center items-center"  >
        <div class="w-[20%] min-w-[20%] h-full flex flex-col items-center justify-center gap-4">
          <div class="flex flex-col justify-center items-center">
            <span v-html="createProjectSVG"></span>
            <div class="flex justify-center text-center">
              <p class="text-base font-normal">Create Project to get started</p>
            </div>
          </div>

          <div class="flex justify-center">
                <Button
                    title="Add Project"
                    theme="primary" class="h-10 hover:bg-[#1a56db] active:bg-[#1e429f]"
                    @click="router.push(`/projects/add`);"
                    >
            </Button>
          </div>

        </div>
      </div>
        <div  v-if="Object.keys(filteredTrashData).length === 0 && activePage === 'Archive'" class="h-[70vh] w-full flex justify-center items-center"  >
        <div class="w-[20%] min-w-[20%] h-20 flex flex-col justify-center gap-4">
          <div class="flex flex-col justify-center items-center">
            <span v-html="createProjectSVG"></span>
            <div class="flex justify-center text-center">
              <p class="text-base font-normal">No Projects in Trash</p>
            </div>
          </div>
        </div>
      </div>
        <div v-if="activePage === 'Published' && pageView === 'card' && Object.keys(filteredData).length > 0" class="h-[75vh]" >
            <ProjectCardView :loading="loading" :data="filteredData" @callApiFunction="handleMultipleAPICall" @openModal="(val,projectId) => {if (val) {openDeleteModal = true; projectToDelete = projectId;}}"/>
      </div>
        <div v-if="activePage === 'Published' && pageView === 'list' && Object.keys(filteredData).length > 0" class="h-[75vh]">
          <ProjectTableView :loading="loading" :data="filteredData" @callApiFunction="handleMultipleAPICall" @openModal="(val,projectId) => {if (val) {openDeleteModal = true; projectToDelete = projectId;}}"/>
      </div>
        <div  v-if="activePage === 'Archive' && pageView === 'card' && Object.keys(filteredTrashData).length > 0" class="h-[75vh]" >
            <ProjectCardView section="Archive" :loading="loading" :data="filteredTrashData" @callApiFunction="handleMultipleTrashAPI"  @openDeleteTrashModal="(projectId)=>{openTrashDeleteModal= true;trashToDelete = projectId}"  @openModal="(projectId) =>{showRestoreProject = true; projectToRestore = projectId;}"/>
      </div>
        <div v-if="activePage === 'Archive' && pageView === 'list' && Object.keys(filteredTrashData).length > 0" class="h-[75vh]">
          <ProjectTableView section="Archive" :loading="loading" :data="filteredTrashData" @callApiFunction="handleMultipleTrashAPI"  @openDeleteTrashModal="(projectId)=>{openTrashDeleteModal= true;trashToDelete = projectId}"  @openModal="(projectId) =>{showRestoreProject = true;projectToRestore = projectId;}"/>
      </div>
      <Modal :open="openTrashDeleteModal">
              <DeleteModalContent
                  :trash="false"
                  :loader="deleteLoader"
                  @closeModal="(e) => openTrashDeleteModal = false"
                  @handleDelete="()=>handleDeleteTrash(trashToDelete)"
                  :dataName="'project'" />
      </Modal>
      <Modal :open="showRestoreProject">
              <RestoreModelContent
                  :loader="restoreLoader"
                  @closeModal="() => showRestoreProject = false"
                  @handleRestore="()=>handleRestoreProject(projectToRestore)"
                  :dataName="'project'" />
      </Modal>
      <Modal :open="openDeleteModal">
              <DeleteModalContent
                  :trash="true"
                  :loader="deleteLoader"
                  @closeModal="() => openDeleteModal = false"
                  @handleDelete="()=>handleMoveToTrash(projectToDelete)"
                  :dataName="'project'" />
      </Modal>

    </div>

    <div v-else class="w-full h-full flex flex-col bg-white  ">
      <div class="w-[95%] h-[12%] md:h-[20%] flex justify-between items-center mx-auto">
        <div class="flex flex-col gap-1">
          <p class="text-lg">Projects</p>
          <p class="text-xs text-gray-500">Create and manage projects.</p>
        </div>
        <div class="flex w-[50%] justify-end gap-2 ">
          <div>
                    <ToggleSliderButton :title="['Card View','List View']"  @toggleData="handleToggleData"/>
          </div>
          <div v-if="activePage === 'Published'" class="md:w-fit">
                    <NewDropDown
                    :title="['Experience','Location']"
                    type="select"
                    inputType="checkbox"
                    :options="filterOptions"
                    @experienceOptionSelected="handleFilterExperience"
                    @locationOptionSelected="handleFilterLocation"
                    @resetFilters="resetFilters"/>
          </div>
          <div class="md:w-fit">
            <button type="submit" class="h-10 w-10  bg-[#1c64f2] text-white hover:bg-[#1a56db] active:bg-[#1e429f]
                  rounded-lg flex flex-row justify-center items-center gap-2"
                  @click="router.push('/projects/add')">
              <span v-html="plusIcon"></span>
            </button>
          </div>
        </div>
      </div>
      <div class="h-[6%] w-[95%] mx-auto border-b-2 mb-2 flex gap-8">
                <span class="h-full mt-[2px] cursor-pointer  w-[6rem] flex justify-center items-center " :class="activePage === 'Published'?'border-b-2 border-b-[#1a56db]':''" @click="()=>activePage = 'Published'">
                    <p class=" text-sm font-medium" :class="activePage === 'Published' ? 'text-[#1a56db] ':'text-gray-500'">Published</p>
        </span>
                <span class="h-full mt-[2px] cursor-pointer w-[6rem] flex justify-center items-center" :class="activePage === 'Archive'?'border-b-2 border-b-[#1a56db]':''" @click="()=>activePage = 'Archive'">
                    <p class="text-sm font-medium" :class="activePage === 'Archive' ? 'text-[#1a56db]':'text-gray-500'">Archive</p>
        </span>
      </div>
        <div  v-if="Object.keys(filteredData).length === 0 && activePage === 'Published'" class="h-[88%] w-full flex justify-center items-center"  >
        <div class=" w-[50%] min-w-[50%] h-20 flex flex-col justify-center gap-4">
          <div class="flex flex-col justify-center items-center">
            <span v-html="createProjectSVG"></span>
            <div class="flex justify-center text-center">
              <p class="text-base font-normal">Create Project to get started</p>
            </div>
          </div>

          <div class="flex justify-center">
                <Button
                    title="Add Project"
                    theme="primary" class="h-10 hover:bg-[#1a56db] active:bg-[#1e429f]"
                    @click="router.push(`/projects/add`);"
                    >
            </Button>
          </div>

        </div>
      </div>
        <div  v-if="Object.keys(filteredTrashData).length === 0 && activePage === 'Archive'" class="h-[88%] w-full flex justify-center items-center"  >
        <div class="w-[50%] min-w-[50%] h-20 flex flex-col justify-center gap-4">
          <div class="flex flex-col justify-center items-center">
            <span v-html="createProjectSVG"></span>
            <div class="flex justify-center text-center">
              <p class="text-base font-normal">No Projects in Trash</p>
            </div>
          </div>
        </div>
      </div>

        <div  v-if="activePage === 'Published' && pageView === 'card' && Object.keys(filteredData).length > 0" class="h-[69%] " >
            <ProjectCardView :loading="loading" :data="filteredData" @callApiFunction="handleMultipleAPICall" @openModal="(val,projectId) => {if (val) {openDeleteModal = true; projectToDelete = projectId;}}"/>
      </div>
        <div v-if="activePage === 'Published' && pageView === 'list' && Object.keys(filteredData).length > 0" class="h-[69%]" >
          <ProjectTableView :loading="loading" :data="filteredData" @callApiFunction="handleMultipleAPICall" @openModal="(val,projectId) => {if (val) {openDeleteModal = true; projectToDelete = projectId;}}"/>
      </div>
        <div  v-if="activePage === 'Archive' && pageView === 'card' && Object.keys(filteredTrashData).length > 0" class="h-[69%] " >
            <ProjectCardView section="Archive" :loading="loading" :data="filteredTrashData" @callApiFunction="handleMultipleTrashAPI" @openDeleteTrashModal="(projectId)=>{openTrashDeleteModal= true;trashToDelete = projectId}" @openModal="(projectId) =>{showRestoreProject = true; projectToRestore = projectId;}"/>
      </div>
        <div v-if="activePage === 'Archive' && pageView === 'list' && Object.keys(filteredTrashData).length > 0" class="h-[69%]" >
          <ProjectTableView section="Archive" :loading="loading" :data="filteredTrashData" @callApiFunction="handleMultipleTrashAPI" @openDeleteTrashModal="(projectId)=>{openTrashDeleteModal = true;trashToDelete = projectId}" @openModal="(projectId) =>{showRestoreProject = true; projectToRestore = projectId;}"/>
      </div>
      <Modal :open="openDeleteModal">
              <DeleteModalContent
                  :trash="true"
                  :loader="deleteLoader"
                  @closeModal="(e) => openDeleteModal = false"
                  @handleDelete="()=>handleMoveToTrash(projectToDelete)"
                  :dataName="'project'" />
      </Modal>
      <Modal :open="openTrashDeleteModal">
              <DeleteModalContent
                  :trash="false"
                  :loader="deleteLoader"
                  @closeModal="(e) => openTrashDeleteModal = false"
                  @handleDelete="()=>handleDeleteTrash(trashToDelete)"
                  :dataName="'project'" />
      </Modal>
      <Modal :open="showRestoreProject">
              <RestoreModelContent
                  :loader="restoreLoader"
                  @closeModal="() => showRestoreProject = false"
                  @handleRestore="()=>handleRestoreProject(projectToRestore)"
                  :dataName="'project'" />
      </Modal>

    </div>
  </div>
  <div v-else>
  <div  class="dynamic-heading">
   <p class="dynamic-topic" :class="{ 'text-gray-600': !isAdmin }">Projects</p>
   <p class="dynamic-sub-topic" :class="{ 'text-gray-400': !isAdmin }">Create and manage projects.</p>
 </div>
  <div class="h-[70vh] flex flex-col items-center justify-center gap-4">
    <div class="flex flex-col justify-center items-center">
      <span v-html="createProjectSVG"></span>
      <div class="lg:max-w-[60%] flex flex-column text-center mt-2">
        <h4 class="text-2xl font-extrabold">Join Organization</h4>
        <p class="text-base font-normal mt-1 text-gray-600">Since you're not part of any organization on this platform, please create one to start using the platform.</p>
      </div>
      <div class="flex justify-center gap-4 mt-4">
        <Button title="Create Organization" theme="primary" class="h-10 hover:bg-[#1a56db] active:bg-[#1e429f]"
          @click="router.push(`/projects/add/organization`);">
        </Button>
        <Button title="Refresh" theme="secondary" class="h-10 hover:bg-[#1a56db] active:bg-[#1e429f]"
          @click="() => router.go(0)">
        </Button>
      </div>
    </div>
  </div>
</div>
</template>

<style>

</style>

<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../../common/Spinner.vue';
import { Form, Field, ErrorMessage, FieldArray } from 'vee-validate';
import { multipleLandmarkSchema } from '../../../validationSchema/landmark';
import Modal from '../../common/Modal/Modal.vue';
import { useRouter } from 'vue-router';
import { resizeImage } from '../../../helpers/helpers';
import { categoryItems } from '../../../helpers/constants';
import { getCookie } from '../../../helpers/domhelper';
import { createLandmark } from '../../../api/projects/landmarks';

const router = useRouter();
const route = useRoute();
const projectId = ref(route.params.project_id);
const loader = ref(false);

const uploadedFiles = ref();
const initialData = ref();
watch(uploadedFiles, async (values) => {
  console.log(values);
  const Landmarks = await Promise.all(values.map(async (file, index) => {
    console.log(index);
    if (!file) {
      return null;
    }

    // Finding the file type and name
    const fileType = file.type.split('/').slice(0, -1).join('');
    const fileName = file.name.split('.').slice(0, -1).join('.');

    let resizedThumbnail = null;
    // Image resize on type image
    if (fileType==='image'){
      resizedThumbnail = await resizeImage(file, 720, 720);
    }
    // Function to read file and return a promise that resolves to a data URL for preview image
    const readFileAsDataURL = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsDataURL(file);
      });
    };
    const preview = await readFileAsDataURL(resizedThumbnail?resizedThumbnail:file);

    return { thumbnail: resizedThumbnail, preview: preview, name: fileName };
  }));

  const obj = { Landmarks: Landmarks.filter((item) => !!item) }; // Filter out null results if any
  initialData.value = obj;
});

const onSubmit = (values) => {
  const organization = getCookie('organization');
  return new Promise((outerResolve, outerReject) => {
    loader.value = true;
    Promise.all(values.Landmarks.map(async (elem, index) => {
      const formData = new FormData();
      formData.append('organization_id', organization);
      formData.append('thumbnail', elem.thumbnail);
      formData.append('name', elem.name);
      elem.description ? formData.append('description', elem.description):{};
      elem.icon ? formData.append('icon', elem.icon):{};
      formData.append('category', elem.category);
      formData.append('distance', +elem.distance);
      formData.append('walk_timing', +elem.walk_timing);
      formData.append('transit_timing', +elem.transit_timing);
      formData.append('car_timing', +elem.car_timing);
      formData.append('project_id', projectId.value);
      elem.lat ? formData.append('lat', +elem.lat) : '';
      elem.long ? formData.append('long', +elem.long) : '';
      await createLandmark(formData).then(() => {
        console.log('index', index);
      });
    })).then(() => {
      loader.value = false;
      document.dispatchEvent(new Event('refreshProjectLandmark'));
      router.go(-1);

      outerResolve();
    }).catch((err) => {
      loader.value = false;
      console.log('Error multiple Landmark creation', err);
      outerReject();
    });
  });
};

</script>

<template>
    <Modal :open="true">
        <div
            class="modal-content-primary sm:max-w-7xl h-screen sm:max-h-[80vh]">
            <div class="p-3 sm:p-6 h-full overflow-y-scroll">
                <div class="mb-2">
                    <h1
                        class="modal-heading-primary">
                        Add Landmarks files</h1>
                    <p class="modal-subheading-primary">Fill details
                        below
                        to Add Landmarks.</p>
                </div>
                     <div v-if="!initialData"
                            class="col-span-auto">
                            <label for="file"
                                class="label-primary">Upload multiple
                                Landmarks Images</label>
                            <div class="mt-2">
                                <Field type="file"
                                v-model="uploadedFiles"
                                   multiple
                                    name="files"
                                    id="files"
                                    autocomplete="files"
                                    class="input-primary"
                                    placeholder="Upload multiple files" />
                            </div>
                            <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                        <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.push(`/projects/${projectId}/landmarks`)"
                            ref="cancelButtonRef">Cancel</button>
                    </div>
                        </div>
        <div>

            <Form v-if="initialData?.Landmarks"
              @submit="onSubmit"
              :initial-values="initialData"
              :validation-schema="multipleLandmarkSchema"
            >
            <div class="h-fit px-8">
                <div :class="[' mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden overflow-x-scroll ' ]">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">sr</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Landmarks name</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">thumbnail</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">icon</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">category</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Description</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Distance (km)</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Walk Timing (min)</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Transit Timing (min)</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Car Timing (min)</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Latitude</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Longitude</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <FieldArray name="Landmarks" v-slot="{ fields, remove }">
                                <tr  v-for="(field, idx) in fields"
                                :key="field.key" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">{{ idx }}</td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <Field :id="`name_${idx}`" :name="`Landmarks[${idx}].name`" style="width: auto;" class="input-primary" />
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].name`" />
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <img v-if="initialData?.Landmarks?.[idx]?.preview" :src="initialData.Landmarks[idx].preview" :alt="`file_${idx}`" width="200" height="200"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].thumbnail`" />
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <Field :id="`icon${idx}`" type="file" :name="`Landmarks[${idx}].icon`" style="width: auto;" class="input-primary" />
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].icon`" />
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">

                                <div class="col-span-auto">
                                    <Field
                                        style="width: auto;"
                                        v-model="initialData.Landmarks[idx].category"
                                        as="select" type="text"
                                        :name="`Landmarks[${idx}].category`"
                                        :id="`category_${idx}`"
                                        autocomplete="category"
                                        class="select-primary"
                                        :placeholder="`Seclect category`">
                                        <option value="" disabled>
                                            Choose
                                        </option>
                                        <option value="" disabled
                                            v-if="!categoryItems">
                                            No category found ! </option>
                                        <option v-else
                                            :value="option"
                                            v-for="option, index in  categoryItems"
                                            :key="index"
                                            class="text-black">
                                            {{
                                                option }} </option>
                                    </Field>
                                    <ErrorMessage as="p"
                                        class="text-sm text-rose-500 mt-1"
                                        :name="`Landmarks[${idx}].category`" />
                                </div>

                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <Field :id="`description_${idx}`" :name="`Landmarks[${idx}].description`" style="width: auto;" class="input-primary" placeholder="Description"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].description`" />
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Landmarks[idx].distance" type="string" :id="`distance_${idx}`" :name="`Landmarks[${idx}].distance`" style="width: auto;"  class="input-primary" placeholder="Distance"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].distance`" />

                            </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Landmarks[idx].walk_timing" type="text" :id="`walk_timing_${idx}`" :name="`Landmarks[${idx}].walk_timing`" style="width: auto;"  class="input-primary" placeholder="Walk Timing"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].walk_timing`" />

                            </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Landmarks[idx].transit_timing" type="text" :id="`transit_timing_${idx}`" :name="`Landmarks[${idx}].transit_timing`" style="width: auto;"  class="input-primary" placeholder="Transit Timing"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].transit_timing`" />

                            </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Landmarks[idx].car_timing" type="text" :id="`car_timing_${idx}`" :name="`Landmarks[${idx}].car_timing`" style="width: auto;"  class="input-primary" placeholder="Car Timing"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].car_timing`" />

                            </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Landmarks[idx].lat" type="text" :id="`lat_${idx}`" :name="`Landmarks[${idx}].lat`" style="width: auto;"  class="input-primary" placeholder="Latitude"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].lat`" />

                            </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Landmarks[idx].long" type="text" :id="`long_${idx}`" :name="`Landmarks[${idx}].long`" style="width: auto;"  class="input-primary" placeholder="Longitude
"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Landmarks[${idx}].long`" />

                            </td>

                                <button type="button" @click="remove(idx)" class="p-1 bg-gray-200 rounded-lg">
                                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
                                </button>
                                               </tr>
                                <!-- <button type="button" @click="push({ file: '', name: '', type: '', category: '' })">
                                Add Item +
                                </button> -->
                            </FieldArray>

                        </tbody>
                    </table>
                </div>
            </div>
              <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                        <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.push(`/projects/${projectId}/landmarks`)"
                            ref="cancelButtonRef">Cancel</button>
                        <button type="submit"
                            :disabled="loader"
                            class="proceed-btn-primary">Save
                            <Spinner v-if="loader" />
                        </button>
                    </div>
            </Form>
            </div>
            </div>
        </div>
    </Modal>
</template>

<style>

</style>

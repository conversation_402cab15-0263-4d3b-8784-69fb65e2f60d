<script setup>
import { ref, watch } from 'vue';
import Spinner from '../../common/Spinner.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { buildingSchema } from '../../../validationSchema/building';
import { useRoute, useRouter } from 'vue-router';
import { updateBuilding, getListOfBuildings } from '../../../api/projects/buildings/index';
import Modal from '../../common/Modal/Modal.vue';
import { ProjectStore } from '../../../store/project';
import Multiselect from 'vue-multiselect';

const route = useRoute();
const router = useRouter();
const projectId = ref(route.params.project_id);
const buildingId = ref(route.params.building_id);
const projectStore = ProjectStore();
const loader = ref();
const buildingType = ['tower', 'villa'];
const initialData = ref();
const options = ref([]);
const floorRef = ref([]);

const handleGetListOfBuildings = () => {
  getListOfBuildings(projectId.value).then((res) => {
    projectStore.SyncMultipleBuildings(res);
  });
};

if (!projectStore.buildings){
  handleGetListOfBuildings();
} else {
  initialData.value = projectStore.buildings[buildingId.value];
}

watch(() => projectStore.buildings, () => {
  initialData.value = projectStore.buildings[buildingId.value];
});

/* Methods */

const addTagFloors = (newTag) => {
  const tag = {
    name: newTag,
  };
  options.value = [...options.value, tag];
  floorRef.value.push(tag);
};

const handleUpdateBuilding = (obj) => {
  loader.value = true;

  updateBuilding(buildingId.value, obj)
    .then(() => {
      document.dispatchEvent(new Event('refreshBuildingList'));
      router.go(-1);
    })
    .catch((err) => console.log(err))
    .finally(() => {
      loader.value = false;
    });
};

const handleSubmitForm = (values) => {

  console.log(values, "inside Submit");

  if (values.floors) {
    if (Array.isArray(values.floors) && values.floors && values.floors.length !== 0) {
      values.floors = values.floors.map((elem) => elem.name);
    } else {
    // If it's a single object
      values.floors = [values.floors.name];
    }
  }

  const payload = {
    ...(initialData.value.name !== values.name && { name: values.name }),
    ...(initialData.value.type !== values.type && { type: values.type }),
    ...(values.floors.length > 0 && values.floors.find((floor) => floor !== undefined)   && { floors: values.floors}),
    // ...(initialData.value.total_floors !== values.total_floors && { total_floors: values.total_floors }),
    ...((!values.community_id && { community_id: null }) || (initialData.value.community_id !== values.community_id && { community_id: values.community_id })),
    project_id: projectId.value,
    total_floors: initialData.value.total_floors,
  };

  handleUpdateBuilding(payload);
};

projectStore.RefreshCommunities(projectId.value);
</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary">
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Edit Building</h1>
          <p class="modal-subheading-primary">
            Fill details below to Edit Building.
          </p>
        </div>
        <Form
        v-if="initialData"
          @submit="handleSubmitForm"
          :validation-schema="buildingSchema"
          class="flex flex-col justify-center"
          :initial-values="initialData"
        >
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">
            <div class="col-span-auto">
              <label for="name" class="label-primary"> Name</label>
              <div class="mt-2">
                <Field
                  type="text"
                  name="name"
                  id="name"
                  class="input-primary"
                  placeholder="Enter Building Name"
                />

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="name"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="type" class="label-primary"> Type</label>
              <div class="mt-2">
                <Field
                  as="select"
                  name="type"
                  id="type"
                  class="input-primary"
                  placeholder="Type"
                >
                  <option value="" disabled>choose</option>
                  <option value="" disabled v-if="!buildingType">
                    no currency!
                  </option>
                  <option
                    v-else
                    :value="option"
                    v-for="(option, index) in buildingType"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                </option>
              </Field>
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="type"
              />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="total_floors" class="label-primary">
                Total Floors</label
              >
              <div class="mt-2">
                <Field
                disabled
                  type="number"
                  name="total_floors"
                  id="total_floors"
                  autocomplete="total_floors"
                  class="h-11 input-primary"
                  placeholder="Total Floors"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="total_floors"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="community_id" class="label-primary">
                Communities</label
              >
              <div class="mt-2">
              <Field
                as="select"
                type="text"
                name="community_id"
                id="community_id"
                autocomplete="community_id"
                class="select-primary"
                :placeholder="`Choose Community`"
              >
                <option class="text-black" value="">choose</option>
                <option
                  value=""
                  disabled
                  v-if="
                    !projectStore.communities ||
                    Object.keys(projectStore.communities).length === 0
                  "
                >
                  No Community Found !
                </option>
                <option
                  v-else
                  :value="option._id"
                  v-for="(option, index) in projectStore.communities"
                  :key="index"
                  class="text-black"
                >
                  {{ option.name }}
                </option>
              </Field>
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="community_id"
              />
            </div>
            </div>

            <div class="col-span-auto">
              <label for="floors" class="label-primary">
                Add Floors
              </label>
                <div class="mt-2">
                <Field  name="floors" :model-value="floorRef">
                  <multiselect v-model="floorRef"
                    open-direction="top"
                    tag-placeholder="Add this as new floor"
                    placeholder="Search or add floor"
                    label="name"
                    track-by="name"
                    :options="options"
                    :multiple="true"
                    :taggable="true"
                    @tag="addTagFloors"
                    maxHeight="250">
                  </multiselect>
                </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="floors" />
               </div>
            </div>

          </div>
          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="router.go(-1)"
              ref="cancelButtonRef"
            >
              Cancel
            </button>
            <button type="submit" class="proceed-btn-primary">
              Save
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

<style scoped></style>

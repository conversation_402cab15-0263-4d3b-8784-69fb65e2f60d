<script setup>
import { ref } from 'vue';
import Spinner from '../../common/Spinner.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { buildingSchema } from '../../../validationSchema/building';
import { removeUndefinedAndNullInObject } from '../../../helpers/domhelper';
import { useRoute, useRouter } from 'vue-router';
import { createBuilding } from '../../../api/projects/buildings/index';
import Modal from '../../common/Modal/Modal.vue';
import { ProjectStore } from '../../../store/project';

const route = useRoute();
const router = useRouter();
const projectId = ref(route.params.project_id);
const projectStore = ProjectStore();
const loader = ref();
const buildingType = ['tower', 'villa'];

const totalFloors = ref();

const communities = ref();

/* Methods */

const handleCreateBuilding = (obj) => {
  loader.value = true;
  createBuilding(obj)
    .then(() => {
      document.dispatchEvent(new Event('refreshBuildingList'));
      router.go(-1);
    })
    .catch(() => {})
    .finally(() => {
      loader.value = false;
    });
};

const handleSubmitForm = (values) => {
  values = removeUndefinedAndNullInObject(values);
  values.project_id = projectId.value;
  handleCreateBuilding(values);
};

projectStore.RefreshCommunities(projectId.value);
</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary">
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Create Building</h1>
          <p class="modal-subheading-primary">
            Fill details below to create Building.
          </p>
        </div>
        <Form
          @submit="handleSubmitForm"
          :validation-schema="buildingSchema"
          class="flex flex-col justify-center"
        >
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">
            <div class="col-span-auto">
              <label for="name" class="label-primary"> Name</label>
              <div class="mt-2">
                <Field
                  type="text"
                  name="name"
                  id="name"
                  class="input-primary"
                  placeholder="Enter Building Name"
                />

                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="name"
                />
              </div>
            </div>
            <div class="col-span-auto">
              <label for="type" class="label-primary"> Type</label>
              <div class="mt-2">
                <Field
                  as="select"
                  name="type"
                  id="type"
                  class="input-primary"
                  placeholder="Type"
                >
                  <option value="" disabled>choose</option>
                  <option value="" disabled v-if="!buildingType">
                    no currency!
                  </option>
                  <option
                    v-else
                    :value="option"
                    v-for="(option, index) in buildingType"
                    :key="index"
                    class="text-black"
                  >
                    {{ option }}
                </option>
              </Field>
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="type"
              />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="total_floors" class="label-primary">
                Total Floors</label
              >
              <div class="mt-2">
                <Field
                  type="number"
                  name="total_floors"
                  id="total_floors"
                  autocomplete="total_floors"
                  v-model="totalFloors"
                  class="h-11 input-primary"
                  placeholder="Total Floors"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="total_floors"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="community_id" class="label-primary">
                Communities</label
              >
              <div class="mt-2">
              <Field
                v-model="communities"
                as="select"
                type="text"
                name="community_id"
                id="community_id"
                autocomplete="community_id"
                class="select-primary"
                :placeholder="`Choose Community`"
              >
                <option class="text-black" value="">choose</option>
                <option
                  value=""
                  disabled
                  v-if="
                    !projectStore.communities ||
                    Object.keys(projectStore.communities).length === 0
                  "
                >
                  No Community Found !
                </option>
                <option
                  v-else
                  :value="option._id"
                  v-for="(option, index) in projectStore.communities"
                  :key="index"
                  class="text-black"
                >
                  {{ option.name }}
                </option>
              </Field>
              {{ communities }}
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="community_id"
              />
            </div>
            </div>
          </div>
          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="router.go(-1)"
              ref="cancelButtonRef"
            >
              Cancel
            </button>
            <button type="submit" class="proceed-btn-primary">
              Save
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

<style scoped></style>

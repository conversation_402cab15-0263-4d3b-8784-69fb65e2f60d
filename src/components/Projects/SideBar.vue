<script setup>
import { ref } from 'vue';

import { useRoute } from 'vue-router';
const route = useRoute();
const project_id = ref(route.params.project_id);
console.log(project_id.value);
// const menuListTop = [
//   {
//     name: 'Session',
//     route: '/sessions',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M440-120v-80h320v-284q0-117-81.5-198.5T480-764q-117 0-198.5 81.5T200-484v244h-40q-33 0-56.5-23.5T80-320v-80q0-21 10.5-39.5T120-469l3-53q8-68 39.5-126t79-101q47.5-43 109-67T480-840q68 0 129 24t109 66.5Q766-707 797-649t40 126l3 52q19 9 29.5 27t10.5 38v92q0 20-10.5 38T840-249v49q0 33-23.5 56.5T760-120H440Zm-80-280q-17 0-28.5-11.5T320-440q0-17 11.5-28.5T360-480q17 0 28.5 11.5T400-440q0 17-11.5 28.5T360-400Zm240 0q-17 0-28.5-11.5T560-440q0-17 11.5-28.5T600-480q17 0 28.5 11.5T640-440q0 17-11.5 28.5T600-400Zm-359-62q-7-106 64-182t177-76q89 0 156.5 56.5T720-519q-91-1-167.5-49T435-698q-16 80-67.5 142.5T241-462Z"/></svg>',
//   },
//   {
//     name: 'Unitplans',
//     route: '/unitplans',
//     icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-6 w-6">
//                 <g data-name="Layer 2">
//                     <g data-name="calendar">
//                         <rect width="24" height="24" opacity="0" />
//                         <path
//                             d="M18 4h-1V3a1 1 0 0 0-2 0v1H9V3a1 1 0 0 0-2 0v1H6a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V7a3 3 0 0 0-3-3zM6 6h1v1a1 1 0 0 0 2 0V6h6v1a1 1 0 0 0 2 0V6h1a1 1 0 0 1 1 1v4H5V7a1 1 0 0 1 1-1zm12 14H6a1 1 0 0 1-1-1v-6h14v6a1 1 0 0 1-1 1z" />
//                         <circle cx="8" cy="16" r="1" />
//                         <path d="M16 15h-4a1 1 0 0 0 0 2h4a1 1 0 0 0 0-2z" />
//                     </g>
//                 </g>
//             </svg>`,
//   },
//   {
//     name: 'Units',
//     route: '/units',
//     icon: `<svg width="24" height="24" viewBox="0 0 24 24" class="h-6 w-6"
//                         xmlns="http://www.w3.org/2000/svg">
//                         <path
//                             d="M19.4062 4.71096C18.6436 3.93261 17.7334 3.31408 16.729 2.89154C15.7245 2.46899 14.6459 2.25091 13.5562 2.25003H13.5253C12.4406 2.24724 11.366 2.45967 10.364 2.87501C9.36189 3.29036 8.45217 3.90036 7.68748 4.66971L1.93216 10.5C1.65353 10.7818 1.49773 11.1624 1.49878 11.5587C1.49984 11.955 1.65766 12.3347 1.93779 12.615L4.62466 15.3019C4.76375 15.4417 4.92914 15.5525 5.11129 15.6279C5.29345 15.7034 5.48875 15.742 5.68591 15.7416H5.69435C5.89294 15.7405 6.08935 15.7 6.27217 15.6225C6.45499 15.5449 6.62058 15.4318 6.75935 15.2897L12.4687 9.43971C12.7475 9.16371 13.1229 9.00726 13.5152 9.00358C13.9075 8.99991 14.2858 9.1493 14.5697 9.42003C14.71 9.55634 14.8215 9.71942 14.8977 9.89962C14.9739 10.0798 15.0131 10.2735 15.0131 10.4691C15.015 10.6773 14.9758 10.8838 14.8978 11.0769C14.8198 11.2699 14.7044 11.4457 14.5584 11.5941L8.71873 17.2388C8.57609 17.3773 8.46242 17.5427 8.38431 17.7256C8.30619 17.9084 8.26521 18.1049 8.26372 18.3037C8.26224 18.5025 8.30029 18.6996 8.37567 18.8836C8.45104 19.0676 8.56224 19.2347 8.70279 19.3753L11.3897 22.0622C11.6698 22.3406 12.0481 22.4978 12.443 22.4999C12.8379 22.502 13.2178 22.3489 13.5009 22.0735L19.2947 16.4063C22.5243 13.1719 22.574 7.92753 19.4062 4.71096ZM5.68591 14.2397L2.99998 11.5538L5.23123 9.29253L7.90498 11.9663L5.68591 14.2397ZM12.449 21L9.76029 18.3141L12.0459 16.1044L14.7187 18.78L12.449 21ZM18.239 15.3338L15.7912 17.7282L13.125 15.0638L15.6037 12.6666L15.6131 12.6582C15.9006 12.3683 16.1279 12.0244 16.2819 11.6463C16.4359 11.2681 16.5135 10.8633 16.5103 10.455C16.5086 10.0605 16.4277 9.67042 16.2725 9.30776C16.1173 8.94511 15.8908 8.6173 15.6065 8.34378C15.0369 7.80195 14.2784 7.50369 13.4923 7.51245C12.7062 7.5212 11.9546 7.83627 11.3972 8.39065L8.95216 10.8919L6.28123 8.22565L8.75154 5.72721C9.3769 5.09825 10.1208 4.59963 10.9403 4.26024C11.7597 3.92085 12.6384 3.74743 13.5253 3.75003H13.5506C14.4421 3.7508 15.3246 3.92928 16.1463 4.27504C16.9681 4.62081 17.7127 5.12691 18.3365 5.76378C20.9297 8.39534 20.8828 12.6919 18.239 15.3357V15.3338Z" />
//                     </svg>`,
//   },
//   {
//     name: 'Landmarks',
//     route: '/landmarks',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M200-120v-680h360l16 80h224v400H520l-16-80H280v280h-80Zm300-440Zm86 160h134v-240H510l-16-80H280v240h290l16 80Z"/></svg>',
//   },
//   {
//     name: 'Scenes',
//     route: '/scenes',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M800-80v-600q0-33-23.5-56.5T720-760h-40v52q0 12-8 20t-20 8H428q-14 0-22.5-15t-1.5-29l76-164q7-15 20.5-23.5T532-920h92q24 0 40 18t16 42v20h40q66 0 113 47t47 113v600h-80ZM508-760h92v-80h-56l-36 80ZM200-80q-51 0-85.5-34.5T80-200v-100q0-33 22-61.5t58-34.5v-84q0-33 23.5-56.5T240-560h320q33 0 56.5 23.5T640-480v84q36 6 58 33t22 63v100q0 51-34.5 85.5T600-80H200Zm40-400v100q18 15 29 35.5t11 44.5v20h240v-20q0-24 11-44.5t29-35.5v-100H240Zm-40 320h400q18 0 29-12.5t11-27.5v-100q0-9-5.5-14.5T620-320q-9 0-14.5 5.5T600-300v100H200v-100q0-9-5.5-14.5T180-320q-9 0-14.5 5.5T160-300v100q0 15 11 27.5t29 12.5Zm320-120H280h240ZM240-480h320-320Zm-40 320h400-400Z"/></svg>',
//   },
//   {
//     name: 'Tours',
//     route: '/tours',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="m360-160-56-56 70-72q-128-17-211-70T80-480q0-83 115.5-141.5T480-680q169 0 284.5 58.5T880-480q0 62-66.5 111T640-296v-82q77-20 118.5-49.5T800-480q0-32-85.5-76T480-600q-149 0-234.5 44T160-480q0 24 51 57.5T356-372l-52-52 56-56 160 160-160 160Z"/></svg>',
//   },
//   {
//     name: 'Tower / Buildings',
//     route: '/buildings',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M120-120v-560h160v-160h400v320h160v400H520v-160h-80v160H120Zm80-80h80v-80h-80v80Zm0-160h80v-80h-80v80Zm0-160h80v-80h-80v80Zm160 160h80v-80h-80v80Zm0-160h80v-80h-80v80Zm0-160h80v-80h-80v80Zm160 320h80v-80h-80v80Zm0-160h80v-80h-80v80Zm0-160h80v-80h-80v80Zm160 480h80v-80h-80v80Zm0-160h80v-80h-80v80Z"/></svg>',
//   },
//   {
//     name: 'Assets',
//     route: '/assets',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="briefcase"><rect opacity="0"/><path d="M19 7h-3V5.5A2.5 2.5 0 0 0 13.5 3h-3A2.5 2.5 0 0 0 8 5.5V7H5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h14a3 3 0 0 0 3-3v-8a3 3 0 0 0-3-3zm-4 2v10H9V9zm-5-3.5a.5.5 0 0 1 .5-.5h3a.5.5 0 0 1 .5.5V7h-4zM4 18v-8a1 1 0 0 1 1-1h2v10H5a1 1 0 0 1-1-1zm16 0a1 1 0 0 1-1 1h-2V9h2a1 1 0 0 1 1 1z"/></g></g></svg>',
//   },
//   {
//     name: 'Amenities',
//     route: '/amenities',
//     icon: `
//     <svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M760-600q-57 0-99-34t-56-86H354q-11 42-41.5 72.5T240-606v251q52 14 86 56t34 99q0 66-47 113T200-40q-66 0-113-47T40-200q0-57 34-99t86-56v-251q-52-14-86-56t-34-98q0-66 47-113t113-47q56 0 98 34t56 86h251q14-52 56-86t99-34q66 0 113 47t47 113q0 66-47 113t-113 47ZM200-120q33 0 56.5-24t23.5-56q0-33-23.5-56.5T200-280q-32 0-56 23.5T120-200q0 32 24 56t56 24Zm0-560q33 0 56.5-23.5T280-760q0-33-23.5-56.5T200-840q-32 0-56 23.5T120-760q0 33 24 56.5t56 23.5ZM760-40q-66 0-113-47t-47-113q0-66 47-113t113-47q66 0 113 47t47 113q0 66-47 113T760-40Zm0-80q33 0 56.5-24t23.5-56q0-33-23.5-56.5T760-280q-33 0-56.5 23.5T680-200q0 32 23.5 56t56.5 24Zm0-560q33 0 56.5-23.5T840-760q0-33-23.5-56.5T760-840q-33 0-56.5 23.5T680-760q0 33 23.5 56.5T760-680ZM200-200Zm0-560Zm560 560Zm0-560Z"/></svg>`,
//   },
//   {
//     name: 'Analytics',
//     route: '/analytics',
//     icon: '',
//   },
//   {
//     name: 'Communities',
//     route: '/communities',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="M360-320q33 0 56.5-23.5T440-400q0-33-23.5-56.5T360-480q-33 0-56.5 23.5T280-400q0 33 23.5 56.5T360-320Zm240 0q33 0 56.5-23.5T680-400q0-33-23.5-56.5T600-480q-33 0-56.5 23.5T520-400q0 33 23.5 56.5T600-320ZM480-520q33 0 56.5-23.5T560-600q0-33-23.5-56.5T480-680q-33 0-56.5 23.5T400-600q0 33 23.5 56.5T480-520Zm0 440q-83 0-156-31.5T197-197q-54-54-85.5-127T80-480q0-83 31.5-156T197-763q54-54 127-85.5T480-880q83 0 156 31.5T763-763q54 54 85.5 127T880-480q0 83-31.5 156T763-197q-54 54-127 85.5T480-80Zm0-80q134 0 227-93t93-227q0-134-93-227t-227-93q-134 0-227 93t-93 227q0 134 93 227t227 93Zm0-320Z"/></svg>',
//   },
//   {
//     name: 'Gallery',
//     route: '/gallery',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"  viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="image"><rect opacity="0"/><path d="M18 3H6a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h12a3 3 0 0 0 3-3V6a3 3 0 0 0-3-3zM6 5h12a1 1 0 0 1 1 1v8.36l-3.2-2.73a2.77 2.77 0 0 0-3.52 0L5 17.7V6a1 1 0 0 1 1-1zm12 14H6.56l7-5.84a.78.78 0 0 1 .93 0L19 17v1a1 1 0 0 1-1 1z"/><circle cx="8" cy="8.5" r="1.5"/></g></g></svg>',
//   },
//   {
//     name: 'Settings',
//     route: '/settings',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24"><path d="m370-80-16-128q-13-5-24.5-12T307-235l-119 50L78-375l103-78q-1-7-1-13.5v-27q0-6.5 1-13.5L78-585l110-190 119 50q11-8 23-15t24-12l16-128h220l16 128q13 5 24.5 12t22.5 15l119-50 110 190-103 78q1 7 1 13.5v27q0 6.5-2 13.5l103 78-110 190-118-50q-11 8-23 15t-24 12L590-80H370Zm70-80h79l14-106q31-8 57.5-23.5T639-327l99 41 39-68-86-65q5-14 7-29.5t2-31.5q0-16-2-31.5t-7-29.5l86-65-39-68-99 42q-22-23-48.5-38.5T533-694l-13-106h-79l-14 106q-31 8-57.5 23.5T321-633l-99-41-39 68 86 64q-5 15-7 30t-2 32q0 16 2 31t7 30l-86 65 39 68 99-42q22 23 48.5 38.5T427-266l13 106Zm42-180q58 0 99-41t41-99q0-58-41-99t-99-41q-59 0-99.5 41T342-480q0 58 40.5 99t99.5 41Zm-2-140Z"/></svg>',
//   },
//   {
//     name: 'sidebar',
//     route: '/sidebar',
//     icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="menu"><rect transform="rotate(180 12 12)" opacity="0"/><rect x="3" y="11" width="18" height="2" rx=".95" ry=".95"/><rect x="3" y="16" width="18" height="2" rx=".95" ry=".95"/><rect x="3" y="6" width="18" height="2" rx=".95" ry=".95"/></g></g></svg>',
//   },
// ];

const current_menu = window.location.href;

const menuList2 = [
  {
    name: 'Unitplanssssss',
    route: '/Unitplans',
    icon: `<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg">
<path d="M1.66807 12.608L1.66814 12.608V12.6V5.4H4.81387C5.32681 5.4 5.82263 5.20457 6.1913 4.85064C6.56067 4.49604 6.77216 4.01065 6.77216 3.5V0.5H11.4195C11.6723 0.505398 11.9087 0.606379 12.0785 0.774997C12.2486 0.943804 12.3382 1.16604 12.3345 1.39198L12.3345 1.39198V1.4V12.6H12.3344L12.3345 12.608C12.3382 12.834 12.2486 13.0562 12.0785 13.225C11.9087 13.3936 11.6723 13.4946 11.4195 13.5H2.58309C2.33031 13.4946 2.0939 13.3936 1.92406 13.225C1.75403 13.0562 1.66445 12.834 1.66807 12.608ZM4.21636 0.976939C4.24769 0.947219 4.28022 0.91874 4.31387 0.89156V3H2.11163C2.1263 2.98451 2.14132 2.96928 2.15667 2.9543L4.21636 0.976939Z" stroke="#6B7280"/>
</svg>
    `,
  },
  {
    name: 'scenes',
    route: '/scenes',
    icon: `<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_18990)">
<path d="M12.4061 6.87478L12.129 7.0128L7.22391 9.45631L7.00096 9.56737L6.77802 9.45631L1.87288 7.0128L1.59583 6.87478V6.56525V6.43517C1.59583 6.38527 1.57524 6.33217 1.52998 6.28906C1.48389 6.24517 1.41639 6.21649 1.34119 6.21649C1.26599 6.21649 1.1985 6.24517 1.15241 6.28906C1.10715 6.33217 1.08656 6.38527 1.08656 6.43517V7.00005V7.00014C1.08655 7.03498 1.09646 7.07085 1.11756 7.10407C1.1388 7.13751 1.17131 7.16793 1.21407 7.1893L12.4061 6.87478ZM12.4061 6.87478V6.56525V6.43517C12.4061 6.38527 12.4267 6.33217 12.4719 6.28906C12.518 6.24517 12.5855 6.21649 12.6607 6.21649C12.7359 6.21649 12.8034 6.24517 12.8495 6.28906L13.1943 5.92699M12.4061 6.87478L13.1943 5.92699M13.1943 5.92699L12.8495 6.28906C12.8948 6.33217 12.9154 6.38527 12.9154 6.43517V7.00005V7.00014M13.1943 5.92699L12.9154 7.00014M12.9154 7.00014C12.9154 7.03498 12.9055 7.07085 12.8844 7.10407C12.8631 7.13751 12.8306 7.16793 12.7878 7.1893L12.9154 7.00014ZM12.4016 10.3352L12.1245 10.4732L7.21938 12.9167L6.99644 13.0278L6.77349 12.9167L1.86836 10.4732L1.5913 10.3352V10.0257V9.89561C1.5913 9.84571 1.57071 9.79261 1.52545 9.7495C1.47936 9.70561 1.41186 9.67693 1.33667 9.67693C1.26147 9.67693 1.19397 9.70561 1.14788 9.7495C1.10262 9.79261 1.08203 9.84571 1.08203 9.89561V10.4612V10.4613C1.08203 10.4961 1.09193 10.532 1.11304 10.5652L0.690991 10.8333L1.11304 10.5652C1.1343 10.5987 1.16686 10.6292 1.20971 10.6505L6.86744 13.4703C6.8677 13.4705 6.86795 13.4706 6.8682 13.4707C6.90674 13.4896 6.95078 13.5 6.99644 13.5C7.0421 13.5 7.08616 13.4896 7.1247 13.4707C7.12494 13.4706 7.12518 13.4705 7.12543 13.4703L12.7832 10.6505L12.4016 10.3352ZM12.4016 10.3352V10.0257M12.4016 10.3352V10.0257M12.4016 10.0257V9.89561C12.4016 9.84571 12.4222 9.79261 12.4674 9.7495C12.5135 9.70561 12.581 9.67693 12.6562 9.67693C12.7314 9.67693 12.7989 9.70561 12.845 9.7495C12.8903 9.79261 12.9108 9.84571 12.9108 9.89561V10.4612V10.4613M12.4016 10.0257L12.9108 10.4613M12.9108 10.4613C12.9108 10.4961 12.9009 10.532 12.8798 10.5652C12.8586 10.5987 12.8261 10.6291 12.7833 10.6505L12.9108 10.4613ZM1.21483 3.72886L1.19666 3.7198C1.16194 3.69929 1.13503 3.67256 1.1166 3.64356C1.09548 3.61033 1.08557 3.57446 1.08557 3.53961C1.08557 3.50477 1.09548 3.46889 1.1166 3.43566C1.13788 3.40219 1.17046 3.37173 1.21332 3.35036C1.21332 3.35036 1.21332 3.35036 1.21333 3.35036L6.87309 0.529548L6.87325 0.529468C6.91124 0.510517 6.95489 0.5 7.00021 0.5C7.04552 0.5 7.08918 0.510517 7.12717 0.529469L7.12733 0.529548L12.7871 3.35036C12.83 3.37173 12.8625 3.40219 12.8838 3.43566C12.9049 3.46889 12.9149 3.50477 12.9149 3.53961C12.9149 3.57446 12.9049 3.61033 12.8838 3.64356L13.2639 3.88518L12.8838 3.64356C12.8625 3.67704 12.83 3.7075 12.7871 3.72886L7.12879 6.54895C7.12861 6.54904 7.12843 6.54913 7.12825 6.54922C7.09005 6.568 7.04631 6.57839 7.00096 6.57839C6.95563 6.57839 6.91191 6.56801 6.87371 6.54923C6.87352 6.54914 6.87333 6.54905 6.87314 6.54895L1.21483 3.72886ZM6.87384 10.0101L1.21423 7.18938H12.7877L7.12808 10.0101L7.12792 10.0102C7.08993 10.0291 7.04628 10.0397 7.00096 10.0397C6.95564 10.0397 6.91199 10.0291 6.874 10.0102L6.87384 10.0101Z" fill="#6B7280" stroke="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_723_18990">
<rect width="14" height="14" fill="white"/>
</clipPath>
</defs>
</svg>

    `,
  },
  {
    name: 'Unitplans',
    route: '/Unitplans',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_18993)">
<path d="M14.4 0.666748H1.6C1.17565 0.666748 0.768687 0.838441 0.468629 1.14406C0.168571 1.44967 0 1.86417 0 2.29638V13.7038C0 14.136 0.168571 14.5505 0.468629 14.8561C0.768687 15.1617 1.17565 15.3334 1.6 15.3334H14.4C14.8243 15.3334 15.2313 15.1617 15.5314 14.8561C15.8314 14.5505 16 14.136 16 13.7038V2.29638C16 1.86417 15.8314 1.44967 15.5314 1.14406C15.2313 0.838441 14.8243 0.666748 14.4 0.666748ZM10 3.92601C10.2373 3.92601 10.4693 3.99769 10.6667 4.13199C10.864 4.26629 11.0178 4.45717 11.1087 4.68051C11.1995 4.90384 11.2232 5.14959 11.1769 5.38667C11.1306 5.62376 11.0164 5.84154 10.8485 6.01247C10.6807 6.1834 10.4669 6.29981 10.2341 6.34697C10.0013 6.39413 9.76005 6.36992 9.54078 6.27742C9.32151 6.18491 9.13409 6.02825 9.00224 5.82726C8.87038 5.62627 8.8 5.38996 8.8 5.14823C8.8 4.82408 8.92643 4.5132 9.15147 4.28399C9.37652 4.05478 9.68174 3.92601 10 3.92601ZM13.5008 12.4661C13.4319 12.594 13.3305 12.7008 13.2072 12.7752C13.0839 12.8496 12.9433 12.8889 12.8 12.889H3.2C3.06362 12.889 2.92949 12.8536 2.81034 12.786C2.6912 12.7184 2.591 12.6209 2.51925 12.5028C2.44751 12.3846 2.40662 12.2498 2.40045 12.111C2.39428 11.9723 2.42304 11.8342 2.484 11.7099L5.284 6.00623C5.34846 5.87467 5.44666 5.76332 5.56821 5.68399C5.68976 5.60466 5.83011 5.56031 5.9744 5.55564C6.11993 5.54739 6.26478 5.58128 6.3922 5.65337C6.51962 5.72547 6.62439 5.83283 6.6944 5.96304L8.9144 9.83912L10.1512 8.30156C10.2315 8.20191 10.3339 8.12309 10.45 8.07159C10.5661 8.0201 10.6925 7.9974 10.8188 8.00535C10.9452 8.0133 11.0679 8.05169 11.1769 8.11735C11.2858 8.18301 11.3779 8.27407 11.4456 8.38304L13.4784 11.6423C13.5537 11.7652 13.5955 11.9063 13.5994 12.0511C13.6033 12.1959 13.5693 12.3391 13.5008 12.4661Z" fill="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_723_18993">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>  `,
  },
  {
    name: 'Unitplans',
    route: '/Unitplans',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.38208 1.15559L10.7899 3.99455C10.9819 4.38974 11.4938 4.7688 11.9258 4.84139L14.4775 5.26884C16.1094 5.54307 16.4934 6.73672 15.3174 7.91424L13.3337 9.9144C12.9977 10.2531 12.8138 10.9064 12.9177 11.3742L13.4857 13.8502C13.9336 15.8101 12.9017 16.5682 11.1819 15.5439L8.79016 14.1164C8.35824 13.8583 7.64632 13.8583 7.20632 14.1164L4.81461 15.5439C3.1028 16.5682 2.06292 15.802 2.51087 13.8502L3.07881 11.3742C3.18279 10.9064 2.99882 10.2531 2.66285 9.9144L0.679071 7.91424C-0.488801 6.73672 -0.11284 5.54307 1.51898 5.26884L4.0707 4.84139C4.49465 4.7688 5.00659 4.38974 5.19857 3.99455L6.6064 1.15559C7.37432 -0.38487 8.62216 -0.38487 9.38208 1.15559Z" fill="#6B7280"/>
</svg>
 `,
  },
  {
    name: 'Unitplans',
    route: '/Unitplans',
    icon: `<svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4.75 1H12.25C13.6519 1 14.3528 1 14.875 1.30144C15.2171 1.49892 15.5011 1.78296 15.6986 2.125C16 2.64711 16 3.34808 16 4.75C16 6.15192 16 6.85285 15.6986 7.375C15.5011 7.71708 15.2171 8.0011 14.875 8.19858C14.3528 8.5 13.6519 8.5 12.25 8.5H11.9547C11.4396 8.5 11.1819 8.5 10.9409 8.44645C10.6183 8.37483 10.3154 8.23293 10.0539 8.03095C9.8584 7.88005 9.69347 7.6822 9.3637 7.28643C9.1009 6.97105 8.9695 6.8134 8.81868 6.7426C8.61678 6.64788 8.38322 6.64788 8.18132 6.7426C8.0305 6.8134 7.8991 6.97105 7.6363 7.28643C7.30653 7.6822 7.1416 7.88005 6.94617 8.03095C6.68457 8.23293 6.38169 8.37483 6.05907 8.44645C5.81805 8.5 5.56046 8.5 5.04527 8.5H4.75C3.34808 8.5 2.64711 8.5 2.125 8.19858C1.78295 8.0011 1.49892 7.71708 1.30144 7.375C1 6.85285 1 6.15192 1 4.75C1 3.34808 1 2.64711 1.30144 2.125C1.49892 1.78296 1.78295 1.49892 2.125 1.30144C2.64711 1 3.34808 1 4.75 1Z" fill="#6B7280" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M8.57545 13L7.06023 11.5M8.57545 13L7.06023 14.5M8.57545 13C4.78804 13 1.60667 11.5 1 10M10.8483 12.8283C13.4518 12.4235 15.4497 11.3419 16 10" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>
`,
  },
  {
    name: 'Unitplans',
    route: '/Unitplans',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_723_19011)">
<path d="M15.1111 9.77778H12.6916L7.11111 15.3582C6.86762 15.5995 6.59948 15.8146 6.31111 16H15.1111C15.3469 16 15.573 15.9064 15.7397 15.7397C15.9064 15.573 16 15.3469 16 15.1111V10.6667C16 10.4309 15.9064 10.2048 15.7397 10.0381C15.573 9.87143 15.3469 9.77778 15.1111 9.77778Z" fill="#6B7280"/>
<path d="M5.33333 0H0.888889C0.653141 0 0.427048 0.0936505 0.260349 0.260349C0.0936505 0.427048 0 0.653141 0 0.888889V12.8889C1.73881e-08 13.714 0.327777 14.5053 0.911224 15.0888C1.49467 15.6722 2.28599 16 3.11111 16C3.93623 16 4.72755 15.6722 5.311 15.0888C5.89445 14.5053 6.22222 13.714 6.22222 12.8889V0.888889C6.22222 0.653141 6.12857 0.427048 5.96187 0.260349C5.79517 0.0936505 5.56908 0 5.33333 0ZM3.11111 13.7778C2.93531 13.7778 2.76345 13.7256 2.61727 13.628C2.47109 13.5303 2.35716 13.3915 2.28988 13.2291C2.22261 13.0666 2.205 12.8879 2.2393 12.7155C2.2736 12.543 2.35826 12.3847 2.48257 12.2603C2.60689 12.136 2.76527 12.0514 2.9377 12.0171C3.11013 11.9828 3.28885 12.0004 3.45127 12.0677C3.6137 12.1349 3.75252 12.2489 3.8502 12.395C3.94787 12.5412 4 12.7131 4 12.8889C4 13.1246 3.90635 13.3507 3.73965 13.5174C3.57295 13.6841 3.34686 13.7778 3.11111 13.7778Z" fill="#6B7280"/>
<path d="M14.3396 4.35556L11.2 1.216C11.0333 1.04936 10.8073 0.955745 10.5716 0.955745C10.3359 0.955745 10.1098 1.04936 9.94311 1.216L8 3.15556V11.9556L14.3396 5.616C14.4226 5.5334 14.4885 5.4352 14.5335 5.32704C14.5784 5.21889 14.6016 5.10291 14.6016 4.98578C14.6016 4.86865 14.5784 4.75267 14.5335 4.64451C14.4885 4.53636 14.4226 4.43816 14.3396 4.35556Z" fill="#6B7280"/>
</g>
<defs>
<clipPath id="clip0_723_19011">
<rect width="16" height="16" fill="white"/>
</clipPath>
</defs>
</svg>

`,
  },
  {
    name: 'Unitplans',
    route: '/Unitplans',
    icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.3028 1.78917C10.5137 1.86742 10.7452 1.86742 11.2082 1.86742H11.2935C12.4747 1.86742 13.0654 1.86742 13.4323 2.23437C13.7993 2.60132 13.7993 3.19191 13.7993 4.3731V4.4585V4.45852C13.7993 4.92147 13.7993 5.15296 13.8775 5.36385C13.8883 5.39314 13.9003 5.42201 13.9133 5.45041C14.0072 5.65486 14.1708 5.81854 14.4982 6.14591C15.2516 6.89936 15.6284 7.27614 15.6631 7.73972C15.6678 7.80199 15.6678 7.86458 15.6631 7.92694C15.6284 8.39043 15.2518 8.76708 14.4986 9.52029L14.4982 9.52071L14.4978 9.52106C14.1707 9.84826 14.0071 10.0119 13.9133 10.2162C13.9003 10.2447 13.8883 10.2735 13.8775 10.3028C13.7993 10.5137 13.7993 10.7452 13.7993 11.2081V11.2082V11.2935C13.7993 12.4747 13.7993 13.0654 13.4323 13.4323C13.0654 13.7993 12.4747 13.7993 11.2935 13.7993H11.2082H11.2081C10.7452 13.7993 10.5137 13.7993 10.3028 13.8775C10.2735 13.8883 10.2447 13.9003 10.2162 13.9133C10.0119 14.0071 9.84826 14.1707 9.52106 14.4978L9.52071 14.4982L9.52029 14.4986C8.76708 15.2518 8.39043 15.6284 7.92694 15.6631C7.86458 15.6678 7.80199 15.6678 7.73972 15.6631C7.27614 15.6284 6.89936 15.2516 6.14591 14.4982C5.81854 14.1708 5.65486 14.0072 5.45041 13.9133C5.42201 13.9003 5.39314 13.8883 5.36385 13.8775C5.15296 13.7993 4.92147 13.7993 4.45852 13.7993H4.4585H4.3731C3.19191 13.7993 2.60132 13.7993 2.23437 13.4323C1.86742 13.0654 1.86742 12.4747 1.86742 11.2935V11.2082C1.86742 10.7452 1.86742 10.5137 1.78917 10.3028C1.7783 10.2735 1.76634 10.2447 1.75332 10.2162C1.65959 10.0119 1.49606 9.84836 1.16912 9.52136L1.16847 9.52071L1.16823 9.52047C0.414909 8.76714 0.0382387 8.39047 0.0035015 7.92694C-0.00116717 7.86466 -0.00116717 7.80199 0.0035015 7.73972C0.0382423 7.27614 0.414986 6.89936 1.16847 6.14591C1.49584 5.81854 1.65953 5.65486 1.75332 5.45041C1.76634 5.42201 1.7783 5.39314 1.78917 5.36385C1.86742 5.15296 1.86742 4.92147 1.86742 4.4585V4.3731C1.86742 3.19191 1.86742 2.60132 2.23437 2.23437C2.60132 1.86742 3.19191 1.86742 4.3731 1.86742H4.4585C4.92147 1.86742 5.15296 1.86742 5.36385 1.78917C5.39314 1.7783 5.42201 1.76634 5.45041 1.75332C5.65486 1.65953 5.81854 1.49584 6.14591 1.16847C6.89936 0.414986 7.27614 0.0382423 7.73972 0.0035015C7.80199 -0.00116717 7.86466 -0.00116717 7.92694 0.0035015C8.39047 0.0382391 8.76715 0.414916 9.52049 1.16826L9.52071 1.16847L9.5214 1.16917C9.84837 1.49608 10.0119 1.6596 10.2162 1.75332C10.2447 1.76634 10.2735 1.7783 10.3028 1.78917ZM7.83334 10.5749C9.34752 10.5749 10.575 9.3474 10.575 7.83322C10.575 6.31904 9.34752 5.09155 7.83334 5.09155C6.31916 5.09155 5.09167 6.31904 5.09167 7.83322C5.09167 9.3474 6.31916 10.5749 7.83334 10.5749Z" fill="#6B7280"/>
</svg>

`,
  },
];

const selectedMenu = ref();
const handleSelect = (name) => {
  selectedMenu.value = name;
};
</script>

<template>
  <div
    class="h-full w-12 bg-gray-100 dark:bg-bg-default flex flex-col justify-between pt-4 overflow-auto border border-red-600">
    <div
      class="flex flex-col justify-center gap-y-2 h-fit p-1">
      <a :href="'/projects/' + project_id + '' + elem.route"
        v-bind:key="elem.name" v-for="(elem,) in menuList2"
        :class="`${current_menu.includes(elem.route) ? 'bg-white shadow-md' : ''}`"
        class="w-full flex flex-col justify-center items-center cursor-pointer relative z-0 p-1.5 rounded-lg">
        <div v-html="elem.icon"
          :class="`px-3 py-1.5 ${current_menu.includes(elem.route) ? 'fill-blue-600' : 'fill-gray-500'}`">
        </div>
      </a>

      <div class="w-full border border-1 border-gray-500"></div>

      <a :href="'/projects/' + project_id + '' + '/unitplan'"
        :class="`${current_menu.includes('unitplan') ? 'bg-white shadow-md' : ''}`"
        class="w-full flex flex-col justify-center items-center cursor-pointer relative z-0 p-1.5 rounded-lg">
        <div
          :class="`px-3 py-1.5 ${current_menu.includes('fdfj') ? 'fill-blue-600' : 'fill-gray-500'}`">

<svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="menu-arrow-circle"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2zm0 16a1 1 0 1 1 1-1 1 1 0 0 1-1 1zm1-5.16V14a1 1 0 0 1-2 0v-2a1 1 0 0 1 1-1 1.5 1.5 0 1 0-1.5-1.5 1 1 0 0 1-2 0 3.5 3.5 0 1 1 4.5 3.34z"/></g></g></svg>

        </div>
      </a>
    </div>
    <div
      class="flex flex-col justify-center gap-y-8 py-2 h-fit">
      <a :href="elem.route"
        v-for="(elem, ) in menuListBottom"
        v-bind:key="elem.name"
        :class="`${selectedMenu === elem.name ? 'selected' : ''} p-1.5`"
        class="w-full flex flex-col justify-center items-center cursor-pointer relative z-0">
        <div v-html="elem.icon" :class="`px-3 py-1.5 ${selectedMenu === elem.name
            ? 'fill-bg-50 dark:fill-bg-950'
            : 'fill-bg-400 dark:fill-bg-600 '
          }`"></div>
        <div v-if="selectedMenu === elem.name"
          class="absolute top-0 -left-1 h-full w-2 rounded-tr-3xl rounded-br-3xl bg-bg-50 dark:bg-bg-950 z-10">
        </div>
      </a>

      <div @click="handleSelect('profile')" :class="`${selectedMenu === 'profile' ? 'selected' : ''} ${showName ? 'p-1.5' : 'p-2.5'
        }`"
        class="w-full flex flex-col justify-center items-center cursor-pointer relative z-0">
        <div :class="`px-3 py-1.5 ${selectedMenu === name ? 'fill-stone-950' : 'fill-gray-600'
          }`">
          <img className="inline-block h-6 w-6 rounded-full"
            src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80"
            alt="" />
        </div>
        <div v-if="selectedMenu === 'profile'"
          class="absolute top-0 -left-1 h-full w-2 rounded-tr-3xl rounded-br-3xl bg-stone-950 z-10">
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>

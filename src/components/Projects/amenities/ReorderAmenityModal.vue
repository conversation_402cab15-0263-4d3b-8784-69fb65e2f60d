<script setup>
import { Draggable } from 'vue-dndrop';
import DnDrop from '../../common/DnDrop.vue';
import Modal from '../../common/Modal/Modal.vue';
import { ref } from 'vue';
import { applyDrag } from '../../../helpers/dnDrophelper';
import { useRouter, useRoute } from 'vue-router';
import Spinner from '../../common/Spinner.vue';
import { getListOfAmenities, updateBulkAmenity } from '../../../api/projects/amenties';

const router = useRouter();
const route = useRoute();
const projectId = ref(route.params.project_id);
const category = ref(route.params.category_id);
const originalListOfItems = ref([]);
const listOfItems = ref([]);

/* Methods */
const frameTheArrayOfItems = () => {
  getListOfAmenities(projectId.value, category.value).then((response) => {
    const sortedItems = [];
    if (Object.keys(response)) {
      Object.values(response).sort((a, b) => {
        return a.order - b.order;
      }).forEach((item) => {
        sortedItems.push(item);
      });
    }
    const generateArrayofItems = sortedItems.map((item, index) => {
      return {
        'id': item._id,
        'order': item.order ? item.order : index + 1,
        'category': item.category,
        'name': item.name,
      };
    });
    originalListOfItems.value = [...generateArrayofItems];
    listOfItems.value = generateArrayofItems;
  });
};

frameTheArrayOfItems();

const handleDrop = (dropResult) => {
  console.log(dropResult);
  listOfItems.value = applyDrag(listOfItems.value, dropResult);
};

const compareTheOriginalListOfItems = () => {
  if (originalListOfItems.value.map((item, index) => item.id === listOfItems.value[index].id).includes(false)) {
    const filteredArray = listOfItems.value.map((item, index) => {
      const itemObject = { ...item };
      itemObject.order = index + 1;
      delete itemObject.name; // Remove the name
      return itemObject;
    });
    return filteredArray;
  }
  return false;

};

const handleSubmit = () => {
  if (compareTheOriginalListOfItems()) {
    const reqBody = {
      'query': compareTheOriginalListOfItems(),
      'project_id': projectId.value,
    };

    console.log(reqBody, 'Body');
    // Api Call
    updateBulkAmenity(reqBody).then(() => {
      document.dispatchEvent(new Event('refreshAmenitiyListByCategory'));
      router.push(`/projects/${projectId.value}/amenities/${category.value}`);
    });
  } else {
    router.push(`/projects/${projectId.value}/amenities/${category.value}`);
  }
};

</script>

<template>
    <Modal :open="true">
        <div class="modal-content-primary !transform-none">
            <div class="p-3 sm:p-6">
                <div class="mb-3">
                    <h1 class="modal-heading-primary">
                        ReOrder Items</h1>
                    <p class="modal-subheading-primary"> Feel free to drag and rearrage the items </p>
                </div>

                <div class="py-4 w-full flex justify-center items-center" v-if="listOfItems.length > 0">

                    <DnDrop :animationDuration="Number('200')" @on-drop="handleDrop" class="px-2 max-h-[500px] w-5/12 overflow-y-scroll">
                        <template #items>
                            <Draggable v-for="item in listOfItems" class="cursor-pointer py-2 text-center"
                                :key="item._id">
                                <div
                                    class="font-medium text-lg capitalize draggable-item bg-bg-150 text-white rounded-full w-full h-fit  px-2 py-3 hover:bg-bg-50">
                                    {{ item.name }}
                                </div>
                            </Draggable>
                        </template>
                    </DnDrop>

                </div>

                <div v-else class="text-center flex flex-col items-center justify-center my-5 gap-2 ">
                    <Spinner />
                    <p class="text-black text-lg font-medium mb-0"> Loading ... </p>
                </div>

                <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                    <button type="button" class="cancel-btn-primary"
                        @click="() => router.push(`/projects/${projectId}/amenities/${category}`)"> Cancel</button>
                    <button type="button" :disabled="!compareTheOriginalListOfItems() ? true : false"
                        @click="handleSubmit" class="proceed-btn-primary">Save
                        <Spinner v-if="loader" />
                    </button>
                </div>

            </div>
        </div>
    </Modal>
</template>

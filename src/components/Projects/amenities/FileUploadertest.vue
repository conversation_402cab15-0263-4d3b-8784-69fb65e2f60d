<script setup>
import { ref } from 'vue';

defineProps({
  multiple: {
    type: Boolean,
    default: false,
  },
});

const files = ref([]);

const isPDF = (fileType) => {
  return fileType === 'application/pdf';
};

const isImage = (fileType) => {
  return fileType.includes('image/');
};

// Handle file input change
const onFileChange = (event) => {
  const selectedFiles = event.target.files;
  for (let i = 0; i < selectedFiles.length; i++) {
    const file = selectedFiles[i];
    const reader = new FileReader();

    reader.onload = (e) => {
      files.value.push({
        type: file.type,
        data: e.target.result,
        name: file.name,
      });
    };
    if (isImage(file.type) || isPDF(file.type)) {
      reader.readAsDataURL(file);
    }
  }
};

const removeFile = (index) => {
  files.value.splice(index, 1);
};
</script>
<template>
    <div>
      <!-- File input field -->
      <input
        type="file"
        :multiple="multiple"
        @change="onFileChange"
        ref="fileInput"
        class=" w-60 h-8 border border-gray-300 rounded cursor-pointer p-1"
        v-if="!multiple || (multiple && files.length === 0)"
      />

      <!-- Preview for multiple files -->
      <div
        v-if="multiple && files.length > 1"
        class=" flex flex-wrap gap-3 mt-3 w-[400px] h-[200px] overflow-y-auto border border-gray-300 p-2"
      >
        <div
          v-for="(file, index) in files"
          :key="index"
          class="relative w-28 h-28 flex justify-center items-center"
        >
          <template v-if="isImage(file.type)">
            <img :src="file.data" alt="Preview" class="w-full h-full object-cover rounded" />
          </template>
          <template v-else-if="isPDF(file.type)">
            <div class=" flex flex-col justify-center items-center bg-gray-200 border border-gray-300 w-full h-full rounded">
              <span class=" text-4xl text-red-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-pdf" viewBox="0 0 16 16">
                <path d="M4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2zm0 1h8a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1"/>
                <path d="M4.603 12.087a.8.8 0 0 1-.438-.42c-.195-.388-.13-.776.08-1.102.198-.307.526-.568.897-.787a7.7 7.7 0 0 1 1.482-.645 20 20 0 0 0 1.062-2.227 7.3 7.3 0 0 1-.43-1.295c-.086-.4-.119-.796-.046-1.136.075-.354.274-.672.65-.823.192-.077.4-.12.602-.077a.7.7 0 0 1 .477.365c.088.164.12.356.127.538.007.187-.012.395-.047.614-.084.51-.27 1.134-.52 1.794a11 11 0 0 0 .98 1.686 5.8 5.8 0 0 1 1.334.05c.364.065.734.195.96.465.12.144.193.32.2.518.007.192-.047.382-.138.563a1.04 1.04 0 0 1-.354.416.86.86 0 0 1-.51.138c-.331-.014-.654-.196-.933-.417a5.7 5.7 0 0 1-.911-.95 11.6 11.6 0 0 0-1.997.406 11.3 11.3 0 0 1-1.021 1.51c-.29.35-.608.655-.926.787a.8.8 0 0 1-.58.029m1.379-1.901q-.25.115-.459.238c-.328.194-.541.383-.647.547-.094.145-.096.25-.04.361q.016.032.026.044l.035-.012c.137-.056.355-.235.635-.572a8 8 0 0 0 .45-.606m1.64-1.33a13 13 0 0 1 1.01-.193 12 12 0 0 1-.51-.858 21 21 0 0 1-.5 1.05zm2.446.45q.226.244.435.41c.24.19.407.253.498.256a.1.1 0 0 0 .07-.015.3.3 0 0 0 .094-.125.44.44 0 0 0 .059-.2.1.1 0 0 0-.026-.063c-.052-.062-.2-.152-.518-.209a4 4 0 0 0-.612-.053zM8.078 5.8a7 7 0 0 0 .2-.828q.046-.282.038-.465a.6.6 0 0 0-.032-.198.5.5 0 0 0-.145.04c-.087.035-.158.106-.196.283-.04.192-.03.469.046.822q.036.167.09.346z"/>
                </svg>
              </span>
              <p class=" text-xs text-gray-700 mt-2 text-center break-words w-28">{{ file.name }}</p>
            </div>
          </template>
          <button class=" absolute top-1 right-1 text-red-900 rounded-full p-1 cursor-pointer text-xs" @click="removeFile(index)">X</button>
        </div>
      </div>

      <!-- Single image or PDF preview -->
      <div v-if="files.length === 1" class="relative w-28 h-28 mt-3 flex justify-center items-center">
        <template v-if="isImage(files[0].type)">
          <img :src="files[0].data" alt="Preview" class=" w-full h-full object-cover rounded" />
        </template>
        <template v-else-if="isPDF(files[0].type)">
          <div class=" flex flex-col justify-center items-center bg-gray-200 border border-gray-300 w-full h-full rounded">
            <span class=" text-4xl text-red-500">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-file-pdf" viewBox="0 0 16 16">
                <path d="M4 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2zm0 1h8a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1"/>
                <path d="M4.603 12.087a.8.8 0 0 1-.438-.42c-.195-.388-.13-.776.08-1.102.198-.307.526-.568.897-.787a7.7 7.7 0 0 1 1.482-.645 20 20 0 0 0 1.062-2.227 7.3 7.3 0 0 1-.43-1.295c-.086-.4-.119-.796-.046-1.136.075-.354.274-.672.65-.823.192-.077.4-.12.602-.077a.7.7 0 0 1 .477.365c.088.164.12.356.127.538.007.187-.012.395-.047.614-.084.51-.27 1.134-.52 1.794a11 11 0 0 0 .98 1.686 5.8 5.8 0 0 1 1.334.05c.364.065.734.195.96.465.12.144.193.32.2.518.007.192-.047.382-.138.563a1.04 1.04 0 0 1-.354.416.86.86 0 0 1-.51.138c-.331-.014-.654-.196-.933-.417a5.7 5.7 0 0 1-.911-.95 11.6 11.6 0 0 0-1.997.406 11.3 11.3 0 0 1-1.021 1.51c-.29.35-.608.655-.926.787a.8.8 0 0 1-.58.029m1.379-1.901q-.25.115-.459.238c-.328.194-.541.383-.647.547-.094.145-.096.25-.04.361q.016.032.026.044l.035-.012c.137-.056.355-.235.635-.572a8 8 0 0 0 .45-.606m1.64-1.33a13 13 0 0 1 1.01-.193 12 12 0 0 1-.51-.858 21 21 0 0 1-.5 1.05zm2.446.45q.226.244.435.41c.24.19.407.253.498.256a.1.1 0 0 0 .07-.015.3.3 0 0 0 .094-.125.44.44 0 0 0 .059-.2.1.1 0 0 0-.026-.063c-.052-.062-.2-.152-.518-.209a4 4 0 0 0-.612-.053zM8.078 5.8a7 7 0 0 0 .2-.828q.046-.282.038-.465a.6.6 0 0 0-.032-.198.5.5 0 0 0-.145.04c-.087.035-.158.106-.196.283-.04.192-.03.469.046.822q.036.167.09.346z"/>
                </svg>
            </span>
            <p class="text-xs text-gray-700 mt-2 text-center break-words w-28">{{ files[0].name }}</p>
          </div>
        </template>
        <button class="absolute top-1 right-1 text-red-900 rounded-full p-1 cursor-pointer text-xs" @click="removeFile(0)">X</button>
      </div>
    </div>
  </template>

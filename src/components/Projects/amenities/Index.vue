<script setup>

import { onMounted, onUnmounted, ref } from 'vue';
import { onBeforeRouteLeave, useRoute, useRouter } from 'vue-router';
import { getCategories, getListOfAmenities, moveAmenityToTrash } from '../../../api/projects/amenties';
import Button from '../../common/Button.vue';
import SwitchButtons from '@/components/common/SwitchButtons.vue';
import { ProjectStore } from '../../../store/project.ts';
import Modal from '@/components/common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import { GetAllTrash, RestoreTrash } from '@/api/trash';
import { formatTimestamp, truncateString, extractFirebaseStoragePath } from '@/helpers/helpers';
import { GetProject } from '@/api/projects/settings';
import ImgPreviewModal from '@/components/common/Modal/imgPreviewModal.vue';
import { getCookie } from '@/helpers/domhelper';
import <PERSON> from 'papaparse';
import AmenityTableRow from './AmenityTableRow.vue';

/* States */
const route = useRoute();
const router = useRouter();

const projectId = ref(route.params.project_id);
const categoryList = ref();
const type = ref(route.query.type || 'Amenities');
const headers = ['S.NO', 'AMENITY NAME', 'SELECT TOWER/COMMUNITY *', 'SELECT CATEGORY', 'SELECT MEDIA TYPE', 'UPLOAD THUMBNAIL', 'UPLOAD MEDIA / LINK', 'MODIFIED', ''];
const previousAmenities = ref({});
const projectStore = ProjectStore();
const openDeleteModal = ref(false);
const deleteAmenity = ref({id: null});
const trashLoader = ref(null);
const archivedModal = ref(false);
const archived = ref(null);
const unarchiveLoader = ref({});
const loader = ref(false);
const listOfItems = ref([]);
const projectSettings = ref(null);
const isModalOpen = ref({ status: false, url: '' });
const isLoading = ref(false);
const newRows = ref({});

projectStore.RefreshCommunities(projectId.value);
projectStore.RefreshVirtualTours(projectId.value);
/* Methods */

onBeforeRouteLeave((to, from, next) => {
  if (Object.values(newRows.value).length) {
    const confirmed = window.confirm('You have unsaved changes. Are you sure you want to leave?');
    if (confirmed) {
      next();
    } else {
      next(false);
    }
  } else {
    next();
  }
});

const handleGetCategory = async () => {
  try {
    GetProject(projectId.value).then((projectDetails) => {
      projectSettings.value = projectDetails.projectSettings;
      getCategories(projectId.value).then((categories) => {
        // Map categories with their settings from project details
        const completeAmenityCategory = categories.map((elem) => {
          const objectToFind = projectDetails.projectSettings?.amenity ?
            Object.values(projectDetails.projectSettings.amenity)
              .find((item) => item.name.category === elem.category)
            : null;
          return objectToFind ? objectToFind : { name: elem };
        });

        // Sort and generate final array
        const sortedItems = [];
        if (completeAmenityCategory) {
          completeAmenityCategory.sort((a, b) => {
            return a.order - b.order;
          }).forEach((item) => {
            sortedItems.push(item);
          });
        }

        const generateArrayofItems = sortedItems.map((item, index) => {
          return {
            ...(item.id && { 'id': item.id }),
            'order': item.order ? item.order : index + 1,
            'name': item.name,
          };
        });

        categoryList.value = generateArrayofItems;
        categoryList.value = categoryList.value.map((e) => e.name.category);
        listOfItems.value = generateArrayofItems;
      });
    });
  } catch (error) {
    console.error('Err', error);
  }
};
handleGetCategory();

const handleListOfAmenities = async () => {
  isLoading.value=true;
  await getListOfAmenities(projectId.value).then((response) => {
    projectStore.SyncMultipleAmenities(response);
    isLoading.value=false;
  });
};

const addNewRow = () => {
  const id = crypto.randomUUID();
  const newFile = {
    _id: id,
    project_id: projectId.value,
    name: '',
    community_id: '',
    category: '',
    thumbnail: '',
    description: '',
    file: '',
    media_type: '',
    embed_link: '',
    tour_id: '',
    isNew: true,
  };

  newRows.value[id] = newFile;
};

function addNewCategory (category) {
  categoryList.value.push(category);
}

async function getArchivedItems () {
  try {
    const response = await GetAllTrash(projectId.value, getCookie('organization'), 'amenitys');
    archived.value = response.items;
  } catch (error) {
    console.error('Err', error);
  }
}

const handleMoveToTrash = (id) => {
  trashLoader.value = true;
  if ( projectId.value && id ) {
    const newObj = {
      amenity_id: [id],
      timeStamp: Date.now(),
    };
    moveAmenityToTrash(newObj, projectId.value).then(async () => {
      delete projectStore.amenities[id];
      trashLoader.value = false;
      archivedModal.value = true;
      setTimeout(() => {
        archivedModal.value = false;
      }, 2000);
    });
  }
};

async function restoreFunc (item) {
  unarchiveLoader.value[item._id] = true;
  const payload = {
    trash_id: item._id,
  };
  // load.value = item._id;
  try {
    await RestoreTrash(payload, projectId.value, 'amenity', 'restoreAmenity');
    await getArchivedItems();
    delete unarchiveLoader.value[item._id];
  } catch (error) {
    console.error("Error during restore or fetching trash:", error);
  }
}

async function handleButtonClick (e) {
  type.value = e;
  try {
    await router.push({ path: route.path, query: { ...route.query, type: e } });
    if (e === 'Amenities') {
      await handleListOfAmenities(); // This will fetch fresh gallery data
    } else if (e === 'Archive') {
      await getArchivedItems(); // This will fetch archived items
    }
  } catch (e) {
    console.log(e);
  }
}

function openMoveToTrashModal (id) {
  openDeleteModal.value = true;
  deleteAmenity.value.id = id;
}

function openImageModal (status, url){
  isModalOpen.value.status = status;
  isModalOpen.value.url = url;
}

function deleteNewRow (){
  delete newRows.value[deleteAmenity.value.id];
  openDeleteModal.value = false;
}

function exportAmenitiestoCSV (amenitiesData) {
  try {
    const amenitiesArray = Object.values(amenitiesData);

    const headers = [
      'Name',
      'Community ID',
      'Media Type',
      'Category',
      'File URL',
      'Thumbnail URL',
      'Embed Link',
      'Tour ID',
      'Description',
      'Modified Date',
    ];

    // Transform data to match headers
    const csvData = amenitiesArray.map((item) => ({
      'Name': item.name || '',
      'Community ID': item.community_id ? projectStore?.communities?.[item.community_id]?.name : '',
      'Media Type': item.media_type || '',
      'Category': item.category || '',
      'File URL': item.file || '',
      'Thumbnail URL': item.thumbnail || '',
      'Embed Link': item.embed_link || '',
      'Tour ID': item.tour_id ? projectStore?.virtualtours?.[item.tour_id]?.tour_name : '',
      'Description': item.description || '',
      'Modified Date': item.modified ? new Date(item.modified).toLocaleString() : '',
    }));

    // Use Papa Parse to convert to CSV
    const csv = Papa.unparse({
      fields: headers,
      data: csvData,
    }, {
      quotes: true, // Force quotes around all fields
      header: true,
    });

    // Create and trigger download
    const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);

    link.setAttribute('href', url);
    link.setAttribute('download', `amenities_export_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

  } catch (error) {
    console.error('Error exporting CSV:', error);
    throw error;
  }
}

const getDisplayValue = (item) => {
  console.log(item);
  const firstDataKey = Object.keys(item?.data || {})[0];
  const data = item?.data?.[firstDataKey];

  if (!data) {
    return '';
  }

  switch (data.media_type) {
    case 'embed_link':
      return truncateString(data.embed_link, 20);
    case 'virtual_tour':
      return projectStore?.virtualtours?.[data.tour_id]?.tour_name;
    default:
      return truncateString(extractFirebaseStoragePath(data.file, 'filename'), 20);
  }
};

document.addEventListener('refreshAmenitiyListByCategory', () => {
  handleGetCategory();
});

onMounted(async () => {
  if (type.value === 'Amenities') {
    await handleListOfAmenities();
  } else if (type.value === 'Archive') {
    await getArchivedItems();
  }
});

onMounted(() => {
  // Handler for page reload/close
  const handleBeforeUnload = (event) => {
    if (Object.values(newRows.value).length) {
      const message = "You have unsaved changes. Are you sure you want to leave?";
      event.preventDefault();
      event.returnValue = message;
      return message;
    }
    // Add explicit return for the case when there are no unsaved changes
    return undefined;
  };

  window.addEventListener('beforeunload', handleBeforeUnload);

  onUnmounted(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload);
  });
});

</script>

<template>
  <div >
    <div class=" dynamic-header">
            <div class="dynamic-heading ">
                <p class="dynamic-topic">Enter Amenities Details</p>
                <p class="dynamic-sub-topic">Details provided here will be used to placing amenity icons on both exterior and interior spaces. <span class="text-[#1c64f2] underline">View Sample</span></p>
            </div>

          <div class="flex gap-2">
              <div>
                <Button :disabled="loader" @click="exportAmenitiestoCSV(previousAmenities)" title="Export" class="!rounded-lg !px-3 !py-2 !bg-bg-1000 border border-gray-200 !text-[#111928]">
                  <template v-slot:svg>
                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 8.37494H0.0780001C0.158987 8.67991 0.313718 8.95814 0.5274 9.18304L2.2242 10.9505C2.44011 11.1731 2.70722 11.3342 3 11.4186V8.37494Z" fill="#1F2A37"/>
                      <path d="M9.285 2.89511C9.3943 3.01299 9.45477 3.17086 9.45341 3.33472C9.45204 3.49859 9.38894 3.65534 9.27769 3.77122C9.16645 3.8871 9.01596 3.95283 8.85864 3.95425C8.70132 3.95567 8.54976 3.89268 8.4366 3.77883L7.8 3.11636L7.8 7.12498C7.8 7.29074 7.73679 7.4497 7.62426 7.56691C7.51174 7.68411 7.35913 7.74996 7.2 7.74996C7.04087 7.74996 6.88826 7.68411 6.77574 7.56691C6.66321 7.4497 6.6 7.29074 6.6 7.12498L6.6 3.11636L5.9634 3.77883C5.90805 3.83853 5.84185 3.88614 5.76864 3.91889C5.69544 3.95165 5.61671 3.96889 5.53704 3.96961C5.45737 3.97033 5.37837 3.95452 5.30463 3.92309C5.23089 3.89167 5.1639 3.84526 5.10756 3.78658C5.05123 3.7279 5.00668 3.65812 4.97651 3.58131C4.94634 3.5045 4.93116 3.42221 4.93185 3.33922C4.93254 3.25624 4.94909 3.17423 4.98054 3.09798C5.01198 3.02173 5.05769 2.95277 5.115 2.89511L6.4542 1.50016H1.2C0.887173 1.49497 0.585146 1.61925 0.360179 1.84573C0.135213 2.07222 0.00568067 2.3824 0 2.70824L0 7.12498H3C3.31826 7.12498 3.62348 7.25667 3.84853 7.49109C4.07357 7.7255 4.2 8.04343 4.2 8.37494V11.4998H10.8C11.1128 11.505 11.4149 11.3807 11.6398 11.1543C11.8648 10.9278 11.9943 10.6176 12 10.2918V2.75012C12 2.41861 11.8736 2.10067 11.6485 1.86626C11.4235 1.63185 11.1183 1.50016 10.8 1.50016H7.9458L9.285 2.89511Z" fill="#1F2A37"/>
                    </svg>
                  </template>
                </Button>
              </div>
              <div>
                <Button :disabled="true" title="Import" class="!rounded-lg !px-3 !py-2 !bg-bg-1000 border border-gray-200 !text-[#111928]">
                  <template v-slot:svg>
                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 8.37494H0.0780001C0.158987 8.67991 0.313718 8.95814 0.5274 9.18304L2.2242 10.9505C2.44011 11.1731 2.70722 11.3342 3 11.4186V8.37494Z" fill="#1F2A37"/>
                      <path d="M9.285 2.89511C9.3943 3.01299 9.45477 3.17086 9.45341 3.33472C9.45204 3.49859 9.38894 3.65534 9.27769 3.77122C9.16645 3.8871 9.01596 3.95283 8.85864 3.95425C8.70132 3.95567 8.54976 3.89268 8.4366 3.77883L7.8 3.11636L7.8 7.12498C7.8 7.29074 7.73679 7.4497 7.62426 7.56691C7.51174 7.68411 7.35913 7.74996 7.2 7.74996C7.04087 7.74996 6.88826 7.68411 6.77574 7.56691C6.66321 7.4497 6.6 7.29074 6.6 7.12498L6.6 3.11636L5.9634 3.77883C5.90805 3.83853 5.84185 3.88614 5.76864 3.91889C5.69544 3.95165 5.61671 3.96889 5.53704 3.96961C5.45737 3.97033 5.37837 3.95452 5.30463 3.92309C5.23089 3.89167 5.1639 3.84526 5.10756 3.78658C5.05123 3.7279 5.00668 3.65812 4.97651 3.58131C4.94634 3.5045 4.93116 3.42221 4.93185 3.33922C4.93254 3.25624 4.94909 3.17423 4.98054 3.09798C5.01198 3.02173 5.05769 2.95277 5.115 2.89511L6.4542 1.50016H1.2C0.887173 1.49497 0.585146 1.61925 0.360179 1.84573C0.135213 2.07222 0.00568067 2.3824 0 2.70824L0 7.12498H3C3.31826 7.12498 3.62348 7.25667 3.84853 7.49109C4.07357 7.7255 4.2 8.04343 4.2 8.37494V11.4998H10.8C11.1128 11.505 11.4149 11.3807 11.6398 11.1543C11.8648 10.9278 11.9943 10.6176 12 10.2918V2.75012C12 2.41861 11.8736 2.10067 11.6485 1.86626C11.4235 1.63185 11.1183 1.50016 10.8 1.50016H7.9458L9.285 2.89511Z" fill="#1F2A37"/>
                    </svg>
                  </template>
                </Button>

              </div>
              <div>
                <Button :disabled="loader || type == 'Archive'" title="Add Row" class="!px-3 !py-2 border-[1px] border-[#1c64f2] !bg-bg-1000 !justify-center !items-center !gap-2 !text-[#1c64f2]"
                @click="addNewRow"
              >
                <template v-slot:svg>
                  <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.2659 5.96686H6.53255V2.23353C6.53255 2.09208 6.47636 1.95642 6.37634 1.85641C6.27632 1.75639 6.14067 1.7002 5.99922 1.7002C5.85777 1.7002 5.72211 1.75639 5.6221 1.85641C5.52208 1.95642 5.46589 2.09208 5.46589 2.23353V5.96686H1.73255C1.5911 5.96686 1.45545 6.02305 1.35543 6.12307C1.25541 6.22309 1.19922 6.35875 1.19922 6.5002C1.19922 6.64164 1.25541 6.7773 1.35543 6.87732C1.45545 6.97734 1.5911 7.03353 1.73255 7.03353H5.46589V10.7669C5.46589 10.9083 5.52208 11.044 5.6221 11.144C5.72211 11.244 5.85777 11.3002 5.99922 11.3002C6.14067 11.3002 6.27632 11.244 6.37634 11.144C6.47636 11.044 6.53255 10.9083 6.53255 10.7669V7.03353H10.2659C10.4073 7.03353 10.543 6.97734 10.643 6.87732C10.743 6.7773 10.7992 6.64164 10.7992 6.5002C10.7992 6.35875 10.743 6.22309 10.643 6.12307C10.543 6.02305 10.4073 5.96686 10.2659 5.96686Z" fill="#1C64F2"/>
                  </svg>
                </template>
              </Button>
              </div>
            </div>            </div>
      <div class="flex justify-between gap-5">
        <div class="flex gap-5 items-center">
        </div>
      </div>
      <div class="w-full inline-flex flex-col rounded-lg shadow-[0px_1px_2px_-1px_rgba(0,0,0,0.10)] border border-gray-200 justify-start items-start">
        <div class="px-[16px] w-full">
          <div class="border-b border-gray-200 w-full">
          <nav class="-mb-[3px] flex">
            <SwitchButtons :disabled="loader" :active="type" :array="['Amenities', 'Archive']" @click="(e) => handleButtonClick(e)" class="w-full p-0.5"/>
          </nav>
        </div>
        </div>
        <!-- table -->
        <div class="w-full overflow-x-auto">
          <div class="flex w-fit bg-gray-50">
            <div v-for="(item, index) in headers" :key="index"
              class="rounded-lg h-[50px] p-[1rem] text-xs font-semibold text-left uppercase leading-[18px] text-nowrap flex items-center"
              :class="{
                          'w-[3.6rem] justify-center': item.toUpperCase() === 'S.NO',
                          'w-[18.75rem]': item.toUpperCase() === 'AMENITY NAME',
                          'w-[15.6rem]':item.toUpperCase() === 'SELECT TOWER/COMMUNITY *' || item.toUpperCase() === 'SELECT CATEGORY' || item.toUpperCase() === 'SELECT MEDIA TYPE',
                          'w-[12.5rem]': item.toUpperCase() === 'UPLOAD THUMBNAIL' || item.toUpperCase() === 'UPLOAD MEDIA / LINK',
                          'w-[8rem]': item.toUpperCase() === 'MODIFIED',
                          'w-40': item === ''
                      }"
            >
              {{ item }}
            </div>
          </div>
          <div v-if="type === 'Amenities' && categoryList">
            <AmenityTableRow
              v-for="(item, id, index) in projectStore.amenities" :key="index"
              :projectId="projectId"
              :index="index"
              :amenity="item"
              :categoryList="categoryList"
              :communities="projectStore.communities"
              @openImageModal="openImageModal"
              @addNewCategory="addNewCategory"
              :virtualtours="projectStore.virtualtours"
              @moveToTrash="handleMoveToTrash"
              @addNewRow="(val)=>{projectStore.amenities[val._id] = val; loader=false}"
              :loader="loader"
              @handleSubmit="()=>loader=true"
            />
            <!-- NewRows -->
            <AmenityTableRow
              v-for="(item, id, index) in newRows" :key="id"
              :projectId="projectId"
              :index="Object.values(projectStore?.amenities)?.length || 0 + index"
              :amenity="item"
              :categoryList="categoryList"
              :communities="projectStore.communities"
              @addNewCategory="addNewCategory"
              :virtualtours="projectStore.virtualtours"
              @deleteRow="openMoveToTrashModal"
              @addNewRow="(val,id)=>{projectStore.amenities[val._id] = val; delete newRows[id]; loader=false}"
              :loader="loader"
              @handleSubmit="loader=true"
            />
          </div>
          <div v-else>
            <div v-for="(item, id, index) in archived" :key="id" class="flex w-fit">
              <div class="p-[1rem] text-sm relative flex items-center justify-center w-[58px] h-[3.8rem]">
                {{ index + 1 }}
              </div>
              <div class="p-[1rem] text-sm relative flex items-center h-[3.8rem] w-[300px]">
                {{ item?.data?.[Object.keys(item.data)[0]].name }}
              </div>
              <div class="p-[1rem] text-sm relative flex items-center gap-4 justify-between h-[3.8rem] w-[15.6rem]">
                <span>
                  {{ item?.data?.[Object.keys(item.data)[0]]?.community_id ? projectStore?.communities[item?.data?.[Object.keys(item.data)[0]]?.community_id]?.name : 'Select'}}
                </span>
                <span>
                  <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5.01106 7.5C4.77238 7.49995 4.5435 7.4123 4.37476 7.25633L0.77476 3.92981C0.6888 3.8531 0.620236 3.76133 0.573068 3.65987C0.5259 3.55841 0.501072 3.44928 0.500034 3.33886C0.498996 3.22843 0.521767 3.11892 0.56702 3.01672C0.612272 2.91452 0.6791 2.82166 0.763604 2.74358C0.848107 2.6655 0.948594 2.60374 1.0592 2.56193C1.16981 2.52011 1.28832 2.49907 1.40782 2.50003C1.52732 2.50099 1.64542 2.52393 1.75522 2.56752C1.86503 2.6111 1.96434 2.67446 2.04736 2.75389L5.01106 5.49245L7.97476 2.75389C8.1445 2.6024 8.37184 2.51858 8.60782 2.52047C8.8438 2.52237 9.06953 2.60983 9.23639 2.76402C9.40326 2.91821 9.49792 3.12679 9.49997 3.34484C9.50202 3.56289 9.4113 3.77296 9.24736 3.92981L5.64736 7.25633C5.47862 7.4123 5.24973 7.49995 5.01106 7.5Z" fill="#6B7280"/>
                  </svg>
                </span>
              </div>
              <div class="p-[1rem] text-sm relative flex items-center gap-4 justify-between h-[3.8rem] w-[15.6rem]">
                <span>
                  {{ item?.data?.[Object.keys(item.data)[0]]?.category || ''}}
                </span>
                <span>
                  <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5.01106 7.5C4.77238 7.49995 4.5435 7.4123 4.37476 7.25633L0.77476 3.92981C0.6888 3.8531 0.620236 3.76133 0.573068 3.65987C0.5259 3.55841 0.501072 3.44928 0.500034 3.33886C0.498996 3.22843 0.521767 3.11892 0.56702 3.01672C0.612272 2.91452 0.6791 2.82166 0.763604 2.74358C0.848107 2.6655 0.948594 2.60374 1.0592 2.56193C1.16981 2.52011 1.28832 2.49907 1.40782 2.50003C1.52732 2.50099 1.64542 2.52393 1.75522 2.56752C1.86503 2.6111 1.96434 2.67446 2.04736 2.75389L5.01106 5.49245L7.97476 2.75389C8.1445 2.6024 8.37184 2.51858 8.60782 2.52047C8.8438 2.52237 9.06953 2.60983 9.23639 2.76402C9.40326 2.91821 9.49792 3.12679 9.49997 3.34484C9.50202 3.56289 9.4113 3.77296 9.24736 3.92981L5.64736 7.25633C5.47862 7.4123 5.24973 7.49995 5.01106 7.5Z" fill="#6B7280"/>
                  </svg>
                </span>
              </div>
              <div class="p-[1rem] text-sm relative flex items-center gap-4 justify-between h-[3.8rem] w-[15.6rem]">
                <span>
                  {{ item?.data?.[Object.keys(item.data)[0]]?.media_type || ''}}
                </span>
                <span>
                  <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5.01106 7.5C4.77238 7.49995 4.5435 7.4123 4.37476 7.25633L0.77476 3.92981C0.6888 3.8531 0.620236 3.76133 0.573068 3.65987C0.5259 3.55841 0.501072 3.44928 0.500034 3.33886C0.498996 3.22843 0.521767 3.11892 0.56702 3.01672C0.612272 2.91452 0.6791 2.82166 0.763604 2.74358C0.848107 2.6655 0.948594 2.60374 1.0592 2.56193C1.16981 2.52011 1.28832 2.49907 1.40782 2.50003C1.52732 2.50099 1.64542 2.52393 1.75522 2.56752C1.86503 2.6111 1.96434 2.67446 2.04736 2.75389L5.01106 5.49245L7.97476 2.75389C8.1445 2.6024 8.37184 2.51858 8.60782 2.52047C8.8438 2.52237 9.06953 2.60983 9.23639 2.76402C9.40326 2.91821 9.49792 3.12679 9.49997 3.34484C9.50202 3.56289 9.4113 3.77296 9.24736 3.92981L5.64736 7.25633C5.47862 7.4123 5.24973 7.49995 5.01106 7.5Z" fill="#6B7280"/>
                  </svg>
                </span>
              </div>
              <div class="p-[1rem] text-sm relative flex items-center gap-4 justify-between h-[3.8rem] w-[12.5rem]">
                {{ item?.data?.[Object.keys(item.data)[0]]?.thumbnail ? truncateString(extractFirebaseStoragePath(item?.data?.[Object.keys(item.data)[0]]?.thumbnail,'filename'),20) : ''}}
              </div>
              <div class="p-[1rem] text-sm relative flex items-center gap-4 justify-between h-[3.8rem] w-[12.5rem]">
                {{ getDisplayValue(item) }}
              </div>
              <div class="p-[1rem] text-sm relative text-nowrap flex items-center h-[3.8rem] w-[8rem]">
                {{ item.createdAt ? formatTimestamp(item.createdAt) : ''}}
              </div>
              <div class="p-[1rem] text-sm flex justify-center items-center w-40 h-[3.8rem]">
                <Button title="Unarchive" class="relative !px-3 !py-2 border-[1px] border-[#1c64f2] !bg-bg-1000 !justify-center !items-center !gap-2 !text-[#1c64f2]"
                  @click="restoreFunc(item)" :disabled="unarchiveLoader[item._id]"
                >
                  <template v-slot:svg>
                    <div v-if="unarchiveLoader[item._id]" class="absolute top-0 left-0 h-full w-full flex items-center justify-center">
                      <div class="loader"></div>
                    </div>
                    <svg   width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M0.666667 9.95961C0.666667 10.3415 0.807142 10.7078 1.05719 10.9778C1.30724 11.2479 1.64638 11.3996 2 11.3996H10C10.3536 11.3996 10.6928 11.2479 10.9428 10.9778C11.1929 10.7078 11.3333 10.3415 11.3333 9.95961V4.91961H0.666667V9.95961ZM4.19533 7.29057C4.32035 7.15559 4.48989 7.07976 4.66667 7.07976C4.84344 7.07976 5.01298 7.15559 5.138 7.29057L5.33333 7.50153V6.35961C5.33333 6.16865 5.40357 5.98552 5.5286 5.85049C5.65362 5.71547 5.82319 5.63961 6 5.63961C6.17681 5.63961 6.34638 5.71547 6.4714 5.85049C6.59643 5.98552 6.66667 6.16865 6.66667 6.35961V7.50153L6.862 7.29057C6.98774 7.15942 7.15614 7.08684 7.33093 7.08848C7.50573 7.09012 7.67294 7.16585 7.79655 7.29934C7.92015 7.43283 7.99026 7.61342 7.99178 7.8022C7.9933 7.99098 7.92611 8.17286 7.80467 8.30865L6.47133 9.74865C6.40941 9.8157 6.33584 9.8689 6.25484 9.9052C6.17385 9.94149 6.08702 9.96018 5.99933 9.96018C5.91164 9.96018 5.82482 9.94149 5.74382 9.9052C5.66283 9.8689 5.58926 9.8157 5.52733 9.74865L4.194 8.30865C4.0692 8.17344 3.99923 7.99023 3.99948 7.79931C3.99973 7.60839 4.07018 7.4254 4.19533 7.29057Z" fill="#1C64F2"/>
                      <path d="M11.3333 0.599609H0.666667C0.298477 0.599609 0 0.921964 0 1.31961L0 2.75961C0 3.15725 0.298477 3.47961 0.666667 3.47961H11.3333C11.7015 3.47961 12 3.15725 12 2.75961V1.31961C12 0.921964 11.7015 0.599609 11.3333 0.599609Z" fill="#1C64F2"/>
                    </svg>
                  </template>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

    <Modal :open="openDeleteModal">
        <DeleteModalContent
            :trash="true"
            @closeModal="(e) => openDeleteModal = false"
            :loader="trashLoader"
            @handleDelete="deleteNewRow"
            :dataName="'Amenity'"
        />
    </Modal>
    <ImgPreviewModal v-if="isModalOpen.url" :isOpen="isModalOpen.status" :imageUrl="isModalOpen.url"
    @close="isModalOpen.status = false" />
    <div v-if="archivedModal" class="fixed top-0 left-0 h-full w-full flex justify-center items-center">
      <div class="archived bg-bg-1000 shadow-[0px_4px_6px_0px_rgba(0,0,0,0.05)] border border-gray-200 rounded-lg">
        <div class="flex flex-col gap-4 items-center justify-center">
          <div>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.15541 18.3336C6.82899 18.3352 6.51503 18.1819 6.28067 17.9065L0.37744 10.9628C0.259778 10.8235 0.165894 10.6576 0.101148 10.4743C0.0364024 10.2911 0.00206288 10.0942 9.0224e-05 9.89489C-0.00389373 9.49236 0.124201 9.10439 0.356196 8.81634C0.588191 8.52829 0.905082 8.36376 1.23716 8.35893C1.56923 8.3541 1.88929 8.50937 2.12691 8.79059L7.1604 14.7088L17.8722 2.09834C18.1102 1.81712 18.4305 1.662 18.7628 1.66711C19.0951 1.67223 19.4122 1.83715 19.6442 2.1256C19.8762 2.41405 20.0041 2.8024 19.9999 3.20522C19.9957 3.60804 19.8596 3.99233 19.6217 4.27355L8.03014 17.9065C7.79578 18.1819 7.48182 18.3352 7.15541 18.3336Z" fill="#057A55"/>
            </svg>
          </div>
          <div class="w-[126px] h-6 text-center">
            Archived
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.upload-btn-wrapper {
  position: relative;
  overflow: hidden;
}
.upload-btn-wrapper input[type=file] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}
.archived{
 @apply py-5 px-6
}
.loader{
  @apply w-6 h-6 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-4 border-solid border-[#020202];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

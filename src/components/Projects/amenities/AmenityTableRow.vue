<script setup>
import { AmenitySchema } from '@/validationSchema/amenity';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { defineProps, defineEmits, ref, onMounted, nextTick } from 'vue';
import { apiMediaTypes } from '../../../enum.ts';
import Multiselect from 'vue-multiselect';
import { truncateString, formatTimestamp, resizeImage, extractFirebaseStoragePath } from '@/helpers/helpers';
import Button from '@/components/common/Button.vue';
import { amenityDataSyncUp, CreateAmenity, UpdateAmenity } from '@/api/projects/amenties/index';
import { onClickOutside } from '@vueuse/core';

const emits = defineEmits(['addNewCategory', 'openImageModal', 'moveToTrash', 'deleteRow', 'addNewRow', 'handleSubmit']);
const props = defineProps({
  projectId: {type: String, default: ''},
  index: {type: Number, default: 0},
  amenity: {type: Object, default: () => ({})},
  categoryList: {type: Array, default: () => []},
  virtualtours: {type: Object, default: () => ({})},
  communities: {type: Object, default: () => ({})},
});

const loader = ref(false);
const categoryList = props?.categoryList;
const isNew = props?.amenity?.isNew;
const thumbnail = ref(props?.amenity?.thumbnail);
const file = ref(props?.amenity?.file), rowRef = ref(null);
let initialValues = {...props?.amenity};

function getObjectDifference (previousObj, newObj) {
  const differences = {};

  for (const key in newObj) {
    const newValue = newObj[key];
    const prevValue = previousObj[key];
    if (key === 'modified'){
      continue;
    }
    // Skip if new value is falsy, but keep empty arrays and objects
    if (!newValue && !Array.isArray(newValue) && !(newValue instanceof Object)) {
      continue;
    }
    // If property is new
    if (!(key in previousObj)) {
      differences[key] = newValue;
      continue;
    }
    // Handle arrays
    if (Array.isArray(newValue)) {
      if (!Array.isArray(prevValue) || JSON.stringify(newValue) !== JSON.stringify(prevValue)) {
        differences[key] = newValue;
      }
      continue;
    }
    // Handle objects
    if (newValue instanceof Object) {
      if (!(prevValue instanceof Object) || JSON.stringify(newValue) !== JSON.stringify(prevValue)) {
        differences[key] = newValue;
      }
      continue;
    }

    // Handle primitive values
    if (prevValue !== newValue) {
      differences[key] = newValue;
    }
  }

  return differences;
}

async function handleSubmit (val) {
  if (isNew){
    loader.value=true;
    emits('handleSubmit');
    const formData = new FormData();
    if (val.thumbnail || val.media_type === '360_image' || val.media_type === 'image'){
      const resizeDimension = val.media_type === '360_image' ? 1200 : 720;
      let resizedThumbnail;
      if (val.media_type === 'image' || val.media_type === '360_image'){
        resizedThumbnail = await resizeImage(val.file, resizeDimension, resizeDimension);
      } else {
        resizedThumbnail = await resizeImage(val.thumbnail, resizeDimension, resizeDimension);
      }
      val.thumbnail = resizedThumbnail;
      formData.append('thumbnail', val?.thumbnail);
    }
    formData.append('project_id', props.projectId);
    formData.append('category', val?.category);
    val?.description && formData.append('description', val?.description);
    val?.community_id && formData.append('community_id', val?.community_id);
    val?.media_type === 'embed_link' && formData.append('embed_link', val?.embed_link);
    val?.media_type === 'virtual_tour' && formData.append('tour_id', val?.tour_id);
    val?.name && formData.append('name', val?.name);
    val?.media_type !== 'embed_link' && val?.media_type !== 'virtual_tour' && formData.append('file', val?.file);
    formData.append('media_type', val.media_type);
    const response = await CreateAmenity(formData);
    await amenityDataSyncUp(props.projectId);
    if (response._id){
      emits('addNewRow', response, val._id);
      initialValues={...response};
    }
    loader.value=false;
  } else {
    const newObj = getObjectDifference(props.amenity, val);
    console.log(newObj);
    delete newObj.__v;
    if (Object.keys(newObj).length > 0){
      loader.value=true;
      emits('handleSubmit');
      const formData = new FormData();
      const isImageType = props.amenity.media_type;
      if (newObj?.thumbnail){
        const resizeDimension = isImageType === '360_image' ? 1200 : 720;
        const resizedThumbnail = await resizeImage(val.thumbnail, resizeDimension, resizeDimension);
        val.thumbnail = resizedThumbnail;
        formData.append('thumbnail', resizedThumbnail);
      }
      newObj?.category && formData.append('category', newObj?.category);
      newObj?.description && formData.append('description', newObj?.description);
      newObj?.embed_link && formData.append('embed_link', newObj?.embed_link);
      newObj?.tour_id && formData.append('tour_id', newObj?.tour_id);
      newObj?.name && formData.append('name', newObj?.name);
      newObj?.file && formData.append('file', newObj?.file);
      newObj?.community_id && formData.append('community_id', newObj?.community_id);
      formData.append('amenity_id', val._id);
      formData.append('project_id', props.projectId);
      const response = await UpdateAmenity(formData);
      await amenityDataSyncUp(props.projectId);
      if (response._id){
        emits('addNewRow', response);
        initialValues={...response};
        console.log(initialValues);
      }
      if (response.thumbnail){
        thumbnail.value=response.thumbnail;
      }
      if (response.file){
        file.value=response.file;
      }
      loader.value=false;
    }
  }
}

const selectedFields = ref({});
const fieldRefs = ref();
const toggleSelect = (field) => {
  selectedFields.value = { [field]: true };
};
onClickOutside(fieldRefs, () => {
  selectedFields.value = {};
});
onMounted(() => {
  if (props?.amenity?.isNew) {
    nextTick(() => {
      if (rowRef.value) {
        rowRef.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
        const input = rowRef.value.querySelector('input');
        if (input) {
          input.focus();
        }
      }
    });
  }
});
</script>

<template>
        <Form :validation-schema="AmenitySchema" :initial-values="initialValues" @submit="handleSubmit" class="mb-0">
          <div ref="rowRef" class="flex w-fit border-1 border-top ">
            <div class="p-[16px] text-sm flex justify-center items-center w-[58px] h-[3.8rem]">
                {{index + 1}}
            </div>
            <div class="p-[16px] text-sm flex items-center h-[3.8rem] w-[300px]"
              :class="{ 'bg-[#EBF5FF]': selectedFields['name'] }"
              @click="toggleSelect('name')" ref="fieldRefs"
            >
              <div class="w-full" :class="{ 'tableSelect': selectedFields['name']}">
                <Field
                  name="name"
                  type="text"
                  v-model="initialValues.name"
                  placeholder="Enter name"
                  class="w-full placeholder:text-left !bg-transparent focus:outline-none text-sm leading-[21px]"
                  id="name"
                  :class="{ 'selected-field': selectedFields.name }"
                />
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="name" />
              </div>
            </div>
            <div class="p-[16px] text-sm flex items-center h-[3.8rem] w-[15.6rem]"
              :class="{ 'bg-[#EBF5FF]': selectedFields['community_id'] }"
              @click="toggleSelect('community_id')" ref="fieldRefs"
            >
              <div class="w-full">
                <Field
                  v-model="initialValues.community_id"
                  type="text"
                  name="community_id"
                  id="community_id"
                  class="select-primary border-none capitalize"
                  autocomplete="community_id"
                  :placeholder="`Select`"
                  as="select"
                  :class="{ 'selected-field': selectedFields.name }"
                >
                    <option value="" disabled>
                                Select
                            </option>
                    <option value="" disabled
                            v-if="!communities">
                            No Tour found ! </option>
                        <option v-else
                            :value="option._id"
                            v-for="option, index in communities"
                            :key="index"
                            class="text-black">
                            {{ option.name }} </option>
                </Field>
                <ErrorMessage as="p"  class="text-sm text-rose-500 mt-1" name="community_id"/>
              </div>
            </div>
            <div class="p-[16px] text-sm flex items-center h-[3.8rem] w-[15.6rem]"
              :class="{ 'bg-[#EBF5FF]': selectedFields['category'] }"
              @click="toggleSelect('category')" ref="fieldRefs"
            >
              <div class="w-full">
                <Field name="category" v-slot="{ field }" v-model="initialValues.category" class="select-primary1">
                        <Multiselect v-bind="field" v-model="initialValues.category"
                          :options="categoryList" :allow-empty="false"
                          placeholder="Select" :close-on-select="true" :show-labels="false"
                          :multiple="false" :taggable="true" class="tableSelect h-fit !capitalize" :maxHeight="150"
                          tag-placeholder="+ Add"
                          @tag="(val)=>$emit('addNewCategory',val)"
                          :class="{ 'selected-field': selectedFields['category'] }"
                        />
                </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="category" />
              </div>
            </div>
            <div class="p-[16px] text-sm relative flex items-center h-[3.8rem] w-[15.6rem]"
              :class="{ 'bg-[#EBF5FF]': selectedFields['media_type'] }"
              @click="toggleSelect('media_type')" ref="fieldRefs"
            >
              <div class="w-full">
                <Field name="media_type" v-slot="{ field }" v-model="initialValues.media_type" class="select-primary1">
                  <Multiselect v-bind="field" v-model="initialValues.media_type"
                      :options="apiMediaTypes" :allow-empty="false" :disabled="!isNew"
                      placeholder="Select" :searchable="false" :close-on-select="true" :show-labels="false"
                      :multiple="false" class="tableSelect h-fit !capitalize" :maxHeight="150"
                      :class="{ 'selected-field': selectedFields['media_type'] }"
                  />
                </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="media_type" />
              </div>
            </div>
            <div class="p-[16px] text-sm relative flex items-center h-[3.8rem] w-[12.5rem]"
              :class="{ 'bg-[#EBF5FF]': selectedFields['thumbnail'] }"
              @click="toggleSelect('thumbnail')" ref="fieldRefs"
            >
              <div class="w-full">
                <Field id="thumbnail" v-slot="{ field }" name="thumbnail" v-model="thumbnail">
                  <div v-bind="field" v-if="thumbnail && thumbnail?.constructor?.name !== 'File'" class="flex items-center justify-between gap-2.5 w-full">
                    <div>
                        <p v-if="thumbnail">
                          {{ truncateString(extractFirebaseStoragePath(thumbnail,'filename'),10) }}
                        </p>
                    </div>
                    <div class="flex items-center gap-2">
                      <div>
                        <button class="p-1.5 h-7 hover:bg-gray-100 rounded-lg"
                          @click.prevent="()=>$emit('openImageModal', true, thumbnail)"
                        >
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <g id="eye">
                              <path id="Vector"
                                d="M8 2C3.6896 2 0 6.57371 0 8C0 9.49314 2.8368 14 8 14C13.1632 14 16 9.49314 16 8C16 6.57371 12.3104 2 8 2ZM8 10.5714C7.52532 10.5714 7.06131 10.4206 6.66663 10.1381C6.27195 9.85551 5.96434 9.45391 5.78269 8.98404C5.60104 8.51418 5.55351 7.99715 5.64612 7.49834C5.73872 6.99953 5.9673 6.54135 6.30294 6.18173C6.63859 5.8221 7.06623 5.5772 7.53178 5.47798C7.99734 5.37876 8.4799 5.42968 8.91844 5.62431C9.35698 5.81893 9.73181 6.14852 9.99553 6.57139C10.2592 6.99426 10.4 7.49142 10.4 8C10.4 8.68199 10.1471 9.33604 9.69706 9.81827C9.24697 10.3005 8.63652 10.5714 8 10.5714Z"
                                fill="#6B7280" />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <button class="p-1.5 h-7 hover:bg-gray-100 rounded-lg"
                        @click.prevent="()=>{thumbnail = ''}"
                      >
                        <svg width="16" height="16" viewBox="0 0 18 20" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M14 20H4C2.89543 20 2 19.1046 2 18V5H0V3H4V2C4 0.89543 4.89543 0 6 0H12C13.1046 0 14 0.89543 14 2V3H18V5H16V18C16 19.1046 15.1046 20 14 20ZM4 5V18H14V5H4ZM6 2V3H12V2H6ZM12 16H10V7H12V16ZM8 16H6V7H8V16Z"
                            class="fill-gray-500" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div v-else class="upload-btn-wrapper w-full">
                    <template v-if="initialValues.media_type !== 'image' && initialValues.media_type !== '360_image' || !isNew">
                      <p v-if="thumbnail?.constructor?.name === 'File'">
                        {{ truncateString(thumbnail.name, 20) }}
                      </p>
                      <button v-else title="Upload" class="!text-[#6b7280] text-xs font-medium m-0 flex items-center gap-2.5">
                        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <g id="download" clip-path="url(#clip0_716_9964)">
                            <g id="Vector">
                              <path
                                d="M10.2949 5.236C10.1636 5.10141 9.98561 5.02579 9.8 5.02579C9.61438 5.02579 9.43637 5.10141 9.3051 5.236L7.7 6.88226V0.717949C7.7 0.527537 7.62625 0.344924 7.49497 0.210282C7.3637 0.0756408 7.18565 0 7 0C6.81435 0 6.6363 0.0756408 6.50503 0.210282C6.37375 0.344924 6.3 0.527537 6.3 0.717949V6.88226L4.6949 5.236C4.63033 5.16743 4.55309 5.11273 4.46768 5.07511C4.38228 5.03748 4.29043 5.01767 4.19748 5.01685C4.10454 5.01602 4.01236 5.03418 3.92633 5.07028C3.8403 5.10638 3.76215 5.15969 3.69642 5.2271C3.6307 5.29451 3.57872 5.37467 3.54352 5.4629C3.50833 5.55114 3.49062 5.64568 3.49142 5.74101C3.49223 5.83633 3.51154 5.93054 3.54823 6.01814C3.58492 6.10573 3.63824 6.18495 3.7051 6.25118L6.5051 9.12297C6.57012 9.18983 6.64737 9.24288 6.73241 9.27907C6.81746 9.31527 6.90863 9.3339 7.0007 9.3339C7.09277 9.3339 7.18394 9.31527 7.26899 9.27907C7.35403 9.24288 7.43128 9.18983 7.4963 9.12297L10.2963 6.25118C10.4273 6.11635 10.5008 5.93367 10.5006 5.74329C10.5003 5.55292 10.4263 5.37045 10.2949 5.236Z"
                                fill="#6B7280" />
                              <path
                                d="M12.6 8.25641H10.815L8.7325 10.3923C8.50499 10.6257 8.2349 10.8108 7.93763 10.9371C7.64037 11.0634 7.32176 11.1284 7 11.1284C6.67824 11.1284 6.35963 11.0634 6.06237 10.9371C5.7651 10.8108 5.49501 10.6257 5.2675 10.3923L3.185 8.25641H1.4C1.0287 8.25641 0.672601 8.40769 0.41005 8.67698C0.1475 8.94626 0 9.31148 0 9.69231V12.5641C0 12.9449 0.1475 13.3102 0.41005 13.5794C0.672601 13.8487 1.0287 14 1.4 14H12.6C12.9713 14 13.3274 13.8487 13.5899 13.5794C13.8525 13.3102 14 12.9449 14 12.5641V9.69231C14 9.31148 13.8525 8.94626 13.5899 8.67698C13.3274 8.40769 12.9713 8.25641 12.6 8.25641ZM10.85 12.5641C10.6423 12.5641 10.4393 12.5009 10.2667 12.3826C10.094 12.2643 9.9594 12.0961 9.87993 11.8993C9.80046 11.7025 9.77966 11.486 9.82018 11.2771C9.86069 11.0682 9.96069 10.8763 10.1075 10.7257C10.2544 10.5751 10.4415 10.4725 10.6452 10.4309C10.8488 10.3894 11.06 10.4107 11.2518 10.4922C11.4437 10.5737 11.6077 10.7118 11.723 10.8889C11.8384 11.066 11.9 11.2742 11.9 11.4872C11.9 11.7728 11.7894 12.0467 11.5925 12.2487C11.3955 12.4506 11.1285 12.5641 10.85 12.5641Z"
                                fill="#6B7280" />
                            </g>
                          </g>
                          <defs>
                            <clipPath id="clip0_716_9964">
                              <rect width="14" height="14" fill="white" />
                            </clipPath>
                          </defs>
                        </svg>
                        Upload
                      </button>
                      <input
                        type="file"
                        name="thumbnail"
                        id="thumbnail"
                        accept="image/*"
                        @change="(event) => {thumbnail = event.target.files[0]}"
                      />
                    </template>
                  </div>
                </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="thumbnail" />
              </div>
            </div>
            <div class="p-[16px] text-sm relative flex items-center h-[3.8rem] w-[12.5rem]"
              :class="{ 'bg-[#EBF5FF]': selectedFields['upload_media'] }"
              @click="toggleSelect('upload_media')" ref="fieldRefs"
            >
              <div v-if="initialValues.media_type == 'virtual_tour'" class="w-32">
                <Field
                  v-model="initialValues.tour_id"
                  type="text"
                  name="tour_id"
                  id="tour_id"
                  class="select-primary !border-none capitalize"
                  autocomplete="tour_id"
                  :placeholder="`Select`"
                  as="select"
                >
                <option value="" disabled>
                            Select
                        </option>
                  <option value="" disabled
                        v-if="!virtualtours">
                        No Tour found ! </option>
                    <option v-else
                        :value="option._id"
                        v-for="option, index in virtualtours"
                        :key="index"
                        class="text-black">
                        {{ option.name ? option.name : option.tour_name }} </option>
                </Field>
                <ErrorMessage as="p"  class="text-sm text-rose-500 mt-1" name="tour_id"/>
              </div>
              <div v-else-if="initialValues.media_type == 'embed_link'" class="w-full" :class="{ 'tableSelect': selectedFields['upload_media']}">
                <Field
                  name="embed_link"
                  type="text"
                  v-model="initialValues.embed_link"
                  placeholder="Paste link"
                  class="w-full placeholder:text-left placeholder-gray-400 focus:outline-none text-gray-400 text-sm leading-[21px]"
                  id="embed_link"
                  :class="{ 'selected-field': selectedFields.upload_media }"
                ></Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="embed_link"/>
              </div>
              <div v-else class="w-full">
                <Field id="file" name="file" v-slot="{ field }" v-model="file">
                  <div v-bind="field" v-if="file && file?.constructor?.name !== 'File'"
                  class="flex items-center justify-between gap-2.5">
                    <div>
                      <p v-if="file">
                        {{ truncateString(extractFirebaseStoragePath(file,'filename'),10) }}
                      </p>
                    </div>
                    <div class="flex items-center gap-2">
                      <div v-show="initialValues.media_type == 'image'">
                        <button class="p-1.5 h-7 hover:bg-gray-100 rounded-lg"
                          @click.prevent="()=>$emit('openImageModal', true, file)"
                        >
                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                            xmlns="http://www.w3.org/2000/svg">
                            <g id="eye">
                              <path id="Vector"
                                d="M8 2C3.6896 2 0 6.57371 0 8C0 9.49314 2.8368 14 8 14C13.1632 14 16 9.49314 16 8C16 6.57371 12.3104 2 8 2ZM8 10.5714C7.52532 10.5714 7.06131 10.4206 6.66663 10.1381C6.27195 9.85551 5.96434 9.45391 5.78269 8.98404C5.60104 8.51418 5.55351 7.99715 5.64612 7.49834C5.73872 6.99953 5.9673 6.54135 6.30294 6.18173C6.63859 5.8221 7.06623 5.5772 7.53178 5.47798C7.99734 5.37876 8.4799 5.42968 8.91844 5.62431C9.35698 5.81893 9.73181 6.14852 9.99553 6.57139C10.2592 6.99426 10.4 7.49142 10.4 8C10.4 8.68199 10.1471 9.33604 9.69706 9.81827C9.24697 10.3005 8.63652 10.5714 8 10.5714Z"
                                fill="#6B7280" />
                            </g>
                          </svg>
                        </button>
                      </div>
                      <button class="p-1.5 h-7 hover:bg-gray-100 rounded-lg"
                        @click.prevent="()=>{file = ''}"
                      >
                        <svg width="16" height="16" viewBox="0 0 18 20" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M14 20H4C2.89543 20 2 19.1046 2 18V5H0V3H4V2C4 0.89543 4.89543 0 6 0H12C13.1046 0 14 0.89543 14 2V3H18V5H16V18C16 19.1046 15.1046 20 14 20ZM4 5V18H14V5H4ZM6 2V3H12V2H6ZM12 16H10V7H12V16ZM8 16H6V7H8V16Z"
                            class="fill-gray-500" />
                        </svg>
                      </button>
                    </div>
                  </div>
                  <div v-else class="upload-btn-wrapper">
                    <p v-if="file?.constructor?.name === 'File'">
                      {{ truncateString(file.name, 20) }}
                    </p>
                    <button v-else title="Upload" class="!text-[#6b7280] text-xs font-medium m-0 flex items-center gap-2.5">
                      <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="download" clip-path="url(#clip0_716_9964)">
                          <g id="Vector">
                            <path
                              d="M10.2949 5.236C10.1636 5.10141 9.98561 5.02579 9.8 5.02579C9.61438 5.02579 9.43637 5.10141 9.3051 5.236L7.7 6.88226V0.717949C7.7 0.527537 7.62625 0.344924 7.49497 0.210282C7.3637 0.0756408 7.18565 0 7 0C6.81435 0 6.6363 0.0756408 6.50503 0.210282C6.37375 0.344924 6.3 0.527537 6.3 0.717949V6.88226L4.6949 5.236C4.63033 5.16743 4.55309 5.11273 4.46768 5.07511C4.38228 5.03748 4.29043 5.01767 4.19748 5.01685C4.10454 5.01602 4.01236 5.03418 3.92633 5.07028C3.8403 5.10638 3.76215 5.15969 3.69642 5.2271C3.6307 5.29451 3.57872 5.37467 3.54352 5.4629C3.50833 5.55114 3.49062 5.64568 3.49142 5.74101C3.49223 5.83633 3.51154 5.93054 3.54823 6.01814C3.58492 6.10573 3.63824 6.18495 3.7051 6.25118L6.5051 9.12297C6.57012 9.18983 6.64737 9.24288 6.73241 9.27907C6.81746 9.31527 6.90863 9.3339 7.0007 9.3339C7.09277 9.3339 7.18394 9.31527 7.26899 9.27907C7.35403 9.24288 7.43128 9.18983 7.4963 9.12297L10.2963 6.25118C10.4273 6.11635 10.5008 5.93367 10.5006 5.74329C10.5003 5.55292 10.4263 5.37045 10.2949 5.236Z"
                              fill="#6B7280" />
                            <path
                              d="M12.6 8.25641H10.815L8.7325 10.3923C8.50499 10.6257 8.2349 10.8108 7.93763 10.9371C7.64037 11.0634 7.32176 11.1284 7 11.1284C6.67824 11.1284 6.35963 11.0634 6.06237 10.9371C5.7651 10.8108 5.49501 10.6257 5.2675 10.3923L3.185 8.25641H1.4C1.0287 8.25641 0.672601 8.40769 0.41005 8.67698C0.1475 8.94626 0 9.31148 0 9.69231V12.5641C0 12.9449 0.1475 13.3102 0.41005 13.5794C0.672601 13.8487 1.0287 14 1.4 14H12.6C12.9713 14 13.3274 13.8487 13.5899 13.5794C13.8525 13.3102 14 12.9449 14 12.5641V9.69231C14 9.31148 13.8525 8.94626 13.5899 8.67698C13.3274 8.40769 12.9713 8.25641 12.6 8.25641ZM10.85 12.5641C10.6423 12.5641 10.4393 12.5009 10.2667 12.3826C10.094 12.2643 9.9594 12.0961 9.87993 11.8993C9.80046 11.7025 9.77966 11.486 9.82018 11.2771C9.86069 11.0682 9.96069 10.8763 10.1075 10.7257C10.2544 10.5751 10.4415 10.4725 10.6452 10.4309C10.8488 10.3894 11.06 10.4107 11.2518 10.4922C11.4437 10.5737 11.6077 10.7118 11.723 10.8889C11.8384 11.066 11.9 11.2742 11.9 11.4872C11.9 11.7728 11.7894 12.0467 11.5925 12.2487C11.3955 12.4506 11.1285 12.5641 10.85 12.5641Z"
                              fill="#6B7280" />
                          </g>
                        </g>
                        <defs>
                          <clipPath id="clip0_716_9964">
                            <rect width="14" height="14" fill="white" />
                          </clipPath>
                        </defs>
                      </svg>
                      Upload
                    </button>
                    <input
                      type="file"
                      name="file"
                      id="file"
                      @change="(event) => { file=event.target.files[0] }"
                    />
                  </div>
                </Field>
                <ErrorMessage as="p" class="text-sm text-rose-500 mt-1" name="file" />
              </div>
            </div>
            <div class="p-[16px] text-sm text-nowrap flex items-center h-[3.8rem] w-[8rem]">
                {{ initialValues.modified ? formatTimestamp(initialValues.modified) : ''}}
            </div>
            <div class="p-[16px] text-sm flex justify-center items-center w-40 h-[3.8rem]">
              <div class="flex items-center justify-start gap-[22px]">
                <div>
                    <Button :disabled="loader" id="submit" type="submit" title="Save" class="!px-3 !py-2 border-[1px] border-[#1c64f2] !bg-bg-1000 !justify-center !items-center !gap-2 !text-[#1c64f2]">
                      <template v-slot:svg>
                        <div v-if="loader" class="loader !h-5 !w-5 !border-3"></div>
                      </template>
                    </Button>
                </div>
                <div class="h-7 flex justify-center items-center px-1.5 bg-gray-100 rounded-lg hover:cursor-pointer">
                  <span @click.prevent="$emit('deleteRow',initialValues._id)" v-if="isNew">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_716_13540)">
                      <path d="M14.5179 3.36842H11.2586V1.68421C11.2586 1.23753 11.0869 0.809144 10.7813 0.493294C10.4757 0.177443 10.0612 0 9.62898 0L6.36972 0C5.93751 0 5.52301 0.177443 5.2174 0.493294C4.91178 0.809144 4.74009 1.23753 4.74009 1.68421V3.36842H1.48083C1.26473 3.36842 1.05748 3.45714 0.904669 3.61507C0.751862 3.77299 0.666016 3.98719 0.666016 4.21053C0.666016 4.43387 0.751862 4.64806 0.904669 4.80598C1.05748 4.96391 1.26473 5.05263 1.48083 5.05263H2.29565V14.3158C2.29565 14.7625 2.46734 15.1909 2.77295 15.5067C3.07857 15.8226 3.49307 16 3.92527 16H12.0734C12.5056 16 12.9201 15.8226 13.2257 15.5067C13.5314 15.1909 13.7031 14.7625 13.7031 14.3158V5.05263H14.5179C14.734 5.05263 14.9412 4.96391 15.094 4.80598C15.2468 4.64806 15.3327 4.43387 15.3327 4.21053C15.3327 3.98719 15.2468 3.77299 15.094 3.61507C14.9412 3.45714 14.734 3.36842 14.5179 3.36842ZM6.36972 1.68421H9.62898V3.36842H6.36972V1.68421ZM7.18453 12.6316C7.18453 12.8549 7.09869 13.0691 6.94588 13.227C6.79307 13.385 6.58582 13.4737 6.36972 13.4737C6.15362 13.4737 5.94637 13.385 5.79356 13.227C5.64075 13.0691 5.5549 12.8549 5.5549 12.6316V6.73684C5.5549 6.5135 5.64075 6.29931 5.79356 6.14138C5.94637 5.98346 6.15362 5.89474 6.36972 5.89474C6.58582 5.89474 6.79307 5.98346 6.94588 6.14138C7.09869 6.29931 7.18453 6.5135 7.18453 6.73684V12.6316ZM10.4438 12.6316C10.4438 12.8549 10.3579 13.0691 10.2051 13.227C10.0523 13.385 9.84508 13.4737 9.62898 13.4737C9.41288 13.4737 9.20562 13.385 9.05282 13.227C8.90001 13.0691 8.81416 12.8549 8.81416 12.6316V6.73684C8.81416 6.5135 8.90001 6.29931 9.05282 6.14138C9.20562 5.98346 9.41288 5.89474 9.62898 5.89474C9.84508 5.89474 10.0523 5.98346 10.2051 6.14138C10.3579 6.29931 10.4438 6.5135 10.4438 6.73684V12.6316Z" fill="#6B7280"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_716_13540">
                        <rect width="16" height="16" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                  </span>
                  <span v-else @click.prevent="$emit('moveToTrash',initialValues._id)">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_1362_13369)">
                      <path d="M15.1111 0.666992H0.888889C0.397969 0.666992 0 1.05326 0 1.52974L0 3.25523C0 3.73171 0.397969 4.11797 0.888889 4.11797H15.1111C15.602 4.11797 16 3.73171 16 3.25523V1.52974C16 1.05326 15.602 0.666992 15.1111 0.666992Z" fill="#6B7280"/>
                      <path d="M0.888889 5.84346V13.6082C0.888889 14.0658 1.07619 14.5047 1.40959 14.8283C1.74299 15.1519 2.19517 15.3337 2.66667 15.3337H13.3333C13.8048 15.3337 14.257 15.1519 14.5904 14.8283C14.9238 14.5047 15.1111 14.0658 15.1111 13.6082V5.84346H0.888889ZM10.6667 8.4317C10.6667 8.66051 10.573 8.87995 10.4063 9.04175C10.2396 9.20355 10.0135 9.29444 9.77778 9.29444H6.22222C5.98647 9.29444 5.76038 9.20355 5.59368 9.04175C5.42698 8.87995 5.33333 8.66051 5.33333 8.4317V7.56895C5.33333 7.34014 5.42698 7.1207 5.59368 6.9589C5.76038 6.7971 5.98647 6.70621 6.22222 6.70621C6.45797 6.70621 6.68406 6.7971 6.85076 6.9589C7.01746 7.1207 7.11111 7.34014 7.11111 7.56895H8.88889C8.88889 7.34014 8.98254 7.1207 9.14924 6.9589C9.31594 6.7971 9.54203 6.70621 9.77778 6.70621C10.0135 6.70621 10.2396 6.7971 10.4063 6.9589C10.573 7.1207 10.6667 7.34014 10.6667 7.56895V8.4317Z" fill="#6B7280"/>
                    </g>
                    <defs>
                      <clipPath id="clip0_1362_13369">
                        <rect width="16" height="16" fill="white"/>
                      </clipPath>
                    </defs>
                  </svg>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </Form>
</template>

<style>
.tableSelect .multiselect__tags{
  border:none;
}
.tableSelect .multiselect__content-wrapper{
    position: absolute !important;
}
.tableSelect .multiselect__option{
  text-transform: capitalize;
}
.tableSelect .multiselect__tags .multiselect__single{
  font-size: 14px !important;
  font-weight: 500 !important;
}
.tableSelect .multiselect__option , .tableSelect .multiselect__option--selected, .tableSelect .multiselect__option:hover, .tableSelect .multiselect__option--selected:hover{
  background-color: transparent !important;
  color: #6b7280 !important;
  font-weight: 100 !important;
}
.tableSelect .multiselect__option--selected, .tableSelect .multiselect__option--selected:hover{
  color: black !important;
}
.tableSelect>.multiselect__select{
  background: none !important;
}
.tableSelect .multiselect__placeholder{
  color: black !important;
}
.tableSelect.selected-field .multiselect__single {
  background-color: #EBF5FF !important;
}

.tableSelect.selected-field .multiselect__input {
  background-color: #EBF5FF !important;
}

.tableSelect>input{
  background-color: #EBF5FF !important;
}
</style>

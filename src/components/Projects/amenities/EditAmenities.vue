<script setup>
import { onMounted, ref } from 'vue';
import { TrashIcon } from '@heroicons/vue/20/solid';
import { Form, Field, ErrorMessage } from 'vee-validate';
import Spinner from '../../common/Spinner.vue';
import { apiMediaTypes } from '../../../enum';
import { ProjectStore } from '../../../store/project.ts';
import Modal from '../../common/Modal/Modal.vue';
import { useRoute, useRouter } from 'vue-router';
import {
  UpdateAmenity,
  DeleteMedia,
  getCategories,
  amenityDataSyncUp,
} from '../../../api/projects/amenties';
import Multiselect from 'vue-multiselect';
import { resizeImage } from '../../../helpers/helpers';
import { editAmenity } from '../../../validationSchema/amenity';
const router = useRouter();
const route = useRoute();
const projectStore = ProjectStore();
const projectId = ref(route.params.project_id);
const amentiesId = ref(route.params.id);
const media = ref([]);
const thumbnail = ref();
const thumbnailView = ref();
const mediaFile = ref();
const mediaFileView = ref();
const categoryList = ref([]);
const initialValues = ref({});
const loader = ref(false);
const initial_category = ref();

projectStore.RefreshVirtualTours(projectId.value);

getCategories(projectId.value).then((res) => { // Get list of categories
  categoryList.value = res.map((elem) => {
    return {name: elem.category};
  });
}).catch((err) => {
  console.log('output->err', err);
});

const addTag = (newTag) => { // To add new category if it is not in the
  const tag = {
    name: newTag,
  };
  categoryList.value.push(tag); // Adding to list
  initial_category.value = tag; // Selecting same new tag
};

// const erroMsg = ref({
//   isShow: false,
//   message: null,
// });

/* Methods */

const removArrayElementUsingId = (id, array, fieldToBePointed) => {
  const newArray = array.filter((item) => item[fieldToBePointed] !== id );
  return newArray;
};

// function mediaUpdate () {
//   const detailsFormData = new FormData();
//   detailsFormData.append('project_id', projectId.value);
//   detailsFormData.append('amenity_id', amentiesId.value);
//   detailsFormData.append('media_id', mediaId.value);
//   detailsFormData.append('media_type', initialValues.value.media_type);
//   initialValues.value.media_type === 'embed_link' ? detailsFormData.append('link', embed_link.value):'';
//   initialValues.value.media_type === 'virtual_tour' ? detailsFormData.append('tour_id', tour_id.value):'';
//   UpdateMedia(detailsFormData).then((res) => {
//     projectStore.amenities[res._id].media[0].link =  res.media[0].link;
//     projectStore.amenities[res._id].media[0].tour_id =  res.media[0].tour_id;
//     router.go(-1);
//   });
// }

// Submission
const handleSubmission = async (values) => {
  loader.value = true;

  // const typeImage = initialValues.value.media_type ==='image' || initialValues.value.media_type ==='360_image';
  const resizeDimension = values.media_type === '360_image' ? 1200 : 720;

  // Api call
  const DTO = new FormData();
  initialValues.value.name !== values.name && DTO.append('name', values.name);
  initialValues.value.description !== values.description && DTO.append('description', values.description);
  initialValues.value.community_id !== values.community_id && DTO.append('community_id', values.community_id);
  DTO.append('media_type', initialValues.value.media_type );

  // Change in thumbnail
  if (thumbnail.value !== null && thumbnail.value !== undefined){
    const resizedThumbnail = await resizeImage(thumbnail.value, resizeDimension, resizeDimension);
    DTO.append('thumbnail', resizedThumbnail);
  }

  if (mediaFile.value !== null && mediaFile.value !== undefined){
    DTO.append('file', mediaFile.value);
    if (initialValues.value.media_type === 'image' || initialValues.value.media_type === '360_image'){
      const resizedThumbnail = await resizeImage(mediaFile.value, resizeDimension, resizeDimension);
      DTO.append('thumbnail', resizedThumbnail);
    }
  }
  initialValues.value.category !== values.category.name &&  DTO.append('category', values.category.name);
  DTO.append('amenity_id', amentiesId.value);
  DTO.append('project_id', projectId.value);

  await UpdateAmenity(DTO).then(async (res) => {
    await amenityDataSyncUp(projectId.value);
    console.log("Fineshed updating", res);
    projectStore.RefreshAmenities(projectId.value);
    router.push(`/projects/${projectId.value}/amenities/${ initial_category.value.name}`);
  }).catch(() => {})
    .finally(() => {
      loader.value = false;
    });
};

// Delete Media
const handleDeleteMedia = (id) => {
  if (projectId.value && amentiesId.value) {
    const newObj = {
      project_id: projectId.value,
      amenity_id: amentiesId.value,
      media_id: id,
    };

    DeleteMedia(newObj).then((res) => {
      media.value = removArrayElementUsingId(id, media.value, '_id'); // Remove from the list
      projectStore.amenities[res._id].media = res.media;
    });
  }
};

// const createMediaCallBack = async (formObj) => {
//   erroMsg.value.isShow = false;
//   if (projectId.value && amentiesId.value) {
//     const resizeDimension = formObj.type === '360_image' ? 1200 : 720;
//     const resizedThumbnail = await resizeImage(formObj.file, resizeDimension, resizeDimension);
//     const DTO = new FormData();
//     DTO.append('project_id', projectId.value);
//     DTO.append('amenity_id', amentiesId.value);
//     resizedThumbnail && DTO.append('thumbnail', resizedThumbnail);
//     UpdateAmenity(DTO).then((res) => {
//       document.dispatchEvent(new Event('refreshAmenitiyListByCategory'));

//     });

//     const formData = new FormData();
//     formData.append('thumbnail', formObj.file);
//     formData.append('media_type', formObj.type);
//     formData.append('project_id', projectId.value);
//     formData.append('amenity_id', amentiesId.value);

//     // Api call
//     CreateMedia(formData).then((res) => {
//       media.value = res.media;
//       projectStore.amenities[res._id].media = res.media;
//       handleDeleteMedia(res.media[0]._id);

//     });
//   }
// };

if (projectId.value) {
  projectStore.RefreshCommunities(projectId.value);
}

/* Hooks */
onMounted(async () => {
  if (projectId.value && amentiesId.value) {
    if (projectStore?.amenities) {
      initialValues.value = projectStore.amenities[amentiesId.value];

      initial_category.value = {name: projectStore.amenities[amentiesId.value].category};
      initialValues.value.media_type = projectStore.amenities[amentiesId.value].media_type;

      initialValues.value.thumbnail = projectStore.amenities[amentiesId.value].thumbnail;
      console.log("*****", projectStore.amenities);
    } else {
      // If no amenities are available, refresh the amenities
      projectStore.RefreshAmenities(projectId.value);
    }
  }
});

async function changeImage (event, type){
  const file = event.target.files[0];

  if (file) {
    if (type === 'file'){
      // Create a Blob URL for replace media
      const blobUrl = URL.createObjectURL(file);
      mediaFileView.value = blobUrl;
      mediaFile.value = file ;
    } else {
      // Create a Blob URL for thumbnail
      const blobUrl = URL.createObjectURL(file);
      thumbnailView.value = blobUrl;
      thumbnail.value = file;
    }
  }
}

</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary sm:max-w-lg">
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Sample Edit Amenities</h1>
        </div>
        <Form v-if="initialValues" :validation-schema="editAmenity"
                   @submit="handleSubmission"
                    :initial-values="initialValues" class="w-full mb-1">
            <div class="flex flex-col">
              <div class="col-span-auto">
                            <label for="name"
                                class="label-primary">
                                Name</label>
                            <Field as="input" type="text"
                                name="name" autocomplete
                                id="name"
                                class="input-primary"
                                :placeholder="`Enter Name`" />
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="name" />
              </div>

              <div class="col-span-full mb-2">
                <label for="description" class="label-primary">
                  description
                </label>
                <Field
                  name="description"
                  as="textarea"
                  class="textarea-primary"
                  placeholder="Write description here..."
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="description"
                />
              </div>

              <div
                  class="col-span-auto">
                  <label for="category"
                      class="label-primary">Category</label>
                  <div class="mt-2">
                     <Field  name="category" v-model="initial_category"  v-slot="{ category }">
                        <Multiselect
                          v-bind="category" v-model="initial_category"
                          tag-placeholder="Add this as new category"
                          placeholder="Search or add Category"
                          label="name"
                          track-by="name"
                          :multiple="false"
                          :taggable="true"
                          @tag="addTag"
                          :options="categoryList" maxHeight="250" >
                        </Multiselect>
                      </Field>
                    <ErrorMessage as="p"
                        class="text-sm text-rose-500 mt-1"
                        name="category" />
                  </div>
              </div>

              <div class="col-span-auto">
                            <label for="community_id"
                                class="label-primary">
                                Communities</label>
                            <Field
                                as="select"
                                name="community_id" id="community_id"
                                autocomplete="community_id"
                                class="select-primary"
                                :placeholder="`Select community`">
                                <option value="">
                                    none
                                </option>
                                <option value="" disabled
                                    v-if="!projectStore.communities">
                                    No community found ! </option>
                                <option v-else
                                    :value="option._id"
                                    v-for="option, index in  projectStore.communities"
                                    :key="index"
                                    class="text-black">
                                    {{ option.name }}
                                </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="community_id" />
              </div>

              <div class="col-span-auto">
                            <label for="media_type"
                                class="label-primary">
                              Media Type</label>
                            <Field
                                v-model="media_type"
                                disabled
                                as="select" type="text"
                                name="media_type" id="media_type"
                                autocomplete="media_type"
                                class="select-primary"
                                :placeholder="`Seclect Media Type`">
                                <option value="" disabled>
                                    Choose
                                </option>
                                <option value="" disabled
                                    v-if="!apiMediaTypes">
                                    No Type found ! </option>
                                <option v-else
                                    :value="option"
                                    v-for="option, index in  apiMediaTypes"
                                    :key="index"
                                    class="text-black">
                                    {{ option }}
                                </option>
                            </Field>
                            <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                name="media_type" />
              </div>

              <div
                class="col-span-auto flex flex-row justify-between items-end gap-3" v-if="initialValues.media_type !== 'image' && initialValues.media_type !== '360_image'">
                <div>
                  <label for="thumbnail"
                                class="label-primary">Upload
                    Thumbnail</label>
                  <div class="mt-2">
                    <Field
                        type="file"
                        name="thumbnail"
                        id="thumbnail"
                        class="input-primary"
                        placeholder="Upload Low Resulation Image"
                        @change="(event) => changeImage(event, 'thumbnail')"
                    />
                    <ErrorMessage as="p"
                        class="text-sm text-rose-500 mt-1"
                        name="thumbnail" />
                  </div>
                </div>
                <img class="w-[100px] h-[50px] border-2 border-black" :src="thumbnailView? thumbnailView:initialValues.thumbnail">
              </div>

              <div v-if="initialValues.media_type !== 'embed_link' && initialValues.media_type !== 'virtual_tour'" class="">
                          <div class="mb-2 flex justify-between items-center" v-if="initialValues.media_type !== 'image' && initialValues.media_type !== '360_image'">
                            <div class="code">
                              <!-- <h1 class="text-base text-black font-semibold">Edit Media</h1> -->
                              <p v-if="initialValues.media_type !== null" class="text-sm text-black">
                                kindly upload a file only in {{ initialValues.media_type }}
                              </p>
                            </div>
                          </div>

                          <div
                            class="flex flex-col justify-start items-start gap-2  col-span-auto"
                            v-if="media !== null"
                          >
                            <div class="grid grid-cols-3 gap-4 items-center justify-start">
                              <div
                                v-bind:key="items._id"
                                v-for="items in media"
                                class="h-full rounded-md overflow-hidden"
                              >
                                <!-- Image -->
                                <div
                                  v-show="
                                    items.media_type.split('/')[0] === apiMediaTypes[0] ||
                                    items.media_type.split('/')[0] === apiMediaTypes[2]
                                  "
                                  class="relative"
                                >
                                  <img
                                    :src="items.file"
                                    alt=""
                                    class="w-screen h-20 rounded-md"
                                  />
                                  <div
                                    v-if="media.length > 1"
                                    class="absolute bg-red-400 p-2 bottom-0 right-0 rounded-tl-3xl cursor-pointer"
                                    @click="handleDeleteMedia(items._id)"
                                  >
                                    <TrashIcon class="w-5 text-red-600 -mr-[3px] -mb-[4px]" />
                                  </div>
                                </div>
                                <!-- Video -->
                                <div
                                  v-show="
                                    items.media_type.split('/')[0] === apiMediaTypes[1] ||
                                    items.media_type.split('/')[0] === apiMediaTypes[3]
                                  "
                                  class="relative bg-[rgba(0,0,0,0.7)] h-20 flex justify-center items-center"
                                >
                                  <p class="text-white">video</p>
                                  <div
                                    v-if="media.length > 1"
                                    class="absolute bg-red-400 p-2 bottom-0 right-0 rounded-tl-3xl cursor-pointer"
                                    @click="handleDeleteMedia(items._id)"
                                  >
                                    <TrashIcon class="w-5 text-red-600 -mr-[3px] -mb-[4px]" />
                                  </div>
                                </div>
                                <!-- Pdf -->
                                <div
                                  v-show="items.media_type === apiMediaTypes[5]"
                                  class="relative bg-[rgba(0,0,0,0.7)] h-20 flex justify-center items-center"
                                >
                                  <p class="text-white">Pdf</p>
                                  <div
                                    v-if="media.length > 1"
                                    class="absolute bg-red-400 p-2 bottom-0 right-0 rounded-tl-3xl cursor-pointer"
                                    @click="handleDeleteMedia(items._id)"
                                  >
                                    <TrashIcon class="w-5 text-red-600 -mr-[3px] -mb-[4px]" />
                                  </div>
                                </div>
                              </div>
                              <!-- <div class="">
                                <label
                                  for="editUploadFile"
                                  :class="[
                                    'flex justify-between items-center gap-1 sm:w-fit h-11 sm:h-10 rounded-full  sm:rounded bg-[#36f] hover:bg-[#4572fc] border-0 m-0 px-2 text-xs text-white font-semibold leading-6',
                                  ]"
                                >

                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 fill-white" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="sync"><rect width="24" height="24" opacity="0"/><path d="M21.66 10.37a.62.62 0 0 0 .07-.19l.75-4a1 1 0 0 0-2-.36l-.37 2a9.22 9.22 0 0 0-16.58.84 1 1 0 0 0 .55 1.3 1 1 0 0 0 1.31-.55A7.08 7.08 0 0 1 12.07 5a7.17 7.17 0 0 1 6.24 3.58l-1.65-.27a1 1 0 1 0-.32 2l4.25.71h.16a.93.93 0 0 0 .34-.06.33.33 0 0 0 .1-.06.78.78 0 0 0 .2-.11l.08-.1a1.07 1.07 0 0 0 .14-.16.58.58 0 0 0 .05-.16z"/><path d="M19.88 14.07a1 1 0 0 0-1.31.56A7.08 7.08 0 0 1 11.93 19a7.17 7.17 0 0 1-6.24-3.58l1.65.27h.16a1 1 0 0 0 .16-2L3.41 13a.91.91 0 0 0-.33 0H3a1.15 1.15 0 0 0-.32.14 1 1 0 0 0-.18.18l-.09.1a.84.84 0 0 0-.07.19.44.44 0 0 0-.07.17l-.75 4a1 1 0 0 0 .8 1.22h.18a1 1 0 0 0 1-.82l.37-2a9.22 9.22 0 0 0 16.58-.83 1 1 0 0 0-.57-1.28z"/></g></g></svg>
                                  Replace Media
                                </label>
                                <input
                                  type="file"
                                  id="editUploadFile"
                                  class="hidden"
                                  @change="{}"
                                />
                              </div> -->

                            <!-- <div
                                class="col-span-auto w-[480px]" v-if="media_type!=='embed_link' && media_type!=='virtual_tour'">

                            </div>    -->

                            <div
                            class="col-span-auto flex flex-row justify-between items-end gap-3 w-[480px]" v-if="media_type!=='embed_link' && media_type!=='virtual_tour'">
                                <div>
                                  <label for="file"
                                          class="label-primary">Replace Media</label>
                                      <div class="mt-2">
                                          <Field type="file"
                                              name="file"
                                              id="file"
                                              autocomplete="file"
                                              class="input-primary"
                                              placeholder="Upload High Resulation Image"
                                              @change="(event) => changeImage(event, 'file')"
                                              />
                                          <ErrorMessage as="p"
                                              class="text-sm text-rose-500 mt-1"
                                              name="file" />
                                    </div>
                                </div>
                                <img class="w-[100px] h-[50px] border-2 border-black" :src="mediaFileView? mediaFileView:initialValues.file">
                            </div>

                            </div>
                          </div>
              </div>

              <div class="p-3 flex justify-end items-start gap-2">
                          <button @click="() => router.go(-1)" class="cancel-btn-primary">
                                  Cancel
                          </button>
                          <button   id="submit"
                                  type="submit"
                                  class="proceed-btn-primary" >
                            Save Changes
                            <Spinner v-if="loader" />
                          </button>
              </div>
           </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

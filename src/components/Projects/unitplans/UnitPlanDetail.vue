
<script setup>
import { onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ProjectStore } from '../../../store/project';
import Button from '../../common/Button.vue';
import DnDrop from '../../common/DnDrop.vue';
import { Draggable } from 'vue-dndrop';
import { applyDrag } from '../../../helpers/dnDrophelper';
import { EditUnitplan } from '../../../api/projects/unitplans';

const route = useRoute();
const projectStore = ProjectStore();
const unitplan_id = ref(route.params.unitplan_id);
const projectId = ref(route.params.project_id);
const unitplanId = ref(route.params.unitplan_id);
const listOfUnitplanUnits = ref();
const router = useRouter();
const villaFloorsUnitplans = ref();
const reorderVillaFloorUnitplans = ref(false);

const refreshData = () => {

  projectStore.ForceRefreshUnitplans(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
};

onMounted(() => {
  refreshData();
});

document.addEventListener('getListOfVillaUnitplan', () => {
  refreshData();
});

watch(() => projectStore.units, () => {
  listOfUnitplanUnits.value = Object.values(projectStore.units).filter((unit) => {
    return unit.unitplan_id === unitplan_id.value;
  });
});

watch(() => route.params.unitplan_id, () => {
  unitplanId.value = route.params.unitplan_id;
});

const handleDrop = (dropResult) => {
  villaFloorsUnitplans.value = applyDrag(villaFloorsUnitplans.value, dropResult);
};

const filterVillaFloorsUnitplans = () => {
  if (projectStore.unitplans?.[unitplanId.value]?.unit_type === 'villa'){
    villaFloorsUnitplans.value = projectStore.unitplans[unitplanId.value].floor_unitplans.map((elem) => {
      return projectStore.unitplans[elem];
    });
  }
};
filterVillaFloorsUnitplans();
watch(() => projectStore.unitplans, () => {
  filterVillaFloorsUnitplans();
});

const compareTheOriginalListOfItems = () => {
  if (projectStore.unitplans[unitplanId.value].floor_unitplans.map((item, index) => item === villaFloorsUnitplans.value[index]._id).includes(false)){
    const filteredArray = villaFloorsUnitplans.value.map((item) => {
      return item._id;
    });
    return filteredArray;
  }
  return false;

};

const handleUnitReorder = () => {
  const formData = new FormData();
  formData.append('floor_unitplans', JSON.stringify(compareTheOriginalListOfItems()));
  formData.append('project_id', projectId.value);
  formData.append('unitplan_id', unitplanId.value);
  EditUnitplan(formData)
    .then((res) => {
      projectStore.SyncMultipleUnitplans({[res._id]: {...res}});
      console.log(projectStore.unitplans);
      window.location = `/projects/${projectId.value}/unitplans/${unitplanId.value}`;
    })
    .catch((err) => {
      console.log(err);
    });

};

</script>

<template>
  <div class="bg-neutral-900 h-full w-full">
    <div  class="w-full p-3">
      <div
        class="w-full  text-white flex justify-between items-center">
        <div>
          <h1
            class="text-xl sm:text-Base font-semibold leading-7">
            Unitplan Details</h1>
        </div>
        <div class="flex justify-center items-center gap-4">
          <Button
          v-if="projectStore.unitplans?.[unitplanId]?.unit_type==='villa'"
          title="Create New Unitplan"
          theme="primary"
          class="h-10"
          @click="router.push(`/projects/${projectId}/unitplans/${unitplanId}/create`)"
        >
          <template v-slot:svg>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 24 24"
              class="h-6 w-6 fill-txt-1000 dark:fill-txt-50"
            >
              <g data-name="Layer 2">
                <g data-name="plus">
                  <rect
                    width="24"
                    height="24"
                    transform="rotate(180 12 12)"
                    opacity="0"
                  />
                  <path
                    d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z"
                  />
                </g>
              </g>
            </svg>
          </template>
        </Button>

        <Button v-if="projectStore.unitplans?.[unitplanId]?.unit_type !== 'villa'" title="Add floorPlan Link" theme="primary"
            class="h-10"
            @click="router.push(`/projects/${projectId}/unitplans/${unitplanId}/createfloorplan_link`);">
            <template v-slot:svg>
                            <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_306_21007)">
                                    <path class="fill-txt-1000 dark:fill-txt-default" d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z"/>
                                    <path class="fill-txt-1000 dark:fill-txt-default" d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_306_21007">
                                        <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </template>
        </Button>

        <Button title="Create Multiple Unitplan" v-if="projectStore.unitplans?.[unitplanId]?.unit_type==='villa'" @click="router.push(`/projects/${projectId}/unitplans/${unitplanId}/createmultiple`)" theme="primary">
                        <template v-slot:svg>
                            <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_306_21007)">
                                    <path class="fill-txt-1000 dark:fill-txt-default" d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z"/>
                                    <path class="fill-txt-1000 dark:fill-txt-default" d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_306_21007">
                                        <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </template>
                </Button>
          <Button title="Edit Unitplan" theme="primary"
            class="h-10"
            @click="router.push(`/projects/${projectId}/unitplans/${unitplanId}/edit`);">
            <template v-slot:svg>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="h-6 w-6 fill-txt-1000 dark:fill-txt-50"><g data-name="Layer 2"><g data-name="edit-2"><rect width="24" height="24" opacity="0"/><path d="M19 20H5a1 1 0 0 0 0 2h14a1 1 0 0 0 0-2z"/><path d="M5 18h.09l4.17-.38a2 2 0 0 0 1.21-.57l9-9a1.92 1.92 0 0 0-.07-2.71L16.66 2.6A2 2 0 0 0 14 2.53l-9 9a2 2 0 0 0-.57 1.21L4 16.91a1 1 0 0 0 .29.8A1 1 0 0 0 5 18zM15.27 4L18 6.73l-2 1.95L13.32 6zm-8.9 8.91L12 7.32l2.7 2.7-5.6 5.6-3 .28z"/></g></g></svg>
            </template>
          </Button>
        </div>
      </div>

      <div
        class="border-t-[1px] mt-3 sm:py-2 border-neutral-800">
        <div class="">
          <div class="grid grid-cols-3 gap-2">
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit pl-1 py-1.5  border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Name
              </label>
              <p
                class="text-white text-base font-medium leading-normal">
                {{ projectStore.unitplans?.[unitplanId]?.name }}
              </p>
            </div>
            <div
              v-if="projectStore.unitplans?.[unitplanId]?.type"
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit pl-1 py-1.5 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Type
              </label>

              <p
                class="text-white text-base font-medium leading-normal">
                {{ projectStore.unitplans?.[unitplanId]?.type }}
              </p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Measurement
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.measurement ? projectStore.unitplans?.[unitplanId]?.measurement : '-'
                }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Measurement Type
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.measurement_type
                }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Balcony Measurement
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                   projectStore.unitplans?.[unitplanId]?.balcony_measurement ? projectStore.unitplans?.[unitplanId]?.balcony_measurement : '-'
                }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Balcony Measurement Type
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.balcony_measurement_type
                }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Suite Area Measurement
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.suite_area ? projectStore.unitplans?.[unitplanId]?.suite_area : '-'
                }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Suite Area Measurement Type
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.suite_area_type ? projectStore.unitplans?.[unitplanId]?.suite_area_type : '-'
                }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Bedrooms
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.bedrooms
                }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                Bathrooms
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.bathrooms
                }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                is Furnished
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.is_furnished?"Yes":"No"
                   }}</p>
            </div>
            <div
              class="w-full h-fit flex-col justify-start items-start inline-flex bg-inherit py-1.5 pl-1 border-b-[1px] sm:border-0 border-neutral-800">
              <label
                class=" text-xs font-semibold mb-1 text-neutral-500">
                is Commercial
              </label>
              <p
                class="text-white text-base  font-medium leading-normal">
                {{
                  projectStore.unitplans?.[unitplanId]?.is_commercial?"Yes":"No"
                   }}</p>
            </div>
          </div>
        </div>
      </div>
<div v-if="projectStore.unitplans?.[unitplanId]?.unit_type==='villa' && projectStore.unitplans[unitplanId].floor_unitplans.length > 0">
      <div
        class="w-full border-[1px] border-solid border-neutral-800 ">
      </div>

      <div
              class="sm:flex sm:items-center sm:justify-between my-3">
              <div class="min-w-0 flex-1">
                <h2
                  class="text-xl font-semibold tracking-2 leading-7 text-white sm:truncate sm:text-xl sm:tracking-tight">
                  Villa Floor</h2>
              </div>
                <div class="flex justify-end items-center px-2 flex-1 gap-3">
                          <button v-if="reorderVillaFloorUnitplans && villaFloorsUnitplans &&villaFloorsUnitplans.length" type="button"
                            class="cancel-btn-primary bg-slate-500"
                            @click="() =>reorderVillaFloorUnitplans = false">cancel</button>
                          <button type="button" v-if="villaFloorsUnitplans && villaFloorsUnitplans.length > 1" :disabled="reorderVillaFloorUnitplans && !compareTheOriginalListOfItems() ? true : false" @click="reorderVillaFloorUnitplans ? handleUnitReorder() : reorderVillaFloorUnitplans = true"
                            class="proceed-btn-primary">{{ !reorderVillaFloorUnitplans?'ReOrder Items':'Save' }}
                            <Spinner v-if="loader" />
                          </button>
                </div>
            </div>
      <div class="w-full overflow-x-auto p-3 flex gap-2" v-if="!reorderVillaFloorUnitplans" >
        <div
            v-for="(unitplan, unitplanId) in projectStore.unitplans[unitplanId].floor_unitplans"
            :key="unitplanId"
            class="max-sm:rounded-sm rounded-md cursor-pointer border-[1px] border-bg-900 bg-white">

            <router-link
              :to="{ path: `/projects/${projectId}/unitplans/${unitplan}` }"
            >
              <img
                :src="projectStore.unitplans[unitplan].thumbnail"
                alt="img"
                class="rounded-t-md w-full"
                style="height: 150px; object-fit: cover"
              />

              <div class="flex justify-between items-center">
                <p class="text-txt-default dark:text-txt-1000 p-2">
                  {{ projectStore.unitplans[unitplan].name }}
                </p>
                <button>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    strokeWidth="{1.5}"
                    stroke="currentColor"
                    class="w-6 h-6 stroke-bg-default dark:stroke-bg-1000"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z"
                    />
                  </svg>
                </button>
              </div>
            </router-link>
          </div>
      </div>
      <div class="w-full overflow-x-auto p-3 flex gap-2" v-else-if="villaFloorsUnitplans">
        <DnDrop  orientation="horizontal" :animationDuration="Number('200')" @on-drop="handleDrop" style="display: flex;" class="w-full h-fit px-3 py-4 border left-0 flex items-center  gap-5 overflow-x-scroll bg-white" >
                                <template #items>
                                    <Draggable v-for="item in villaFloorsUnitplans" class="cursor-pointer w-52 h-fit bg-black rounded-md flex-shrink-0 " :key="item._id">
                                      <div class="max-sm:rounded-sm rounded-md cursor-pointer border-[1px] border-bg-900 bg-white">
                                        <img :src="item.thumbnail" alt="image" class="h-32 w-full fit-cover rounded-md cursor-pointer"/>
                                        <div><p class="text-txt-default dark:text-txt-1000 p-2">
                                        {{ item.name }}
                                        </p></div>
                                      </div>
                                    </Draggable>
                                </template>
                </DnDrop>
      </div>
    </div>
      <div
        class="w-full border-[1px] border-solid border-neutral-800 ">
      </div>
      <div
        class="mx-auto max-w-7xl hidden sm:block  ">
        <div  class="bg-neutral-900">
          <div
            class="py-3 flex flex-col">
            <div
              class="sm:flex sm:items-center sm:justify-between mb-3">
              <div class="min-w-0 flex-1">
                <h2
                  class="text-xl font-semibold tracking-2 leading-7 text-white sm:truncate sm:text-xl sm:tracking-tight">
                  Unitplan Units</h2>
              </div>
            </div>
            <div class="pt-2 flow-root">
              <div
                style="height: inherit; overflow-y: scroll;"
                class="-mx-4 -my-2 min-h-full sm:-mx-6 lg:-mx-8 h-full">
                <div
                  v-if="!listOfUnitplanUnits || listOfUnitplanUnits.length === 0"
                  class=" text-white w-full h-full flex justify-center items-center p-2 ">
                  <div class="mb-60">
                    <p class="text-sm text-neutral-100 mt-2">
                      Oops! No Data Found, Contact admin to
                      add Units to this unitplan</p>
                  </div>
                </div>
                <div v-else
                  class="inline-block min-w-full h-[47vh] align-middle sm:px-6 ">
                  <table
                    class="min-w-full pb-40 overflow-y-auto ">
                    <thead>
                      <tr>
                        <th scope="col"
                          class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                          name
                        </th>
                        <th scope="col"
                          class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                          status
                        </th>
                        <th scope="col"
                          class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                          metadata
                        </th>
                        <th scope="col"
                          class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                          Building
                        </th>
                        <th scope="col"
                          class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                          building_id
                        </th>
                        <th scope="col"
                          class="sticky top-0 z-10 bg-neutral-900 px-3 py-1.5 text-left text-sm font-semibold text-neutral-500">
                          floor_id
                        </th>
                      </tr>
                    </thead>
                    <tbody
                      class="divide-y divide-[#56565680]">
                      <tr
                        v-for="unit, unitId in listOfUnitplanUnits"
                        :key="unitId">
                        <td
                          class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                          {{ unit.name }} </td>
                        <td
                          class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                          {{
                            projectStore.unitplans?.[unit.unitplan_id].name
                          }}
                        </td>

                        <td
                          class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                          {{ unit.status ? unit.status : '' }}
                        </td>
                        <td
                          class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                          {{
                            projectStore.buildings?.[unit.building_id]?.name
                          }}
                        </td>
                        <td
                          class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                          {{ unit.building_id }}
                        </td>
                        <td
                          class="whitespace-nowrap px-3 py-3 text-xs text-gray-300">
                          {{
                            projectStore.buildings?.[unit.building_id]?.floors[unit.floor_id]?.floor_name
                          }}
                        </td>
                      </tr>
                      <div class=" border-0 h-10"></div>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

  </div>
</template>

<style scoped></style>

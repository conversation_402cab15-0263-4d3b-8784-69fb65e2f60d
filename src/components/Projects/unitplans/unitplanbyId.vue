<script setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import unitplanSidebar from './unitplanSidebar.vue';
import { ProjectStore } from '@/store/project';
import SafeAreaHighlight from '@/components/scenes/SafeAreaHighlight.vue';
import { unitplanDescTypes, unitplanListTypes, unitplanScaleTypes } from '@/enum';
import { GetStructuredTourById, GetVirtualTourList } from '@/api/projects/tours';
import { nextTick } from 'vue';
import { createHotspots, deleteHotspots, editHotspots, EditUnitplan } from '@/api/projects/unitplans';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import ImgPreviewModal from '@/components/common/Modal/imgPreviewModal.vue';
import { GetRequest } from '@/helpers';

const route = useRoute();
const projectStore = ProjectStore();
const projectId = ref(route.params.project_id);
const safeFrame = ref(false);
const selectedMenu = ref('unitplan');
const unitplanId = ref(route.params.unitplan_id);

const router = useRouter();
const isImageRendered = ref(false);
const imageDimensions = ref({ width: 0, height: 0 });
const imageRef = ref(null);
const detailsBoxRef = ref(null);
const existingHotspotsBoxRef = ref({});
const currentDetailsBoxHeight = ref(null);
const parentRef = ref(null);
const fieldName = ref(null);
const errorRef = ref({
  open: false,
  message: null,
});
const newElement = ref(null);
const isBottom = ref(false);
const isRight = ref(false);
const toggleListRef = ref({
  anchorElem: null,
  ishide: true,
});
const toggleDescTypeRef = ref({
  anchorElem: unitplanDescTypes.PILL,
});
const selectedPill = ref(null);
const imageList = ref(null);
const selectedImageItem = ref(null);
const groupsList = ref(null);
const selectedGroupItem = ref({
  parent: null,
  child: null,
});
// const toggleDragDrop = ref(true);
const dragDropItemsRef = ref({});
const openDeleteModal = ref(false);
const deleteLoader = ref(false);
const selectedScaleItem = ref(null);
const isUpdating = ref(false);

const refreshData = () => {
  projectStore.RefreshUnitplans(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
};

refreshData();

// Find the Villa Floor Parent Unitplan Id
const findVillaFloorParentId = (searchId) => {
  console.log("findVillaFloorParentId", searchId);
  const listOfUnitplans = projectStore.unitplans;
  let parentId = null;
  Object.keys(listOfUnitplans).forEach((key) => {
    // Check the floor_unitplans key in the object and should be an array if then length check
    if (Array.isArray(listOfUnitplans[key]?.floor_unitplans) && listOfUnitplans[key]?.floor_unitplans?.length > 0){
      // Check the searchId is contains in the array
      if (listOfUnitplans[key].floor_unitplans.includes(searchId)){
        parentId = listOfUnitplans[key]._id; // return the parent id of villa
      }
    }
  });
  return parentId;
};

const unitplanHotspotPillsList = ref({
  images: [],
  groups: {},
  labels: {},
});

const selectedTourId = ref('');
const tourList = ref([]);

const updateSelectedIdFromUnitplan = () => {
  const up = projectStore.unitplans?.[unitplanId.value];
  if (up?.tour_id) {
    const matchingTour = tourList.value.find((t) => t._id === up.tour_id);
    selectedTourId.value = matchingTour ? matchingTour._id : '';
  } else {
    selectedTourId.value = '';
  }
};

// Image Or Group list fetch data
const getTourDetails = () => {
  let upId; // unitplan id

  if (projectStore.unitplans?.[unitplanId.value]?.unit_type === 'villa_floor'){
    upId = findVillaFloorParentId(projectStore.unitplans?.[unitplanId.value]?._id);
  } else {
    upId = unitplanId.value; //
  }

  if (upId && projectStore.unitplans?.[upId]?.tour_id) {
    GetStructuredTourById(projectId.value, projectStore.unitplans[upId].tour_id).then((res) => {
      unitplanHotspotPillsList.value = {
        images: [],
        groups: {},
        labels: {},
      };

      const images = res.images;
      const groups = res.groups || {};
      const labels = res.labels || {};
      const tourType = computed(() => res.type || res.virtualtour_type);
      const isCustom = tourType.value === 'custom';
      const isMLE = tourType.value === 'MLE';
      const isExternal = tourType.value === 'external';
      // ✅ MLE tours
      if (isMLE) {
        unitplanHotspotPillsList.value.labels = Object.entries(labels).map(([id, label]) => ({
          id,
          ...label,
          type: 'label',
        }));
        console.log("MLE Mode - only labels", unitplanHotspotPillsList.value);
        return;
      }

      // ✅ External FP tour handling
      if (isExternal && res.link) {
        const tourLink = res.link;
        const tourPaths = tourLink.split("/");

        if (tourPaths[2] === "fp.propvr.tech") {
          const userId = tourPaths[tourPaths.length - 2];
          const project = tourPaths[tourPaths.length - 1];

          GetRequest(`https://app.propvr.tech/getImages?userid=${userId}&project=${project}`).then((fpRes) => {
            console.log("FP Tour Data", fpRes);

            // Images
            if (fpRes.images && Object.keys(fpRes.images).length > 0) {
              const updatedImageList = { ...fpRes.images };
              Object.keys(updatedImageList).forEach((key) => {
                updatedImageList[key] = { ...updatedImageList[key], _id: key };
              });
              imageList.value = updatedImageList;
              console.log("imageList", imageList.value);
            }

            // Groups
            if (fpRes.groups && Object.keys(fpRes.groups).length > 0) {
              const updatedGroupList = { ...fpRes.groups };
              Object.keys(updatedGroupList).forEach((key) => {
                const group = updatedGroupList[key];
                if (group.subcat && Object.keys(group.subcat).length > 0) {
                  const updatedSubCatList = { ...group.subcat };
                  Object.keys(updatedSubCatList).forEach((subKey) => {
                    updatedSubCatList[subKey] = { ...updatedSubCatList[subKey], _id: subKey };
                  });
                  updatedGroupList[key] = {
                    ...group,
                    subcat: updatedSubCatList,
                    _id: key,
                  };
                } else {
                  updatedGroupList[key] = {
                    ...group,
                    _id: key,
                  };
                }
              });
              groupsList.value = updatedGroupList;
              console.log("groupsList", groupsList.value);
            }

            // Toggle image/group display
            if (
              imageList.value && Object.keys(imageList.value).length > 0 &&
              groupsList.value && Object.keys(groupsList.value).length > 0
            ) {
              toggleListRef.value.ishide = false;
              toggleListRef.value.anchorElem = unitplanListTypes.IMAGE;
            } else {
              toggleListRef.value.ishide = true;
              toggleListRef.value.anchorElem = null;
            }

            // ✅ Update unitplanHotspotPillsList structure for FP tours
            unitplanHotspotPillsList.value = {
              images: [],
              groups: {},
              labels: {},
            };

            Object.values(imageList.value).forEach((img) => {
              const imageData = {
                ...img,
                type: 'image',
              };

              const hasGroup = !!img.groupId;
              const hasSubGroup = !!img.subGroupId;

              if (!hasGroup && !hasSubGroup) {
                unitplanHotspotPillsList.value.images.push(imageData);
                return;
              }

              const groupId = img.groupId;
              const groupInfo = groupsList.value[groupId] || {};

              if (!unitplanHotspotPillsList.value.groups[groupId]) {
                unitplanHotspotPillsList.value.groups[groupId] = {
                  name: groupInfo.name,
                  images: [],
                };
              }

              unitplanHotspotPillsList.value.groups[groupId].images.push(imageData);
            });

            console.log("✅ FP Structured unitplanHotspotPillsList", unitplanHotspotPillsList.value);
          });

          return; // Exit after FP external tour handling
        }
      }

      // ✅ Custom and generic external tour image/group handling
      if (images) {
        Object.values(images).forEach((img) => {
          const imageData = {
            ...img,
            type: 'image',
          };
          const hasGroup = !!img.groupId;
          const hasSubGroup = !!img.subGroupId;
          if (!hasGroup && !hasSubGroup) {
            unitplanHotspotPillsList.value.images.push(imageData);
            return;
          }
          if (isExternal && hasSubGroup) {
            return;
          }
          const groupId = img.groupId;
          const groupInfo = groups[groupId] || {};
          if (!unitplanHotspotPillsList.value.groups[groupId]) {
            unitplanHotspotPillsList.value.groups[groupId] = {
              name: groupInfo.name,
              images: [],
              subGroups: {},
            };
          }
          const group = unitplanHotspotPillsList.value.groups[groupId];
          if (isCustom && hasSubGroup) {
            const subGroupId = img.subGroupId;
            const subGroupInfo = groupInfo.subgroups?.[subGroupId];
            if (!group.subGroups[subGroupId]) {
              group.subGroups[subGroupId] = {
                name: subGroupInfo?.name,
                images: [],
              };
            }
            group.subGroups[subGroupId].images.push(imageData);
          } else {
            group.images.push(imageData);
          }
        });
      }

      if (isExternal) {
        Object.values(unitplanHotspotPillsList.value.groups).forEach((group) => {
          delete group.subGroups;
        });
      }

      console.log("Structured unitplanHotspotPillsList", unitplanHotspotPillsList.value);
    });
  } else {
    unitplanHotspotPillsList.value = [];
  }

  // Always get tour list
  GetVirtualTourList(projectId.value).then((res) => {
    tourList.value = Object.values(res);
    updateSelectedIdFromUnitplan();
  });
};

// Scale setup
const scaleSetup = () => {
  console.log("--scaleSetup--");

  if (projectStore.unitplans?.[unitplanId.value]?.hotspots){
    console.log("if scaleSetup");

    const hotspots = projectStore.unitplans?.[unitplanId.value]?.hotspots;
    console.log(hotspots);

    const isScaleExist = Object.keys(hotspots).some((key) => hotspots[key].scale); // all previous hotspots

    console.log(isScaleExist);

    if (isScaleExist){

      const getAllScaleValues = Object.keys(hotspots).map((key) => hotspots[key].scale).filter(Boolean); // cut off all the null or undefiend values from hotspots scale values

      // find the majority of the scale and update the scale reference
      // cases : ['a','a','a','a','a','a','c','y','a','a'] --> a ['a','c','d'] --> d ['a','a','c','d','c','d'] --> d

      console.log(getAllScaleValues);

      const groupTheScaleValues = Object.groupBy(getAllScaleValues, (item) => item);
      console.log(groupTheScaleValues);

      let getTheHighestScale = getAllScaleValues[getAllScaleValues.length - 1];
      let maxScaleKey = 0;
      console.log(getTheHighestScale);

      for (const key in groupTheScaleValues) {
        if (groupTheScaleValues[key].length >= maxScaleKey) {
          maxScaleKey = groupTheScaleValues[key].length;
          getTheHighestScale = key;
        }
      }

      console.log(maxScaleKey);
      console.log(getTheHighestScale);

      selectedScaleItem.value = getTheHighestScale; // update the scale reference

      /*   console.log(groupTheScaleValues.sort((a, b) =>
              getAllScaleValues.filter(x => x === b).length - getAllScaleValues.filter(x => x === a).length
          )); */

    } else {
      selectedScaleItem.value = unitplanScaleTypes.SMALL; // default
    }

  } else {
    console.log("else scaleSetup");
    selectedScaleItem.value = unitplanScaleTypes.SMALL; // default
  }

};

if (projectStore.unitplans !== null){
  getTourDetails(); // update the image and group list on load
  // framePreviewURL();
}

const setPreviousDetailsToFields = async (layerId) => {
  const val = layerId;
  if (val && Object.keys(projectStore.unitplans[unitplanId.value].hotspots).length > 0){

    /* Text */
    if (projectStore.unitplans[unitplanId.value].hotspots[val].text){
      const hotspot = projectStore.unitplans[unitplanId.value].hotspots[val];
      const desc = hotspot.text;
      const allImages = [
        ...unitplanHotspotPillsList.value.images,
        ...Object.values(unitplanHotspotPillsList.value.groups).flatMap((group) => [
          ...group.images,
          ...Object.values(group.subGroups).flatMap((sub) => sub.images),
        ]),
        ...(Array.isArray(unitplanHotspotPillsList.value.labels)
          ? unitplanHotspotPillsList.value.labels.map((label) => ({ ...label, type: 'label' }))
          : []),
      ];
      console.log("allImages", allImages);

      if (hotspot.type === unitplanListTypes.DEFAULT) {
        if (hotspot.image_id) {
          const match = allImages.find((img) => img._id === hotspot.image_id || img.id === hotspot.image_id);
          selectedPill.value = match || null;
        } else {
          selectedPill.value = null;
        }
        fieldName.value = desc.replace(/\\n/g, '\n');
        toggleDescTypeRef.value.anchorElem = unitplanDescTypes.TEXT;
      } else if (hotspot.type === unitplanListTypes.IMAGE) {
        const match = allImages.find((img) => img._id === hotspot.image_id || img.id === hotspot.image_id);
        if (match) {
          selectedPill.value = match;
          toggleDescTypeRef.value.anchorElem = unitplanDescTypes.PILL;
        } else {
          selectedPill.value = null;
          toggleDescTypeRef.value.anchorElem = unitplanDescTypes.PILL;
        }
      } else if (hotspot.type === unitplanListTypes.LABEL) {
        const match = allImages.find((label) => label.id === hotspot.label_id);
        if (match) {
          selectedPill.value = match;
          toggleDescTypeRef.value.anchorElem = unitplanDescTypes.PILL;
        } else {
          selectedPill.value = null;
          toggleDescTypeRef.value.anchorElem = unitplanDescTypes.PILL;
        }
      }
    }

    /* Scale */
    if (projectStore.unitplans[unitplanId.value].hotspots[val].scale){
      selectedScaleItem.value = projectStore.unitplans[unitplanId.value].hotspots[val].scale;
    } else {
      scaleSetup(); // if no scale then setup the scale
    }

    // Set the toggle list if both lists are exist, depending on the type.
    if (
      (imageList.value && Object.values(imageList.value).length > 0) &&
  (groupsList.value && Object.values(groupsList.value).length > 0)
    ) {
      toggleListRef.value.ishide = false;
      const hotspotType = projectStore.unitplans[unitplanId.value].hotspots[val].type;
      if (hotspotType === unitplanListTypes.DEFAULT || hotspotType === unitplanListTypes.IMAGE) {
        toggleListRef.value.anchorElem = unitplanListTypes.IMAGE;
      } else if (hotspotType === unitplanListTypes.GROUP || hotspotType === unitplanListTypes.SUBGROUP) {
        toggleListRef.value.anchorElem = unitplanListTypes.GROUP;
      }
    }

    /* Dynamic Box display */
    if (projectStore.unitplans[unitplanId.value].hotspots[val].y){
      const rect = imageRef.value.getBoundingClientRect();
      const originalXPoint = (projectStore.unitplans[unitplanId.value].hotspots[val].x / 100) * rect.width;
      const originalYPoint = (projectStore.unitplans[unitplanId.value].hotspots[val].y / 100) * rect.height;
      // Within the bounds of the image
      if (Math.round(originalYPoint) > Math.round(rect.height/2) ) {
        isBottom.value = true;
      } else {
        isBottom.value = false;
      }

      if (Math.round(originalXPoint) > Math.round(rect.width/2) ){
        isRight.value = true;
      } else {
        isRight.value = false;
      }

      await nextTick(); // let's calm down till the dom element is re-rendered.

      // Get height after div is rendered
      if (existingHotspotsBoxRef.value[val]){

        currentDetailsBoxHeight.value = existingHotspotsBoxRef.value[val].getBoundingClientRect().height;
      }

    }
  }
};

watch(newElement, async (newVal) => {
  if (newVal && !route.query.layerId) {
    // newElement
    await nextTick();
    // Get height after newElement is rendered
    currentDetailsBoxHeight.value = detailsBoxRef.value.getBoundingClientRect().height;
  }
});

watch([toggleListRef, toggleDescTypeRef], async (val) => {
  if (isBottom.value){
    // Wait for DOM to update first
    await nextTick();
    if (newElement.value && !route.query.layerId && detailsBoxRef.value && val[1].anchorElem){
      // new Element
      await nextTick(); // let's be calm till dom element is re-rendered.
      // Get height after the rendered
      currentDetailsBoxHeight.value = detailsBoxRef.value.getBoundingClientRect().height;
    } else if (route.query.layerId && val[1].anchorElem){
      // Edit existing hotspots
      await nextTick(); // let's be calm till dom element is re-rendered.
      // Get height after the rendered
      if (existingHotspotsBoxRef.value[route.query.layerId]){
        currentDetailsBoxHeight.value = existingHotspotsBoxRef.value[route.query.layerId].getBoundingClientRect().height;
      }
    }
  }
}, {deep: true});

watch(() => route.query.layerId, async (val) => {
  setPreviousDetailsToFields(val); // set the previous data to the select layerId.
});

watch(() => projectStore.unitplans, async (val) => {
  if (val[unitplanId.value]){
    getTourDetails();
  }
}, {deep: true});

const updateImageDimensions = () => {
  if (imageRef.value) {
    const rect = imageRef.value.getBoundingClientRect();
    imageDimensions.value = {
      width: rect.width,
      height: rect.height,
    };
  }
};

const convertToParentPoint = (coOrdinateType, imageCalcPoint) => {
  if (!imageRef.value || !parentRef.value) {
    return false;
  }

  const imageRect = imageRef.value.getBoundingClientRect();
  const parentRect = parentRef.value.getBoundingClientRect();

  console.log(parentRect);
  console.log(imageRect);

  // Calculate the position and size of the image relative to the parent
  const imageRelativeLeft = imageRect.left - parentRect.left;
  const imageRelativeTop = imageRect.top - parentRect.top;

  // Convert image percentages to parent percentages
  // For X coordinate
  if (coOrdinateType === 'x') {
    // Convert the percentage point on image to actual pixels
    const pointInPixels = (imageCalcPoint / 100) * imageRect.width;
    // Add the image's offset from parent
    const absolutePoint = pointInPixels + imageRelativeLeft;
    // Convert back to percentage relative to parent
    return (absolutePoint / parentRect.width) * 100;
  }

  // For Y coordinate
  // Convert the percentage point on image to actual pixels
  const pointInPixels = (imageCalcPoint / 100) * imageRect.height;
  // Add the image's offset from parent
  const absolutePoint = pointInPixels + imageRelativeTop;
  // Convert back to percentage relative to parent
  return (absolutePoint / parentRect.height) * 100;

};

const resetValues = () => {
  newElement.value = null; // Reset the new element
  selectedImageItem.value = null; // Reset the selectedImageItem
  selectedGroupItem.value = {
    parent: null,
    child: null,
  }; // Reset the selectedGroupItem
  fieldName.value = null; // Reset the fieldName
  selectedPill.value = null; // Reset the selectedPill
  toggleDescTypeRef.value.anchorElem = unitplanDescTypes.PILL; // Reset the toggleDescType
  isBottom.value = false; // Reset the isBottom
  isRight.value = false; // Reset the isRight
  errorRef.value.open = false; // Reset the error message
  errorRef.value.message = null; // Reset the error message
  scaleSetup(); // scale val setup
};

const handleImageClick = (e) => {
  console.log("ENTEREDD");
  if (!route.query.layerId) {

    e.stopPropagation();
    resetValues();
    const rect = imageRef.value.getBoundingClientRect();
    console.log(rect);

    const xpoint = e.clientX - rect.left;
    const ypoint = e.clientY - rect.top;
    console.log(xpoint, ypoint);

    const calcXPoint = (xpoint/rect.width) * 100;
    const calcYPoint = (ypoint/rect.height) * 100;
    console.log(calcXPoint, calcYPoint);
    // Within the bounds of the image
    if ( Math.round(ypoint) > Math.round(rect.height/2) ) {
      isBottom.value = true;
    } else {
      isBottom.value = false;
    }

    if (Math.round(xpoint) > Math.round(rect.width/2)){
      isRight.value = true;
    } else {
      isRight.value = false;
    }

    if (!String(calcXPoint).includes('-') && !String(calcYPoint).includes('-')) {
      newElement.value = {
        x: calcXPoint,
        y: calcYPoint,
        text: null,
      };
    } else {
      resetValues();
    }
  }

};

const validateChecks = async () => {
  const text = fieldName.value; // Text
  const pill = selectedPill.value; // Pill
  /* Description Check */
  if ((text === null || text.trim().length <= 0) && pill === null){
    errorRef.value.message = 'Description is required!';
    errorRef.value.open = true;
    return false;
  }

  errorRef.value.message = null;
  errorRef.value.open = false;
  return true;
};

const handleDescToggle = () => {
//   toggleDescTypeRef.value.anchorElem = toggleDescTypeRef.value.anchorElem === unitplanDescTypes.PILL ? unitplanDescTypes.TEXT : unitplanDescTypes.PILL;
  console.log(toggleDescTypeRef.value.anchorElem);

  fieldName.value = null; // Reset the fieldName
  selectedPill.value = null; // Reset the selectedPill
  // if (toggleDescTypeRef.anchorElem !== type) {
  //     toggleDescTypeRef.anchorElem = type;
  //     fieldName.value = null;
  //     selectedPill.value = null;
  // }
};

const frameTypeParms = () => {
  const frameTypeParms = {};
  if (toggleDescTypeRef.value.anchorElem === unitplanDescTypes.PILL) {
    if (selectedPill.value.type === 'image') {
      frameTypeParms.type = unitplanListTypes.IMAGE;
      frameTypeParms.image_id = selectedPill.value._id || selectedPill.value.id;
    }
    if (selectedPill.value.type === 'label') {
      frameTypeParms.type = unitplanListTypes.LABEL;
      frameTypeParms.label_id = selectedPill.value.id;
    }
  } else {
    frameTypeParms.type = unitplanListTypes.DEFAULT;
    if (selectedPill.value) {
      frameTypeParms.image_id = selectedPill.value._id || selectedPill.value.id;
    }
  }
  // if (selectedImageItem.value && (selectedGroupItem.value.parent === null && selectedGroupItem.value.child === null)){
  //   // image
  //   frameTypeParms.type = unitplanListTypes.IMAGE;
  //   frameTypeParms.image_id = selectedImageItem.value._id;
  // } else if ((selectedGroupItem.value.parent || selectedGroupItem.value.child) && selectedImageItem.value === null){
  //   // group & subgroup
  //   if (selectedGroupItem.value.parent && selectedGroupItem.value.child) {
  //     frameTypeParms.type = unitplanListTypes.SUBGROUP;
  //     frameTypeParms.group_id = selectedGroupItem.value.parent._id;
  //     frameTypeParms.subGroup_id = selectedGroupItem.value.child._id;
  //   } else if (selectedGroupItem.value.parent) {
  //     frameTypeParms.type = unitplanListTypes.GROUP;
  //     frameTypeParms.group_id = selectedGroupItem.value.parent._id;
  //   }
  // } else {
  //   // default
  //   frameTypeParms.type = unitplanListTypes.DEFAULT;
  // }
  console.log("frameTypeParms", frameTypeParms);

  return frameTypeParms;
};

const handleAddItems = async () => {

  const isValid = await validateChecks(); // Validate checkup
  if (isValid){
    const content = fieldName.value ? fieldName.value.trim().replace(/\r?\n/g, '\\n') : selectedPill.value.name; // description
    const frameParms = {
      project_id: projectId.value,
      unitplan_id: unitplanId.value,
      hotspots: {
        ...newElement.value,
        text: content,
        scale: selectedScaleItem.value,
        ...frameTypeParms(),
      },
    };
    console.log("frameParms", frameParms);

    createHotspots(frameParms).then((res) => {
      console.log(res.hotspots);
      projectStore.unitplans[unitplanId.value].hotspots = res.hotspots; // Update the hotspots Objects in projectStore
      resetValues();
    });
  }

};

const handleEditItem = async (prevData, selectedImageId) => {
  const isValid = await validateChecks(); // Validate checkup
  if (isValid){
    const content = fieldName.value ? fieldName.value.trim().replace(/\r?\n/g, '\\n') : selectedPill.value.name ; // Desc
    const getTheCurrentTypeDetails =  frameTypeParms();
    let comparesionTypeParms = {};

    if (prevData?.type === undefined){
      comparesionTypeParms = {
        ...getTheCurrentTypeDetails,
      };
    } else if (prevData.type.toLowerCase() !== getTheCurrentTypeDetails.type.toLowerCase()){
    // different
      comparesionTypeParms = {
        ...getTheCurrentTypeDetails,
      };
    } else {
      // same, then campare keys differs
      if (getTheCurrentTypeDetails.type === unitplanListTypes.IMAGE){
        comparesionTypeParms={
          ...(prevData.image_id !== getTheCurrentTypeDetails.image_id && {image_id: getTheCurrentTypeDetails.image_id} ),
        };
      } else if (getTheCurrentTypeDetails.type === unitplanListTypes.LABEL) {
        comparesionTypeParms = {
          ...(prevData.label_id !== getTheCurrentTypeDetails.label_id && { label_id: getTheCurrentTypeDetails.label_id }),
        };
      } else if (getTheCurrentTypeDetails.type === unitplanListTypes.GROUP || getTheCurrentTypeDetails.type === unitplanListTypes.SUBGROUP){
        comparesionTypeParms = {
          ...( (getTheCurrentTypeDetails.group_id ? prevData.group_id !== getTheCurrentTypeDetails.group_id : false) && {group_id: getTheCurrentTypeDetails.group_id } ),
          ...(( getTheCurrentTypeDetails.subGroup_id ? prevData.subGroup_id !== getTheCurrentTypeDetails.subGroup_id : false) && {subGroup_id: getTheCurrentTypeDetails.subGroup_id } ),
        };
      }
    }
    const selectedImageIdValue = selectedImageId?._id || selectedImageId?.id;
    const frameParms = {
      project_id: projectId.value,
      unitplan_id: unitplanId.value,
      hotspot_id: prevData._id,
      hotspots: {
        ...(content !== prevData.text && { text: content}),
        ...((selectedImageId === null) ? { image_id: null } : (selectedImageIdValue && selectedImageIdValue !== prevData.image_id && { image_id: selectedImageIdValue })),
        ...(selectedImageIdValue && selectedImageIdValue !== prevData.image_id && { image_id: selectedImageIdValue }),
        ...(selectedScaleItem.value !== prevData.scale && { scale: selectedScaleItem.value}),
        ...comparesionTypeParms,
      },
    };
    console.log(frameParms);
    if (Object.keys(frameParms.hotspots).length > 0) {
      // Api call
      editHotspots(frameParms).then((res) => {
        projectStore.unitplans[unitplanId.value].hotspots[prevData._id] = res.hotspots[prevData._id]; // Updated hotspots objects for edit layer key
        resetValues();
        selectedPill.value = null;
        router.push({path: route.path,
          query: {},
        });
      });
    } else {
      router.push({path: route.path,
        query: {},
      });
    }
  }
};

const handleUpdateHotspotPosition = () => {
  const updates = Object.keys(dragDropItemsRef.value);

  if (updates.length === 0) {
    return;
  }

  const promises = updates.map((key) => {
    const frameParms = {
      project_id: projectId.value,
      unitplan_id: unitplanId.value,
      hotspot_id: key,
      hotspots: {
        x: dragDropItemsRef.value[key].x,
        y: dragDropItemsRef.value[key].y,
      },
    };

    return editHotspots(frameParms);
  });

  Promise.all(promises).then((res) => {
    if (res.length > 0) {
      projectStore.unitplans[unitplanId.value].hotspots = res[res.length - 1].hotspots;
      dragDropItemsRef.value = {};
    }
  });
};

const handleDeleteHotspot = () => {
  console.log("handleDeleteHotspot");
  deleteLoader.value = true;
  const deletehotspotId = route.query.deleteId;

  if (deletehotspotId && unitplanId.value && projectId.value){
    const frameParms = {
      project_id: projectId.value,
      unitplan_id: unitplanId.value,
      hotspot_id: deletehotspotId,
    };

    deleteHotspots(frameParms).then((res) => {
      console.log(res.hotspots);
      projectStore.unitplans[unitplanId.value].hotspots = res.hotspots; // Update the hotspots Objects in projectStore
      openDeleteModal.value = false; // reset the delete modal
      deleteLoader.value = false; // reset the deleteLoader
      router.push({ path: route.path,  query: {} }); // clear the query parameters
    });
  }

};

/* Drag and Drop (HTML Drag and Drop API)*/

// Drag & Drop Events handlers
const handleDragStart = (e) => {

  console.log("handleDragStart Zone");
  const draggableElement = e.target;
  if (!draggableElement.getAttribute('draggable')) {
    e.preventDefault();
    return;
  }
  e.dataTransfer.clearData(); // first clean up the mess.
  const rect = draggableElement.getBoundingClientRect();
  // Store offsets in DTO
  e.dataTransfer.setData('layerId', draggableElement.dataset.hotspotId);
  e.dataTransfer.setData('offsetX', e.clientX - rect.left);
  e.dataTransfer.setData('offsetY', e.clientY - rect.top);

};

// Drop zone - Drop
const handleDrop = (e) => {
  console.log("handleDrop Zone");
  const layerId = e.dataTransfer.getData('layerId');
  const draggableElement = existingHotspotsBoxRef.value[layerId];

  // Get offsets from DTO
  const grabOffsetX = parseInt(e.dataTransfer.getData('offsetX'));
  const grabOffsetY = parseInt(e.dataTransfer.getData('offsetY'));
  console.log(grabOffsetX);
  console.log(grabOffsetY);

  // Get parent container bounds
  const rect = imageRef.value.getBoundingClientRect();

  // Calculate points relative to parent, accounting for grab offset
  let xpoint = e.clientX - rect.left - grabOffsetX;
  let ypoint = e.clientY - rect.top - grabOffsetY;

  // Account for element size to prevent overflow
  const maxX = rect.width - draggableElement.offsetWidth;
  const maxY = rect.height - draggableElement.offsetHeight;

  // Constrain positions
  xpoint = Math.max(0, Math.min(xpoint, maxX));
  ypoint = Math.max(0, Math.min(ypoint, maxY));

  // Convert to percentages
  const calcXPoint = (xpoint / rect.width) * 100;
  const calcYPoint = (ypoint / rect.height) * 100;

  // temporarily update positions in dom
  draggableElement.style.left = calcXPoint + '%';
  draggableElement.style.top = calcYPoint + '%';

  if (dragDropItemsRef.value[layerId]){
    // existing
    dragDropItemsRef.value[layerId] = {
      ...dragDropItemsRef.value[layerId],
      x: calcXPoint,
      y: calcYPoint,
    };
  } else {
    // new element
    dragDropItemsRef.value[layerId] = {
      hotspot_id: layerId,
      x: calcXPoint,
      y: calcYPoint,
    };
  }
  handleUpdateHotspotPosition();
};

const dragEvents = computed(() => {
  console.log("dragEvents tiggered");
  //   if (!toggleDragDrop.value) {
  //     return {};
  //   }
  return {
    dragstart: handleDragStart,
  };
});

const dropZoneEvents = computed(() => {
  console.log("dropZoneEvents tiggered");

  //   if (!toggleDragDrop.value) {
  //     return {};
  //   }

  return {
    dragover: (e) => e.preventDefault(),
    dragenter: (e) => e.preventDefault(),
    drop: handleDrop,
  };
});

onMounted(() => {
  if (route.query.deleteId){
    openDeleteModal.value = true;
  }
  window.addEventListener('resize', updateImageDimensions);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateImageDimensions);
});

watch(() => route.params.unitplan_id, async (newVal) => {
  unitplanId.value = newVal;
  getTourDetails(); // Make sure tourList is up to date
});

const updateTourId = async () => {
  const formData = new FormData();
  formData.append('project_id', projectId.value);
  formData.append('unitplan_id', unitplanId.value);
  formData.append('tour_id', selectedTourId.value);
  isUpdating.value = true;
  try {
    const updatedUnitplan = await EditUnitplan(formData);

    if (updatedUnitplan && updatedUnitplan._id) {
      projectStore.unitplans[updatedUnitplan._id] = updatedUnitplan;
    }

    getTourDetails();
  } catch (error) {
    console.error("Error updating unit plan or fetching tour details:", error);
  } finally {
    isUpdating.value = false;
  }
};

const isModalOpen = ref({ status: false, url: '' });
function openImageModal (status, url){
  isModalOpen.value.status = status;
  isModalOpen.value.url = url;
}

</script>

<template>
    <unitplanSidebar :selectedMenu="selectedMenu"/>
    <!-- scene section -->
    <div class="flex-1 h-full overflow-hidden flex flex-col justify-start items-start bg-white gap-2">
        <div class="h-[90%] w-full flex-none">
            <div class="h-full overflow-hidden">
                <div class="bg-neutral-900 h-full w-full text-white">
                    <div class="h-full flex justify-center items-center text-white relative z-0 check">
                        <SafeAreaHighlight :show="safeFrame" :safePadding="{ top: '70px', bottom: '70px', left: '80px', right: '80px' }">
                            <div ref="parentRef" v-if="projectStore.unitplans && projectStore.unitplans?.[unitplanId]?.image_url !== undefined" class="bg-transparent w-full h-[100%] flex justify-center items-center relative overflow-hidden">
                                <img ref="imageRef" v-if="unitplanId && projectStore.unitplans && projectStore.unitplans[unitplanId]" :src="projectStore.unitplans[unitplanId].image_url" v-on="dropZoneEvents" @click="handleImageClick" @load="() => {isImageRendered = true; updateImageDimensions();}"/>
                                <!-- Create New Hotspot -->
                                <div ref="detailsBoxRef" v-if="newElement && !route.query.layerId"  :style="`left: ${convertToParentPoint('x',newElement.x)}%; top:${convertToParentPoint('y',newElement.y)}%;  ${ !isBottom ? `margin-top:0;`: `margin-top:-${currentDetailsBoxHeight}px;`} ${!isRight ? `margin-left:0;` : `margin-left:-480px;` }`" class="bg-white text-black flex flex-col justify-start items-end gap-2 rounded absolute z-50 w-[480px] p-3 boxShadow">
                                    <div class="w-full flex justify-between items-center">
                                        <p class="text-base font-medium">Add Name</p>
                                        <svg @click="() => {resetValues();}" class="cursor-pointer" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                            <g clip-path="url(#clip0_5532_34612)">
                                                <path d="M7.09045 5.99961L11.1665 1.92359C11.24 1.85256 11.2987 1.76758 11.339 1.67363C11.3794 1.57968 11.4006 1.47863 11.4015 1.37638C11.4024 1.27413 11.3829 1.17272 11.3442 1.07808C11.3055 0.983445 11.2483 0.897464 11.176 0.825159C11.1037 0.752855 11.0177 0.695674 10.9231 0.656954C10.8284 0.618234 10.727 0.59875 10.6248 0.599638C10.5225 0.600527 10.4215 0.621771 10.3275 0.662129C10.2336 0.702488 10.1486 0.761155 10.0776 0.834705L6.00156 4.91072L1.92555 0.834705C1.78031 0.694429 1.58578 0.616809 1.38387 0.618564C1.18196 0.620319 0.988817 0.701307 0.846038 0.844085C0.70326 0.986864 0.622272 1.18001 0.620517 1.38192C0.618763 1.58383 0.696382 1.77835 0.836658 1.92359L4.91267 5.99961L0.836658 10.0756C0.763108 10.1467 0.704442 10.2316 0.664083 10.3256C0.623724 10.4195 0.60248 10.5206 0.601592 10.6228C0.600703 10.7251 0.620187 10.8265 0.658907 10.9211C0.697627 11.0158 0.754808 11.1018 0.827113 11.1741C0.899417 11.2464 0.985398 11.3035 1.08004 11.3423C1.17468 11.381 1.27608 11.4005 1.37833 11.3996C1.48058 11.3987 1.58163 11.3774 1.67558 11.3371C1.76954 11.2967 1.85451 11.2381 1.92555 11.1645L6.00156 7.0885L10.0776 11.1645C10.2228 11.3048 10.4173 11.3824 10.6193 11.3807C10.8212 11.3789 11.0143 11.2979 11.1571 11.1551C11.2999 11.0124 11.3809 10.8192 11.3826 10.6173C11.3844 10.4154 11.3067 10.2209 11.1665 10.0756L7.09045 5.99961Z" fill="#9CA3AF"/>
                                            </g>
                                            <defs>
                                            <clipPath id="clip0_5532_34612">
                                                <rect width="12" height="12" fill="white"/>
                                            </clipPath>
                                            </defs>
                                        </svg>
                                    </div>
                                    <div class="w-full flex flex-col justify-start items-start h-[300px] overflow-y-auto pe-2 gap-2">
                                      <div class="flex flex-col justify-start items-start w-full gap-2">
                                        <span class="text-sm">How you want to add name ?</span>
                                        <div class="flex text-sm gap-3">
                                            <label class="cursor-pointer">
                                                <input
                                                    type="radio"
                                                    name="descType"
                                                    class="hidden"
                                                    :value="unitplanDescTypes.PILL"
                                                    v-model="toggleDescTypeRef.anchorElem"
                                                    @change="handleDescToggle"
                                                />
                                                <div class="py-2 px-3 text-center rounded-lg" :class="toggleDescTypeRef.anchorElem === unitplanDescTypes.PILL ? 'text-white bg-blue-600' : 'bg-white text-black border border-gray-200'">
                                                    Fetch names from VR tour
                                                </div>
                                            </label>
                                            <label class="cursor-pointer">
                                                <input
                                                    type="radio"
                                                    name="descType"
                                                    class="hidden"
                                                    :value="unitplanDescTypes.TEXT"
                                                    v-model="toggleDescTypeRef.anchorElem"
                                                    @change="handleDescToggle"
                                                />
                                                <div class="py-2 px-3 text-center rounded-lg" :class="toggleDescTypeRef.anchorElem === unitplanDescTypes.TEXT ? 'text-white bg-blue-600' : 'bg-white text-black border border-gray-200'">
                                                    Add Manually
                                                </div>
                                            </label>
                                        </div>
                                      </div>
                                      <div class="relative w-full flex items-center" v-if="toggleDescTypeRef.anchorElem === unitplanDescTypes.PILL">
                                        <select
                                          v-model="selectedTourId"
                                          @change="updateTourId"
                                          :disabled="!!selectedTourId"
                                          class="border p-2 bg-white text-black w-full rounded-lg"
                                        >
                                          <option disabled value="">Choose</option>
                                          <option
                                            v-for="tour in tourList"
                                            :key="tour._id"
                                            :value="tour._id"
                                          >
                                            {{ tour.name }}
                                          </option>
                                        </select>
                                        <div v-if="isUpdating" class="absolute right-0 mr-2 loader !h-5 !w-5 !border-3 !border-[#1C64F2] !border-t-gray-200"></div>
                                      </div>
                                      <span class="w-full text-gray-500 text-xs">Names will be Fetched from this VR Tour</span>

                                      <div class="flex flex-col justify-start items-start w-full pt-2">
                                          <div class="flex flex-col gap-3">
                                              <div>
                                                  <p class="text-sm pb-1">Select Size</p>
                                                  <p class="text-xs text-gray-500">Available Names from Upload VR Tour of this Unit </p>
                                              </div>
                                              <div class="flex gap-2">
                                                  <div v-for="size in Object.values(unitplanScaleTypes)" :key="size">
                                                      <button @click="selectedScaleItem = size" :class="[
                                                          'rounded-lg px-3 py-1 capitalize',
                                                          selectedScaleItem === size ? 'bg-blue-600 text-white' : 'bg-white border  text-gray-700']"
                                                      class="rounded-lg px-4 py-2 text-sm capitalize">{{ size }}</button>
                                                  </div>
                                              </div>
                                          </div>
                                      </div>

                                      <div v-if="toggleDescTypeRef.anchorElem !== unitplanDescTypes.PILL" class="flex flex-col w-full gap-2 pt-2">
                                          <p class="text-sm">Enter Text</p>
                                          <textarea v-model="fieldName" id="create" rows="3" placeholder="text.." class="border select-primary outline-none rounded text-black bg-transparent p-2 h-fit block"></textarea>
                                      </div>
                                      <div class="flex flex-col w-full pt-2">
                                        <p class="mb-2 pb-1 text-black font-semibold">Names Fetched From VR Tours</p>
                                        <!-- Container -->
                                        <div class="flex flex-col gap-y-2 overflow-y-auto max-h-[200px] px-3 py-2 bg-gray-50 rounded-2xl">
                                          <!-- Labels -->
                                          <div
                                            v-for="(item, index) in unitplanHotspotPillsList.labels"
                                            :key="index"
                                            @click="() => {if(item === selectedPill) {selectedPill = null;} else {selectedPill = item;}}"
                                            :class="[
                                              'flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer transition-all '
                                            ]"
                                          >
                                            <div class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300">
                                              <span class="">{{ item.name }}</span>
                                              <svg v-if="selectedPill === item" xmlns="http://www.w3.org/2000/svg" width="10" height="9" viewBox="0 0 10 9" fill="none">
                                                <path d="M3.5777 8.33333C3.4145 8.33411 3.25752 8.25745 3.14033 8.11974L0.18872 4.64789C0.129889 4.57827 0.082947 4.49528 0.0505741 4.40367C0.0182012 4.31205 0.00103144 4.21361 4.5112e-05 4.11395C-0.00194686 3.91268 0.0621007 3.7187 0.178098 3.57468C0.294096 3.43065 0.452541 3.34838 0.618578 3.34597C0.784615 3.34355 0.944643 3.42119 1.06346 3.5618L3.5802 6.5209L8.9361 0.215672C9.05508 0.0750621 9.21526 -0.00249541 9.38142 6.12625e-05C9.54757 0.00261793 9.70608 0.0850794 9.82208 0.229305C9.93808 0.37353 10.0021 0.567705 9.99995 0.769114C9.99784 0.970523 9.92981 1.16267 9.81083 1.30328L4.01507 8.11974C3.89789 8.25745 3.74091 8.33411 3.5777 8.33333Z" fill="#0E9F6E"/>
                                              </svg>
                                            </div>
                                          </div>
                                          <!-- Top-Level Images -->
                                          <div
                                            v-for="(item, index) in unitplanHotspotPillsList.images"
                                            :key="index"
                                            @click="() => {if(item === selectedPill) {selectedPill = null;} else {selectedPill = item;}}"
                                            :class="[
                                              'flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer transition-all '
                                            ]"
                                          >
                                            <div class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300">
                                              <span class="">{{ item.name }}</span>
                                              <svg v-if="selectedPill === item" xmlns="http://www.w3.org/2000/svg" width="10" height="9" viewBox="0 0 10 9" fill="none">
                                                <path d="M3.5777 8.33333C3.4145 8.33411 3.25752 8.25745 3.14033 8.11974L0.18872 4.64789C0.129889 4.57827 0.082947 4.49528 0.0505741 4.40367C0.0182012 4.31205 0.00103144 4.21361 4.5112e-05 4.11395C-0.00194686 3.91268 0.0621007 3.7187 0.178098 3.57468C0.294096 3.43065 0.452541 3.34838 0.618578 3.34597C0.784615 3.34355 0.944643 3.42119 1.06346 3.5618L3.5802 6.5209L8.9361 0.215672C9.05508 0.0750621 9.21526 -0.00249541 9.38142 6.12625e-05C9.54757 0.00261793 9.70608 0.0850794 9.82208 0.229305C9.93808 0.37353 10.0021 0.567705 9.99995 0.769114C9.99784 0.970523 9.92981 1.16267 9.81083 1.30328L4.01507 8.11974C3.89789 8.25745 3.74091 8.33411 3.5777 8.33333Z" fill="#0E9F6E"/>
                                              </svg>
                                              <svg @click.stop="openImageModal(true, item.thumbnail)" xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
                                                <g clip-path="url(#clip0_6048_21058)">
                                                  <path d="M6.5 1.5C3.2672 1.5 0.5 4.93029 0.5 6C0.5 7.11986 2.6276 10.5 6.5 10.5C10.3724 10.5 12.5 7.11986 12.5 6C12.5 4.93029 9.7328 1.5 6.5 1.5ZM6.5 7.92857C6.14399 7.92857 5.79598 7.81546 5.49997 7.60355C5.20397 7.39163 4.97325 7.09043 4.83702 6.73803C4.70078 6.38563 4.66513 5.99786 4.73459 5.62375C4.80404 5.24965 4.97547 4.90601 5.22721 4.63629C5.47894 4.36658 5.79967 4.1829 6.14884 4.10849C6.498 4.03407 6.85992 4.07226 7.18883 4.21823C7.51774 4.3642 7.79886 4.61139 7.99665 4.92854C8.19443 5.24569 8.3 5.61856 8.3 6C8.3 6.51149 8.11036 7.00203 7.77279 7.36371C7.43523 7.72538 6.97739 7.92857 6.5 7.92857Z" fill="#6B7280"/>
                                                </g>
                                                <defs>
                                                  <clipPath id="clip0_6048_21058">
                                                    <rect width="12" height="12" fill="white" transform="translate(0.5)"/>
                                                  </clipPath>
                                                </defs>
                                              </svg>
                                            </div>
                                          </div>
                                          <!-- Groups -->
                                          <div
                                            v-for="(group, groupId) in unitplanHotspotPillsList.groups"
                                            :key="groupId"
                                            class="relative mt-2 border-l border-gray-300"
                                          >
                                            <!-- Group Name -->
                                            <div class="-ml-1.5 -mt-2 relative flex items-center gap-2 text-sm text-gray-500 mb-2">
                                              <!-- <div class="absolute left-[-24px] top-1/2 transform -translate-y-1/2 w-px h-full bg-gray-300"></div> -->
                                              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6" fill="none">
                                                <path d="M0.266744 1.35016L5.35589 5.76842C5.52673 5.9167 5.75842 6 6 6C6.24158 6 6.47327 5.9167 6.64411 5.76842L11.7333 1.35016C11.8606 1.23954 11.9474 1.09862 11.9825 0.945213C12.0176 0.791806 11.9996 0.632798 11.9307 0.488291C11.8617 0.343785 11.745 0.220267 11.5952 0.133353C11.4454 0.0464399 11.2693 3.33974e-05 11.0891 0L0.910859 0C0.730698 3.33974e-05 0.554593 0.0464399 0.404807 0.133353C0.255021 0.220267 0.138279 0.343785 0.0693395 0.488291C0.000400064 0.632798 -0.0176418 0.791806 0.0174953 0.945213C0.0526324 1.09862 0.139371 1.23954 0.266744 1.35016Z" fill="#6B7280"/>
                                              </svg>
                                              {{ group.name }}
                                            </div>
                                            <!-- Group Images -->
                                            <div
                                              v-for="(image, idx) in group.images"
                                              :key="idx"
                                              @click="() => {if(image === selectedPill) {selectedPill = null;} else {selectedPill = image;}}"
                                              :class="[
                                                'ml-4 flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer transition-all mb-1'
                                              ]"
                                            >
                                              <div class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300">
                                                <span>{{ image.name }}</span>
                                                <svg v-if="selectedPill === image" xmlns="http://www.w3.org/2000/svg" width="10" height="9" viewBox="0 0 10 9" fill="none">
                                                  <path d="M3.5777 8.33333C3.4145 8.33411 3.25752 8.25745 3.14033 8.11974L0.18872 4.64789C0.129889 4.57827 0.082947 4.49528 0.0505741 4.40367C0.0182012 4.31205 0.00103144 4.21361 4.5112e-05 4.11395C-0.00194686 3.91268 0.0621007 3.7187 0.178098 3.57468C0.294096 3.43065 0.452541 3.34838 0.618578 3.34597C0.784615 3.34355 0.944643 3.42119 1.06346 3.5618L3.5802 6.5209L8.9361 0.215672C9.05508 0.0750621 9.21526 -0.00249541 9.38142 6.12625e-05C9.54757 0.00261793 9.70608 0.0850794 9.82208 0.229305C9.93808 0.37353 10.0021 0.567705 9.99995 0.769114C9.99784 0.970523 9.92981 1.16267 9.81083 1.30328L4.01507 8.11974C3.89789 8.25745 3.74091 8.33411 3.5777 8.33333Z" fill="#0E9F6E"/>
                                                </svg>
                                                <svg @click.stop="openImageModal(true, image.thumbnail)" xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
                                                  <g clip-path="url(#clip0_6048_21058)">
                                                    <path d="M6.5 1.5C3.2672 1.5 0.5 4.93029 0.5 6C0.5 7.11986 2.6276 10.5 6.5 10.5C10.3724 10.5 12.5 7.11986 12.5 6C12.5 4.93029 9.7328 1.5 6.5 1.5ZM6.5 7.92857C6.14399 7.92857 5.79598 7.81546 5.49997 7.60355C5.20397 7.39163 4.97325 7.09043 4.83702 6.73803C4.70078 6.38563 4.66513 5.99786 4.73459 5.62375C4.80404 5.24965 4.97547 4.90601 5.22721 4.63629C5.47894 4.36658 5.79967 4.1829 6.14884 4.10849C6.498 4.03407 6.85992 4.07226 7.18883 4.21823C7.51774 4.3642 7.79886 4.61139 7.99665 4.92854C8.19443 5.24569 8.3 5.61856 8.3 6C8.3 6.51149 8.11036 7.00203 7.77279 7.36371C7.43523 7.72538 6.97739 7.92857 6.5 7.92857Z" fill="#6B7280"/>
                                                  </g>
                                                  <defs>
                                                    <clipPath id="clip0_6048_21058">
                                                      <rect width="12" height="12" fill="white" transform="translate(0.5)"/>
                                                    </clipPath>
                                                  </defs>
                                                </svg>
                                              </div>
                                            </div>
                                            <!-- Subgroups -->
                                            <div
                                              v-for="(subGroup, subGroupId) in group.subGroups"
                                              :key="subGroupId"
                                              class="relative ml-6 mt-3 border-l border-gray-300"
                                            >
                                              <div class="relative flex flex-col">
                                                <!-- Subgroup Name -->
                                                <div class="-ml-1.5 relative flex items-center gap-2 text-sm text-gray-500 -mt-2 mb-1">
                                                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6" fill="none">
                                                    <path d="M0.266744 1.35016L5.35589 5.76842C5.52673 5.9167 5.75842 6 6 6C6.24158 6 6.47327 5.9167 6.64411 5.76842L11.7333 1.35016C11.8606 1.23954 11.9474 1.09862 11.9825 0.945213C12.0176 0.791806 11.9996 0.632798 11.9307 0.488291C11.8617 0.343785 11.745 0.220267 11.5952 0.133353C11.4454 0.0464399 11.2693 3.33974e-05 11.0891 0L0.910859 0C0.730698 3.33974e-05 0.554593 0.0464399 0.404807 0.133353C0.255021 0.220267 0.138279 0.343785 0.0693395 0.488291C0.000400064 0.632798 -0.0176418 0.791806 0.0174953 0.945213C0.0526324 1.09862 0.139371 1.23954 0.266744 1.35016Z" fill="#6B7280"/>
                                                  </svg>
                                                  {{ subGroup.name }}
                                                </div>
                                                <!-- Subgroup Images -->
                                                <div
                                                  v-for="(image, subIdx) in subGroup.images"
                                                  :key="subIdx"
                                                  @click="() => {if(image === selectedPill) {selectedPill = null;} else {selectedPill = image;}}"
                                                  :class="[
                                                    'ml-4 flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer transition-all mb-1'
                                                  ]"
                                                >
                                                  <div class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300">
                                                    <span>{{ image.name }}</span>
                                                    <svg v-if="selectedPill === image" xmlns="http://www.w3.org/2000/svg" width="10" height="9" viewBox="0 0 10 9" fill="none">
                                                      <path d="M3.5777 8.33333C3.4145 8.33411 3.25752 8.25745 3.14033 8.11974L0.18872 4.64789C0.129889 4.57827 0.082947 4.49528 0.0505741 4.40367C0.0182012 4.31205 0.00103144 4.21361 4.5112e-05 4.11395C-0.00194686 3.91268 0.0621007 3.7187 0.178098 3.57468C0.294096 3.43065 0.452541 3.34838 0.618578 3.34597C0.784615 3.34355 0.944643 3.42119 1.06346 3.5618L3.5802 6.5209L8.9361 0.215672C9.05508 0.0750621 9.21526 -0.00249541 9.38142 6.12625e-05C9.54757 0.00261793 9.70608 0.0850794 9.82208 0.229305C9.93808 0.37353 10.0021 0.567705 9.99995 0.769114C9.99784 0.970523 9.92981 1.16267 9.81083 1.30328L4.01507 8.11974C3.89789 8.25745 3.74091 8.33411 3.5777 8.33333Z" fill="#0E9F6E"/>
                                                    </svg>
                                                    <svg @click.stop="openImageModal(true, image.thumbnail)" xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
                                                      <g clip-path="url(#clip0_6048_21058)">
                                                        <path d="M6.5 1.5C3.2672 1.5 0.5 4.93029 0.5 6C0.5 7.11986 2.6276 10.5 6.5 10.5C10.3724 10.5 12.5 7.11986 12.5 6C12.5 4.93029 9.7328 1.5 6.5 1.5ZM6.5 7.92857C6.14399 7.92857 5.79598 7.81546 5.49997 7.60355C5.20397 7.39163 4.97325 7.09043 4.83702 6.73803C4.70078 6.38563 4.66513 5.99786 4.73459 5.62375C4.80404 5.24965 4.97547 4.90601 5.22721 4.63629C5.47894 4.36658 5.79967 4.1829 6.14884 4.10849C6.498 4.03407 6.85992 4.07226 7.18883 4.21823C7.51774 4.3642 7.79886 4.61139 7.99665 4.92854C8.19443 5.24569 8.3 5.61856 8.3 6C8.3 6.51149 8.11036 7.00203 7.77279 7.36371C7.43523 7.72538 6.97739 7.92857 6.5 7.92857Z" fill="#6B7280"/>
                                                      </g>
                                                      <defs>
                                                        <clipPath id="clip0_6048_21058">
                                                          <rect width="12" height="12" fill="white" transform="translate(0.5)"/>
                                                        </clipPath>
                                                      </defs>
                                                    </svg>
                                                  </div>
                                                </div>
                                                <div class="h-px w-4 bg-gray-300 mt-2"></div>
                                              </div>
                                            </div>
                                            <div class="h-px w-4 bg-gray-300 mt-2"></div>
                                          </div>
                                        </div>
                                      </div>
                                      <p v-if="errorRef.open" class="w-full h-fit text-right text-red-600 font-semibold text-[12px] italic transition-all">
                                          {{ errorRef.message }}
                                      </p>
                                      <div class="flex items-center justify-end w-full">
                                          <button @click="() => handleAddItems()" class="bg-[#1A56DB] text-white px-7 py-2 text-sm rounded-lg">
                                          Add
                                      </button>
                                    </div>
                                    <span v-if="!isBottom && !isRight" class="w-4 h-4 absolute top-0 left-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-blue-600">
                                    <!--  &#10022; -->
                                    </span>

                                    <span v-if="!isBottom && isRight" class="w-4 h-4 absolute top-0 right-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-blue-600">
                                    <!--  &#10022; -->
                                    </span>

                                    <span v-if="isBottom && !isRight" class="w-4 h-4 absolute bottom-0 left-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-blue-600">
                                    <!--  &#10022; -->
                                    </span>

                                    <span v-if="isBottom && isRight" class="w-4 h-4 absolute bottom-0 right-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-blue-600">
                                    <!--  &#10022; -->
                                    </span>
                                  </div>
                                </div>
                                <!-- Existing Hotspots -->
                                <div v-if="isImageRendered && projectStore.unitplans?.[unitplanId]?.hotspots !== undefined " :style="`width:${imageDimensions.width}px; height:${imageDimensions.height}px; left: 50%; top: 50%; transform: translate(-50%, -50%);`"  class="pointer-events-none absolute inset-0">
                                    <div :ref="el => { if(el) existingHotspotsBoxRef[index] = el }" v-for="item,index in projectStore.unitplans[unitplanId].hotspots" :key="index" :draggable="true" :style="`left: ${item.x}%; top:${item.y}%; ${ isBottom && route.query.layerId == String(index) ? `margin-top:-${currentDetailsBoxHeight}px;` : 'margin-top:0;'} ${ isRight && route.query.layerId == String(index) ? `margin-left:-302px;` : 'margin-left:0px;'}`" :data-hotspot-id="index" class="text-base rounded-2xl absolute  py-1 px-3 w-fit h-fit boxShadow  pointer-events-auto" :class="[`${route.query.layerId ? route.query.layerId  === String(index) ? 'brightness-100 opacity-100 z-50' : 'brightness-50 opacity-80 z-10' : 'brightness-100 opacity-100 z-10'} ${newElement ? 'brightness-50 opacity-80 z-10' : 'brightness-100 opacity-100 z-20' } `]"
                                    v-on="dragEvents" @click="handleUpdateHotspotPosition">
                                        <!-- View -->
                                        <div v-if="isImageRendered" class="relative" :class="[`${route.query.layerId ? route.query.layerId === String(index)  ? 'invisible w-0 h-0' : 'visible w-fit h-fit' : 'visible w-fit h-fit'}`,]">
                                            <!-- Main label with padding for icons -->
                                            <div class="border-2 border-blue-600 rounded-md py-3 pb-4 flex justify-center items-center">
                                                <span class="bg-gray-600 text-white px-3 py-1 rounded-full" :class="['select-none text-center', item.scale === unitplanScaleTypes.SMALL && 'text-sm' , item.scale === unitplanScaleTypes.MEDIUM && 'text-lg' , item.scale === unitplanScaleTypes.LARGE && 'text-2xl' ]">{{ item.text }}</span>
                                            </div>
                                            <button
                                                @click="router.push({ path: route.path, query: { deleteId: index } }); openDeleteModal = true"
                                                class="absolute -top-3 -right-3 bg-red-200 w-7 h-7 rounded-full flex items-center justify-center z-10 transition-transform hover:scale-110"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                    <g clip-path="url(#clip0_5534_38017)">
                                                    <path d="M7.09045 5.99961L11.1665 1.92359C11.24 1.85256 11.2987 1.76758 11.339 1.67363C11.3794 1.57968 11.4006 1.47863 11.4015 1.37638C11.4024 1.27413 11.3829 1.17272 11.3442 1.07808C11.3055 0.983445 11.2483 0.897464 11.176 0.825159C11.1037 0.752855 11.0177 0.695674 10.9231 0.656954C10.8284 0.618234 10.727 0.59875 10.6248 0.599638C10.5225 0.600527 10.4215 0.621771 10.3275 0.662129C10.2336 0.702488 10.1486 0.761155 10.0776 0.834705L6.00156 4.91072L1.92555 0.834705C1.78031 0.694429 1.58578 0.616809 1.38387 0.618564C1.18196 0.620319 0.988817 0.701307 0.846038 0.844085C0.70326 0.986864 0.622272 1.18001 0.620517 1.38192C0.618763 1.58383 0.696382 1.77835 0.836658 1.92359L4.91267 5.99961L0.836658 10.0756C0.763108 10.1467 0.704442 10.2316 0.664083 10.3256C0.623724 10.4195 0.60248 10.5206 0.601592 10.6228C0.600703 10.7251 0.620187 10.8265 0.658907 10.9211C0.697627 11.0158 0.754808 11.1018 0.827113 11.1741C0.899417 11.2464 0.985398 11.3035 1.08004 11.3423C1.17468 11.381 1.27608 11.4005 1.37833 11.3996C1.48058 11.3987 1.58163 11.3774 1.67558 11.3371C1.76954 11.2967 1.85451 11.2381 1.92555 11.1645L6.00156 7.0885L10.0776 11.1645C10.2228 11.3048 10.4173 11.3824 10.6193 11.3807C10.8212 11.3789 11.0143 11.2979 11.1571 11.1551C11.2999 11.0124 11.3809 10.8192 11.3826 10.6173C11.3844 10.4154 11.3067 10.2209 11.1665 10.0756L7.09045 5.99961Z" fill="#E02424"/>
                                                    </g>
                                                    <defs>
                                                    <clipPath id="clip0_5534_38017">
                                                        <rect width="12" height="12" fill="white"/>
                                                    </clipPath>
                                                    </defs>
                                                </svg>
                                            </button>
                                            <!-- Edit Button -->
                                            <button
                                                @click="router.push({ path: route.path, query: { layerId: index } })"
                                                class="absolute -bottom-3 -right-3 bg-blue-600 w-7 h-7 rounded-full flex items-center justify-center z-10 transition-transform hover:scale-110"
                                            >
                                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                    <circle cx="12" cy="12" r="12" fill="#1C64F2"/>
                                                    <path d="M14.4106 7.48679L15.4619 6.43547C16.0425 5.85484 16.9839 5.85484 17.5645 6.43547C18.1451 7.0161 18.1451 7.95748 17.5645 8.53811L16.5132 9.58943M14.4106 7.48679L8.23517 13.6622C7.4512 14.4462 7.05919 14.8381 6.79228 15.3158C6.52535 15.7935 6.2568 16.9214 6 18C7.07857 17.7432 8.20649 17.4746 8.68417 17.2077C9.16184 16.9408 9.55383 16.5488 10.3378 15.7648L16.5132 9.58943M14.4106 7.48679L16.5132 9.58943L14.4106 7.48679Z" fill="white"/>
                                                    <path d="M11.25 18H15.75" stroke="white" stroke-width="1.125" stroke-linecap="round"/>
                                                </svg>
                                            </button>
                                        </div>
                                        <!-- Edit -->
                                        <div v-if="route.query.layerId !== null && route.query.layerId === String(index)" class="pointer-events-auto">
                                            <div class="flex flex-col justify-start items-center gap-1 w-[480px] px-3 py-2 h-fit font-medium bg-white rounded">
                                                <div  class="flex flex-col justify-start items-start w-full overflow-hidden">
                                                  <div class="w-full flex justify-between items-center">
                                                    <p class="text-base font-medium text-black">Add Name</p>
                                                    <svg @click="() =>{
                                                        router.push({
                                                                        path: route.path,
                                                                        query: {}, });
                                                            resetValues();
                                                        }" class="cursor-pointer" xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                        <g clip-path="url(#clip0_5532_34612)">
                                                            <path d="M7.09045 5.99961L11.1665 1.92359C11.24 1.85256 11.2987 1.76758 11.339 1.67363C11.3794 1.57968 11.4006 1.47863 11.4015 1.37638C11.4024 1.27413 11.3829 1.17272 11.3442 1.07808C11.3055 0.983445 11.2483 0.897464 11.176 0.825159C11.1037 0.752855 11.0177 0.695674 10.9231 0.656954C10.8284 0.618234 10.727 0.59875 10.6248 0.599638C10.5225 0.600527 10.4215 0.621771 10.3275 0.662129C10.2336 0.702488 10.1486 0.761155 10.0776 0.834705L6.00156 4.91072L1.92555 0.834705C1.78031 0.694429 1.58578 0.616809 1.38387 0.618564C1.18196 0.620319 0.988817 0.701307 0.846038 0.844085C0.70326 0.986864 0.622272 1.18001 0.620517 1.38192C0.618763 1.58383 0.696382 1.77835 0.836658 1.92359L4.91267 5.99961L0.836658 10.0756C0.763108 10.1467 0.704442 10.2316 0.664083 10.3256C0.623724 10.4195 0.60248 10.5206 0.601592 10.6228C0.600703 10.7251 0.620187 10.8265 0.658907 10.9211C0.697627 11.0158 0.754808 11.1018 0.827113 11.1741C0.899417 11.2464 0.985398 11.3035 1.08004 11.3423C1.17468 11.381 1.27608 11.4005 1.37833 11.3996C1.48058 11.3987 1.58163 11.3774 1.67558 11.3371C1.76954 11.2967 1.85451 11.2381 1.92555 11.1645L6.00156 7.0885L10.0776 11.1645C10.2228 11.3048 10.4173 11.3824 10.6193 11.3807C10.8212 11.3789 11.0143 11.2979 11.1571 11.1551C11.2999 11.0124 11.3809 10.8192 11.3826 10.6173C11.3844 10.4154 11.3067 10.2209 11.1665 10.0756L7.09045 5.99961Z" fill="#9CA3AF"/>
                                                        </g>
                                                        <defs>
                                                        <clipPath id="clip0_5532_34612">
                                                            <rect width="12" height="12" fill="white"/>
                                                        </clipPath>
                                                        </defs>
                                                    </svg>
                                                  </div>
                                                  <div class="w-full flex flex-col justify-start items-start h-[300px] overflow-y-auto pe-2 gap-2 mt-1">
                                                    <div class="flex flex-col justify-start items-start w-full gap-2">
                                                        <span class="text-sm text-black">How you want to add name ?</span>
                                                        <div class="flex text-sm gap-3">
                                                            <label class="cursor-pointer">
                                                                <input
                                                                    type="radio"
                                                                    name="descType"
                                                                    class="hidden"
                                                                    :value="unitplanDescTypes.PILL"
                                                                    v-model="toggleDescTypeRef.anchorElem"
                                                                    @change="handleDescToggle"
                                                                />
                                                                <div class="rounded-lg py-2 px-3 text-center" :class="toggleDescTypeRef.anchorElem === unitplanDescTypes.PILL ? 'text-white bg-blue-600' : 'bg-white text-black border border-gray-200'">
                                                                    Fetch names from VR tour
                                                                </div>
                                                            </label>
                                                            <label class="cursor-pointer">
                                                                <input
                                                                    type="radio"
                                                                    name="descType"
                                                                    class="hidden"
                                                                    :value="unitplanDescTypes.TEXT"
                                                                    v-model="toggleDescTypeRef.anchorElem"
                                                                    @change="handleDescToggle"
                                                                />
                                                                <div class="rounded-lg py-2 px-3 text-center" :class="toggleDescTypeRef.anchorElem === unitplanDescTypes.TEXT ? 'text-white bg-blue-600' : 'bg-white text-black border border-gray-200'">
                                                                    Add Manually
                                                                </div>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="flex flex-col justify-start items-start w-full pt-2">
                                                        <div class="flex flex-col gap-3">
                                                            <div>
                                                                <p class="text-sm text-black pb-1">Select Size</p>
                                                                <p class="text-xs text-gray-500">Available Names from Upload VR Tour of this Unit </p>
                                                            </div>
                                                            <div class="flex gap-2">
                                                                <div v-for="size in Object.values(unitplanScaleTypes)" :key="size">
                                                                    <button @click="selectedScaleItem = size" :class="[
                                                                        'rounded-lg px-3 py-1 capitalize',
                                                                        selectedScaleItem === size ? 'bg-blue-600 text-white' : 'bg-white border  text-gray-700']"
                                                                    class="rounded-lg px-4 py-2 text-sm capitalize">{{ size }}</button>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div v-if="toggleDescTypeRef.anchorElem !== unitplanDescTypes.PILL" class="flex flex-col w-full gap-2 pt-2">
                                                        <p class="text-sm text-black">Enter Text</p>
                                                        <textarea v-model="fieldName" id="create" rows="3" placeholder="text.." class="border select-primary outline-none rounded text-black bg-transparent p-2 h-fit block"></textarea>
                                                    </div>
                                                    <div class="flex flex-col w-full pt-2">
                                                        <p class="mb-2 pb-1 text-black">Names Fetched From VR Tours</p>
                                                        <!-- Container -->
                                                        <div class="flex flex-col gap-y-2 overflow-y-auto max-h-[200px] px-3 py-2 bg-gray-50 rounded-2xl">
                                                          <!-- Labels -->
                                                          <div
                                                            v-for="(item, index) in unitplanHotspotPillsList.labels"
                                                            :key="index"
                                                            @click="() => {if(item === selectedPill) {selectedPill = null;} else {selectedPill = item;}}"
                                                            :class="[
                                                              'flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer transition-all '
                                                            ]"
                                                          >
                                                            <div class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300">
                                                              <span class="">{{ item.name }}</span>
                                                              <svg v-if="JSON.stringify(selectedPill) === JSON.stringify(item)" xmlns="http://www.w3.org/2000/svg" width="10" height="9" viewBox="0 0 10 9" fill="none">
                                                                <path d="M3.5777 8.33333C3.4145 8.33411 3.25752 8.25745 3.14033 8.11974L0.18872 4.64789C0.129889 4.57827 0.082947 4.49528 0.0505741 4.40367C0.0182012 4.31205 0.00103144 4.21361 4.5112e-05 4.11395C-0.00194686 3.91268 0.0621007 3.7187 0.178098 3.57468C0.294096 3.43065 0.452541 3.34838 0.618578 3.34597C0.784615 3.34355 0.944643 3.42119 1.06346 3.5618L3.5802 6.5209L8.9361 0.215672C9.05508 0.0750621 9.21526 -0.00249541 9.38142 6.12625e-05C9.54757 0.00261793 9.70608 0.0850794 9.82208 0.229305C9.93808 0.37353 10.0021 0.567705 9.99995 0.769114C9.99784 0.970523 9.92981 1.16267 9.81083 1.30328L4.01507 8.11974C3.89789 8.25745 3.74091 8.33411 3.5777 8.33333Z" fill="#0E9F6E"/>
                                                              </svg>
                                                            </div>
                                                          </div>
                                                          <!-- Top-Level Images -->
                                                          <div
                                                            v-for="(item, index) in unitplanHotspotPillsList.images"
                                                            :key="index"
                                                            @click="() => {if(item === selectedPill) {selectedPill = null;} else {selectedPill = item;}}"
                                                            :class="[
                                                              'flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer transition-all '
                                                            ]"
                                                          >
                                                            <div class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300">
                                                              <span class="">{{ item.name }}</span>
                                                              <svg v-if="selectedPill === item" xmlns="http://www.w3.org/2000/svg" width="10" height="9" viewBox="0 0 10 9" fill="none">
                                                                <path d="M3.5777 8.33333C3.4145 8.33411 3.25752 8.25745 3.14033 8.11974L0.18872 4.64789C0.129889 4.57827 0.082947 4.49528 0.0505741 4.40367C0.0182012 4.31205 0.00103144 4.21361 4.5112e-05 4.11395C-0.00194686 3.91268 0.0621007 3.7187 0.178098 3.57468C0.294096 3.43065 0.452541 3.34838 0.618578 3.34597C0.784615 3.34355 0.944643 3.42119 1.06346 3.5618L3.5802 6.5209L8.9361 0.215672C9.05508 0.0750621 9.21526 -0.00249541 9.38142 6.12625e-05C9.54757 0.00261793 9.70608 0.0850794 9.82208 0.229305C9.93808 0.37353 10.0021 0.567705 9.99995 0.769114C9.99784 0.970523 9.92981 1.16267 9.81083 1.30328L4.01507 8.11974C3.89789 8.25745 3.74091 8.33411 3.5777 8.33333Z" fill="#0E9F6E"/>
                                                              </svg>
                                                              <svg @click.stop="openImageModal(true, item.thumbnail)" xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
                                                                <g clip-path="url(#clip0_6048_21058)">
                                                                  <path d="M6.5 1.5C3.2672 1.5 0.5 4.93029 0.5 6C0.5 7.11986 2.6276 10.5 6.5 10.5C10.3724 10.5 12.5 7.11986 12.5 6C12.5 4.93029 9.7328 1.5 6.5 1.5ZM6.5 7.92857C6.14399 7.92857 5.79598 7.81546 5.49997 7.60355C5.20397 7.39163 4.97325 7.09043 4.83702 6.73803C4.70078 6.38563 4.66513 5.99786 4.73459 5.62375C4.80404 5.24965 4.97547 4.90601 5.22721 4.63629C5.47894 4.36658 5.79967 4.1829 6.14884 4.10849C6.498 4.03407 6.85992 4.07226 7.18883 4.21823C7.51774 4.3642 7.79886 4.61139 7.99665 4.92854C8.19443 5.24569 8.3 5.61856 8.3 6C8.3 6.51149 8.11036 7.00203 7.77279 7.36371C7.43523 7.72538 6.97739 7.92857 6.5 7.92857Z" fill="#6B7280"/>
                                                                </g>
                                                                <defs>
                                                                  <clipPath id="clip0_6048_21058">
                                                                    <rect width="12" height="12" fill="white" transform="translate(0.5)"/>
                                                                  </clipPath>
                                                                </defs>
                                                              </svg>
                                                            </div>
                                                          </div>
                                                          <!-- Groups -->
                                                          <div
                                                            v-for="(group, groupId) in unitplanHotspotPillsList.groups"
                                                            :key="groupId"
                                                            class="relative mt-2 border-l border-gray-300"
                                                          >
                                                            <!-- Group Name -->
                                                            <div class="-ml-1.5 -mt-2 relative  flex items-center gap-2 text-sm text-gray-500 mb-2">
                                                              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6" fill="none">
                                                                <path d="M0.266744 1.35016L5.35589 5.76842C5.52673 5.9167 5.75842 6 6 6C6.24158 6 6.47327 5.9167 6.64411 5.76842L11.7333 1.35016C11.8606 1.23954 11.9474 1.09862 11.9825 0.945213C12.0176 0.791806 11.9996 0.632798 11.9307 0.488291C11.8617 0.343785 11.745 0.220267 11.5952 0.133353C11.4454 0.0464399 11.2693 3.33974e-05 11.0891 0L0.910859 0C0.730698 3.33974e-05 0.554593 0.0464399 0.404807 0.133353C0.255021 0.220267 0.138279 0.343785 0.0693395 0.488291C0.000400064 0.632798 -0.0176418 0.791806 0.0174953 0.945213C0.0526324 1.09862 0.139371 1.23954 0.266744 1.35016Z" fill="#6B7280"/>
                                                              </svg>
                                                              {{ group.name }}
                                                            </div>
                                                            <!-- Group Images -->
                                                            <div
                                                              v-for="(image, idx) in group.images"
                                                              :key="idx"
                                                              @click="() => {if(image === selectedPill) {selectedPill = null;} else {selectedPill = image;}}"
                                                              :class="[
                                                                'ml-4 flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer transition-all mb-1'
                                                              ]"
                                                            >
                                                              <div class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300">
                                                                <span>{{ image.name }}</span>
                                                                <svg v-if="selectedPill === image" xmlns="http://www.w3.org/2000/svg" width="10" height="9" viewBox="0 0 10 9" fill="none">
                                                                  <path d="M3.5777 8.33333C3.4145 8.33411 3.25752 8.25745 3.14033 8.11974L0.18872 4.64789C0.129889 4.57827 0.082947 4.49528 0.0505741 4.40367C0.0182012 4.31205 0.00103144 4.21361 4.5112e-05 4.11395C-0.00194686 3.91268 0.0621007 3.7187 0.178098 3.57468C0.294096 3.43065 0.452541 3.34838 0.618578 3.34597C0.784615 3.34355 0.944643 3.42119 1.06346 3.5618L3.5802 6.5209L8.9361 0.215672C9.05508 0.0750621 9.21526 -0.00249541 9.38142 6.12625e-05C9.54757 0.00261793 9.70608 0.0850794 9.82208 0.229305C9.93808 0.37353 10.0021 0.567705 9.99995 0.769114C9.99784 0.970523 9.92981 1.16267 9.81083 1.30328L4.01507 8.11974C3.89789 8.25745 3.74091 8.33411 3.5777 8.33333Z" fill="#0E9F6E"/>
                                                                </svg>
                                                                <svg @click.stop="openImageModal(true, image.thumbnail)" xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
                                                                  <g clip-path="url(#clip0_6048_21058)">
                                                                    <path d="M6.5 1.5C3.2672 1.5 0.5 4.93029 0.5 6C0.5 7.11986 2.6276 10.5 6.5 10.5C10.3724 10.5 12.5 7.11986 12.5 6C12.5 4.93029 9.7328 1.5 6.5 1.5ZM6.5 7.92857C6.14399 7.92857 5.79598 7.81546 5.49997 7.60355C5.20397 7.39163 4.97325 7.09043 4.83702 6.73803C4.70078 6.38563 4.66513 5.99786 4.73459 5.62375C4.80404 5.24965 4.97547 4.90601 5.22721 4.63629C5.47894 4.36658 5.79967 4.1829 6.14884 4.10849C6.498 4.03407 6.85992 4.07226 7.18883 4.21823C7.51774 4.3642 7.79886 4.61139 7.99665 4.92854C8.19443 5.24569 8.3 5.61856 8.3 6C8.3 6.51149 8.11036 7.00203 7.77279 7.36371C7.43523 7.72538 6.97739 7.92857 6.5 7.92857Z" fill="#6B7280"/>
                                                                  </g>
                                                                  <defs>
                                                                    <clipPath id="clip0_6048_21058">
                                                                      <rect width="12" height="12" fill="white" transform="translate(0.5)"/>
                                                                    </clipPath>
                                                                  </defs>
                                                                </svg>
                                                              </div>
                                                            </div>
                                                            <!-- Subgroups -->
                                                            <div
                                                              v-for="(subGroup, subGroupId) in group.subGroups"
                                                              :key="subGroupId"
                                                              class="relative ml-6 mt-3 border-l border-gray-300"
                                                            >
                                                              <div class="relative flex flex-col">
                                                                <!-- Subgroup Name -->
                                                                <div class="-ml-1.5 relative flex items-center gap-2 text-sm text-gray-500 -mt-2 mb-1">
                                                                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="6" viewBox="0 0 12 6" fill="none">
                                                                    <path d="M0.266744 1.35016L5.35589 5.76842C5.52673 5.9167 5.75842 6 6 6C6.24158 6 6.47327 5.9167 6.64411 5.76842L11.7333 1.35016C11.8606 1.23954 11.9474 1.09862 11.9825 0.945213C12.0176 0.791806 11.9996 0.632798 11.9307 0.488291C11.8617 0.343785 11.745 0.220267 11.5952 0.133353C11.4454 0.0464399 11.2693 3.33974e-05 11.0891 0L0.910859 0C0.730698 3.33974e-05 0.554593 0.0464399 0.404807 0.133353C0.255021 0.220267 0.138279 0.343785 0.0693395 0.488291C0.000400064 0.632798 -0.0176418 0.791806 0.0174953 0.945213C0.0526324 1.09862 0.139371 1.23954 0.266744 1.35016Z" fill="#6B7280"/>
                                                                  </svg>
                                                                  {{ subGroup.name }}
                                                                </div>
                                                                <!-- Subgroup Images -->
                                                                <div
                                                                  v-for="(image, subIdx) in subGroup.images"
                                                                  :key="subIdx"
                                                                  @click="() => {if(image === selectedPill) {selectedPill = null;} else {selectedPill = image;}}"
                                                                  :class="[
                                                                    'ml-4 flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer transition-all mb-1'
                                                                  ]"
                                                                >
                                                                  <div class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-200 hover:bg-gray-300">
                                                                    <span>{{ image.name }}</span>
                                                                    <svg v-if="selectedPill === image" xmlns="http://www.w3.org/2000/svg" width="10" height="9" viewBox="0 0 10 9" fill="none">
                                                                      <path d="M3.5777 8.33333C3.4145 8.33411 3.25752 8.25745 3.14033 8.11974L0.18872 4.64789C0.129889 4.57827 0.082947 4.49528 0.0505741 4.40367C0.0182012 4.31205 0.00103144 4.21361 4.5112e-05 4.11395C-0.00194686 3.91268 0.0621007 3.7187 0.178098 3.57468C0.294096 3.43065 0.452541 3.34838 0.618578 3.34597C0.784615 3.34355 0.944643 3.42119 1.06346 3.5618L3.5802 6.5209L8.9361 0.215672C9.05508 0.0750621 9.21526 -0.00249541 9.38142 6.12625e-05C9.54757 0.00261793 9.70608 0.0850794 9.82208 0.229305C9.93808 0.37353 10.0021 0.567705 9.99995 0.769114C9.99784 0.970523 9.92981 1.16267 9.81083 1.30328L4.01507 8.11974C3.89789 8.25745 3.74091 8.33411 3.5777 8.33333Z" fill="#0E9F6E"/>
                                                                    </svg>
                                                                    <svg @click.stop="openImageModal(true, image.thumbnail)" xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
                                                                      <g clip-path="url(#clip0_6048_21058)">
                                                                        <path d="M6.5 1.5C3.2672 1.5 0.5 4.93029 0.5 6C0.5 7.11986 2.6276 10.5 6.5 10.5C10.3724 10.5 12.5 7.11986 12.5 6C12.5 4.93029 9.7328 1.5 6.5 1.5ZM6.5 7.92857C6.14399 7.92857 5.79598 7.81546 5.49997 7.60355C5.20397 7.39163 4.97325 7.09043 4.83702 6.73803C4.70078 6.38563 4.66513 5.99786 4.73459 5.62375C4.80404 5.24965 4.97547 4.90601 5.22721 4.63629C5.47894 4.36658 5.79967 4.1829 6.14884 4.10849C6.498 4.03407 6.85992 4.07226 7.18883 4.21823C7.51774 4.3642 7.79886 4.61139 7.99665 4.92854C8.19443 5.24569 8.3 5.61856 8.3 6C8.3 6.51149 8.11036 7.00203 7.77279 7.36371C7.43523 7.72538 6.97739 7.92857 6.5 7.92857Z" fill="#6B7280"/>
                                                                      </g>
                                                                      <defs>
                                                                        <clipPath id="clip0_6048_21058">
                                                                          <rect width="12" height="12" fill="white" transform="translate(0.5)"/>
                                                                        </clipPath>
                                                                      </defs>
                                                                    </svg>
                                                                  </div>
                                                                </div>
                                                                <div class="h-px w-4 bg-gray-300 mt-2"></div>
                                                              </div>
                                                            </div>
                                                            <div class="h-px w-4 bg-gray-300 mt-2"></div>
                                                          </div>
                                                        </div>
                                                      </div>
                                                    <p v-if="errorRef.open" class="w-full h-fit text-right text-red-600 font-semibold text-[12px] italic transition-all">
                                                        {{ errorRef.message }}
                                                    </p>

                                                    <div class="flex justify-end items-end gap-2 mt-2 w-full">
                                                        <button @click="() => handleEditItem(item, selectedPill)" class="bg-[#1A56DB] text-white px-7 py-2 text-sm rounded-lg">
                                                            Add
                                                        </button>
                                                    </div>
                                                </div>

                                              </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else class="w-full h-[90vh] py-3 bg-gray-50 rounded">
                                <div class="h-full w-full flex flex-col justify-center items-center">
                                    <img class="w-fit" :src="noDataFound"
                                    alt="" />
                                    <h1 class="text-center text-base font-medium italic text-black">No Image found! </h1>
                                </div>
                            </div>
                            <div v-if="openDeleteModal"  class="!bg-[rgba(0,0,0,0.9)] z-10 h-full w-full fixed top-0 left-0 flex justify-center items-center" >
                                <DeleteModalContent
                                    :trash="false"
                                    :loader="deleteLoader"
                                    @closeModal="(e) =>{openDeleteModal = false;  router.push({ path: route.path,  query: {}, });}"
                                    @handleDelete="handleDeleteHotspot"
                                    :dataName="'Hotspot'"
                                />
                            </div>
                            <ImgPreviewModal v-if="isModalOpen.url" :isOpen="isModalOpen.status" :imageUrl="isModalOpen.url"
    @close="isModalOpen.status = false" />
                        </SafeAreaHighlight>
                    </div>
                </div>
            </div>
        </div>
        <div class="h-[10%] w-full flex flex-col justify-center flex-none">
            <div class="w-full flex-1 min-h-20 bg-white-500 flex justify-end items-center px-3">
                <div class="flex items-center gap-3">
                    <button class="flex justify-center items-center w-9 h-9 rounded-md bg-gray-100 fill-gray-900 cursor-pointer">
                        <svg class="h-4 w-4 fill-gray-900" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.38955 10.0616L7.838 8.51008C7.62654 8.29862 7.5208 8.19288 7.40669 8.13633C7.18966 8.02877 6.93487 8.02877 6.71777 8.13633C6.60373 8.19288 6.49797 8.29862 6.28648 8.51008C6.07497 8.72162 5.96922 8.82736 5.9127 8.9414C5.80514 9.1585 5.80514 9.4133 5.9127 9.63032C5.96922 9.74443 6.07497 9.85017 6.28648 10.0616L7.838 11.6132M9.38955 10.0616L14.0443 14.7164C14.2557 14.9278 14.3615 15.0336 14.418 15.1477C14.5256 15.3647 14.5256 15.6195 14.418 15.8366C14.3615 15.9506 14.2557 16.0564 14.0443 16.2679C13.8327 16.4794 13.727 16.5851 13.613 16.6417C13.3959 16.7492 13.1411 16.7492 12.924 16.6417C12.8099 16.5851 12.7042 16.4794 12.4927 16.2679L7.838 11.6132M9.38955 10.0616L7.838 11.6132L9.38955 10.0616Z"/>
                            <path d="M11.6115 2.27759L11.8245 2.85291C12.1036 3.60732 12.2432 3.98452 12.5184 4.25969C12.7935 4.53486 13.1707 4.67443 13.9251 4.95359L14.5004 5.16648L13.9251 5.37937C13.1707 5.65852 12.7935 5.7981 12.5184 6.07326C12.2432 6.34843 12.1036 6.72563 11.8245 7.48004L11.6115 8.05537L11.3986 7.48004C11.1195 6.72564 10.9799 6.34843 10.7047 6.07326C10.4296 5.7981 10.0524 5.65852 9.29798 5.37937L8.72266 5.16648L9.29798 4.95359C10.0524 4.67443 10.4296 4.53486 10.7047 4.25969C10.9799 3.98452 11.1195 3.60732 11.3986 2.85291L11.6115 2.27759Z" stroke="#111928" stroke-linejoin="round"/>
                            <path d="M3.66667 3.72217L3.82634 4.15366C4.0357 4.71946 4.14039 5.00237 4.34676 5.20875C4.55313 5.41511 4.83604 5.5198 5.40184 5.72917L5.83333 5.88883L5.40184 6.0485C4.83604 6.25787 4.55313 6.36256 4.34675 6.56893C4.14039 6.7753 4.0357 7.05821 3.82634 7.62401L3.66667 8.0555L3.507 7.62401C3.29763 7.05821 3.19295 6.7753 2.98657 6.56893C2.7802 6.36256 2.4973 6.25787 1.93149 6.0485L1.5 5.88883L1.93149 5.72917C2.4973 5.5198 2.7802 5.41511 2.98657 5.20874C3.19295 5.00237 3.29763 4.71946 3.507 4.15366L3.66667 3.72217Z" stroke="#111928" stroke-linejoin="round"/>
                        </svg>
                    </button>
                    <button class="flex justify-center items-center w-9 h-9 rounded-md bg-gray-100 fill-gray-900 cursor-pointer">
                        <svg class="h-4 w-4 fill-gray-900" width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_723_16934)">
                        <path d="M8.40668 4.53125C8.61865 4.70516 8.80979 4.89893 9.00311 5.09288C9.041 5.13078 9.0789 5.16868 9.11681 5.20657C9.2192 5.30895 9.3215 5.41143 9.42377 5.51393C9.53086 5.62121 9.63804 5.72841 9.74521 5.83561C9.94791 6.03841 10.1505 6.24129 10.3531 6.44419C10.5839 6.67529 10.8147 6.90629 11.0455 7.13728C11.5202 7.61226 11.9947 8.08734 12.4692 8.5625C12.2328 9.06911 11.9846 9.56972 11.7353 10.0701C11.7247 10.0913 11.7141 10.1126 11.7032 10.1344C11.6032 10.3354 11.5031 10.5362 11.4028 10.7371C11.2757 10.9919 11.1487 11.2469 11.022 11.502C10.9323 11.6828 10.8423 11.8634 10.7521 12.0439C10.6985 12.1512 10.645 12.2586 10.5917 12.3662C10.5418 12.4669 10.4916 12.5675 10.4412 12.668C10.4141 12.7222 10.3872 12.7766 10.3604 12.831C10.1541 13.2404 9.86989 13.3643 9.4582 13.526C9.43053 13.537 9.40287 13.548 9.37436 13.5593C9.28269 13.5958 9.19093 13.632 9.09918 13.6682C9.03314 13.6944 8.9671 13.7206 8.90107 13.7469C8.75928 13.8031 8.61747 13.8594 8.47562 13.9155C8.11305 14.059 7.75067 14.2031 7.38828 14.3471C7.32877 14.3707 7.26925 14.3944 7.20973 14.418C6.4209 14.7314 5.63318 15.0475 4.84579 15.3645C4.33607 15.5696 3.82621 15.7745 3.31637 15.9793C3.15977 16.0423 3.00318 16.1052 2.8466 16.1682C2.53607 16.2931 2.22548 16.4179 1.91467 16.5421C1.79 16.5919 1.66536 16.6419 1.54083 16.6921C1.42821 16.7374 1.31548 16.7826 1.20266 16.8275C1.1621 16.8436 1.12157 16.8599 1.08109 16.8763C1.02664 16.8984 0.972036 16.9201 0.91742 16.9417C0.872071 16.9599 0.872071 16.9599 0.825806 16.9784C0.750427 17 0.750427 17 0.656677 16.9687C2.53355 15.0919 4.41043 13.215 6.34418 11.2812C6.4473 11.3122 6.55043 11.3431 6.65668 11.375C7.05285 11.4529 7.41031 11.3679 7.75043 11.1562C7.96352 11.0037 8.10382 10.8282 8.21918 10.5937C8.22981 10.5727 8.24045 10.5517 8.2514 10.53C8.37199 10.2408 8.35683 9.8293 8.25201 9.53772C8.19179 9.41123 8.11517 9.29957 8.03168 9.1875C8.01299 9.15978 7.99429 9.13207 7.97504 9.10352C7.73545 8.85024 7.38683 8.68843 7.03992 8.67676C6.66545 8.67141 6.35465 8.75431 6.06281 9.00171C5.74127 9.31507 5.61574 9.64666 5.59418 10.0937C5.61015 10.2897 5.66514 10.4671 5.71918 10.6562C3.8423 12.5331 1.96543 14.41 0.0316774 16.3437C-0.0139335 16.2069 0.0469012 16.1104 0.0989076 15.9826C0.10988 15.955 0.120853 15.9273 0.132158 15.8989C0.169183 15.8058 0.206632 15.713 0.24408 15.6201C0.270581 15.5538 0.297057 15.4874 0.323509 15.4211C0.395659 15.2403 0.468178 15.0597 0.540764 14.8792C0.617303 14.6886 0.693506 14.4979 0.769753 14.3072C0.937206 13.8885 1.10517 13.47 1.27324 13.0516C1.35678 12.8436 1.44025 12.6357 1.52373 12.4277C1.75064 11.8624 1.97765 11.2971 2.20511 10.732C2.45574 10.1094 2.70612 9.48658 2.95467 8.86306C2.99073 8.77261 3.0268 8.68216 3.06288 8.59172C3.16267 8.3415 3.26242 8.09126 3.36184 7.84089C3.4011 7.74209 3.44047 7.64334 3.47987 7.54459C3.49786 7.49942 3.51581 7.45424 3.5337 7.40903C3.55795 7.34778 3.58236 7.28659 3.60681 7.22542C3.62032 7.19144 3.63382 7.15746 3.64773 7.12245C3.71472 6.97047 3.76997 6.86839 3.91683 6.78519C3.95868 6.76146 3.95868 6.76146 4.00137 6.73725C4.17453 6.64442 4.3496 6.55572 4.52553 6.46828C4.56807 6.44702 4.6106 6.42574 4.65313 6.40446C4.76759 6.34722 4.88213 6.29014 4.99668 6.23308C5.11676 6.17324 5.23676 6.11325 5.35678 6.05328C5.5836 5.93997 5.81049 5.82678 6.0374 5.71364C6.29591 5.58473 6.55436 5.45568 6.8128 5.32662C7.34401 5.06134 7.87531 4.79623 8.40668 4.53125Z"/>
                        <path d="M11.5417 0.988281C11.5846 0.986952 11.5846 0.986952 11.6284 0.985596C11.8167 1.01603 11.9502 1.16108 12.0802 1.29223C12.0968 1.30871 12.1134 1.32519 12.1304 1.34217C12.1856 1.39706 12.2405 1.45216 12.2954 1.50726C12.335 1.54681 12.3747 1.58634 12.4143 1.62586C12.5217 1.73295 12.6289 1.84025 12.736 1.94758C12.8482 2.05994 12.9605 2.17214 13.0729 2.28436C13.2614 2.47276 13.4498 2.66129 13.6381 2.8499C13.8558 3.068 14.0738 3.28588 14.2919 3.50364C14.4792 3.69066 14.6664 3.87779 14.8535 4.06503C14.9652 4.17685 15.077 4.28862 15.1888 4.40028C15.2939 4.5052 15.3989 4.61028 15.5037 4.71547C15.5422 4.75407 15.5808 4.79262 15.6194 4.83111C15.6721 4.8836 15.7246 4.93628 15.777 4.98902C15.7924 5.00425 15.8077 5.01949 15.8236 5.03519C15.9554 5.16851 16.0079 5.26169 16.0105 5.44922C16.0114 5.47782 16.0123 5.50642 16.0132 5.53589C15.9798 5.7425 15.8062 5.88907 15.6636 6.03073C15.6324 6.06217 15.6324 6.06217 15.6005 6.09424C15.5319 6.16308 15.4631 6.23169 15.3943 6.30029C15.3464 6.34821 15.2985 6.39614 15.2507 6.44407C15.1506 6.5443 15.0503 6.64442 14.95 6.74446C14.8213 6.87273 14.693 7.00128 14.5647 7.12991C14.466 7.22884 14.3672 7.32762 14.2683 7.42635C14.2209 7.4737 14.1736 7.5211 14.1263 7.56854C14.0602 7.63475 13.994 7.70074 13.9277 7.76669C13.9082 7.78634 13.8886 7.80598 13.8685 7.82622C13.8504 7.84418 13.8323 7.86213 13.8136 7.88064C13.798 7.89624 13.7823 7.91184 13.7662 7.92791C13.6581 8.0186 13.5641 8.04101 13.4246 8.04297C13.396 8.04386 13.3674 8.04474 13.3379 8.04565C13.1496 8.01521 13.0161 7.87017 12.8861 7.73902C12.8695 7.72254 12.853 7.70606 12.8359 7.68908C12.7807 7.63419 12.7258 7.57909 12.6709 7.52399C12.6313 7.48444 12.5917 7.44491 12.552 7.40539C12.4446 7.2983 12.3374 7.191 12.2303 7.08367C12.1181 6.97131 12.0058 6.85911 11.8934 6.74689C11.7049 6.55849 11.5165 6.36996 11.3282 6.18135C11.1105 5.96325 10.8925 5.74537 10.6744 5.52761C10.4871 5.34059 10.2999 5.15346 10.1128 4.96622C10.0011 4.8544 9.88935 4.74263 9.77748 4.63097C9.67238 4.52605 9.56743 4.42097 9.4626 4.31578C9.42408 4.27718 9.38551 4.23863 9.34688 4.20014C9.29421 4.14765 9.24175 4.09497 9.18933 4.04223C9.17395 4.027 9.15857 4.01176 9.14273 3.99606C9.01094 3.86274 8.95844 3.76956 8.95581 3.58203C8.95492 3.55343 8.95404 3.52483 8.95312 3.49536C8.98652 3.28875 9.16009 3.14218 9.30267 3.00052C9.32352 2.97956 9.34437 2.9586 9.36585 2.93701C9.43442 2.86817 9.50321 2.79956 9.57202 2.73096C9.6199 2.68304 9.66776 2.63511 9.71562 2.58718C9.81576 2.48695 9.91601 2.38683 10.0163 2.28679C10.145 2.15852 10.2733 2.02997 10.4016 1.90134C10.5003 1.80241 10.5991 1.70363 10.698 1.6049C10.7454 1.55755 10.7928 1.51015 10.8401 1.46271C10.9061 1.3965 10.9723 1.33051 11.0386 1.26456C11.0582 1.24491 11.0777 1.22527 11.0978 1.20503C11.1159 1.18707 11.134 1.16912 11.1527 1.15061C11.1684 1.13501 11.184 1.11941 11.2001 1.10334C11.3082 1.01265 11.4022 0.990236 11.5417 0.988281Z"/>
                        <path d="M7.125 9.56253C7.27621 9.63533 7.3647 9.72383 7.4375 9.87503C7.4626 10.03 7.45787 10.1697 7.38477 10.3106C7.27936 10.4502 7.20677 10.4884 7.03711 10.5274C6.89447 10.5403 6.78018 10.508 6.65625 10.4375C6.52336 10.3186 6.48336 10.1735 6.46875 10C6.49493 9.82553 6.55265 9.7374 6.6875 9.62503C6.82818 9.5408 6.9652 9.5423 7.125 9.56253Z"/>
                        </g>
                        <defs>
                        <clipPath id="clip0_723_16934">
                        <rect width="16" height="16" fill="white" transform="translate(0 1)"/>
                        </clipPath>
                        </defs>
                        </svg>
                    </button>
                    <div class="flex items-center gap-2">
                        <div class="flex justify-between items-center gap-2 rounded-b-md cursor-pointer hover:bg-blue-50">
                            <div class="relative inline-flex flex-col items-start mb-0 cursor-pointer">
                                <input id="safe_area" v-model="safeFrame" class="sr-only peer" name="safe_area" type="checkbox" :value="true" />
                                <label for="safe_area" class="w-11 h-6 mb-0 peer-focus:outline-none rounded-full peer bg-gray-200 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[3px] after:bg-white after:rounded-full after:h-[19px] after:w-[19px] after:transition-all peer-checked:bg-blue-600 cursor-pointer">
                                </label>
                            </div>
                            <label for="safe_area" class="text-sm text-gray-900 mb-0">Safe Frame</label>
                        </div>
                        <svg class="h-4 w-4 fill-gray-500" width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_723_16947)">
                            <path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z"/>
                            </g>
                            <defs>
                            <clipPath id="clip0_723_16947">
                            <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                            </clipPath>
                            </defs>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- right section -->
    <!-- <div class="w-[200px] h-full bg-white p-2">
        <div  class="">
            <div class="flex items-center gap-4">
            </div>
    </div>

    </div> -->

</template>

<style scoped>
.item {
  padding: 8px;
  background: #f0f0f0;
  margin-bottom: 5px;
}
.sub-item {
  padding: 5px;
  margin-left: 20px;
  background: #d3d3d3;
}
.loader{
  @apply w-6 h-6 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-4 border-solid border-[#020202];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}
</style>

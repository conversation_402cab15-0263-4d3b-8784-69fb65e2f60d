<script setup>

import { computed, onMounted, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Button from '../../common/Button.vue';
import { ProjectStore } from '../../../store/project.ts';
import Modal from '@/components/common/Modal/Modal.vue';
import DeleteModalContent from '@/components/common/ModalContent/DeleteModalContent.vue';
import { GetAllTrash, RestoreTrash } from '@/api/trash';
import ImgPreviewModal from '@/components/common/Modal/imgPreviewModal.vue';
import { getCookie } from '@/helpers/domhelper';
import UnitplanTable from './UnitplanTable.vue';
import { getListofUnitplan, moveUnitplanToTrash } from '@/api/projects/unitplans';
import SwitchButtons from '@/components/common/SwitchButtons.vue';
import FilesUploader from '@/components/Exterior/FilesUploader.vue';
import { getListofAssets } from '@/api/projects/assets';
import <PERSON> from 'papaparse';

/* States */
const route = useRoute();
const router = useRouter();

const projectId = ref(route.params.project_id);

const type = ref(route.query.type || 'Unitplans');
const headers = ['ID', 'Select type *', 'Unit name', 'No.of.Bed', 'Measurement type *', 'Measurement', 'Upload image', 'STYLE name', 'TYPE', 'No.of.Bath', 'balcony type *', 'balcony Sq.ft', 'Exterior Type', 'Scene Id', 'Gallery Items', 'Tour', 'is Residential', 'is Furnished', ''];
const projectStore = ProjectStore();
const openDeleteModal = ref(false);
const openUploadModal = ref(false);
const trashUnitplanId = ref({id: null, index: null, parentID: null});
const uploadUnitplanId = ref({id: null});
const uploadedFiles = ref({});
const trashLoader = ref(null);
const archivedModal = ref(false);
const archived = ref(null);
const unarchiveLoader = ref({});
const loader = ref(false);
const isModalOpen = ref({ status: false, url: '' });
const newfloorRows = ref({}), newRows = ref({});
const filesData = ref({});

if (projectId.value) {
  projectStore.RefreshAssets(projectId.value);
  getListofAssets(projectId.value)
    .then((res) => {
      filesData.value = res.assets.items;
    })
    .catch((err) => {
      console.log("Error:", err);
    });
}

/* Methods */
const handleListOfUnitplans = async () => {
  await getListofUnitplan(projectId.value).then((response) => {
    projectStore.SyncMultipleUnitplans(response);
  });
};

async function getArchivedItems () {
  try {
    const response = await GetAllTrash(projectId.value, getCookie('organization'), 'unitplans', 1);
    console.log(response.items);

    archived.value = response.items;
    console.log(archived.value);
  } catch (error) {
    console.error('Err', error);
  }
}

const addNewRow = () => {
  const id = crypto.randomUUID();
  const newFile = {
    _id: id,
    unit_type: '',
    name: '',
    style: '',
    type: '',
    bedrooms: '',
    bathrooms: '',
    measurement_type: '',
    measurement: '',
    balcony_measurement_type: '',
    balcony_measurement: '',
    thumbnail: '',
    image_url: '',
    is_residential: false,
    is_furnished: false,
    isNew: true,
  };

  newRows.value[id] = newFile;
};

const addNewFloor = (parentID) => {
  const id = crypto.randomUUID();
  if (!projectStore.unitplans[parentID]) {
    console.error(`Unitplan with ID ${parentID} not found.`);
    return; // Prevent further execution
  }

  if (!projectStore.unitplans[parentID].floor_unitplans) {
    projectStore.unitplans[parentID].floor_unitplans = [];
  }

  const existingFloors = newfloorRows.value[parentID] ? Object.keys(newfloorRows.value[parentID]).length : 0;
  const totalFloors = existingFloors + projectStore.unitplans[parentID].floor_unitplans.length;

  const newFile = {
    _id: id,
    unit_type: 'villa_floor',
    name: '',
    style: '',
    type: '',
    bedrooms: '',
    bathrooms: '',
    measurement_type: '',
    measurement: '',
    balcony_measurement_type: '',
    balcony_measurement: '',
    thumbnail: '',
    image_url: '',
    is_residential: false,
    is_furnished: false,
    isNew: true,
    parent: parentID,
    serialNo: totalFloors + 1,
  };
  if (!newfloorRows.value[parentID]) {
    newfloorRows.value[parentID] = {};
  }
  newfloorRows.value[parentID][id] = newFile;
};

function openImageModal (status, url){
  isModalOpen.value.status = status;
  isModalOpen.value.url = url;
}

function deleteNewRow (id, parentID){
  delete newfloorRows.value[parentID][id];
}
function deleteNewRow1 (id){
  delete newRows.value[id];
}

const filteredData = computed(() => {
  return projectStore.unitplans ?
    Object.values(projectStore.unitplans || {}).filter( (item) => item.unit_type === "flat" || item.unit_type === "villa")
    : [];
});

document.addEventListener('refreshAmenitiyListByCategory', () => {
  handleGetCategory();
});

const openAccordionIds = ref({});
const isAccordionOpen = (id) => openAccordionIds.value[id] || false;
const toggleAccordion = (id) => {
  openAccordionIds.value[id] = !isAccordionOpen(id);
};

document.addEventListener('refreshUnitplans', () => {
  handleListOfUnitplans();
});

const handleMoveToTrash = () => {
  let newObj= {};
  trashLoader.value = true;
  if ( projectId.value && trashUnitplanId.value.id && !trashUnitplanId.value.parentID ) {
    newObj = {
      unitplan_id: [trashUnitplanId.value.id],
      timeStamp: Date.now(),
    };
  } else {
    newObj = {
      unitplan_id: [trashUnitplanId.value.id],
      timeStamp: Date.now(),
      parentID: trashUnitplanId.value.parentID,
    };
  }
  if (newObj) {
    moveUnitplanToTrash(newObj, projectId.value).then(async () => {
      delete projectStore.unitplans[trashUnitplanId.value.id];
      document.dispatchEvent(new Event('refreshUnitplans'));
      trashLoader.value = false;
      openDeleteModal.value = false;
      trashUnitplanId.value = {id: null, index: null};
      archivedModal.value = true;
      setTimeout(() => {
        archivedModal.value = false;
      }, 2000);
    });
  }
};

function openMoveToTrashModal (id, index, parentID) {
  console.log(id, index, parentID);
  openDeleteModal.value = true;
  trashUnitplanId.value.id = id,
  trashUnitplanId.value.index = index;
  parentID ? trashUnitplanId.value.parentID = parentID : null;
}

function openUploadfileModal (id) {
  openUploadModal.value = true;
  uploadUnitplanId.value.id = id;
}

const handleFileUpload = (event) => {
  uploadedFiles.value[uploadUnitplanId.value.id] = event;
  openUploadModal.value = false;
};

async function restoreFunc (item) {
  unarchiveLoader.value[item._id] = true;
  const payload = {
    trash_id: item._id,
  };
  // load.value = item._id;
  try {
    await RestoreTrash(payload, projectId.value, 'unitplan', 'restoreUnitplan');
    await getArchivedItems();
    delete unarchiveLoader.value[item._id];
  } catch (error) {
    console.error("Error during restore or fetching trash:", error);
  }
}

async function handleButtonClick (e) {
  type.value = e;
  try {
    await router.push({ path: route.path, query: { ...route.query, type: e } });
    if (e === 'Unitplans') {
      await handleListOfUnitplans(); // This will fetch fresh gallery data
    } else if (e === 'Archive') {
      console.log("ARCHIVED");
      await getArchivedItems(); // This will fetch archived items
    }
  } catch (e) {
    console.log(e);
  }
}

// export csv functions
function objectToCsv (data) {
  const csvData = [];

  const title = ['_id', 'unit_type', 'tour_id', 'thumbnail', 'project_id', 'name', 'measurement_type', 'measurement', 'is_furnished', 'is_commercial', 'image_url', 'exterior_type', 'scene_id', 'gallery_id', 'floor_unitplans', 'bedrooms', 'balcony_measurement', 'balcony_measurement_type', 'bathrooms', 'style', 'type'];
  csvData.push(title);

  for (const key in data) {
    const entry = data[key];
    const csvEntry = [];
    const { ...cleanedEntry } = entry;
    csvEntry.push(cleanedEntry._id);
    csvEntry.push(cleanedEntry.unit_type);
    csvEntry.push(cleanedEntry.tour_id);
    csvEntry.push(cleanedEntry.thumbnail);
    csvEntry.push(cleanedEntry.project_id);
    csvEntry.push(cleanedEntry.name);
    csvEntry.push(cleanedEntry.measurement_type);
    csvEntry.push(cleanedEntry.measurement);
    csvEntry.push(cleanedEntry.is_furnished);
    csvEntry.push(cleanedEntry.is_commercial);
    csvEntry.push(cleanedEntry.image_url);
    csvEntry.push(cleanedEntry.exterior_type);
    csvEntry.push(cleanedEntry.scene_id);
    csvEntry.push(cleanedEntry.gallery_id);
    csvEntry.push(cleanedEntry.floor_unitplans);
    csvEntry.push(cleanedEntry.bedrooms);
    csvEntry.push(cleanedEntry.balcony_measurement);
    csvEntry.push(cleanedEntry.balcony_measurement_type);
    csvEntry.push(cleanedEntry.bathrooms);
    csvEntry.push(cleanedEntry.style);
    csvEntry.push(cleanedEntry.type);
    if (cleanedEntry.metadata!==undefined){
      Object.keys(cleanedEntry.metadata).forEach((metaKey) => {
        if (!title.includes('metadata_'+metaKey)){
          title.push('metadata_'+metaKey);
        }
        csvEntry.push(cleanedEntry.metadata[metaKey]);
      });
    }
    csvEntry.push(cleanedEntry.cta_link);
    csvData.push(csvEntry);
  }
  return csvData;
}

function exportCsv () {
  const csvData = objectToCsv(projectStore.unitplans);
  const csv = Papa.unparse(csvData);
  // Create a Blob with the CSV data
  const blob = new Blob([csv], { type: 'text/csv' });
  // Create a URL for the Blob
  const url = window.URL.createObjectURL(blob);
  // Create a link element and trigger the download
  const a = document.createElement('a');
  a.href = url;
  a.download = 'data.csv';
  document.body.appendChild(a);
  a.click();

  // Clean up
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}

onMounted(async () => {
  if (type.value === 'Unitplans') {
    await handleListOfUnitplans();
  } else if (type.value === 'Archive') {
    await getArchivedItems();
  }
});

onMounted(() => {
  filteredData;
});

watch(() => projectStore.unitplans, (newval) => {
  filteredData.value = Object.values(newval).filter(
    (item) => item.unit_type === "flat" || item.unit_type === "villa",
  );
}, {deep: true});

</script>

<template>
  <div >
    <div class=" dynamic-header">
      <div class="dynamic-heading ">
          <p class="dynamic-topic"> Enter Unitplan Details</p>
          <p class="dynamic-sub-topic">Details provided here will be used to to create the project's Unitplan Screens. <span class="text-[#1c64f2] underline underline-offset-4">View Sample</span>.</p>

      </div>
      <div class="flex gap-3">
        <!-- export button  -->
        <Button :disabled="loader" @click="exportCsv" title="Export" class="!rounded-lg !px- !py-3 !bg-bg-1000 border border-gray-200 !text-[#111928]">
          <template v-slot:svg>
            <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 8.37494H0.0780001C0.158987 8.67991 0.313718 8.95814 0.5274 9.18304L2.2242 10.9505C2.44011 11.1731 2.70722 11.3342 3 11.4186V8.37494Z" fill="#1F2A37"/>
              <path d="M9.285 2.89511C9.3943 3.01299 9.45477 3.17086 9.45341 3.33472C9.45204 3.49859 9.38894 3.65534 9.27769 3.77122C9.16645 3.8871 9.01596 3.95283 8.85864 3.95425C8.70132 3.95567 8.54976 3.89268 8.4366 3.77883L7.8 3.11636L7.8 7.12498C7.8 7.29074 7.73679 7.4497 7.62426 7.56691C7.51174 7.68411 7.35913 7.74996 7.2 7.74996C7.04087 7.74996 6.88826 7.68411 6.77574 7.56691C6.66321 7.4497 6.6 7.29074 6.6 7.12498L6.6 3.11636L5.9634 3.77883C5.90805 3.83853 5.84185 3.88614 5.76864 3.91889C5.69544 3.95165 5.61671 3.96889 5.53704 3.96961C5.45737 3.97033 5.37837 3.95452 5.30463 3.92309C5.23089 3.89167 5.1639 3.84526 5.10756 3.78658C5.05123 3.7279 5.00668 3.65812 4.97651 3.58131C4.94634 3.5045 4.93116 3.42221 4.93185 3.33922C4.93254 3.25624 4.94909 3.17423 4.98054 3.09798C5.01198 3.02173 5.05769 2.95277 5.115 2.89511L6.4542 1.50016H1.2C0.887173 1.49497 0.585146 1.61925 0.360179 1.84573C0.135213 2.07222 0.00568067 2.3824 0 2.70824L0 7.12498H3C3.31826 7.12498 3.62348 7.25667 3.84853 7.49109C4.07357 7.7255 4.2 8.04343 4.2 8.37494V11.4998H10.8C11.1128 11.505 11.4149 11.3807 11.6398 11.1543C11.8648 10.9278 11.9943 10.6176 12 10.2918V2.75012C12 2.41861 11.8736 2.10067 11.6485 1.86626C11.4235 1.63185 11.1183 1.50016 10.8 1.50016H7.9458L9.285 2.89511Z" fill="#1F2A37"/>
            </svg>
          </template>
        </Button>
        <Button :disabled="loader || type == 'Archive'" title="Add Unitplan" class="!px-3 !py-1 border !border-blue-600 !bg-bg-1000 !justify-center !items-center !gap-2 !text-[#1c64f2] w-fit" @click="addNewRow">
          <template v-slot:svg>
              <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M10.2659 5.96686H6.53255V2.23353C6.53255 2.09208 6.47636 1.95642 6.37634 1.85641C6.27632 1.75639 6.14067 1.7002 5.99922 1.7002C5.85777 1.7002 5.72211 1.75639 5.6221 1.85641C5.52208 1.95642 5.46589 2.09208 5.46589 2.23353V5.96686H1.73255C1.5911 5.96686 1.45545 6.02305 1.35543 6.12307C1.25541 6.22309 1.19922 6.35875 1.19922 6.5002C1.19922 6.64164 1.25541 6.7773 1.35543 6.87732C1.45545 6.97734 1.5911 7.03353 1.73255 7.03353H5.46589V10.7669C5.46589 10.9083 5.52208 11.044 5.6221 11.144C5.72211 11.244 5.85777 11.3002 5.99922 11.3002C6.14067 11.3002 6.27632 11.244 6.37634 11.144C6.47636 11.044 6.53255 10.9083 6.53255 10.7669V7.03353H10.2659C10.4073 7.03353 10.543 6.97734 10.643 6.87732C10.743 6.7773 10.7992 6.64164 10.7992 6.5002C10.7992 6.35875 10.743 6.22309 10.643 6.12307C10.543 6.02305 10.4073 5.96686 10.2659 5.96686Z" fill="#1C64F2"/>
              </svg>
          </template>
        </Button>
      </div>
      </div>
      <div class="w-full rounded-lg shadow-[0px_1px_2px_-1px_rgba(0,0,0,0.10)] border border-gray-200 justify-start items-start">
        <div class=" w-full flex justify-between !text-[#111928] items-center">
          <div class="flex gap-5">

          </div>
        </div>
        <div class="w-full">
          <div class="border-b border-gray-200 w-full">
          <nav class="-mb-[3px] flex">
            <SwitchButtons :disabled="loader" :active="type" :array="['Unitplans', 'Archive']" @click="(e) => handleButtonClick(e)" class="w-full p-0.5"/>
          </nav>
        </div>
        </div>
        <!-- table -->
        <div class="w-full overflow-x-auto h-fit">
          <div class="flex w-fit bg-gray-50">
            <template v-for="(item) in headers" :key="item._id">
            <div
              v-if="!(type === 'Archive' && (item === 'Exterior Type' || item === 'Scene Id' || item === 'STYLE name' || item === 'Upload image' || item === 'TYPE' || item === 'Gallery Items' || item === 'Tour'))"
              class="rounded-lg py-[20px] px-2 text-gray-500 text-xs font-semibold text-left uppercase leading-[18px] text-nowrap flex items-center"
              :class="{
                'w-16 ml-[1.5rem]': item === 'ID',
                'w-[7rem]': item === 'No.of.Bath',
                'w-[8rem]': item === 'is Furnished' || item === 'balcony type *',
                'w-[8.5rem]': item === 'Select type *' || item === 'balcony Sq.ft' || item === 'TYPE',
                '!w-[9rem]': item === '' || item === 'Measurement' || item === 'is Residential' || item === 'Unit name',
                'w-[9.5rem]': item === 'Exterior Type',
                'w-[10rem]': item === 'No.of.Bed' || item === 'STYLE name',
                'w-[10.5rem]': item === 'Measurement type *',
                'w-[11rem]': item === 'Tour',
                'w-[12rem]': item === 'Scene Id',
                'w-[13rem]': item === 'Gallery Items',
                'w-[19rem]': item === 'Upload image',
                'sticky right-0 bg-gray-50': item === ''
              }"
            >
              {{ item }}
            </div>
          </template>
          </div>

          <div v-if="type === 'Unitplans'" class="">
            <div v-if="!projectStore.unitplans" class="flex flex-col items-center justify-center p-4">
              <div class="loader !h-11 !w-11 !border-3 !border-[#1C64F2] !border-t-gray-200"></div>
              <p class="text-black text-xs font-normal pt-1">Loading...</p>
            </div>
            <div v-else-if="Object.keys(projectStore.unitplans).length === 0 && Object.keys(newRows).length === 0" class="flex flex-col items-center justify-center p-4 text-gray-500 text-lg">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                <g clip-path="url(#clip0_4982_18313)">
                  <path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z" fill="#6B7280"/>
                </g>
                <defs>
                  <clipPath id="clip0_4982_18313">
                    <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                  </clipPath>
                </defs>
              </svg>
              <p class="text-black text-xs font-normal pt-1">Empty</p>
            </div>
            <template v-else v-for="(item, index) in filteredData" :key="item._id">
                <UnitplanTable
                    v-if="item.unit_type === 'flat' || item.unit_type === 'villa'"
                    :projectId="projectId"
                    :index="index"
                    :unitplan="item"
                    :isAccordionOpen="isAccordionOpen(item._id)"
                    @openImageModal="openImageModal"
                    @click="item.unit_type === 'villa' && toggleAccordion(item._id)"
                    @toggleAccordion="toggleAccordion"
                    :unitplanId="item._id"
                    @moveToTrashModal="openMoveToTrashModal"
                    @handleSubmit="()=>loader=true"
                    @uploadModal="openUploadfileModal"
                    :uploadedFiles="uploadedFiles[item._id]"
                >
                    <template #accordian v-if="item.unit_type === 'villa'">
                        <UnitplanTable
                            v-for="(floor, id) in item.floor_unitplans" :key="id"
                            :index="`${index}_${id + 1}`"
                            :unitplan="Object.values(projectStore.unitplans).find(item => item._id === floor)"
                            :projectId="projectId"
                            @addNewRow="(val)=>{projectStore.unitplans[val._id] = val; loader=false}"
                            :loader="loader"
                            :parentID="item._id"
                            @handleSubmit="()=>loader=true"
                            :unitplanId="floor"
                            @moveToTrashModal="openMoveToTrashModal"
                            @uploadModal="openUploadfileModal"
                            @openImageModal="openImageModal"
                            :uploadedFiles="uploadedFiles[floor]"
                        />
                        <template v-for="(newFloor, newid) in newfloorRows[item._id]" :key="newid">
                            <UnitplanTable
                                v-if="newFloor.unit_type === 'villa_floor'"
                                :projectId="projectId"
                                :index="`${index}_${newFloor.serialNo}`"
                                :unitplan="newFloor"
                                :parentID="item._id"
                                @openImageModal="openImageModal"
                                @deleteRow="deleteNewRow"
                                @addNewRow="(val,id,parentid)=>{
                                        projectStore.unitplans[val._id] = val;
                                        projectStore.unitplans[parentid].floor_unitplans = [
                                            ...(projectStore.unitplans[parentid].floor_unitplans),
                                            val._id
                                        ];
                                        console.log(projectStore.unitplans[parentid].floor_unitplans)
                                        delete newfloorRows[item._id][id];
                                        loader=false
                                }"
                                :loader="loader"
                                @handleSubmit="()=>loader=true"
                                :unitplanId="id"
                                @uploadModal="openUploadfileModal"
                                :uploadedFiles="uploadedFiles[newid]"
                            />
                        </template>
                        <Button :disabled="loader || type == 'Archive'" title="Add" class="ml-[13.5rem] !px-3 !py-1 !bg-bg-1000 !justify-center !items-center !gap-2 !text-[#1c64f2]"
                            @click="addNewFloor(item._id)"
                        >
                            <template v-slot:svg>
                            <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.2659 5.96686H6.53255V2.23353C6.53255 2.09208 6.47636 1.95642 6.37634 1.85641C6.27632 1.75639 6.14067 1.7002 5.99922 1.7002C5.85777 1.7002 5.72211 1.75639 5.6221 1.85641C5.52208 1.95642 5.46589 2.09208 5.46589 2.23353V5.96686H1.73255C1.5911 5.96686 1.45545 6.02305 1.35543 6.12307C1.25541 6.22309 1.19922 6.35875 1.19922 6.5002C1.19922 6.64164 1.25541 6.7773 1.35543 6.87732C1.45545 6.97734 1.5911 7.03353 1.73255 7.03353H5.46589V10.7669C5.46589 10.9083 5.52208 11.044 5.6221 11.144C5.72211 11.244 5.85777 11.3002 5.99922 11.3002C6.14067 11.3002 6.27632 11.244 6.37634 11.144C6.47636 11.044 6.53255 10.9083 6.53255 10.7669V7.03353H10.2659C10.4073 7.03353 10.543 6.97734 10.643 6.87732C10.743 6.7773 10.7992 6.64164 10.7992 6.5002C10.7992 6.35875 10.743 6.22309 10.643 6.12307C10.543 6.02305 10.4073 5.96686 10.2659 5.96686Z" fill="#1C64F2"/>
                            </svg>
                            </template>
                        </Button>
                    </template>
                </UnitplanTable>
            </template>

            <template v-for="(item, id, index) in newRows" :key="id">
                <UnitplanTable
                    v-if="item.unit_type !== 'villa_floor'"
                    :projectId="projectId"
                    :index="filteredData.length + index"
                    :unitplan="item"
                    @openImageModal="openImageModal"
                    @deleteRow="deleteNewRow1"
                    @addNewRow="(val,id)=>{projectStore.unitplans[val._id] = val; delete newRows[id]; loader=false}"
                    :loader="loader"
                    @handleSubmit="()=>loader=true"
                    :unitplanId="item._id"
                    @uploadModal="openUploadfileModal"
                    :uploadedFiles="uploadedFiles[item._id]"
                />
            </template>
          </div>
          <div v-else>
            <div v-if="archived == null" class="flex flex-col items-center justify-center p-4">
              <div class="loader !h-11 !w-11 !border-3 !border-[#1C64F2] !border-t-gray-200"></div>
              <p class="text-black text-xs font-normal pt-1">Loading</p>
            </div>
            <div v-else-if="archived && Object.keys(archived).length == 0" class="flex flex-col items-center justify-center p-4 text-gray-500 text-lg">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                <g clip-path="url(#clip0_4982_18313)">
                  <path d="M8 0.5C6.41775 0.5 4.87103 0.969192 3.55544 1.84824C2.23985 2.72729 1.21447 3.97672 0.608967 5.43853C0.00346627 6.90034 -0.15496 8.50887 0.153721 10.0607C0.462403 11.6126 1.22433 13.038 2.34315 14.1569C3.46197 15.2757 4.88743 16.0376 6.43928 16.3463C7.99113 16.655 9.59966 16.4965 11.0615 15.891C12.5233 15.2855 13.7727 14.2602 14.6518 12.9446C15.5308 11.629 16 10.0822 16 8.5C15.9977 6.37898 15.1541 4.3455 13.6543 2.84572C12.1545 1.34593 10.121 0.502329 8 0.5ZM7.6 3.7C7.83734 3.7 8.06935 3.77038 8.26669 3.90224C8.46402 4.03409 8.61783 4.22151 8.70866 4.44078C8.79948 4.66005 8.82325 4.90133 8.77694 5.13411C8.73064 5.36688 8.61635 5.5807 8.44853 5.74853C8.28071 5.91635 8.06689 6.03064 7.83411 6.07694C7.60133 6.12324 7.36005 6.09948 7.14078 6.00865C6.92151 5.91783 6.7341 5.76402 6.60224 5.56668C6.47038 5.36934 6.4 5.13734 6.4 4.9C6.4 4.58174 6.52643 4.27651 6.75147 4.05147C6.97652 3.82643 7.28174 3.7 7.6 3.7ZM9.6 12.5H6.4C6.18783 12.5 5.98435 12.4157 5.83432 12.2657C5.68429 12.1157 5.6 11.9122 5.6 11.7C5.6 11.4878 5.68429 11.2843 5.83432 11.1343C5.98435 10.9843 6.18783 10.9 6.4 10.9H7.2V8.5H6.4C6.18783 8.5 5.98435 8.41571 5.83432 8.26568C5.68429 8.11565 5.6 7.91217 5.6 7.7C5.6 7.48782 5.68429 7.28434 5.83432 7.13431C5.98435 6.98428 6.18783 6.9 6.4 6.9H8C8.21218 6.9 8.41566 6.98428 8.56569 7.13431C8.71572 7.28434 8.8 7.48782 8.8 7.7V10.9H9.6C9.81217 10.9 10.0157 10.9843 10.1657 11.1343C10.3157 11.2843 10.4 11.4878 10.4 11.7C10.4 11.9122 10.3157 12.1157 10.1657 12.2657C10.0157 12.4157 9.81217 12.5 9.6 12.5Z" fill="#6B7280"/>
                </g>
                <defs>
                  <clipPath id="clip0_4982_18313">
                    <rect width="16" height="16" fill="white" transform="translate(0 0.5)"/>
                  </clipPath>
                </defs>
              </svg>
              <p class="text-black text-xs font-normal pt-1">Empty</p>
            </div>
            <div v-for="(item, id, index) in archived" :key="id" class="flex w-fit" :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
              <div class="py-1 text-gray-500 text-sm relative flex items-center w-[5rem] ml-4 pl-2">
                {{ index + 1 }}
              </div>
              <div class="py-1 text-gray-500 text-sm relative flex w-[8rem]">
                <div class="w-full flex items-center gap-2">
                  {{ item?.data ? Object.values(item?.data)[0]?.unit_type : '' }}
                </div>
              </div>

              <!-- NAME -->
              <div class="py-1 text-gray-500 text-sm relative flex items-center w-[9.25rem]">
                <div class="w-full">
                  {{ item?.data ? Object.values(item?.data)[0]?.name : '' }}
                </div>
              </div>

              <!-- BEDROOM -->
              <div class="py-1 text-gray-500 text-sm relative flex w-[10rem] items-center">
                  <div class="w-full">
                    {{ item?.data ? Object.values(item?.data)[0]?.bedrooms : '' }}
                  </div>
              </div>

              <!-- BATHROOM -->
              <div class="py-1 text-gray-500 text-sm relative flex w-[7rem] items-center">
                  <div class="w-full">
                    {{ item?.data ? Object.values(item?.data)[0]?.bathrooms : '' }}
                  </div>
              </div>

              <!-- MEASUREMENT TYPE -->
              <div class="py-1 text-gray-500 text-sm relative flex w-[10.5rem] items-center">
                  <div class="w-full">
                    {{ item?.data ? Object.values(item?.data)[0]?.measurement_type : '' }}
                  </div>
              </div>

              <!-- MEASUREMENT -->
              <div class="py-1 text-gray-500 text-sm relative flex w-[9rem] items-center">
                  <div class="w-full">
                    {{ item?.data ? Object.values(item?.data)[0]?.measurement : '' }}
                  </div>
              </div>

              <!-- BALCONY TYPE -->
              <div class="py-1 text-gray-500 text-sm relative flex w-[8rem] items-center">
                  <div class="w-full">
                    {{ item?.data ? Object.values(item?.data)[0]?.balcony_measurement_type : '' }}
                  </div>
              </div>

              <!-- BALCONY -->
              <div class="py-1 text-gray-500 text-sm relative flex w-[8.25rem] items-center">
                  <div class="w-full">
                    {{ item?.data ? Object.values(item?.data)[0]?.balcony_measurement : '' }}
                  </div>
              </div>

              <!-- is_residential -->
              <div class="py-1 text-gray-500 text-sm relative flex w-[9rem] items-center">
                <div class="w-full">
                  {{ item?.data ? Object.values(item?.data)[0]?.is_residential : '' }}
                </div>
              </div>
              <!-- is_furnished -->
              <div class="py-1 text-gray-500 text-sm relative flex w-[7rem] items-center">
                <div class="w-full">
                  {{ item?.data ? Object.values(item?.data)[0]?.is_furnished : '' }}
                </div>
              </div>

              <div class="py-1 text-gray-500 text-sm flex justify-center items-center w-40 sticky right-0 pr-2" :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
                <Button title="Unarchive" class="relative !px-3 !py-1 border !border-blue-600 !bg-bg-1000 !justify-center !items-center !gap-2 !text-[#1c64f2]"
                  @click="restoreFunc(item)" :disabled="unarchiveLoader[item._id]"
                >
                  <template v-slot:svg>
                    <div v-if="unarchiveLoader[item._id]" class="loader !h-5 !w-5 !border-2"></div>
                    <svg   width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M0.666667 9.95961C0.666667 10.3415 0.807142 10.7078 1.05719 10.9778C1.30724 11.2479 1.64638 11.3996 2 11.3996H10C10.3536 11.3996 10.6928 11.2479 10.9428 10.9778C11.1929 10.7078 11.3333 10.3415 11.3333 9.95961V4.91961H0.666667V9.95961ZM4.19533 7.29057C4.32035 7.15559 4.48989 7.07976 4.66667 7.07976C4.84344 7.07976 5.01298 7.15559 5.138 7.29057L5.33333 7.50153V6.35961C5.33333 6.16865 5.40357 5.98552 5.5286 5.85049C5.65362 5.71547 5.82319 5.63961 6 5.63961C6.17681 5.63961 6.34638 5.71547 6.4714 5.85049C6.59643 5.98552 6.66667 6.16865 6.66667 6.35961V7.50153L6.862 7.29057C6.98774 7.15942 7.15614 7.08684 7.33093 7.08848C7.50573 7.09012 7.67294 7.16585 7.79655 7.29934C7.92015 7.43283 7.99026 7.61342 7.99178 7.8022C7.9933 7.99098 7.92611 8.17286 7.80467 8.30865L6.47133 9.74865C6.40941 9.8157 6.33584 9.8689 6.25484 9.9052C6.17385 9.94149 6.08702 9.96018 5.99933 9.96018C5.91164 9.96018 5.82482 9.94149 5.74382 9.9052C5.66283 9.8689 5.58926 9.8157 5.52733 9.74865L4.194 8.30865C4.0692 8.17344 3.99923 7.99023 3.99948 7.79931C3.99973 7.60839 4.07018 7.4254 4.19533 7.29057Z" fill="#1C64F2"/>
                      <path d="M11.3333 0.599609H0.666667C0.298477 0.599609 0 0.921964 0 1.31961L0 2.75961C0 3.15725 0.298477 3.47961 0.666667 3.47961H11.3333C11.7015 3.47961 12 3.15725 12 2.75961V1.31961C12 0.921964 11.7015 0.599609 11.3333 0.599609Z" fill="#1C64F2"/>
                    </svg>
                  </template>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

    <Modal :open="openDeleteModal">
      <DeleteModalContent
        :trash="true"
        @closeModal="(e) => openDeleteModal = false"
        :loader="trashLoader"
        @handleDelete="handleMoveToTrash"
        :dataName="'Unitplan'"
      />
    </Modal>
    <Modal :open="openUploadModal">
      <FilesUploader
        :data="filesData"
        @closeModal="(e) => openUploadModal = false"
        @fileUploaded="handleFileUpload"
      />
    </Modal>
    <ImgPreviewModal v-if="isModalOpen.url" :isOpen="isModalOpen.status" :imageUrl="isModalOpen.url"
    @close="isModalOpen.status = false" />
    <div v-if="archivedModal" class="fixed top-0 left-0 h-full w-full flex justify-center items-center">
      <div class="archived bg-bg-1000 shadow-[0px_4px_6px_0px_rgba(0,0,0,0.05)] border border-gray-200 rounded-lg">
        <div class="flex flex-col gap-4 items-center justify-center">
          <div>
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M7.15541 18.3336C6.82899 18.3352 6.51503 18.1819 6.28067 17.9065L0.37744 10.9628C0.259778 10.8235 0.165894 10.6576 0.101148 10.4743C0.0364024 10.2911 0.00206288 10.0942 9.0224e-05 9.89489C-0.00389373 9.49236 0.124201 9.10439 0.356196 8.81634C0.588191 8.52829 0.905082 8.36376 1.23716 8.35893C1.56923 8.3541 1.88929 8.50937 2.12691 8.79059L7.1604 14.7088L17.8722 2.09834C18.1102 1.81712 18.4305 1.662 18.7628 1.66711C19.0951 1.67223 19.4122 1.83715 19.6442 2.1256C19.8762 2.41405 20.0041 2.8024 19.9999 3.20522C19.9957 3.60804 19.8596 3.99233 19.6217 4.27355L8.03014 17.9065C7.79578 18.1819 7.48182 18.3352 7.15541 18.3336Z" fill="#057A55"/>
            </svg>
          </div>
          <div class="w-[126px] h-6 text-center">
            Archived
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.upload-btn-wrapper {
  position: relative;
  overflow: hidden;
}
.upload-btn-wrapper input[type=file] {
  font-size: 100px;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0;
}
.archived{
 @apply py-5 px-6
}
.loader{
  @apply w-6 h-6 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-4 border-solid border-[#020202];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
.w-full::-webkit-scrollbar-track {
  background: #E5E7EB;
}

.w-full::-webkit-scrollbar-thumb {
  background: #9CA3AF;
}

</style>

<script setup>

import { Form, Field } from 'vee-validate';
import { defineProps, defineEmits, ref, watch, computed, onMounted, nextTick } from 'vue';
import Multiselect from 'vue-multiselect';
import { truncateString, checkNumberValue, resizeImage } from '@/helpers/helpers';
import Button from '@/components/common/Button.vue';
import { createUnitplan, EditUnitplan, getStyles, getTypes } from '@/api/projects/unitplans';
import { ProjectStore } from '@/store/project';
import { measurementTypeList, unitplanTypeList } from '@/helpers/constants';
import { unitplanSchema } from '@/validationSchema/unitplan';
import { getListofGalleryItems } from '../../../api/projects/gallery';
import { onClickOutside } from '@vueuse/core';

const emit = defineEmits(['toggleAccordion', 'addNewRow', 'moveToTrashModal', 'deleteRow', 'handleSubmit', 'uploadModal']);
const props = defineProps({
  projectId: {type: String, default: ''},
  unitplan: {type: Object, default: () => ({})},
  index: {type: Number, default: 0},
  isAccordionOpen: { type: Boolean, default: false },
  parentID: { type: String },
  unitplanId: {type: String},
  uploadedFiles: {type: File},
});

const projectStore = ProjectStore();
const loader = ref(false), rowRef = ref(null);
const isNew = props?.unitplan?.isNew;
const initialValues = ref({...props?.unitplan, type: props?.unitplan?.type ? { name: props.unitplan.type } : null, style: props?.unitplan?.style ? { name: props.unitplan.style } : null, is_residential: props.unitplan.is_residential ? "Yes" : "No", is_furnished: props.unitplan.is_furnished ? "Yes" : "No"});
const unitTypeList = ["flat", "villa", "villa_floor"];
const exteriorType = ["gallery", "scene"];
const uploadProgress = ref(0);
let uploadInterval = null;
const unitplanTypeListRef = ref([]);
const unitplanTypeRef = ref(initialValues.value.type);
const styleRef = ref(initialValues.value.style);
const styleList = ref([]);
const galleryItems = ref([]);
const selectedGalleryItems = ref(initialValues.value.gallery_id);
const previewFileName = ref(null);
const thumbnail = ref(props.unitplan.thumbnail || null);
const selectedFields = ref({});
const fieldRefs = ref();
const isEdited = ref(false);
const copied = ref(false);

projectStore.RefreshVirtualTours(props.projectId);
projectStore.RefreshScenes(props.projectId);
projectStore.RefreshGalleryItems(props.projectId);

const trackChanges = () => {
  isEdited.value = true;
};

const handleGalleryItemList = () => {
  if (initialValues.value.exterior_type==='gallery' && projectStore.galleryItems) {
    galleryItems.value = projectStore.galleryItems && Object.values(projectStore.galleryItems)
      .map((item) => ({ '_id': item._id, 'name': item.name }));
  }
};

const setInitialGalleryItems = () => {
  if (initialValues.value && projectStore.galleryItems){
    selectedGalleryItems.value = (initialValues.value.gallery_id && projectStore.galleryItems) && initialValues.value.gallery_id.map((elem) => {
      return {'_id': elem, 'name': projectStore.galleryItems[elem].name};
    });
  }
};

getListofGalleryItems(props.projectId).then((res) => {
  galleryItems.value = Object.values(res)
    .map((item) => ({ '_id': item._id, 'name': item.name }));
}).catch((err) => {
  console.log('output->err', err);
});

watch(initialValues.value.exterior_type, () => {
  handleGalleryItemList();
  setInitialGalleryItems();

});

watch(() => projectStore.galleryItems, () => {
  handleGalleryItemList();
  setInitialGalleryItems();
});

getStyles(props.projectId).then((res) => { // Get list of styles
  styleList.value = res.map((elem) => {
    return {name: elem};
  });
}).catch((err) => {
  console.log('output->err', err);
});

const addTag = (newTag) => {
  const tag = { name: newTag };
  styleList.value.push(tag); // Adding to list
  styleRef.value = tag;
  trackChanges();
};

getTypes(props.projectId).then((res) => {
  unitplanTypeListRef.value = res.map((elem) => {
    return { name: elem };
  });
}).catch((err) => {
  console.log('output->err', err);
});

const addTagUnitPlanType = (newTag) => {
  const tag = { name: newTag };
  unitplanTypeListRef.value.push(tag);
  unitplanTypeRef.value = tag;
  trackChanges();
};

const sceneOptions = computed(() => {
  return projectStore.scenes ? Object.values(projectStore.scenes).map((scene) =>
    scene.sceneData._id,
  ) : [];
});

const tourOptions = computed(() => {
  return projectStore.virtualtours ? Object.values(projectStore.virtualtours).map((tour) =>
    tour._id,
  ) : [];
});

function extractFirebaseStoragePath (url, key) {
  // console.log(url)
  if (typeof url === 'string') {
    if (url.includes("firebasestorage.googleapis.com")) {
      // Existing logic for Firebase Storage URLs
      const pathMatch = url.match(/\/o\/(.+)\?/);
      if (!pathMatch || !pathMatch[1]) {
        console.warn("Invalid Firebase Storage URL format:", url);
        return null;
      }

      const fullPath = decodeURIComponent(pathMatch[1]);
      const lastSlashIndex = fullPath.lastIndexOf('/');

      return key === 'filename'
        ? lastSlashIndex === -1 ? fullPath : fullPath.substring(lastSlashIndex + 1)
        : lastSlashIndex === -1 ? '' : fullPath.substring(0, lastSlashIndex);
    }

    if (url.includes("storage.googleapis.com")) {
      // Handling non-standard Firebase Storage URLs
      const urlParts = url.split('/');
      return key === 'filename' ? urlParts[urlParts.length - 1] : urlParts.slice(3, -1).join('/');
    }

    throw new Error('Invalid Firebase Storage URL');
  }

  return null; // Add explicit return for non-string inputs
}

function handleDownloadFile (previewFileUrl, Fname) {
  const xhr = new XMLHttpRequest();
  xhr.open("GET", previewFileUrl, true);
  xhr.responseType = "blob"; // Get the response as a Blob

  xhr.onload = function () {
    if (xhr.status === 200) {
      const blob = new Blob([xhr.response]);
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download =  Fname || "downloaded-file";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href); // Clean up memory
    }
  };

  xhr.onerror = function () {
    console.error("Download failed.");
  };

  xhr.send();
}

function getObjectDifference (previousObj, newObj) {
  const differences = {};

  for (const key in newObj) {
    let newValue = newObj[key];
    const prevValue = previousObj[key];
    if (key === 'modified'){
      continue;
    }

    // **Exclude 'file' if it's a string**
    if (key === 'file' && typeof newValue === 'string') {
      continue;
    }

    // Normalize measurement: "" -> 0
    if (key === 'measurement' && newValue === '') {
      newValue = 0;
    }
    // Skip only undefined, null, or empty string (keep 0, false, etc.)
    if (newValue === undefined || newValue === null || newValue === '') {
      continue;
    }
    // If property is new
    if (!(key in previousObj)) {
      differences[key] = newValue;
      continue;
    }
    // Handle arrays
    if (Array.isArray(newValue)) {
      if (!Array.isArray(prevValue) || JSON.stringify(newValue) !== JSON.stringify(prevValue)) {
        differences[key] = newValue;
      }
      continue;
    }
    // Handle objects
    if (newValue instanceof Object) {
      if (!(prevValue instanceof Object) || JSON.stringify(newValue) !== JSON.stringify(prevValue)) {
        differences[key] = newValue;
      }
      continue;
    }

    // Handle primitive values
    if (prevValue !== newValue) {
      differences[key] = newValue;
    }
  }
  return differences;
}

async function handleSubmit (values) {
  console.log("SAVED CONTENT", values);
  if (isNew) {
    loader.value = true;
    emit('handleSubmit');
    const formData = new FormData();

    let galleryList = null;
    if (values.gallery_id && values.gallery_id.length !== 0) {
      galleryList = values.gallery_id.map((elem) => elem._id);
    }

    formData.append('project_id', props.projectId);
    formData.append('unit_type', values?.unit_type);
    formData.append('name', values?.name);
    values?.style && formData.append('style', values?.style.name);
    values?.type && formData.append('type', values?.type.name);
    values?.bedrooms && formData.append('bedrooms', values?.bedrooms);
    values?.bathrooms && formData.append('bathrooms', values?.bathrooms);
    values?.measurement_type && formData.append('measurement_type', values?.measurement_type);
    values?.measurement && formData.append('measurement', values?.measurement);
    values?.balcony_measurement_type && formData.append('balcony_measurement_type', values?.balcony_measurement_type);
    values?.balcony_measurement && formData.append('balcony_measurement', values?.balcony_measurement);
    formData.append('is_residential', values.is_residential === 'Yes' ? true : false);
    formData.append('is_furnished', values.is_furnished === 'Yes' ? true : false);
    values.exterior_type && formData.append('exterior_type', values.exterior_type);
    values.scene_id && formData.append('scene_id', values.scene_id);
    galleryList && formData.append('gallery_id', JSON.stringify(galleryList));
    values.tour_id && formData.append('tour_id', values.tour_id);
    values.unit_type==="villa_floor" && formData.append('parent_unitplan', props.parentID);
    if (values.unit_type!=='villa'){
      const resizedThumbnail =await resizeImage(values.file, 1280, 720);
      formData.append('lowRes', resizedThumbnail);
      formData.append('highRes', values.file);
    }
    const response = await createUnitplan(formData);
    if (response._id && (values.unit_type === 'flat' || values.unit_type === 'villa') ){
      emit('addNewRow', response, values._id);
    }
    if (response._id && values.unit_type === 'villa_floor'){
      emit('addNewRow', response, values._id, props.parentID);
    }
    loader.value = false;

  } else {
    const newObj = getObjectDifference(props.unitplan, values);
    console.log("Compared items", newObj);

    if (Object.keys(newObj).length > 0) {
      loader.value = true;
      emit('handleSubmit');
      const formData = new FormData();

      let galleryList = null;
      if (newObj.gallery_id && newObj.gallery_id.length !== 0) {
        galleryList = newObj.gallery_id.map((elem) => elem._id);
      }
      if (galleryList && galleryList.length > 0) {
        formData.append('gallery_id', JSON.stringify(galleryList));
      }
      if (newObj.unit_type){
        formData.append('unit_type', newObj.unit_type);
      }
      if (newObj.name){
        formData.append('name', newObj.name);
      }
      newObj.style && formData.append('style', newObj.style.name);
      newObj.type && formData.append('type', newObj.type.name);
      if (newObj.bedrooms){
        formData.append('bedrooms', newObj.bedrooms);
      }
      if (checkNumberValue(newObj.bathrooms)){
        formData.append('bathrooms', newObj.bathrooms);
      }
      if (checkNumberValue(newObj.measurement)){
        formData.append('measurement', newObj.measurement);
      }
      if (newObj.measurement_type){
        formData.append('measurement_type', newObj.measurement_type);
      }
      if (newObj.balcony_measurement_type) {
        formData.append('balcony_measurement_type', newObj.balcony_measurement_type);
      }
      if (newObj.balcony_measurement) {
        formData.append('balcony_measurement', newObj.balcony_measurement);
      }
      newObj.is_residential && newObj.is_residential === 'Yes' ? formData.append('is_residential', true) : formData.append('is_residential', false);
      newObj.is_residential && newObj.is_furnished === 'Yes' ? formData.append('is_furnished', true) : formData.append('is_furnished', false);
      newObj.exterior_type && formData.append('exterior_type', newObj.exterior_type);

      newObj.scene_id && formData.append('scene_id', newObj.scene_id);
      if (newObj.tour_id){
        formData.append('tour_id', newObj.tour_id);
      }
      if (newObj.file && newObj.unit_type!=='villa'){
        const resizedThumbnail = await resizeImage(newObj.file, 1280, 720);
        formData.append('lowRes', resizedThumbnail);
        formData.append('highRes', values.file);
      }
      formData.append('project_id', props.projectId);
      formData.append('unitplan_id', props.unitplanId);
      const response = await EditUnitplan(formData);
      if (response._id){
        emit('addNewRow', response);
      }
      if (response.thumbnail){
        thumbnail.value=response.thumbnail;
      }
      loader.value = false;
    }
  }
  isEdited.value = false;
}

const toggleSelect = (field) => {
  selectedFields.value = { [field]: true };
};

onClickOutside(fieldRefs, () => {
  selectedFields.value = {};
});

const startUpload = () => {
  uploadProgress.value = 0;
  uploadInterval = setInterval(() => {
    if (uploadProgress.value < 100) {
      uploadProgress.value += 10;
    }
    if (uploadProgress.value === 100) {
      clearInterval(uploadInterval);
      trackChanges();
    }
  }, 500);
};

const cancelUpload = () => {
  thumbnail.value = null;
  uploadProgress.value = 0;
  clearInterval(uploadInterval);
};

watch(() => props.uploadedFiles, (newFile) => {
  if (newFile) {
    thumbnail.value = newFile;
    startUpload();
  }
}, { immediate: true });

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(initialValues.value._id);
    copied.value = true;
    setTimeout(() => (copied.value = false), 1000);
  } catch (e) {
    console.error('Failed to copy ID:', e);
  }
};

onMounted(() => {
  if (props?.unitplan?.isNew) {
    nextTick(() => {
      if (rowRef.value) {
        rowRef.value.scrollIntoView({ behavior: 'smooth', block: 'center' });
        const input = rowRef.value.querySelector('input');
        if (input) {
          input.focus();
        }
      }
    });
  }
});

</script>

<template>
  <Form :validation-schema="unitplanSchema" :initial-values="initialValues" @submit="handleSubmit" class="mb-0" v-slot="{ errors }">
    <div ref="rowRef" class="flex w-fit" :class="(typeof index === 'string' && index.includes('_') ? parseInt(index.split('_')[0]) : index) % 2 === 0 ? 'bg-white' : 'bg-gray-50'">
        <div class="py-2 text-sm flex flex-col items-center justify-center w-[1.5rem] px-2 sticky left-0 z-10"
          :class="{ 'bg-[#EBF5FF]': selectedFields['accordion'], 'bg-inherit': !selectedFields['accordion'] }"
          @click="toggleSelect('accordion')"
          ref="fieldRefs"
        >
          <svg v-if="initialValues.unit_type === 'villa'" @click="initialValues.unit_type === 'villa' && emit('toggleAccordion', initialValues._id)" :class="{ 'rotate-90 !fill-[#1C64F2]': isAccordionOpen, 'fill-black' : !isAccordionOpen }" class="cursor-pointer transition-transform w-3 h-3" xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 10 10">
            <path d="M7.5 4.98894C7.49995 5.22762 7.4123 5.4565 7.25633 5.62524L3.92981 9.22524C3.8531 9.3112 3.76133 9.37976 3.65987 9.42693C3.55841 9.4741 3.44928 9.49893 3.33886 9.49997C3.22843 9.501 3.11892 9.47823 3.01672 9.43298C2.91452 9.38773 2.82166 9.3209 2.74358 9.2364C2.6655 9.15189 2.60374 9.05141 2.56193 8.9408C2.52011 8.83019 2.49907 8.71168 2.50003 8.59218C2.50099 8.47268 2.52393 8.35458 2.56752 8.24478C2.6111 8.13497 2.67446 8.03566 2.75389 7.95264L5.49245 4.98894L2.75389 2.02524C2.6024 1.8555 2.51858 1.62816 2.52047 1.39218C2.52237 1.1562 2.60983 0.930472 2.76402 0.763605C2.91821 0.596738 3.12679 0.502085 3.34484 0.500034C3.56289 0.497984 3.77296 0.588698 3.92981 0.752641L7.25633 4.35264C7.4123 4.52138 7.49995 4.75027 7.5 4.98894Z"/>
          </svg>
        </div>
        <div class="py-2 text-black text-sm relative flex items-center w-[4rem] ml-1 px-2" :class="!isNew && 'cursor-pointer'" @click="copyToClipboard">
            {{ initialValues.unit_type !== 'villa_floor' && !isNew ? '..' + initialValues._id.slice(-4) : '' }}
            <span
              v-if="copied && !isNew"
              class="absolute -top-1 left-1/2 transform -translate-x-1/2 text-xs text-black"
            >
              Copied!
            </span>
        </div>
        <div class="py-2 text-sm relative flex flex-col items-center w-[9rem] px-2"
        :class="{ 'bg-[#EBF5FF]': selectedFields['unit_type'], 'bg-red-50': errors.unit_type }"
        @click="initialValues.unit_type !== 'villa_floor' && toggleSelect('unit_type')"
        ref="fieldRefs"
        >
          <div class="w-full" v-if="initialValues.unit_type !== 'villa_floor'">
            <Field name="unit_type" v-slot="{ field }" v-model="initialValues.unit_type" class="select-primary1">
              <Multiselect
                v-bind="field"
                v-model="initialValues.unit_type"
                :options="unitTypeList"
                :searchable="false"
                placeholder="Choose"
                class="tableSelect h-fit !capitalize placeholder:text-left typebold"
                :show-labels="false"
                :class="{ 'selected-field': selectedFields['unit_type'], 'error-placeholder error-dropdown': errors?.unit_type }"
                @update:modelValue="trackChanges"
              >
              </Multiselect>
            </Field>
          </div>
        </div>

        <!-- NAME -->
        <div class="py-2 text-black text-sm relative flex items-center w-[8rem] px-2"
        :class="{ 'bg-[#EBF5FF]': selectedFields['name'], 'bg-red-50': errors.name }"
        @click="toggleSelect('name')" ref="fieldRefs"
        >
              <div class="w-full" :class="{ 'tableSelect': selectedFields['name'], 'error-tableSelect': errors?.name }">
                <Field
                    type="text"
                    name="name"
                    id="name"
                    v-model="initialValues.name"
                    class="w-full placeholder:text-left bg-inherit"
                    placeholder="Enter"
                    :class="{ 'selected-field': selectedFields.name, 'placeholder:text-red-700': errors?.name }"
                    @input="trackChanges"
                />
              </div>
        </div>

        <!-- BEDROOM -->
        <div class="py-2 text-sm relative flex items-center w-[11rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['bedrooms'], 'bg-red-50': errors.bedrooms }"
          @click="toggleSelect('bedrooms')"
          ref="fieldRefs"
        >
            <div class="w-full">
                <Field name="bedrooms" v-slot="{ field }" v-model="initialValues.bedrooms" class="select-primary1">
                  <Multiselect
                    v-bind="field"
                    v-model="initialValues.bedrooms"
                    :options="unitplanTypeList"
                    :searchable="false"
                    :show-labels="false"
                    placeholder="Choose"
                    class="tableSelect h-fit !capitalize"
                    :class="{ 'selected-field': selectedFields['bedrooms'], 'error-placeholder error-dropdown': errors?.bedrooms }"
                    @update:modelValue="trackChanges"
                  >
                  </Multiselect>
                </Field>
            </div>
        </div>

        <!-- MEASUREMENT TYPE -->
        <div class="py-2 text-sm relative flex items-center w-[11rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['measurement_type'], 'bg-red-50': errors.measurement_type }"
          @click="toggleSelect('measurement_type')"
          ref="fieldRefs"
        >
            <div class="w-full">
              <Field name="measurement_type" v-slot="{ field }" v-model="initialValues.measurement_type" class="select-primary1">
                <Multiselect
                  v-bind="field"
                  v-model="initialValues.measurement_type"
                  :options="measurementTypeList"
                  :searchable="false"
                  :show-labels="false"
                  placeholder="Choose"
                  class="tableSelect h-fit !capitalize"
                  :class="{ 'selected-field': selectedFields['measurement_type'], 'error-placeholder error-dropdown': errors?.measurement_type }"
                  @update:modelValue="trackChanges"
                >
                </Multiselect>
              </Field>
            </div>
        </div>

        <!-- MEASUREMENT -->
        <div class="py-2 text-black text-sm relative flex items-center w-[8rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['measurement'], 'bg-red-50': errors.measurement }"
          @click="toggleSelect('measurement')"
          ref="fieldRefs"
        >
            <div class="w-full" :class="{ 'tableSelect': selectedFields['measurement'], 'error-tableSelect': errors?.measurement }">
                <Field
                  type="number"
                  name="measurement"
                  :value="initialValues.measurement === 0 ? '' : initialValues.measurement"
                  id="measurement"
                  autocomplete="measurement"
                  placeholder="Enter"
                  class="w-full placeholder:text-left bg-inherit"
                  :class="{ 'selected-field': selectedFields['measurement'], 'placeholder:text-red-700': errors?.measurement }"
                  @input="trackChanges"
                />
            </div>
        </div>

        <!-- IMAGE -->
        <div class="py-2 text-black text-sm relative flex items-center w-[18rem] px-2" :class="{ 'bg-[#EBF5FF]': selectedFields['thumbnail'], 'bg-red-50': errors.file }" @click="toggleSelect('thumbnail')" ref="fieldRefs">
          <div class="w-full">
            <Field id="file" v-slot="{ field }" name="file" v-model="thumbnail" v-if="initialValues.unit_type !== 'villa'">
              <div v-bind="field" v-if="thumbnail && thumbnail?.constructor?.name !== 'File'" class="flex items-center justify-between w-full">
                <div>
                  <img :src="thumbnail" alt="Preview" class="w-10 h-10 rounded-lg" loading="lazy" />
                </div>
                <div>
                    <p class="text-sm">
                      {{ truncateString(extractFirebaseStoragePath(thumbnail,'filename'),15) }}
                    </p>
                </div>
                <div class="flex items-center gap-2">
                  <div>
                    <button class="p-1.5 h-7 hover:bg-gray-100 rounded-lg"
                      @click.prevent="()=>$emit('openImageModal', true, thumbnail)"
                    >
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <g id="eye">
                          <path id="Vector"
                            d="M8 2C3.6896 2 0 6.57371 0 8C0 9.49314 2.8368 14 8 14C13.1632 14 16 9.49314 16 8C16 6.57371 12.3104 2 8 2ZM8 10.5714C7.52532 10.5714 7.06131 10.4206 6.66663 10.1381C6.27195 9.85551 5.96434 9.45391 5.78269 8.98404C5.60104 8.51418 5.55351 7.99715 5.64612 7.49834C5.73872 6.99953 5.9673 6.54135 6.30294 6.18173C6.63859 5.8221 7.06623 5.5772 7.53178 5.47798C7.99734 5.37876 8.4799 5.42968 8.91844 5.62431C9.35698 5.81893 9.73181 6.14852 9.99553 6.57139C10.2592 6.99426 10.4 7.49142 10.4 8C10.4 8.68199 10.1471 9.33604 9.69706 9.81827C9.24697 10.3005 8.63652 10.5714 8 10.5714Z"
                            fill="#6B7280" />
                        </g>
                      </svg>
                    </button>
                    <button
                      class="p-1.5 h-7 hover:bg-gray-100 rounded-lg"
                      @click.prevent="handleDownloadFile(thumbnail, previewFileName ?? extractFirebaseStoragePath(thumbnail, 'filename'))"
                    >
                      <svg width="16" height="16" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="download" clip-path="url(#clip0_716_9964)">
                        <g id="Vector">
                            <path
                            d="M10.2949 5.236C10.1636 5.10141 9.98561 5.02579 9.8 5.02579C9.61438 5.02579 9.43637 5.10141 9.3051 5.236L7.7 6.88226V0.717949C7.7 0.527537 7.62625 0.344924 7.49497 0.210282C7.3637 0.0756408 7.18565 0 7 0C6.81435 0 6.6363 0.0756408 6.50503 0.210282C6.37375 0.344924 6.3 0.527537 6.3 0.717949V6.88226L4.6949 5.236C4.63033 5.16743 4.55309 5.11273 4.46768 5.07511C4.38228 5.03748 4.29043 5.01767 4.19748 5.01685C4.10454 5.01602 4.01236 5.03418 3.92633 5.07028C3.8403 5.10638 3.76215 5.15969 3.69642 5.2271C3.6307 5.29451 3.57872 5.37467 3.54352 5.4629C3.50833 5.55114 3.49062 5.64568 3.49142 5.74101C3.49223 5.83633 3.51154 5.93054 3.54823 6.01814C3.58492 6.10573 3.63824 6.18495 3.7051 6.25118L6.5051 9.12297C6.57012 9.18983 6.64737 9.24288 6.73241 9.27907C6.81746 9.31527 6.90863 9.3339 7.0007 9.3339C7.09277 9.3339 7.18394 9.31527 7.26899 9.27907C7.35403 9.24288 7.43128 9.18983 7.4963 9.12297L10.2963 6.25118C10.4273 6.11635 10.5008 5.93367 10.5006 5.74329C10.5003 5.55292 10.4263 5.37045 10.2949 5.236Z"
                            fill="#6B7280" />
                            <path
                            d="M12.6 8.25641H10.815L8.7325 10.3923C8.50499 10.6257 8.2349 10.8108 7.93763 10.9371C7.64037 11.0634 7.32176 11.1284 7 11.1284C6.67824 11.1284 6.35963 11.0634 6.06237 10.9371C5.7651 10.8108 5.49501 10.6257 5.2675 10.3923L3.185 8.25641H1.4C1.0287 8.25641 0.672601 8.40769 0.41005 8.67698C0.1475 8.94626 0 9.31148 0 9.69231V12.5641C0 12.9449 0.1475 13.3102 0.41005 13.5794C0.672601 13.8487 1.0287 14 1.4 14H12.6C12.9713 14 13.3274 13.8487 13.5899 13.5794C13.8525 13.3102 14 12.9449 14 12.5641V9.69231C14 9.31148 13.8525 8.94626 13.5899 8.67698C13.3274 8.40769 12.9713 8.25641 12.6 8.25641Z"
                            fill="#6B7280" />
                        </g>
                        </g>
                      </svg>
                    </button>
                    <button class="p-1.5 h-7 hover:bg-gray-100 rounded-lg"
                      @click.prevent="$emit('uploadModal', initialValues._id, index, parentID)"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M20.5 5.5H9.5C5.78672 5.5 3 8.18503 3 12" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M3.5 18.5H14.5C18.2133 18.5 21 15.815 21 12" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M18.5 3C18.5 3 21 4.84122 21 5.50002C21 6.15882 18.5 8 18.5 8" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M5.49998 16C5.49998 16 3.00001 17.8412 3 18.5C2.99999 19.1588 5.5 21 5.5 21" stroke="#6B7280" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
              <div v-else class="upload-btn-wrapper w-full">
                <template v-if="(isNew || initialValues.thumbnail)">
                  <div class="flex items-center justify-between" v-if="thumbnail?.constructor?.name === 'File'">
                    <p class="text-sm" v-if="uploadProgress <= 0 || uploadProgress >= 100">
                      {{ truncateString(thumbnail.name, 20) }}
                    </p>
                  </div>
                  <button v-else @click.prevent="$emit('uploadModal', initialValues._id, index, parentID)" title="Upload" class="text-xs font-medium m-0 flex items-center gap-2.5 " :class="{ 'bg-[#EBF5FF]': selectedFields['thumbnail'],'text-red-700 bg-red-50': errors?.file, 'text-[#6b7280]': !errors?.file }">
                    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" :class="{ 'fill-red-700': errors?.file, 'fill-[#6B7280]': !errors?.file }">
                      <g id="download" clip-path="url(#clip0_716_9964)">
                        <g id="Vector">
                          <path
                            d="M10.2949 5.236C10.1636 5.10141 9.98561 5.02579 9.8 5.02579C9.61438 5.02579 9.43637 5.10141 9.3051 5.236L7.7 6.88226V0.717949C7.7 0.527537 7.62625 0.344924 7.49497 0.210282C7.3637 0.0756408 7.18565 0 7 0C6.81435 0 6.6363 0.0756408 6.50503 0.210282C6.37375 0.344924 6.3 0.527537 6.3 0.717949V6.88226L4.6949 5.236C4.63033 5.16743 4.55309 5.11273 4.46768 5.07511C4.38228 5.03748 4.29043 5.01767 4.19748 5.01685C4.10454 5.01602 4.01236 5.03418 3.92633 5.07028C3.8403 5.10638 3.76215 5.15969 3.69642 5.2271C3.6307 5.29451 3.57872 5.37467 3.54352 5.4629C3.50833 5.55114 3.49062 5.64568 3.49142 5.74101C3.49223 5.83633 3.51154 5.93054 3.54823 6.01814C3.58492 6.10573 3.63824 6.18495 3.7051 6.25118L6.5051 9.12297C6.57012 9.18983 6.64737 9.24288 6.73241 9.27907C6.81746 9.31527 6.90863 9.3339 7.0007 9.3339C7.09277 9.3339 7.18394 9.31527 7.26899 9.27907C7.35403 9.24288 7.43128 9.18983 7.4963 9.12297L10.2963 6.25118C10.4273 6.11635 10.5008 5.93367 10.5006 5.74329C10.5003 5.55292 10.4263 5.37045 10.2949 5.236Z"/>
                          <path
                            d="M12.6 8.25641H10.815L8.7325 10.3923C8.50499 10.6257 8.2349 10.8108 7.93763 10.9371C7.64037 11.0634 7.32176 11.1284 7 11.1284C6.67824 11.1284 6.35963 11.0634 6.06237 10.9371C5.7651 10.8108 5.49501 10.6257 5.2675 10.3923L3.185 8.25641H1.4C1.0287 8.25641 0.672601 8.40769 0.41005 8.67698C0.1475 8.94626 0 9.31148 0 9.69231V12.5641C0 12.9449 0.1475 13.3102 0.41005 13.5794C0.672601 13.8487 1.0287 14 1.4 14H12.6C12.9713 14 13.3274 13.8487 13.5899 13.5794C13.8525 13.3102 14 12.9449 14 12.5641V9.69231C14 9.31148 13.8525 8.94626 13.5899 8.67698C13.3274 8.40769 12.9713 8.25641 12.6 8.25641ZM10.85 12.5641C10.6423 12.5641 10.4393 12.5009 10.2667 12.3826C10.094 12.2643 9.9594 12.0961 9.87993 11.8993C9.80046 11.7025 9.77966 11.486 9.82018 11.2771C9.86069 11.0682 9.96069 10.8763 10.1075 10.7257C10.2544 10.5751 10.4415 10.4725 10.6452 10.4309C10.8488 10.3894 11.06 10.4107 11.2518 10.4922C11.4437 10.5737 11.6077 10.7118 11.723 10.8889C11.8384 11.066 11.9 11.2742 11.9 11.4872C11.9 11.7728 11.7894 12.0467 11.5925 12.2487C11.3955 12.4506 11.1285 12.5641 10.85 12.5641Z"/>
                        </g>
                      </g>
                      <defs>
                        <clipPath id="clip0_716_9964">
                          <rect width="14" height="14" fill="white" />
                        </clipPath>
                      </defs>
                    </svg>
                    Upload
                  </button>
                </template>
              </div>

              <div class="flex gap-2">
                <div v-if="uploadProgress > 0 && uploadProgress < 100" class="flex flex-col w-full gap-1">
                  <div class="flex items-center justify-between">
                    <div class="flex gap-2 items-center">
                      <svg v-if="uploadProgress > 0 && uploadProgress < 100" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                        <path d="M16.3181 0H8.95768V5C8.95768 6.103 8.02331 7 6.87435 7H1.66602V18C1.66602 19.103 2.56914 20 3.6806 20H16.3191C17.4296 20 18.3327 19.103 18.3327 18V2C18.3327 0.897 17.4296 0 16.3181 0ZM12.6035 6C13.466 6 14.166 6.672 14.166 7.5C14.166 8.328 13.466 9 12.6035 9C11.741 9 11.041 8.328 11.041 7.5C11.041 6.672 11.741 6 12.6035 6ZM14.9171 16.515C14.7285 16.816 14.39 17 14.0233 17H5.68997C5.33268 17 5.00039 16.824 4.80872 16.534C4.61706 16.244 4.59518 15.88 4.74935 15.57L7.49622 10.039C7.67435 9.681 8.0306 9.458 8.47227 9.47C8.88581 9.484 9.25247 9.731 9.4056 10.101L10.6368 13.082L11.2004 12.085C11.3837 11.762 11.6858 11.56 12.1202 11.563C12.5035 11.564 12.8546 11.768 13.0358 12.093L14.9441 15.531C15.115 15.84 15.1046 16.213 14.9171 16.515Z" fill="#9CA3AF"/>
                        <path d="M6.87435 5V0.13C6.37227 0.268 5.90664 0.514 5.52747 0.879L2.58164 3.707C2.20247 4.071 1.94622 4.518 1.80143 5H6.87435Z" fill="#9CA3AF"/>
                      </svg>
                      <p v-if="thumbnail" class="text-sm text-gray-700">
                        {{ truncateString(thumbnail.name, 20) }}
                      </p>
                    </div>
                    <span class="text-xs text-gray-500">{{ uploadProgress }}%</span>
                  </div>

                  <div class="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div class="bg-blue-500 h-2 rounded-full" :style="{ width: uploadProgress + '%' }"></div>
                  </div>
                </div>

                <div class="flex justify-end mt-1" v-if="uploadProgress > 0 && uploadProgress < 100">
                  <button @click.prevent="cancelUpload">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 0C6.41775 0 4.87103 0.469192 3.55544 1.34824C2.23985 2.22729 1.21447 3.47672 0.608967 4.93853C0.00346627 6.40034 -0.15496 8.00887 0.153721 9.56072C0.462403 11.1126 1.22433 12.538 2.34315 13.6569C3.46197 14.7757 4.88743 15.5376 6.43928 15.8463C7.99113 16.155 9.59966 15.9965 11.0615 15.391C12.5233 14.7855 13.7727 13.7602 14.6518 12.4446C15.5308 11.129 16 9.58225 16 8C15.9977 5.87898 15.1541 3.8455 13.6543 2.34572C12.1545 0.845932 10.121 0.00232928 8 0ZM10.9656 9.8344C11.042 9.90819 11.103 9.99647 11.1449 10.0941C11.1868 10.1917 11.2089 10.2967 11.2098 10.4029C11.2107 10.5091 11.1905 10.6144 11.1503 10.7128C11.11 10.8111 11.0506 10.9004 10.9755 10.9755C10.9004 11.0506 10.8111 11.11 10.7128 11.1503C10.6144 11.1905 10.5091 11.2107 10.4029 11.2098C10.2967 11.2089 10.1917 11.1868 10.0941 11.1449C9.99648 11.103 9.9082 11.042 9.8344 10.9656L8 9.1312L6.1656 10.9656C6.01472 11.1113 5.81264 11.192 5.60288 11.1901C5.39312 11.1883 5.19247 11.1042 5.04415 10.9559C4.89582 10.8075 4.81169 10.6069 4.80986 10.3971C4.80804 10.1874 4.88868 9.98528 5.0344 9.8344L6.8688 8L5.0344 6.1656C4.88868 6.01472 4.80804 5.81263 4.80986 5.60288C4.81169 5.39312 4.89582 5.19247 5.04415 5.04414C5.19247 4.89582 5.39312 4.81168 5.60288 4.80986C5.81264 4.80804 6.01472 4.88867 6.1656 5.0344L8 6.8688L9.8344 5.0344C9.98528 4.88867 10.1874 4.80804 10.3971 4.80986C10.6069 4.81168 10.8075 4.89582 10.9559 5.04414C11.1042 5.19247 11.1883 5.39312 11.1901 5.60288C11.192 5.81263 11.1113 6.01472 10.9656 6.1656L9.1312 8L10.9656 9.8344Z"
                        fill="#6B7280" />
                    </svg>
                  </button>
                </div>
              </div>
            </Field>
            <p v-else class="text-[#9CA3AF]">Not Applicable</p>
          </div>
        </div>

        <!-- STYLE -->
        <div class="py-2 text-sm relative flex items-center w-[10rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['style'], 'bg-red-50': errors.style }"
          @click="toggleSelect('style')"
          ref="fieldRefs"
        >
            <div class="w-full">
                <Field  name="style" v-slot="{ style }" :model-value="styleRef">
                    <Multiselect
                        v-bind="style"
                        v-model="styleRef"
                        tag-placeholder="Add"
                        placeholder="Search"
                        label="name"
                        track-by="name"
                        :multiple="false"
                        :taggable="true"
                        @tag="addTag"
                        :show-labels="false"
                        :options="styleList" class="tableSelect h-fit !capitalize" maxHeight="250"
                        :class="{ 'selected-field': selectedFields['style'], 'error-placeholder error-dropdown': errors?.style }"
                        @update:modelValue="trackChanges"
                      >
                    </Multiselect>
                </Field>
            </div>
        </div>

        <!-- TYPE -->
        <div class="py-2 text-sm relative flex items-center  w-[10rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['type'], 'bg-red-50': errors.type }"
          @click="toggleSelect('type')"
          ref="fieldRefs"
        >
            <div class="w-full">
                <Field  name="type" v-slot="{ type }" :model-value="unitplanTypeRef">
                    <Multiselect
                        v-bind="type"
                        v-model="unitplanTypeRef"
                        tag-placeholder="Add"
                        placeholder="Search"
                        label="name"
                        track-by="name"
                        :multiple="false"
                        :taggable="true"
                        :show-labels="false"
                        @tag="addTagUnitPlanType"
                        :options="unitplanTypeListRef" class="tableSelect h-fit !capitalize" maxHeight="250"
                        :class="{ 'selected-field': selectedFields['type'], 'error-placeholder error-dropdown': errors?.type }"
                        @update:modelValue="trackChanges"
                        >
                    </Multiselect>
                </Field>
            </div>
        </div>

        <!-- BATHROOM -->
        <div class="py-2 text-black text-sm relative flex items-center w-[6rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['bathrooms'], 'bg-red-50': errors.bathrooms }"
          @click="toggleSelect('bathrooms')"
          ref="fieldRefs"
        >
            <div class="w-full" :class="{ 'tableSelect': selectedFields['bathrooms'], 'error-tableSelect': errors?.bathrooms }">
                <Field
                    type="number"
                    name="bathrooms"
                    id="bathrooms"
                    v-model="initialValues.bathrooms"
                    autocomplete="bathrooms"
                    placeholder="Enter"
                    class="w-full placeholder:text-left bg-inherit"
                    :class="{ 'selected-field': selectedFields['bathrooms'], 'placeholder:text-red-700': errors?.bathrooms }"
                    @input="trackChanges"
                />
            </div>
        </div>

        <!-- BALCONY TYPE -->
        <div class="py-2 text-sm relative flex items-center w-[9rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['balcony_measurement_type'], 'bg-red-50': errors.balcony_measurement_type }"
          @click="toggleSelect('balcony_measurement_type')"
          ref="fieldRefs"
        >
            <div class="w-full">
                <Field name="balcony_measurement_type" v-slot="{ field }" v-model="initialValues.balcony_measurement_type" class="select-primary1">
                  <Multiselect
                    v-bind="field"
                    v-model="initialValues.balcony_measurement_type"
                    :options="measurementTypeList"
                    :searchable="false"
                    :show-labels="false"
                    placeholder="Choose"
                    class="tableSelect h-fit !capitalize"
                    :class="{ 'selected-field': selectedFields['balcony_measurement_type'], 'error-placeholder error-dropdown': errors?.balcony_measurement_type }"
                    @update:modelValue="trackChanges"
                  >
                  </Multiselect>
                </Field>
            </div>
        </div>

        <!-- BALCONY -->
        <div class="py-2 text-black text-sm relative flex items-center w-[8rem] pl-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['balcony_measurement'], 'bg-red-50': errors.balcony_measurement }"
          @click="toggleSelect('balcony_measurement')"
          ref="fieldRefs"
        >
            <div class="w-full" :class="{ 'tableSelect': selectedFields['balcony_measurement'], 'error-tableSelect': errors?.balcony_measurement }">
                <Field
                    type="number"
                    v-model="initialValues.balcony_measurement"
                    name="balcony_measurement"
                    id="balcony_measurement"
                    autocomplete="balcony_measurement"
                    placeholder="Enter"
                    class="w-full placeholder:text-left bg-inherit"
                    :class="{ 'selected-field': selectedFields['balcony_measurement'], 'placeholder:text-red-700': errors?.balcony_measurement }"
                    @input="trackChanges"
                />
            </div>
        </div>

        <!-- EXTERIOR TYPE -->
        <div class="py-2 text-sm relative flex items-center w-[9rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['exterior_type'], 'bg-red-50': errors.exterior_type }"
          @click="toggleSelect('exterior_type')"
          ref="fieldRefs"
        >
            <div class="w-full">
                <Field name="exterior_type" v-slot="{ field }" v-model="initialValues.exterior_type" class="select-primary1">
                  <Multiselect
                    v-bind="field"
                    v-model="initialValues.exterior_type"
                    :options="exteriorType"
                    :searchable="false"
                    :show-labels="false"
                    placeholder="Choose"
                    class="tableSelect h-fit !capitalize"
                    :class="{ 'selected-field': selectedFields['exterior_type'], 'error-placeholder error-dropdown': errors?.exterior_type }"
                    @update:modelValue="trackChanges"
                  >
                  </Multiselect>
                </Field>
            </div>
        </div>

        <!-- SCENE ID -->
        <div class="py-2 text-sm relative flex items-center w-[12.5rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['scene_id'], 'bg-red-50': errors.scene_id }"
          @click="toggleSelect('scene_id')"
          ref="fieldRefs"
        >
            <div class="w-full">
                <Field v-if="initialValues.exterior_type === 'scene' && projectStore.scenes" name="scene_id" v-slot="{ field }" v-model="initialValues.scene_id" class="select-primary1">
                  <Multiselect
                    v-bind="field"
                    v-model="initialValues.scene_id"
                    :options="sceneOptions"
                    :custom-label="(val) => projectStore.scenes[val].sceneData.name"
                    :show-labels="false"
                    :searchable="false"
                    placeholder="Choose"
                    label="name"
                    class="tableSelect h-fit !capitalize"
                    :class="{ 'selected-field': selectedFields['scene_id'], 'error-placeholder error-dropdown': errors?.scene_id }"
                    @update:modelValue="trackChanges"
                  >
                  </Multiselect>
                </Field>
                <p v-else class="text-[#9CA3AF] pl-3">Not Applicable</p>
            </div>
        </div>

        <!-- GALLERY ID -->
        <div class="py-2 text-sm relative flex items-center w-[12.5rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['gallery_id'], 'bg-red-50': errors.gallery_id }"
          @click="toggleSelect('gallery_id')"
          ref="fieldRefs"
        >
            <div class="w-full">
                <Field
                    v-if="initialValues.exterior_type === 'gallery'"
                    name="gallery_id"
                    v-model="selectedGalleryItems"
                    class="sr-only select-primary1"
                    v-slot="{ field }"
                >
                  <Multiselect
                    v-bind="field"
                    v-model="selectedGalleryItems"
                    :options="galleryItems"
                    :searchable="false"
                    :multiple="true"
                    placeholder="Choose"
                    :close-on-select="false"
                    :show-labels="true"
                    label="name" track-by="_id"
                    class="tableSelect h-fit !capitalize"
                    :class="{ 'selected-field': selectedFields['gallery_id'], 'error-placeholder error-dropdown': errors?.gallery_id }"
                    @update:modelValue="trackChanges"
                  >
                    <template #selection="{ values }">
                      <span class="multiselect__single" v-if="values.length">
                        {{ values[0]?.name }}
                        <span v-if="values.length > 1">+{{ values.length - 1 }} more</span>
                      </span>
                    </template>
                  </Multiselect>
                </Field>
                <p v-else class="text-[#9CA3AF] px-2">Not Applicable</p>
            </div>
        </div>

        <!-- TOUR -->
        <div class="py-2 text-sm relative flex items-center w-[11rem] px-2"
          :class="{ 'bg-[#EBF5FF]': selectedFields['tour_id'], 'bg-red-50': errors.tour_id }"
          @click="toggleSelect('tour_id')"
          ref="fieldRefs"
        >
            <div class="w-full">
                <Field v-if="projectStore.virtualtours" name="tour_id" v-slot="{ field }" v-model="initialValues.tour_id" class="select-primary1">
                  <Multiselect
                    v-bind="field"
                    v-model="initialValues.tour_id"
                    :options="tourOptions"
                    :custom-label="(val) => projectStore.virtualtours?.[val]?.name || projectStore.virtualtours?.[val]?.tour_name"
                    :show-labels="false"
                    :placeholder="'Choose'"
                    label="name"
                    class="tableSelect h-fit !capitalize"
                    :class="{ 'selected-field': selectedFields['tour_id'], 'error-placeholder error-dropdown': errors?.tour_id }"
                    @update:modelValue="trackChanges"
                  >
                  </Multiselect>
                </Field>
            </div>
        </div>

        <!-- is_residential -->
        <div class="py-2 text-black text-sm relative flex items-center w-[9rem] px-2" :class="{ 'bg-[#EBF5FF]': selectedFields['is_residential'] }" @click="toggleSelect('is_residential')" ref="fieldRefs">
          <div class="w-full">
            <Field name="is_residential" v-slot="{ field }" class="select-primary1">
              <Multiselect
                id="23"
                v-bind="field"
                v-model="initialValues.is_residential"
                :options="['Yes', 'No']"
                :searchable="false"
                :show-labels="false"
                placeholder="Choose"
                class="tableSelect h-fit !capitalize"
                :class="{ 'selected-field': selectedFields['is_residential'] }"
                @update:modelValue="trackChanges"
              >
              </Multiselect>
            </Field>
          </div>
        </div>
        <!-- is_furnished -->
        <div class="py-2 text-sm relative flex items-center w-[9rem] px-2" :class="{ 'bg-[#EBF5FF]': selectedFields['is_furnished'] }" @click="toggleSelect('is_furnished')" ref="fieldRefs">
          <div class="w-full">
            <Field name="is_furnished" v-slot="{ field }" class="select-primary1">
              <Multiselect
                id="33"
                v-bind="field"
                v-model="initialValues.is_furnished"
                :options="['Yes', 'No']"
                :searchable="false"
                :show-labels="false"
                placeholder="Choose"
                class="tableSelect h-fit !capitalize"
                :class="{ 'selected-field': selectedFields['is_furnished'] }"
                @update:modelValue="trackChanges"
              >
              </Multiselect>
            </Field>
          </div>
        </div>

        <div class="py-2 text-sm flex justify-end items-center w-36 sticky right-0 pr-2 bg-inherit">
              <div class="flex items-center justify-start gap-[22px]">
                <div v-if="isEdited">
                    <Button id="submit" type="submit" title="Save" class="!px-3 !py-2 bg-blue-600 !justify-center !items-center !gap-2 text-white">
                      <template v-slot:svg>
                        <div v-if="loader" class="loader !h-5 !w-5 !border-2"></div>
                      </template>
                    </Button>
                </div>
                <div class="h-7 flex justify-center items-center px-1.5 bg-gray-100 rounded-lg hover:cursor-pointer">
                  <span @click.prevent="$emit('deleteRow',initialValues._id, parentID)" v-if="isNew">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_1362_13369)">
                        <path d="M15.1111 0.666992H0.888889C0.397969 0.666992 0 1.05326 0 1.52974L0 3.25523C0 3.73171 0.397969 4.11797 0.888889 4.11797H15.1111C15.602 4.11797 16 3.73171 16 3.25523V1.52974C16 1.05326 15.602 0.666992 15.1111 0.666992Z" fill="#6B7280"/>
                        <path d="M0.888889 5.84346V13.6082C0.888889 14.0658 1.07619 14.5047 1.40959 14.8283C1.74299 15.1519 2.19517 15.3337 2.66667 15.3337H13.3333C13.8048 15.3337 14.257 15.1519 14.5904 14.8283C14.9238 14.5047 15.1111 14.0658 15.1111 13.6082V5.84346H0.888889ZM10.6667 8.4317C10.6667 8.66051 10.573 8.87995 10.4063 9.04175C10.2396 9.20355 10.0135 9.29444 9.77778 9.29444H6.22222C5.98647 9.29444 5.76038 9.20355 5.59368 9.04175C5.42698 8.87995 5.33333 8.66051 5.33333 8.4317V7.56895C5.33333 7.34014 5.42698 7.1207 5.59368 6.9589C5.76038 6.7971 5.98647 6.70621 6.22222 6.70621C6.45797 6.70621 6.68406 6.7971 6.85076 6.9589C7.01746 7.1207 7.11111 7.34014 7.11111 7.56895H8.88889C8.88889 7.34014 8.98254 7.1207 9.14924 6.9589C9.31594 6.7971 9.54203 6.70621 9.77778 6.70621C10.0135 6.70621 10.2396 6.7971 10.4063 6.9589C10.573 7.1207 10.6667 7.34014 10.6667 7.56895V8.4317Z" fill="#6B7280"/>
                      </g>
                      <defs>
                        <clipPath id="clip0_1362_13369">
                          <rect width="16" height="16" fill="white"/>
                        </clipPath>
                      </defs>
                    </svg>
                  </span>
                  <span v-else @click.prevent="$emit('moveToTrashModal',initialValues._id, index, parentID)">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_1362_13369)">
                        <path d="M15.1111 0.666992H0.888889C0.397969 0.666992 0 1.05326 0 1.52974L0 3.25523C0 3.73171 0.397969 4.11797 0.888889 4.11797H15.1111C15.602 4.11797 16 3.73171 16 3.25523V1.52974C16 1.05326 15.602 0.666992 15.1111 0.666992Z" fill="#6B7280"/>
                        <path d="M0.888889 5.84346V13.6082C0.888889 14.0658 1.07619 14.5047 1.40959 14.8283C1.74299 15.1519 2.19517 15.3337 2.66667 15.3337H13.3333C13.8048 15.3337 14.257 15.1519 14.5904 14.8283C14.9238 14.5047 15.1111 14.0658 15.1111 13.6082V5.84346H0.888889ZM10.6667 8.4317C10.6667 8.66051 10.573 8.87995 10.4063 9.04175C10.2396 9.20355 10.0135 9.29444 9.77778 9.29444H6.22222C5.98647 9.29444 5.76038 9.20355 5.59368 9.04175C5.42698 8.87995 5.33333 8.66051 5.33333 8.4317V7.56895C5.33333 7.34014 5.42698 7.1207 5.59368 6.9589C5.76038 6.7971 5.98647 6.70621 6.22222 6.70621C6.45797 6.70621 6.68406 6.7971 6.85076 6.9589C7.01746 7.1207 7.11111 7.34014 7.11111 7.56895H8.88889C8.88889 7.34014 8.98254 7.1207 9.14924 6.9589C9.31594 6.7971 9.54203 6.70621 9.77778 6.70621C10.0135 6.70621 10.2396 6.7971 10.4063 6.9589C10.573 7.1207 10.6667 7.34014 10.6667 7.56895V8.4317Z" fill="#6B7280"/>
                      </g>
                      <defs>
                        <clipPath id="clip0_1362_13369">
                          <rect width="16" height="16" fill="white"/>
                        </clipPath>
                      </defs>
                    </svg>
                  </span>
                </div>
              </div>
        </div>
      </div>
    </Form>

  <slot name="accordian"  v-if="isAccordionOpen"></slot>
</template>

<style>
.tableSelect .multiselect__tags{
  border:none;
  padding-right: 30px; /* space for the arrow */
  max-width: 80%;
}
.tableSelect .multiselect__option , .tableSelect .multiselect__option--selected, .tableSelect .multiselect__option:hover, .tableSelect .multiselect__option--selected:hover{
  background-color: transparent !important;
  color: #6B7280 !important;
  font-weight: 100 !important;
}
.tableSelect>.multiselect__select{
  background: none !important;
  right: 20px;
}
.tableSelect .multiselect__single{
  background-color: inherit;
  font-size: 14px;
}
.tabselect .multiselect__option--highlight .multiselect__option::after{
  background: red !important;
}

.tableSelect .multiselect__content-wrapper{
  width: 12rem;
  max-height: 175px !important;
}
.tableSelect .multiselect__element:hover {
  background-color: #EBF5FF;
}
.tableSelect.selected-field .multiselect__single {
  background-color: #EBF5FF;
}

.tableSelect.selected-field .multiselect__input {
  background-color: #EBF5FF;
}

.tableSelect>input{
  background-color: #EBF5FF;
}

.tableSelect .multiselect__tags .multiselect__tags-wrap .multiselect__tag{
  background-color: #1c64f2;
}

.tableSelect .multiselect__select::before{
  width: 7px;
  height: 7px;
}

.tableSelect .multiselect__content-wrapper .multiselect__content .multiselect__element .multiselect__option--highlight::after{
  background-color: #1c64f2;
}

.error-placeholder .multiselect__tags .multiselect__placeholder{
  color: #C81E1E;
}
.error-dropdown .multiselect__select::before {
  border-left-color: #C81E1E;
  border-bottom-color: #C81E1E;
}

.error-tableSelect.selected-field .multiselect__single {
  background-color: #FDF2F2;
}

.error-tableSelect.selected-field .multiselect__input {
  background-color: #FDF2F2;
}

.error-tableSelect>input{
  background-color: #FDF2F2;
}

.typebold .multiselect__tags .multiselect__single{
  font-weight: 500;
}

</style>

<script setup>
import { ref, nextTick, computed, onMounted, onUnmounted} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import noDataFound from '../../../assets/noDataFound.png';
import { ProjectStore } from '../../../store/project';
import Button from '../../common/Button.vue';
import Multiselect from 'vue-multiselect';
import { watch } from 'vue';
import { createHotspots, editHotspots, deleteHotspots } from '../../../api/projects/unitplans';
import {GetVirtualTourList} from '../../../api/projects/tours/index';
import { GetRequest } from '../../../helpers';
import TreeDataDropDown from '../../UIElements/TreeDataDropDown.vue';
import { unitplanListTypes, unitplanScaleTypes, unitplanDescTypes, unitplanHotspotPills } from '../../../enum.ts';
import DeleteModalContent from '../../common/ModalContent/DeleteModalContent.vue';
import Modal from '../../common/Modal/Modal.vue';

const router = useRouter();
// const preview_domain = import.meta.env.VITE_PREVIEW_DOMAIN;
const framePreviewURLVal = ref(null);
const projectStore = ProjectStore();
const route = useRoute();
const projectId = ref(route.params.project_id);
const unitplanId = ref(route.params.unitplan_id);
const isImageRendered = ref(false);
const imageDimensions = ref({ width: 0, height: 0 });
const imageRef = ref(null);
const detailsBoxRef = ref(null);
const existingHotspotsBoxRef = ref({});
const currentDetailsBoxHeight = ref(null);
const parentRef = ref(null);
const fieldName = ref(null);
const errorRef = ref({
  open: false,
  message: null,
});
const newElement = ref(null);
const itemMenuRef = ref({});
const isBottom = ref(false);
const isRight = ref(false);
const toggleListRef = ref({
  anchorElem: null,
  ishide: true,
});
const toggleDescTypeRef = ref({
  anchorElem: unitplanDescTypes.PILL,
});
const selectedPill = ref(null);
const imageList = ref(null);
const selectedImageItem = ref(null);
const groupsList = ref(null);
const selectedGroupItem = ref({
  parent: null,
  child: null,
});
const toggleDragDrop = ref(false);
const dragDropItemsRef = ref({});
const openDeleteModal = ref(false);
const deleteLoader = ref(false);
const selectedScaleItem = ref(null);
const descPillsRef = ref({});

/* Methods */
const refreshData = () => {
  projectStore.RefreshUnitplans(projectId.value);
  projectStore.RefreshUnits(projectId.value);
  projectStore.RefreshBuildings(projectId.value);
};

refreshData(); // initially load the data

// Find the Villa Floor Parent Unitplan Id
const findVillaFloorParentId = (searchId) => {
  console.log("findVillaFloorParentId", searchId);
  const listOfUnitplans = projectStore.unitplans;
  let parentId = null;
  Object.keys(listOfUnitplans).forEach((key) => {
    // Check the floor_unitplans key in the object and should be an array if then length check
    if (Array.isArray(listOfUnitplans[key]?.floor_unitplans) && listOfUnitplans[key]?.floor_unitplans?.length > 0){
      // Check the searchId is contains in the array
      if (listOfUnitplans[key].floor_unitplans.includes(searchId)){
        parentId = listOfUnitplans[key]._id; // return the parent id of villa
      }
    }
  });
  return parentId;
};

// Image Or Group list fetch data
const getTourDetails = () => {
  console.log(projectStore.unitplans?.[unitplanId.value]);

  let upId; // unitplan id

  if (projectStore.unitplans?.[unitplanId.value]?.unit_type === 'villa_floor'){
    upId = findVillaFloorParentId(projectStore.unitplans?.[unitplanId.value]?._id);
  } else {
    upId = unitplanId.value; //
  }

  console.log(upId);
  console.log(projectStore.unitplans[upId]);

  if (upId && projectStore.unitplans?.[upId]?.tour_id) {
    console.log(projectStore.unitplans[upId].tour_id);
    GetVirtualTourList(projectId.value).then((res) => {
      if (res[projectStore.unitplans[upId].tour_id]?.link){
        // Sample link - https://app.propvr.tech/getImages?userid=iRdFhLmW08hOntxWqG2jI5PMSPH2&project=Bayz%20102%20Studio
        const tour_paths = res[projectStore.unitplans[upId].tour_id].link.split("/");
        console.log(tour_paths);
        if (tour_paths[2] === "fp.propvr.tech"){
          // Api call
          GetRequest(`https://app.propvr.tech/getImages?userid=${tour_paths[tour_paths.length-2]}&project=${tour_paths[tour_paths.length-1]}`).then((res) => {
            if (Object.prototype.hasOwnProperty.call(res, 'images') || Object.prototype.hasOwnProperty.call(res, 'groups')){
              // images
              console.log("Yes inside the images or groups");

              if (Object.prototype.hasOwnProperty.call(res, 'images') ? Object.keys(res.images).length > 0 : false){
                const updatedImageList = res.images;
                Object.keys(res.images).forEach((key) => {
                  updatedImageList[key] = {
                    ...updatedImageList[key],
                    _id: key,
                  };
                });
                console.log(updatedImageList);
                imageList.value = updatedImageList;
              }
              // groups
              if (Object.prototype.hasOwnProperty.call(res, 'groups') ? Object.keys(res.groups).length > 0 : false){
                const updatedGroupList = res.groups;
                Object.keys(res.groups).forEach((key) => {
                  if (Object.prototype.hasOwnProperty.call(updatedGroupList[key], 'subcat') ? Object.keys(updatedGroupList[key].subcat).length > 0 : false){
                    // parent with sub categories
                    const updatedSubCatList = updatedGroupList[key].subcat;
                    Object.keys(updatedGroupList[key].subcat).forEach((subKey) => {
                      updatedSubCatList[subKey] = {
                        ...updatedSubCatList[subKey],
                        _id: subKey,
                      };
                    });

                    // update the parent
                    updatedGroupList[key] = {
                      ...updatedGroupList[key],
                      subcat: updatedSubCatList,
                      _id: key,
                    };
                  } else {
                    // parent with no sub categories
                    updatedGroupList[key] = {
                      ...updatedGroupList[key],
                      _id: key,
                    };
                  }
                });
                groupsList.value = updatedGroupList;
              }

              if (imageList.value && Object.keys(imageList.value).length > 0 && groupsList.value && Object.keys(groupsList.value).length > 0) {
                toggleListRef.value.ishide = false;
                toggleListRef.value.anchorElem = unitplanListTypes.IMAGE; // set to image as default
              } else {
                toggleListRef.value.ishide = true;
                toggleListRef.value.anchorElem = null; // reset default
              }

            }
          });
        }
      }
    });
  }

};

// Scale setup
const scaleSetup = () => {
  console.log("--scaleSetup--");

  if (projectStore.unitplans?.[unitplanId.value]?.hotspots){
    console.log("if scaleSetup");

    const hotspots = projectStore.unitplans?.[unitplanId.value]?.hotspots;
    console.log(hotspots);

    const isScaleExist = Object.keys(hotspots).some((key) => hotspots[key].scale); // all previous hotspots

    console.log(isScaleExist);

    if (isScaleExist){

      const getAllScaleValues = Object.keys(hotspots).map((key) => hotspots[key].scale).filter(Boolean); // cut off all the null or undefiend values from hotspots scale values

      // find the majority of the scale and update the scale reference
      // cases : ['a','a','a','a','a','a','c','y','a','a'] --> a ['a','c','d'] --> d ['a','a','c','d','c','d'] --> d

      console.log(getAllScaleValues);

      const groupTheScaleValues = Object.groupBy(getAllScaleValues, (item) => item);
      console.log(groupTheScaleValues);

      let getTheHighestScale = getAllScaleValues[getAllScaleValues.length - 1];
      let maxScaleKey = 0;
      console.log(getTheHighestScale);

      for (const key in groupTheScaleValues) {
        if (groupTheScaleValues[key].length >= maxScaleKey) {
          maxScaleKey = groupTheScaleValues[key].length;
          getTheHighestScale = key;
        }
      }

      console.log(maxScaleKey);
      console.log(getTheHighestScale);

      selectedScaleItem.value = getTheHighestScale; // update the scale reference

      /*   console.log(groupTheScaleValues.sort((a, b) =>
              getAllScaleValues.filter(x => x === b).length - getAllScaleValues.filter(x => x === a).length
          )); */

    } else {
      selectedScaleItem.value = unitplanScaleTypes.SMALL; // default
    }

  } else {
    console.log("else scaleSetup");
    selectedScaleItem.value = unitplanScaleTypes.SMALL; // default
  }

};

// Frame preview URL
/* const framePreviewURL = () => {
  if(projectStore.unitplans?.[unitplanId.value] !== undefined && projectStore.units !== null){
    console.log(projectStore.unitplans?.[unitplanId.value]);
    // find the units to

     const currentUnitplanUnitsData = Object.values(projectStore.units).filter((val) => {
      if (val?.unitplan_id === unitplanId.value){
        return val;
      }
     });

    const getInfo = currentUnitplanUnitsData.length > 0 ? currentUnitplanUnitsData[0] : null;

    if(getInfo){
      console.log(getInfo);
      if(getInfo.project_id && getInfo.building_id && getInfo.floor_id && getInfo.status && getInfo._id && getInfo.name){
      console.log("If case");
      const framePreviewURLVal = `${preview_domain}/${getCookie('organization')}/projectscene/${getInfo.project_id}//${unitplanId.value}/?building_id=${getInfo.building_id}&floor_id=${getInfo.floor_id}&status=${getInfo.status}&unit_id=${getInfo._id}&type=unitplan&unit_name=${getInfo.name}`;
      console.log(framePreviewURLVal);
      } else{
        framePreviewURLVal.value = null;
        console.log(framePreviewURLVal);
      }

    } else {
      framePreviewURLVal.value = null;
    }

  }
} */

if (projectStore.unitplans !== null){
  getTourDetails(); // update the image and group list on load
  // framePreviewURL();
}

const setPreviousDetailsToFields = async (layerId) => {
  const val = layerId;
  if (val && Object.keys(projectStore.unitplans[unitplanId.value].hotspots).length > 0){

    /* Text */
    if (projectStore.unitplans[unitplanId.value].hotspots[val].text){
      const desc = projectStore.unitplans[unitplanId.value].hotspots[val].text;
      unitplanHotspotPills.forEach((item) => {
        if (item === desc){
          selectedPill.value = desc;
          toggleDescTypeRef.value.anchorElem = unitplanDescTypes.PILL;
        }
      });

      if (selectedPill.value === null) {
        fieldName.value = desc.replace(/\\n/g, '\n');
        toggleDescTypeRef.value.anchorElem = unitplanDescTypes.TEXT;
      }
    }

    /* Scale */
    if (projectStore.unitplans[unitplanId.value].hotspots[val].scale){
      selectedScaleItem.value = projectStore.unitplans[unitplanId.value].hotspots[val].scale;
    } else {
      scaleSetup(); // if no scale then setup the scale
    }

    /* Types */
    if (projectStore.unitplans[unitplanId.value].hotspots[val].type === unitplanListTypes.DEFAULT){
      selectedImageItem.value = null;
      selectedGroupItem.value = {
        parent: null,
        child: null,
      };
    } else if (projectStore.unitplans[unitplanId.value].hotspots[val].type === unitplanListTypes.IMAGE){
      selectedImageItem.value = projectStore.unitplans[unitplanId.value].hotspots[val].image_id && imageList.value ? imageList.value[projectStore.unitplans[unitplanId.value].hotspots[val].image_id] : null;
      selectedGroupItem.value = {
        parent: null,
        child: null,
      };

    } else if (projectStore.unitplans[unitplanId.value].hotspots[val].type === unitplanListTypes.GROUP){
      selectedImageItem.value = null;
      selectedGroupItem.value = {
        parent: projectStore.unitplans[unitplanId.value].hotspots[val].group_id && groupsList.value ? groupsList.value[projectStore.unitplans[unitplanId.value].hotspots[val].group_id] : null,
        child: null,
      };
    } else if (projectStore.unitplans[unitplanId.value].hotspots[val].type === unitplanListTypes.SUBGROUP){
      selectedImageItem.value = null;
      selectedGroupItem.value = {
        parent: projectStore.unitplans[unitplanId.value].hotspots[val].group_id && groupsList.value ? groupsList.value[projectStore.unitplans[unitplanId.value].hotspots[val].group_id] : null,
        child: projectStore.unitplans[unitplanId.value].hotspots[val].subGroup_id && groupsList.value ? groupsList.value[projectStore.unitplans[unitplanId.value].hotspots[val].group_id].subcat[projectStore.unitplans[unitplanId.value].hotspots[val].subGroup_id] : null,
      };
    }

    // Set the toggle list if both lists are exist, depending on the type.
    if (
      (imageList.value && Object.values(imageList.value).length > 0) &&
  (groupsList.value && Object.values(groupsList.value).length > 0)
    ) {
      toggleListRef.value.ishide = false;
      const hotspotType = projectStore.unitplans[unitplanId.value].hotspots[val].type;
      if (hotspotType === unitplanListTypes.DEFAULT || hotspotType === unitplanListTypes.IMAGE) {
        toggleListRef.value.anchorElem = unitplanListTypes.IMAGE;
      } else if (hotspotType === unitplanListTypes.GROUP || hotspotType === unitplanListTypes.SUBGROUP) {
        toggleListRef.value.anchorElem = unitplanListTypes.GROUP;
      }
    }

    /* Dynamic Box display */
    if (projectStore.unitplans[unitplanId.value].hotspots[val].y){
      const rect = imageRef.value.getBoundingClientRect();
      const originalXPoint = (projectStore.unitplans[unitplanId.value].hotspots[val].x / 100) * rect.width;
      const originalYPoint = (projectStore.unitplans[unitplanId.value].hotspots[val].y / 100) * rect.height;
      // Within the bounds of the image
      if (Math.round(originalYPoint) > Math.round(rect.height/2) ) {
        isBottom.value = true;
      } else {
        isBottom.value = false;
      }

      if (Math.round(originalXPoint) > Math.round(rect.width/2) ){
        isRight.value = true;
      } else {
        isRight.value = false;
      }

      await nextTick(); // let's calm down till the dom element is re-rendered.

      // Get height after div is rendered
      if (existingHotspotsBoxRef.value[val]){

        currentDetailsBoxHeight.value = existingHotspotsBoxRef.value[val].getBoundingClientRect().height;
      }

      // If selectedPill is available then make to viewable area
      if (selectedPill.value ? descPillsRef.value : false){
        const selectedElemFindIndex = unitplanHotspotPills.findIndex((item) => item === selectedPill.value);
        if (selectedElemFindIndex !== -1){
          const elementToScroll =  descPillsRef.value[selectedElemFindIndex];
          elementToScroll.scrollIntoView({ behavior: "smooth", block: "center", inline: "nearest" });
        }
      }

    }
  }
};

watch(newElement, async (newVal) => {
  if (newVal && !route.query.layerId) {
    // newElement
    await nextTick();
    // Get height after newElement is rendered
    currentDetailsBoxHeight.value = detailsBoxRef.value.getBoundingClientRect().height;
  }
});

watch([toggleListRef, toggleDescTypeRef], async (val) => {
  if (isBottom.value){
    // Wait for DOM to update first
    await nextTick();
    if (newElement.value && !route.query.layerId && detailsBoxRef.value && val[1].anchorElem){
      // new Element
      await nextTick(); // let's be calm till dom element is re-rendered.
      // Get height after the rendered
      currentDetailsBoxHeight.value = detailsBoxRef.value.getBoundingClientRect().height;
    } else if (route.query.layerId && val[1].anchorElem){
      // Edit existing hotspots
      await nextTick(); // let's be calm till dom element is re-rendered.
      // Get height after the rendered
      if (existingHotspotsBoxRef.value[route.query.layerId]){
        currentDetailsBoxHeight.value = existingHotspotsBoxRef.value[route.query.layerId].getBoundingClientRect().height;
      }
    }
  }
}, {deep: true});

watch(() => route.query.layerId, async (val) => {
  setPreviousDetailsToFields(val); // set the previous data to the select layerId.
});

watch(() => projectStore.unitplans, async (val) => {
  console.log("Yes Updating", val);
  if (val){
    getTourDetails();
  }
}, {deep: true});

const updateImageDimensions = () => {
  if (imageRef.value) {
    const rect = imageRef.value.getBoundingClientRect();
    imageDimensions.value = {
      width: rect.width,
      height: rect.height,
    };
  }
};

const handleExportCSV = () => {
  const hotspots = projectStore.unitplans[unitplanId.value].hotspots; // hotspots objects
  const fileName = projectStore.unitplans[unitplanId.value].name; // name unitplan

  const requiredKeys = ['text', 'x', 'y'];
  const originalImageWidth = imageRef.value.naturalWidth; // original image width
  const originalImageHeight = imageRef.value.naturalHeight; // original image height

  const filteredHotspots = Object.fromEntries(
    Object.entries(hotspots).map(([key, value]) => [key, Object.fromEntries(Object.entries(value).filter(([key2]) => requiredKeys.includes(key2)).map(([key2, value2]) => {
      if (key2 === 'x') {
        return [key2, Math.round(originalImageWidth * (value2/100))];
      } else if (key2 === 'y') {
        return [key2, Math.round(originalImageHeight * (value2/100))];
      }
      return [key2, value2];
    }))],
    ),
  ); // delete unwanted keys except the required columns keys
  console.log(filteredHotspots);

  const finalCsvData = [
    ...Object.entries(filteredHotspots).map(([, value]) => Object.values(value).join(',')),
  ].join('\n'); // array to string with newline characters

  console.log(finalCsvData);

  // Conversion
  const blob =  new Blob([finalCsvData], { type: 'text/csv' }); // Csv blob creation
  const url = window.URL.createObjectURL(blob); // bind to url
  const link = document.createElement('a'); // temp Create a link element
  link.href = url; // set the link's href to the created object URL
  link.download = `${fileName}.csv`; // set the link's download attribute to 'hotspots.csv'
  link.click();
  window.URL.revokeObjectURL(url); // Remove the object URL blob after the link is clicked to free up memory
};

const convertToParentPoint = (coOrdinateType, imageCalcPoint) => {
  if (!imageRef.value || !parentRef.value) {
    return false;
  }

  const imageRect = imageRef.value.getBoundingClientRect();
  const parentRect = parentRef.value.getBoundingClientRect();

  console.log(parentRect);
  console.log(imageRect);

  // Calculate the position and size of the image relative to the parent
  const imageRelativeLeft = imageRect.left - parentRect.left;
  const imageRelativeTop = imageRect.top - parentRect.top;

  // Convert image percentages to parent percentages
  // For X coordinate
  if (coOrdinateType === 'x') {
    // Convert the percentage point on image to actual pixels
    const pointInPixels = (imageCalcPoint / 100) * imageRect.width;
    // Add the image's offset from parent
    const absolutePoint = pointInPixels + imageRelativeLeft;
    // Convert back to percentage relative to parent
    return (absolutePoint / parentRect.width) * 100;
  }

  // For Y coordinate
  // Convert the percentage point on image to actual pixels
  const pointInPixels = (imageCalcPoint / 100) * imageRect.height;
  // Add the image's offset from parent
  const absolutePoint = pointInPixels + imageRelativeTop;
  // Convert back to percentage relative to parent
  return (absolutePoint / parentRect.height) * 100;

};

const resetValues = () => {
  newElement.value = null; // Reset the new element
  selectedImageItem.value = null; // Reset the selectedImageItem
  selectedGroupItem.value = {
    parent: null,
    child: null,
  }; // Reset the selectedGroupItem
  fieldName.value = null; // Reset the fieldName
  selectedPill.value = null; // Reset the selectedPill
  toggleDescTypeRef.value.anchorElem = unitplanDescTypes.PILL; // Reset the toggleDescType
  isBottom.value = false; // Reset the isBottom
  isRight.value = false; // Reset the isRight
  errorRef.value.open = false; // Reset the error message
  errorRef.value.message = null; // Reset the error message
  scaleSetup(); // scale val setup
};

const handleImageClick = (e) => {
  if (!route.query.layerId && !toggleDragDrop.value) {
    e.stopPropagation();
    resetValues();
    const rect = imageRef.value.getBoundingClientRect();
    console.log(rect);

    const xpoint = e.clientX - rect.left;
    const ypoint = e.clientY - rect.top;
    const calcXPoint = (xpoint/rect.width) * 100;
    const calcYPoint = (ypoint/rect.height) * 100;
    // Within the bounds of the image
    if ( Math.round(ypoint) > Math.round(rect.height/2) ) {
      isBottom.value = true;
    } else {
      isBottom.value = false;
    }

    if (Math.round(xpoint) > Math.round(rect.width/2)){
      isRight.value = true;
    } else {
      isRight.value = false;
    }

    if (!String(calcXPoint).includes('-') && !String(calcYPoint).includes('-')) {
      newElement.value = {
        x: calcXPoint,
        y: calcYPoint,
        text: null,
      };
    } else {
      resetValues();
    }
  }

};

const validateChecks = async () => {
  const text = fieldName.value; // Text
  const pill = selectedPill.value; // Pill
  /* Description Check */
  if ((text === null || text.trim().length <= 0) && pill === null){
    errorRef.value.message = 'Description is required!';
    errorRef.value.open = true;
    return false;
  }

  errorRef.value.message = null;
  errorRef.value.open = false;
  return true;
};

const handleToggleList = () => {
  toggleListRef.value.anchorElem = toggleListRef.value.anchorElem === unitplanListTypes.GROUP ? unitplanListTypes.IMAGE : unitplanListTypes.GROUP;
  console.log(toggleListRef.value);
  selectedImageItem.value = null; // Reset the selectedImageItem
  selectedGroupItem.value = {
    parent: null,
    child: null,
  }; // Reset the selectedGroupItem
};

const handleDescToggle = () => {
  toggleDescTypeRef.value.anchorElem = toggleDescTypeRef.value.anchorElem === unitplanDescTypes.PILL ? unitplanDescTypes.TEXT : unitplanDescTypes.PILL;
  console.log(toggleDescTypeRef.value);

  fieldName.value = null; // Reset the fieldName
  selectedPill.value = null; // Reset the selectedPill
};

const handleTreeDropDownSelection = (val) => {
  console.log(val);
  console.log("handleTreeDropDownSelection");
  if (val){
    if (val.parent && val.child){
      selectedGroupItem.value = {
        parent: val.parent,
        child: val.child,
      };
    } else if (val.parent) {
      selectedGroupItem.value = {
        parent: val.parent,
        child: null,
      };
    } else if (!val.parent && !val.child){
      selectedGroupItem.value = {
        parent: null,
        child: null,
      };
    }
  }
};

const frameTypeParms = () => {
  const frameTypeParms = {};
  if (selectedImageItem.value && (selectedGroupItem.value.parent === null && selectedGroupItem.value.child === null)){
    // image
    frameTypeParms.type = unitplanListTypes.IMAGE;
    frameTypeParms.image_id = selectedImageItem.value._id;
  } else if ((selectedGroupItem.value.parent || selectedGroupItem.value.child) && selectedImageItem.value === null){
    // group & subgroup
    if (selectedGroupItem.value.parent && selectedGroupItem.value.child) {
      frameTypeParms.type = unitplanListTypes.SUBGROUP;
      frameTypeParms.group_id = selectedGroupItem.value.parent._id;
      frameTypeParms.subGroup_id = selectedGroupItem.value.child._id;
    } else if (selectedGroupItem.value.parent) {
      frameTypeParms.type = unitplanListTypes.GROUP;
      frameTypeParms.group_id = selectedGroupItem.value.parent._id;
    }
  } else {
    // default
    frameTypeParms.type = unitplanListTypes.DEFAULT;
  }
  return frameTypeParms;
};

const handleAddItems = async () => {
  const isValid = await validateChecks(); // Validate checkup
  if (isValid){
    const content = fieldName.value ? fieldName.value.trim().replace(/\r?\n/g, '\\n') : selectedPill.value; // description
    const frameParms = {
      project_id: projectId.value,
      unitplan_id: unitplanId.value,
      hotspots: {
        ...newElement.value,
        text: content,
        scale: selectedScaleItem.value,
        ...frameTypeParms(),
      },
    };
    console.log(frameParms);
    createHotspots(frameParms).then((res) => {
      console.log(res.hotspots);
      projectStore.unitplans[unitplanId.value].hotspots = res.hotspots; // Update the hotspots Objects in projectStore
      resetValues();
    });
  }

};

const handleEditItem = async (prevData) => {
  const isValid = await validateChecks(); // Validate checkup
  if (isValid){
    const content = fieldName.value ? fieldName.value.trim().replace(/\r?\n/g, '\\n') : selectedPill.value ; // Desc
    const getTheCurrentTypeDetails =  frameTypeParms();
    console.log(getTheCurrentTypeDetails);
    let comparesionTypeParms = {};

    if (prevData?.type === undefined){
      comparesionTypeParms = {
        ...getTheCurrentTypeDetails,
      };
    } else if (prevData.type.toLowerCase() !== getTheCurrentTypeDetails.type.toLowerCase()){
    // different
      comparesionTypeParms = {
        ...getTheCurrentTypeDetails,
      };
    } else {
      // same, then campare keys differs
      if (getTheCurrentTypeDetails.type === unitplanListTypes.IMAGE){
        comparesionTypeParms={
          ...(prevData.image_id !== getTheCurrentTypeDetails.image_id && {image_id: getTheCurrentTypeDetails.image_id} ),
        };
      } else if (getTheCurrentTypeDetails.type === unitplanListTypes.GROUP || getTheCurrentTypeDetails.type === unitplanListTypes.SUBGROUP){
        comparesionTypeParms = {
          ...( (getTheCurrentTypeDetails.group_id ? prevData.group_id !== getTheCurrentTypeDetails.group_id : false) && {group_id: getTheCurrentTypeDetails.group_id } ),
          ...(( getTheCurrentTypeDetails.subGroup_id ? prevData.subGroup_id !== getTheCurrentTypeDetails.subGroup_id : false) && {subGroup_id: getTheCurrentTypeDetails.subGroup_id } ),
        };
      }
    }

    const frameParms = {
      project_id: projectId.value,
      unitplan_id: unitplanId.value,
      hotspot_id: prevData._id,
      hotspots: {
        ...(content !== prevData.text && { text: content}),
        ...(selectedScaleItem.value !== prevData.scale && { scale: selectedScaleItem.value}),
        ...comparesionTypeParms,
      },
    };
    console.log(frameParms);
    if (Object.keys(frameParms.hotspots).length > 0) {
      // Api call
      editHotspots(frameParms).then((res) => {
        projectStore.unitplans[unitplanId.value].hotspots[prevData._id] = res.hotspots[prevData._id]; // Updated hotspots objects for edit layer key
        resetValues();
        router.push({path: route.path,
          query: {},
        });
      });
    } else {
      router.push({path: route.path,
        query: {},
      });
    }
  }
};

const handleUpdateHotspotPosition = () => {
  if (Object.keys(dragDropItemsRef.value).length > 0){
    console.log(dragDropItemsRef.value);
    const promises = Object.keys(dragDropItemsRef.value).map(async (key) => {
      return new Promise ((resolve) => {
        const frameParms = {
          project_id: projectId.value,
          unitplan_id: unitplanId.value,
          hotspot_id: key,
          hotspots: {
            x: dragDropItemsRef.value[key].x,
            y: dragDropItemsRef.value[key].y,
          },
        };
        // Api call
        editHotspots(frameParms).then((res) => {
          resolve(res);
        });
      });
    });

    Promise.all(promises).then((res) => {
      if (res.length > 0){
        projectStore.unitplans[unitplanId.value].hotspots = res[res.length - 1].hotspots; // update the store
        dragDropItemsRef.value = {}; // reset the dragDropItems Ref
        toggleDragDrop.value = false;  // reset the Toggler
      }
    });
  } else {
    dragDropItemsRef.value = {};
    toggleDragDrop.value = false;
  }
};

const handleDeleteHotspot = () => {
  console.log("handleDeleteHotspot");
  deleteLoader.value = true;
  const deletehotspotId = route.query.deleteId;

  if (deletehotspotId && unitplanId.value && projectId.value){
    const frameParms = {
      project_id: projectId.value,
      unitplan_id: unitplanId.value,
      hotspot_id: deletehotspotId,
    };

    deleteHotspots(frameParms).then((res) => {
      console.log(res.hotspots);
      projectStore.unitplans[unitplanId.value].hotspots = res.hotspots; // Update the hotspots Objects in projectStore
      openDeleteModal.value = false; // reset the delete modal
      deleteLoader.value = false; // reset the deleteLoader
      router.push({ path: route.path,  query: {} }); // clear the query parameters
    });
  }

};

/* Drag and Drop (HTML Drag and Drop API)*/

// Drag & Drop Events handlers
const handleDragStart = (e) => {

  console.log("handleDragStart Zone");
  const draggableElement = e.target;
  if (!draggableElement.getAttribute('draggable')) {
    e.preventDefault();
    return;
  }
  e.dataTransfer.clearData(); // first clean up the mess.
  const rect = draggableElement.getBoundingClientRect();
  // Store offsets in DTO
  e.dataTransfer.setData('layerId', draggableElement.dataset.hotspotId);
  e.dataTransfer.setData('offsetX', e.clientX - rect.left);
  e.dataTransfer.setData('offsetY', e.clientY - rect.top);

};

// Drop zone - Drop
const handleDrop = (e) => {
  console.log("handleDrop Zone");
  const layerId = e.dataTransfer.getData('layerId');
  const draggableElement = existingHotspotsBoxRef.value[layerId];

  // Get offsets from DTO
  const grabOffsetX = parseInt(e.dataTransfer.getData('offsetX'));
  const grabOffsetY = parseInt(e.dataTransfer.getData('offsetY'));
  console.log(grabOffsetX);
  console.log(grabOffsetY);

  // Get parent container bounds
  const rect = imageRef.value.getBoundingClientRect();

  // Calculate points relative to parent, accounting for grab offset
  let xpoint = e.clientX - rect.left - grabOffsetX;
  let ypoint = e.clientY - rect.top - grabOffsetY;

  // Account for element size to prevent overflow
  const maxX = rect.width - draggableElement.offsetWidth;
  const maxY = rect.height - draggableElement.offsetHeight;

  // Constrain positions
  xpoint = Math.max(0, Math.min(xpoint, maxX));
  ypoint = Math.max(0, Math.min(ypoint, maxY));

  // Convert to percentages
  const calcXPoint = (xpoint / rect.width) * 100;
  const calcYPoint = (ypoint / rect.height) * 100;

  // temporarily update positions in dom
  draggableElement.style.left = calcXPoint + '%';
  draggableElement.style.top = calcYPoint + '%';

  if (dragDropItemsRef.value[layerId]){
    // existing
    dragDropItemsRef.value[layerId] = {
      ...dragDropItemsRef.value[layerId],
      x: calcXPoint,
      y: calcYPoint,
    };
  } else {
    // new element
    dragDropItemsRef.value[layerId] = {
      hotspot_id: layerId,
      x: calcXPoint,
      y: calcYPoint,
    };
  }

};

const dragEvents = computed(() => {
  console.log("dragEvents tiggered");
  if (!toggleDragDrop.value) {
    return {};
  }
  return {
    dragstart: handleDragStart,
  };
});

const dropZoneEvents = computed(() => {
  console.log("dropZoneEvents tiggered");

  if (!toggleDragDrop.value) {
    return {};
  }

  return {
    dragover: (e) => e.preventDefault(),
    dragenter: (e) => e.preventDefault(),
    drop: handleDrop,
  };
});

onMounted(() => {
  if (route.query.deleteId){
    openDeleteModal.value = true;
  }
  window.addEventListener('resize', updateImageDimensions);
});

onUnmounted(() => {
  window.removeEventListener('resize', updateImageDimensions);
});

</script>

<template>
  <Modal :open="true">
    <div class="!bg-[#202020] h-full w-full fixed top-0 left-0">
      <div class="bg-transparent h-full w-full relative">

      <!-- Content -->
      <div class="px-0 py-0 sm:px-0 flex flex-col justify-start items-start gap-2 h-full w-full overflow-hidden bg-tranparent ">
        <!-- Hotspots & Image -->
        <div ref="parentRef" v-if="projectStore.unitplans && projectStore.unitplans?.[unitplanId]?.image_url !== undefined" class="bg-transparent w-full h-[100%] flex justify-center items-center relative overflow-hidden" >

                    <img ref="imageRef" v-on="dropZoneEvents" :onclick="handleImageClick" class="object-contain w-auto h-auto max-w-[92.5%] max-h-[92.5%] relative flex-shrink-0 flex-grow-0" :src="projectStore.unitplans[unitplanId].image_url" @load="() => {isImageRendered = true; updateImageDimensions();}" />
                    <!-- Create New Hotspot -->
                    <div ref="detailsBoxRef" v-if="newElement && !route.query.layerId"  :style="`left: ${convertToParentPoint('x',newElement.x)}%; top:${convertToParentPoint('y',newElement.y)}%;  ${ !isBottom ? `margin-top:0;`: `margin-top:-${currentDetailsBoxHeight}px;`} ${!isRight ? `margin-left:0;` : `margin-left:-300px;` }`" class="bg-white text-black flex flex-col justify-start items-end gap-2 rounded absolute z-50 w-[300px] p-2 boxShadow" >

                          <div  class="flex flex-col justify-start items-start w-full overflow-hidden">
                            <!-- Name -->
                             <div class="w-full flex justify-between items-center">

                              <label class="label-primary">
                                Description <span class="text-red-500">*</span></label>

                              <div class="flex gap-1 justify-start items-center" >
                                          <button
                                             @click="handleDescToggle()"
                                            :class="[(toggleDescTypeRef.anchorElem === unitplanDescTypes.PILL ?  ' text-end before:left-[5px]' : '  text-start before:left-[17px] ' ),' border-none before:bg-zinc-800 text-[#161616] bg-gray-300 w-[30px] h-[15px] px-2 rounded-[30px] z-[2] cursor-pointer relative toggleShape '] ">

                                          </button>
                                          <i class="text-gray-500 text-sm capitalize"> ({{ toggleDescTypeRef.anchorElem }})</i>
                                      </div>
                             </div>

                            <textarea v-if="toggleDescTypeRef.anchorElem !== unitplanDescTypes.PILL" v-model="fieldName" id="create" rows="3" placeholder="text.." class="border select-primary outline-none rounded text-black bg-transparent p-2 h-fit block">
                            </textarea>

                            <div v-else class="flex justify-start items-center gap-x-1 gap-y-1 flex-wrap min-h-[auto] max-h-[100px] overflow-y-auto overflow-x-hidden">
                                  <p :class="['rounded-3xl px-3 py-2 capitalize select-none cursor-pointer transition-all', selectedPill === item ? 'text-white bg-black' : 'text-black bg-gray-200']" :key="index" v-for="item, index in unitplanHotspotPills" @click="() => {if(item === selectedPill) {selectedPill = null;} else {selectedPill = item;}} ">
                                        {{ item }}
                                  </p>
                            </div>

                          </div>

                          <div class="flex flex-col justify-start items-start w-full ">
                            <label class="label-primary capitalize">
                                Scale <span class="text-red-500">*</span>
                            </label>
                            <Multiselect
                                      v-if="Object.values(unitplanScaleTypes)"
                                      :show-labels="false"
                                      v-model="selectedScaleItem"
                                      placeholder="search text scale"
                                      :multiple="false"
                                      :allow-empty="false"
                                      :custom-label="(val) => val"
                                      :options="Object.values(unitplanScaleTypes)" maxHeight="250" >
                                    </Multiselect>
                          </div>

                          <div v-if="imageList || groupsList" class="flex flex-col justify-start items-start w-full">

                                    <div class="flex w-full justify-between items-center ">
                                        <label class="label-primary capitalize">
                                          <!-- If both exist, show the label based on current anchorElem -->
                                          <template v-if="imageList && groupsList">
                                            {{ toggleListRef.anchorElem === unitplanListTypes.GROUP ? unitplanListTypes.GROUP : unitplanListTypes.IMAGE }}
                                          </template>
                                          <!-- If only one exists, show its respective label -->
                                          <template v-else>
                                            {{ imageList ? unitplanListTypes.IMAGE : unitplanListTypes.GROUP }}
                                          </template>
                                        </label>

                                       <div class="flex gap-1 justify-start items-center"  v-if="imageList && groupsList && !toggleListRef.ishide" >
                                          <button
                                             @click="handleToggleList"
                                            :class="[(toggleListRef.anchorElem === unitplanListTypes.GROUP ?  ' text-end before:left-[5px]' : '  text-start before:left-[17px] ' ),' border-none before:bg-zinc-800 text-[#161616] bg-gray-300 w-[30px] h-[15px] px-2 rounded-[30px] z-[2] cursor-pointer relative toggleShape '] ">

                                          </button>
                                          <i class="text-gray-500 text-sm capitalize"> ({{ toggleListRef.anchorElem }})</i>
                                      </div>
                                    </div>

                                    <Multiselect
                                      v-if="imageList && (groupsList ? toggleListRef.anchorElem === unitplanListTypes.IMAGE : true)"
                                      :show-labels="false"
                                      v-model="selectedImageItem"
                                      placeholder="search image"
                                      label="name"
                                      track-by="name"
                                      :multiple="false"
                                      :allow-empty="true"
                                      :custom-label="(val) => val.name"
                                      :options="Object.values(imageList)" maxHeight="250" >
                                    </Multiselect>

                                    <TreeDataDropDown
                                      v-if="groupsList && (imageList ? toggleListRef.anchorElem === unitplanListTypes.GROUP : true)"
                                    :data="groupsList" displayKey="heading" subCategoryKey="subcat" subCategoryDisplayKey="heading" @updateSelectedItem="(val) => handleTreeDropDownSelection(val)"   />

                           </div>

                          <p v-if="errorRef.open" class="w-full h-fit text-right text-red-600 font-semibold text-[12px] italic transition-all">
                                  {{ errorRef.message }}
                          </p>

                          <div class="flex justify-end items-center gap-2">
                            <Button
                             title="Cancel"
                             theme="secondary"
                             class=" !py-2"
                             @click="() => {
                              resetValues();
                             }">
                            </Button>
                            <Button
                            title="Add"
                            theme="primary"
                            class=" !py-2"
                            @click="() => handleAddItems()">

                          </Button>
                          </div>

                          <span v-if="!isBottom && !isRight" class="w-2 h-2 absolute top-0 left-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600">
                            <!--  &#10022; -->
                          </span>

                          <span v-if="!isBottom && isRight" class="w-2 h-2 absolute top-0 right-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600">
                            <!--  &#10022; -->
                          </span>

                          <span v-if="isBottom && !isRight" class="w-2 h-2 absolute bottom-0 left-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600">
                            <!--  &#10022; -->
                          </span>

                          <span v-if="isBottom && isRight" class="w-2 h-2 absolute bottom-0 right-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600">
                            <!--  &#10022; -->
                          </span>

                    </div>
                    <!-- Existing Hotspots -->
                    <div v-if="isImageRendered && projectStore.unitplans?.[unitplanId]?.hotspots !== undefined " :style="`width:${imageDimensions.width}px; height:${imageDimensions.height}px; left: 50%;
                        top: 50%; transform: translate(-50%, -50%);`"  class="pointer-events-none absolute inset-0">
                      <div :ref="el => { if(el) existingHotspotsBoxRef[index] = el }" v-for="item,index in projectStore.unitplans[unitplanId].hotspots" :key="index" :draggable="toggleDragDrop" :style="`left: ${item.x}%; top:${item.y}%; ${ isBottom && route.query.layerId == String(index) ? `margin-top:-${currentDetailsBoxHeight}px;` : 'margin-top:0;'} ${ isRight && route.query.layerId == String(index) ? `margin-left:-302px;` : 'margin-left:0px;'}`" :data-hotspot-id="index" class="bg-white text-black font-semibold text-base border rounded border-black absolute  py-1 w-fit h-fit boxShadow  pointer-events-auto" :class="[`${route.query.layerId ? route.query.layerId  === String(index) ? 'brightness-100 opacity-100 z-50' : 'brightness-50 opacity-80 z-10' : 'brightness-100 opacity-100 z-10'} ${newElement ? 'brightness-50 opacity-80 z-10' : 'brightness-100 opacity-100 z-20' } `]"
                            v-on="dragEvents"
                        >

                          <span v-if="!isBottom && !isRight && route.query.layerId" class="w-2 h-2 absolute top-0 left-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600">
                            <!--  &#10022; -->
                          </span>

                          <span v-else-if="!isBottom && isRight && route.query.layerId === String(index)" class="w-2 h-2 absolute top-0 right-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600">
                            <!--  &#10022; -->
                          </span>

                          <span v-else-if="isBottom && !isRight && route.query.layerId === String(index)" class="w-2 h-2 absolute bottom-0 left-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600">
                            <!--  &#10022; -->
                          </span>

                          <span v-else-if="isBottom && isRight && route.query.layerId === String(index)" class="w-2 h-2 absolute bottom-0 right-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600">
                            <!--  &#10022; -->
                          </span>

                          <span v-else  class="w-2 h-2 absolute top-0 left-0 select-none rounded-s-[30px] rounded-e-[30px] z-90 bg-red-600"></span>

                            <!-- View -->
                            <div class="flex flex-row justify-start items-start gap-1 ps-1 pointer-events-auto" :class="[`${route.query.layerId ? route.query.layerId === String(index)  ? 'invisible w-0 h-0' : 'visible w-fit h-fit' : 'visible w-fit h-fit'}`,]">
                              <p :class="['select-none text-center', item.scale === unitplanScaleTypes.SMALL && 'text-sm' , item.scale === unitplanScaleTypes.MEDIUM && 'text-lg' , item.scale === unitplanScaleTypes.LARGE && 'text-2xl' ]" v-html="item.text.replace(/\\n/g, '<br>')"></p>
                              <button :class="[ (toggleDragDrop || route.query.layerId ? 'invisible' : 'visible') ,'w-fit h-fit mt-1']" :onclick="() => { if(!route.query.layerId && newElement === null) itemMenuRef[index].classList.remove('hidden'); itemMenuRef[index].classList.add('block');} ">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="currentColor"
                                    class="w-5 h-4 fill-black">
                                    <path fillRule="evenodd"
                                      d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"
                                      clipRule="evenodd" />
                                </svg>
                              </button>
                              <ul :ref="el => { if (el) itemMenuRef[index] = el }" :onmouseover="() => {itemMenuRef[index].classList.remove('hidden'); itemMenuRef[index].classList.add('block');}" :onmouseout="() => {itemMenuRef[index].classList.remove('block'); itemMenuRef[index].classList.add('hidden');}" :key="'menuList'+index" class="absolute -right-10 -bottom-9 z-60  bg-white boxShadow p-2 rounded hidden">
                                      <li :onclick="() => router.push({
                                                        path: route.path,
                                                        query: { layerId: index, }, })" class="text-black text-sm font-medium cursor-pointer border-b border-[#a6a6a6]"> Edit </li>
                                      <li :onclick="() =>{
                                         router.push({
                                                        path: route.path,
                                                        query: { deleteId: index, }, });
                                                        openDeleteModal = true;
                                      }" class="text-black text-sm font-medium  cursor-pointer"> Delete </li>
                              </ul>
                            </div>

                            <!-- Edit -->
                            <div v-if="route.query.layerId !== null && route.query.layerId === String(index)" class=" pointer-events-auto">

                                  <div class="flex flex-col justify-start items-center gap-1 w-[300px] p-2 h-fit font-medium">

                                    <div  class="flex flex-col justify-start items-start w-full overflow-hidden">

                                          <div class="w-full flex justify-between items-center">

                                              <label class="label-primary">
                                                Description <span class="text-red-500">*</span></label>

                                              <div class="flex gap-1 justify-start items-center" >
                                                          <button
                                                            @click="handleDescToggle()"
                                                            :class="[(toggleDescTypeRef.anchorElem === unitplanDescTypes.PILL ?  ' text-end before:left-[5px]' : '  text-start before:left-[17px] ' ),' border-none before:bg-zinc-800 text-[#161616] bg-gray-300 w-[30px] h-[15px] px-2 rounded-[30px] z-[2] cursor-pointer relative toggleShape '] ">

                                                          </button>
                                                          <i class="text-gray-500 text-sm capitalize"> ({{ toggleDescTypeRef.anchorElem }})</i>
                                               </div>
                                          </div>

                                      <textarea v-if="toggleDescTypeRef.anchorElem !== unitplanDescTypes.PILL" v-model="fieldName" rows="3" placeholder="text.." class="border select-primary outline-none rounded text-black bg-transparent p-2 h-fit block">
                                  </textarea>

                                      <div v-else  class="flex justify-start items-center gap-x-1 gap-y-1 flex-wrap min-h-[auto] max-h-[100px] overflow-y-auto overflow-x-hidden">
                                            <p :ref="el => descPillsRef[index] = el" :class="['rounded-3xl px-3 py-2 capitalize select-none cursor-pointer transition-all', selectedPill === item ? 'text-white bg-black' : 'text-black bg-gray-200']" :key="index" v-for="item, index in unitplanHotspotPills" @click="() => {if(item === selectedPill) {selectedPill = null;} else {selectedPill = item;}} ">
                                                  {{ item }}
                                            </p>
                                      </div>

                                    </div>

                                    <div class="flex flex-col justify-start items-start w-full ">
                                      <label class="label-primary capitalize">
                                          Scale <span class="text-red-500">*</span>
                                      </label>
                                      <Multiselect
                                                v-if="Object.values(unitplanScaleTypes)"
                                                :show-labels="false"
                                                v-model="selectedScaleItem"
                                                placeholder="search text scale"
                                                :multiple="false"
                                                :allow-empty="false"
                                                :custom-label="(val) => val"
                                                :options="Object.values(unitplanScaleTypes)" maxHeight="250" >
                                              </Multiselect>
                                    </div>

                                  <div v-if="imageList || groupsList" class="flex flex-col justify-start items-start w-full">

                                      <div class="flex w-full justify-between items-center ">
                                          <label class="label-primary capitalize">
                                            <!-- If both exist, show the label based on current anchorElem -->
                                            <template v-if="imageList && groupsList">
                                              {{ toggleListRef.anchorElem === unitplanListTypes.GROUP ? unitplanListTypes.GROUP : unitplanListTypes.IMAGE }}
                                            </template>
                                            <!-- If only one exists, show its respective label -->
                                            <template v-else>
                                              {{ imageList ? unitplanListTypes.IMAGE : unitplanListTypes.GROUP }}
                                            </template>
                                          </label>

                                        <div class="flex gap-1 justify-start items-center"  v-if="imageList && groupsList && !toggleListRef.ishide" >
                                            <button
                                              @click="handleToggleList"
                                              :class="[(toggleListRef.anchorElem === unitplanListTypes.GROUP ?  ' text-end before:left-[5px]' : '  text-start before:left-[17px] ' ),' border-none before:bg-zinc-800 text-[#161616] bg-gray-300 w-[30px] h-[15px] px-2 rounded-[30px] z-[2] cursor-pointer relative toggleShape '] ">

                                            </button>
                                            <i class="text-gray-500 text-sm capitalize"> ({{ toggleListRef.anchorElem }})</i>
                                        </div>
                                      </div>

                                      <Multiselect
                                        v-if="imageList && (groupsList ? toggleListRef.anchorElem === unitplanListTypes.IMAGE : true)"
                                        :show-labels="false"
                                        v-model="selectedImageItem"
                                        placeholder="search image"
                                        label="name"
                                        track-by="name"
                                        :multiple="false"
                                        :allow-empty="true"
                                        :custom-label="(val) => val.name"
                                        :options="Object.values(imageList)" maxHeight="250" >
                                      </Multiselect>

                                      <TreeDataDropDown
                                        v-if="groupsList && (imageList ? toggleListRef.anchorElem === unitplanListTypes.GROUP : true)"
                                      :data="groupsList"
                                      displayKey="heading"
                                      subCategoryKey="subcat"
                                      subCategoryDisplayKey="heading"
                                      :selectedParent="selectedGroupItem.parent ? selectedGroupItem.parent['heading'] : null"
                                      :selectedSubChild="selectedGroupItem.child ? selectedGroupItem.child['heading'] : null "
                                      @updateSelectedItem="(val) => handleTreeDropDownSelection(val)" />

                            </div>

                                    <p v-if="errorRef.open" class="w-full h-fit text-right text-red-600 font-semibold text-[12px] italic transition-all">
                                      {{ errorRef.message }}
                                    </p>

                                  <div class="flex justify-end items-end gap-2 mt-2 w-full">

                                  <Button
                                        title="Cancel"
                                        theme="secondary"
                                        class=" !py-2"
                                        @click="() =>{
                                          router.push({
                                                        path: route.path,
                                                        query: {}, });
                                            resetValues();
                                        }">

                                      </Button>
                                  <Button
                                        title="Save"
                                        theme="primary"
                                        class=" !py-2"
                                        @click="() => handleEditItem(item)">

                                      </Button>

                                    </div>

                                  </div>

                            </div>
                  <!--     </div> -->
                      </div>
                   </div>

        </div>
        <div v-else class="w-full h-[90vh] py-3 bg-gray-50 rounded">
                <div class="h-full w-full flex flex-col justify-center items-center">
                  <img class="w-fit" :src="noDataFound"
                  alt="" />
                   <h1 class="text-center text-base font-medium italic text-black">No Image found! </h1>
                </div>
        </div>

      </div>

        <!-- Title -->
        <div class="mb-2 w-fit h-fit absolute top-0 left-0 py-1 px-2 text-white">
          <h1
            class="text-lg font-semibold leading-7 p-0">
            Add Unitplan Hotspots </h1>
            <p class="modal-subheading-primary !text-xs">select the place to add hotspots</p>
        </div>

        <!-- Action Buttons -->
        <div class="w-fit h-fit flex justify-center items-center gap-4 absolute right-0 top-0 p-2">

        <Button title="Back" theme="secondary"
          class="h-10"
          @click="() => router.push(`/projects/${projectId}/unitplans/${unitplanId}`)">
              >
        </Button>

        <a v-if="framePreviewURLVal" :href="framePreviewURLVal" target="_blank"
          type="button"
          class=" w-full flex gap-2 justify-between items-center rounded-lg bg-black px-3 h-10 text-sm font-medium text-white select-none">
          <svg  class="w-5 h-5 cursor-pointer" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                      <path d="M23.1853 11.6963C23.1525 11.6222 22.3584 9.86062 20.5931 8.09531C18.2409 5.74312 15.27 4.5 12 4.5C8.72999 4.5 5.75905 5.74312 3.40687 8.09531C1.64155 9.86062 0.843741 11.625 0.814679 11.6963C0.772035 11.7922 0.75 11.896 0.75 12.0009C0.75 12.1059 0.772035 12.2097 0.814679 12.3056C0.847491 12.3797 1.64155 14.1403 3.40687 15.9056C5.75905 18.2569 8.72999 19.5 12 19.5C15.27 19.5 18.2409 18.2569 20.5931 15.9056C22.3584 14.1403 23.1525 12.3797 23.1853 12.3056C23.2279 12.2097 23.25 12.1059 23.25 12.0009C23.25 11.896 23.2279 11.7922 23.1853 11.6963ZM12 18C9.11437 18 6.59343 16.9509 4.50655 14.8828C3.65028 14.0313 2.92179 13.0603 2.34374 12C2.92164 10.9396 3.65014 9.9686 4.50655 9.11719C6.59343 7.04906 9.11437 6 12 6C14.8856 6 17.4066 7.04906 19.4934 9.11719C20.3514 9.9684 21.0815 10.9394 21.6609 12C20.985 13.2619 18.0403 18 12 18ZM12 7.5C11.11 7.5 10.2399 7.76392 9.49993 8.25839C8.7599 8.75285 8.18313 9.45566 7.84253 10.2779C7.50194 11.1002 7.41282 12.005 7.58646 12.8779C7.76009 13.7508 8.18867 14.5526 8.81801 15.182C9.44735 15.8113 10.2492 16.2399 11.1221 16.4135C11.995 16.5872 12.8998 16.4981 13.7221 16.1575C14.5443 15.8169 15.2471 15.2401 15.7416 14.5001C16.2361 13.76 16.5 12.89 16.5 12C16.4988 10.8069 16.0242 9.66303 15.1806 8.81939C14.337 7.97575 13.1931 7.50124 12 7.5ZM12 15C11.4066 15 10.8266 14.8241 10.3333 14.4944C9.83993 14.1648 9.45542 13.6962 9.22835 13.148C9.00129 12.5999 8.94188 11.9967 9.05764 11.4147C9.17339 10.8328 9.45911 10.2982 9.87867 9.87868C10.2982 9.45912 10.8328 9.1734 11.4147 9.05764C11.9967 8.94189 12.5999 9.0013 13.148 9.22836C13.6962 9.45542 14.1648 9.83994 14.4944 10.3333C14.824 10.8266 15 11.4067 15 12C15 12.7956 14.6839 13.5587 14.1213 14.1213C13.5587 14.6839 12.7956 15 12 15Z" fill="white"/>
           </svg>
          Preview
        </a>

        <Button title="Export Csv" theme="primary" @click="() => handleExportCSV()" :disabled="projectStore.unitplans?.[unitplanId]?.hotspots !== undefined && isImageRendered ? false : true">
         <template v-slot:svg>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 22" class="w-4 h-4" >
                <g fill="none" fill-rule="evenodd" stroke="white" stroke-linecap="round" stroke-linejoin="round" stroke-width="2">
                  <path d="M1 16v3a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-3M6 11l4 4 4-4M10 1v14"></path>
                </g>
              </svg>
          </template>
          </Button>

        </div>

      <!-- Drag & Drop -->
      <div v-if="projectStore.unitplans?.[unitplanId]?.hotspots !== undefined ? Object.keys(projectStore.unitplans[unitplanId].hotspots).length > 0 : false" class="w-[200px] h-auto absolute top-16 left-0 p-2 bg-[#313131] rounded-e-lg text-white">

          <div class="flex flex-col justify-start items-start gap-4 w-full">
            <div class="flex justify-start items-center">
            <p class="font-semibold mb-0 me-1">Drag & Drop:</p>
            <button
              @click="() => toggleDragDrop = !toggleDragDrop"
            :class="[(toggleDragDrop ?  'text-end before:left-[5px] before:bg-green-600' : '  text-start before:left-[17px] before:bg-zinc-800' ),' border-none before:bg-zinc-800 text-[#161616] bg-gray-300 w-[30px] h-[15px] px-2 mt-[1px] rounded-[30px] z-[2] cursor-pointer relative toggleShape '] ">
          </button>
          <i class="text-gray-500 text-xs capitalize block ms-1 mt-[1px]">({{toggleDragDrop ? 'Enabled' : 'Disabled'}})</i>
          </div>

          <div v-if="toggleDragDrop" class="flex justify-end items-center gap-2 h-fit w-full">
            <button
                             class="bg-[#e7e5e5] text-black text-center px-2 py-1.5 rounded text-sm"
                              type="button"
                             :onclick="() => {
                                dragDropItemsRef = {}; // reset
                                toggleDragDrop = false; // reset
                             }">
                    Cancel
                            </button>

            <button
                             :class="['bg-[#080808] text-white text-center px-3 py-1.5 rounded text-sm']"
                             type="button"
                             :onclick="handleUpdateHotspotPosition">
                    Save
                            </button>
          </div>

        </div>

        </div>
  </div>
  <div v-if="openDeleteModal"  class="!bg-[rgba(0,0,0,0.9)] z-10 h-full w-full fixed top-0 left-0 flex justify-center items-center" >
          <DeleteModalContent
           :trash="false"
           :loader="deleteLoader"
            @closeModal="(e) =>{openDeleteModal = false;  router.push({ path: route.path,  query: {}, });}"
            @handleDelete="handleDeleteHotspot"
            :dataName="'Hotspot'" />
  </div>
</div>
</Modal>
</template>

<style scoped>
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #adadad;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 10px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Layer */
 .boxShadow{
  box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px, rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px, rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
 }

 .toggleShape::before{
    content: '';
    position: absolute;
    top:3px;
    width: 28%;
    height: 65%;
    font-size: 18px;
    z-index: 2;
    border-radius: 50%;
    transition: all linear 200ms;
}
</style>

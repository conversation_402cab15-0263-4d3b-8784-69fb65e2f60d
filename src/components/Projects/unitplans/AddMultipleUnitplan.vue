<script setup>
import { ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import Spinner from '../../common/Spinner.vue';
import { Form, Field, ErrorMessage, FieldArray } from 'vee-validate';
import { multipleUnitplanSchema } from '../../../validationSchema/unitplan';
import Modal from '../../common/Modal/Modal.vue';
import { useRouter } from 'vue-router';
import { resizeImage } from '../../../helpers/helpers';
import { ProjectStore } from '../../../store/project';
import { createUnitplan, getStyles, getTypes } from '../../../api/projects/unitplans';
import { unitplanTypeList, measurementTypeList} from '../../../helpers/constants';
import Multiselect from 'vue-multiselect';

const router = useRouter();
const projectStore = ProjectStore();
const route = useRoute();
const projectId = ref(route.params.project_id);
const loader = ref(false);

const uploadedFiles = ref();
const initialData = ref();
const unitplanId = ref(route.params.unitplan_id);
const unitTypeList = ["flat", "villa", "villa_floor"];
const exteriorType = ["gallery", "scene"];
const galleryItems = ref([]);
const styleList = ref([]);
const typeList = ref([]);

projectStore.RefreshVirtualTours(projectId.value);
projectStore.RefreshGalleryItems(projectId.value);
projectStore.RefreshScenes(projectId.value);

getStyles(projectId.value).then((res) => { // Get list of styles
  styleList.value = res.map((elem) => {
    return {name: elem};
  });
}).catch((err) => {
  console.log('output->err', err);
});
getTypes(projectId.value).then((res) => {
  typeList.value = res.map((elem) => {
    return {name: elem};
  });
}).catch((err) => {
  console.log('output->err', err);
});
const setMultiselectGalleryOptions = () => {
  if (projectStore.galleryItems) {
    galleryItems.value = Object.values(projectStore.galleryItems)
      .map((item) => ({ '_id': item._id, 'name': item.name }));
  }

};
const addTag = (newTag) => { // To add new style if it is not in the
  const tag = {
    name: newTag,
  };
  styleList.value.push(tag); // Adding to list
  initialData.value.Unitplans[idx].style = tag; // Selecting same new tag
};

const addTagType = (newTag) => {
  const tag = {
    name: newTag,
  };
  typeList.value.push(tag);
  initialData.value.Unitplans[idx].type = tag;
};

setMultiselectGalleryOptions(1);

watch(() => projectStore.galleryItems, () => {
  setMultiselectGalleryOptions(2);
});

watch(uploadedFiles, async (values) => {
  console.log('1111111111111111111111111111111111', values);
  const Unitplans = await Promise.all(values.map(async (file, index) => {
    console.log(index);
    if (!file) {
      return null;
    }

    // Finding the file type and name
    const fileType = file.type.split('/').slice(0, -1).join('');
    const fileName = file.name.split('.').slice(0, -1).join('.');

    let resizedThumbnail = null;
    // Image resize on type image
    if (fileType==='image'){
      resizedThumbnail = await resizeImage(file, 1280, 720);
    }
    // Function to read file and return a promise that resolves to a data URL for preview image
    const readFileAsDataURL = (file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(e);
        reader.readAsDataURL(file);
      });
    };
    const preview = await readFileAsDataURL(resizedThumbnail?resizedThumbnail:file);

    return { file: file, thumbnail: resizedThumbnail, type: {}, unit_type: unitplanId.value?'villa_floor':'', name: fileName, measurement_type: 'sqft', preview: preview, is_furnished: false, is_commercial: false, style: {} };
  }));

  const obj = { Unitplans: Unitplans.filter((item) => !!item) }; // Filter out null results if any
  initialData.value = obj;
});

const onSubmit = (values) => {
  console.log('values', values);

  return new Promise((outerResolve, outerReject) => {
    loader.value = true;
    Promise.all(values.Unitplans.map(async (elem) => {
      let galleryList = null;
      if (elem.gallery_id && elem.gallery_id.length !== 0) {
        galleryList = elem.gallery_id.map((item) => item._id);
      }
      const formData = new FormData();
      formData.append('project_id', projectId.value);
      formData.append('name', elem.name);
      elem.type && elem.type.name && formData.append('type', elem.type.name);
      formData.append('measurement', elem.measurement);
      formData.append('measurement_type', elem.measurement_type);
      formData.append('bedrooms', elem.bedrooms);
      formData.append('bathrooms', elem.bathrooms);
      formData.append('is_furnished', elem.is_furnished);
      formData.append('is_commercial', elem.is_commercial);
      formData.append('unit_type', elem.unit_type);
      elem.balcony_measurement && formData.append('balcony_measurement', elem.balcony_measurement);
      elem.balcony_measurement_type && formData.append('balcony_measurement_type', elem.balcony_measurement_type);
      elem.suite_area && formData.append('suite_area', elem.suite_area);
      elem.suite_area_type && formData.append('suite_area_type', elem.suite_area_type);
      elem.exterior_type && formData.append('exterior_type', elem.exterior_type);
      elem.style && elem.style.name && formData.append('style', elem.style.name);
      elem.scene_id && formData.append('scene_id', elem.scene_id);
      galleryList && formData.append('gallery_id', JSON.stringify(galleryList));
      elem.unit_type==="villa_floor" && formData.append('parent_unitplan', unitplanId.value);
      if (elem.unit_type!=='villa'){
        formData.append('lowRes', elem.thumbnail);
        formData.append('highRes', elem.file);
      }
      elem.tour_id?formData.append('tour_id', elem.tour_id):{};
      await createUnitplan(formData);
    })).then(() => {
      loader.value = false;
      document.dispatchEvent(new Event('getListOfUnitplan'));
      router.go(-1);

      outerResolve();
    }).catch((err) => {
      loader.value = false;
      console.log('Error multiple unitplan creation', err);
      outerReject();
    });
  });
};

</script>

<template>
    <Modal :open="true">
        <div
            class="modal-content-primary sm:max-w-7xl h-screen sm:max-h-[80vh]">
            <div class="p-3 sm:p-6 h-full overflow-y-scroll">
                <div class="mb-2">
                    <h1
                        class="modal-heading-primary">
                        Add Unitplans files</h1>
                    <p class="modal-subheading-primary">Fill details
                        below
                        to Add Unitplans.</p>
                </div>
                     <div v-if="!initialData"
                            class="col-span-auto">
                            <label for="file"
                                class="label-primary">Upload multiple
                                Unitplans Images</label>
                            <div class="mt-2">
                                <Field type="file"
                                v-model="uploadedFiles"
                                   multiple
                                    name="files"
                                    id="files"
                                    autocomplete="files"
                                    class="input-primary"
                                    placeholder="Upload multiple files" />
                            </div>
                            <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                        <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.push(`/projects/${projectId}/unitplans`)"
                            ref="cancelButtonRef">Cancel</button>
                    </div>
                        </div>
        <div>

            <Form v-if="initialData?.Unitplans"
              @submit="onSubmit"
              :initial-values="initialData"
              :validation-schema="multipleUnitplanSchema"
            >
            <div class="h-fit px-8">
                <div :class="[' mb-4 mt-6 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden overflow-x-scroll ' ]">
                    <table class="w-full rounded-lg bg-transparent">
                        <thead>
                            <tr class="bg-gray-50 dark:bg-bg-150">
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">sr</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Unitplans name</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">file</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">thumbnail</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Unit type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900" v-if="!unitplanId">Exterior type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900" v-if="!unitplanId">Scene</th>
                                <th class="py-3 px-6 text-left text-sm font-semibold text-gray-900" v-if="!unitplanId">Gallery items</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Measurement</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Measurement Type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Balcony Measurement</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Balcony Measurement Type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Suite Area</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Suite Area Measurement Type</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Bedrooms</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Bathrooms</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Tour</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">Style</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">isFurnished</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900">isCommercial</th>
                                <th class="p-3 text-left text-sm font-semibold text-gray-900"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <FieldArray name="Unitplans" v-slot="{ fields, remove }">
                                <tr  v-for="(field, idx) in fields"
                                :key="field.key" class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">{{ idx }}</td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <Field :id="`name_${idx}`" :name="`Unitplans[${idx}].name`" style="width: auto;" class="input-primary" />
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Unitplans[${idx}].name`" />
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <img class="max-w-[10rem] h-auto" v-if="initialData?.Unitplans?.[idx]?.preview" :src="initialData.Unitplans[idx].preview" :alt="`file_${idx}`" width="200" height="200"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Unitplans[${idx}].file`" />
                                </td>

                                <td  class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <p  v-if="initialData.Unitplans[idx].thumbnail" >--------</p>
                                <p  v-else >--err--</p>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Unitplans[${idx}].thumbnail`" />
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  <div class="col-span-auto w-52">
                                        <Field :id="`type_${idx}`" :name="`Unitplans[${idx}].type`" :model-value="initialData.Unitplans[idx].type" v-slot="{ type }" class="w-52">
                                          <Multiselect
                                          v-bind="type"
                                          v-model="initialData.Unitplans[idx].type"
                                          tag-placeholder="Add this as new type"
                                          placeholder="Search or add type"
                                          label="name"
                                          track-by="name"
                                          :multiple="false"
                                          :taggable="true"
                                          @tag="addTagType"
                                          :options="typeList" maxHeight="250" >
                                          </Multiselect>

                                        </Field>
                                    <!-- <ErrorMessage as="p"
                                        class="text-sm text-rose-500 mt-1"
                                        :name="`Unitplans[${idx}].style`" /> -->
                            </div>
                          </td>
                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                  <div class="col-span-auto">
                                    <Field
                                    :disabled="unitplanId"
                                    style="width: auto;"
                                    v-model="initialData.Unitplans[idx].unit_type"
                                    as="select" type="text"
                                    :name="`Unitplans[${idx}].unit_type`"
                                    :id="`unit_type${idx}`"
                                    autocomplete="type"
                                    class="select-primary"
                                    :placeholder="`Seclect Type`">
                                    <option value="" disabled>
                                      Choose
                                    </option>
                                    <option value="" disabled
                                    v-if="!unitTypeList">
                                    No Type found ! </option>
                                    <option v-else
                                    :disabled="!unitplanId && option==='villa_floor'"
                                    :value="option"
                                    v-for="option, index in  unitTypeList"
                                    :key="index"
                                    class="text-black">
                                    {{
                                      option }} </option>
                                    </Field>
                                    <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    :name="`Unitplans[${idx}].unit_type`" />
                                  </div>
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap" v-if="!unitplanId">
                                  <div class="col-span-auto">
                                    <Field
                                    style="width: auto;"
                                    v-model="initialData.Unitplans[idx].exterior_type"
                                    as="select" type="text"
                                    :name="`Unitplans[${idx}].exterior_type`"
                                    :id="`type_${idx}`"
                                    autocomplete="type"
                                    class="select-primary"
                                    :placeholder="`Seclect Type`">
                                    <option value="" disabled>
                                      Choose
                                    </option>
                                    <option value="" disabled
                                    v-if="!exteriorType">
                                    No Type found ! </option>
                                    <option v-else
                                    :value="option"
                                    v-for="option, index in  exteriorType"
                                    :key="index"
                                    class="text-black">
                                    {{
                                      option }} </option>
                                    </Field>
                                    <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    :name="`Unitplans[${idx}].exterior_type`" />
                                  </div>
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap " v-if="!unitplanId">
                                  <div class="col-span-auto" v-if="initialData.Unitplans[idx].exterior_type==='scene'">
                                    <Field
                                    style="width: auto;"
                                    v-model="initialData.Unitplans[idx].scene_id"
                                    as="select" type="text"
                                    :name="`Unitplans[${idx}].scene_id`"
                                    :id="`type_${idx}`"
                                    autocomplete="type"
                                    class="select-primary"
                                    :placeholder="`Seclect scene`">
                                    <option value="" disabled>
                                      Choose
                                    </option>
                                    <option value="" disabled
                                    v-if="!projectStore.scenes">
                                    No Type found ! </option>
                                    <option v-else
                                    :value="option.sceneData._id"
                                    v-for="option, index in  projectStore.scenes"
                                    :key="index"
                                    class="text-black">
                                    {{
                                      option.sceneData.name }} </option>
                                    </Field>
                                    <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    :name="`Unitplans[${idx}].scene_id`" />
                                  </div>
                                  <div v-else>
                                    <p>--------</p>
                                  </div>
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap" v-if="!unitplanId">
                                  <div class="col-span-auto" v-if="initialData.Unitplans[idx].exterior_type==='gallery'">
                                    <Field as="input"
                                v-model="initialData.Unitplans[idx].gallery_id"
                                class="sr-only"
                                :name="`Unitplans[${idx}].gallery_id`">
                            </Field>
                            <Multiselect
                                v-model="initialData.Unitplans[idx].gallery_id"
                                :options="galleryItems"
                                :searchable="false"
                                :multiple="true"
                                :taggable="true"
                                placeholder="gallery_id"
                                :close-on-select="false"
                                label="name" track-by="_id"
                                :open-direction="idx===0?'bottom':'top'"
                                :max-height="150">
                            </Multiselect>
                            <ErrorMessage :name="`Unitplans[${idx}].gallery_id`"
                                class="text-sm text-rose-500 mt-1"
                                as="p" />
                                  </div>
                                  <div v-else>
                                    <p>--------</p>
                                  </div>
                                </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Unitplans[idx].measurement" type="text" :id="`measurement_${idx}`" :name="`Unitplans[${idx}].measurement`" style="width: auto;"  class="input-primary" placeholder="Measurement"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Unitplans[${idx}].measurement`" />

                            </td>

                                <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <div class="col-span-auto">
                                    <Field
                                    style="width: auto;"
                                        v-model="initialData.Unitplans[idx].measurement_type"
                                        as="select" type="text"
                                        :name="`Unitplans[${idx}].measurement_type`"
                                        :id="`type_${idx}`"
                                        autocomplete="measurement_type"
                                        class="select-primary"
                                        :placeholder="`Seclect Type`">
                                        <option value="" disabled>
                                            Choose
                                        </option>
                                        <option
                                          :value="option"
                                          v-for="(option, index) in measurementTypeList"
                                          :key="index"
                                          class="text-black"
                                        >
                                          {{ option }}
                                        </option>
                                    </Field>
                                    <ErrorMessage as="p"
                                        class="text-sm text-rose-500 mt-1"
                                        :name="`Unitplans[${idx}].measurement_type`" />
                                </div>

                            </td>

                            <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Unitplans[idx].balcony_measurement" type="text" :id="`balcony_measurement_${idx}`" :name="`Unitplans[${idx}].balcony_measurement`" style="width: auto;"  class="input-primary" placeholder="Balcony Measurement"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Unitplans[${idx}].balcony_measurement`" />

                            </td>

                            <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <div class="col-span-auto">
                                    <Field
                                    style="width: auto;"
                                        v-model="initialData.Unitplans[idx].balcony_measurement_type"
                                        as="select" type="text"
                                        :name="`Unitplans[${idx}].balcony_measurement_type`"
                                        :id="`type_${idx}`"
                                        autocomplete="balcony_measurement_type"
                                        class="select-primary"
                                        :placeholder="`Select Type`">
                                        <option value="" disabled>
                                            Choose
                                        </option>
                                        <option
                                          :value="option"
                                          v-for="(option, index) in measurementTypeList"
                                          :key="index"
                                          class="text-black"
                                        >
                                          {{ option }}
                                        </option>
                                    </Field>
                                    <ErrorMessage as="p"
                                        class="text-sm text-rose-500 mt-1"
                                        :name="`Unitplans[${idx}].balcony_measurement_type`" />
                                </div>

                            </td>

                            <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                <Field v-model="initialData.Unitplans[idx].suite_area" type="text" :id="`suite_area_${idx}`" :name="`Unitplans[${idx}].suite_area`" style="width: auto;"  class="input-primary" placeholder="Suite Area"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Unitplans[${idx}].suite_area`" />

                            </td>

                            <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <div class="col-span-auto">
                                    <Field
                                    style="width: auto;"
                                        v-model="initialData.Unitplans[idx].suite_area_type"
                                        as="select" type="text"
                                        :name="`Unitplans[${idx}].suite_area_type`"
                                        :id="`type_${idx}`"
                                        autocomplete="suite_area_type"
                                        class="select-primary"
                                        :placeholder="`Select Type`">
                                        <option value="" disabled>
                                            Choose
                                        </option>
                                        <option
                                          :value="option"
                                          v-for="(option, index) in measurementTypeList"
                                          :key="index"
                                          class="text-black"
                                        >
                                          {{ option }}
                                        </option>
                                    </Field>
                                    <ErrorMessage as="p"
                                        class="text-sm text-rose-500 mt-1"
                                        :name="`Unitplans[${idx}].suite_area_type`" />
                                </div>

                            </td>

                            <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                              <div class="col-span-auto">
                                <Field
                                style="width: auto;"
                                v-model="initialData.Unitplans[idx].bedrooms"
                                as="select" type="text"
                                :name="`Unitplans[${idx}].bedrooms`"
                                :id="`bedrooms_${idx}`"
                                autocomplete="type"
                                class="select-primary"
                                :placeholder="`Bedrooms`">
                                <option value="" disabled>
                                  Choose
                                </option>
                                <option value="" disabled
                                v-if="!unitplanTypeList">
                                No Type found ! </option>
                                <option v-else
                                :value="option"
                                v-for="option, index in  unitplanTypeList"
                                :key="index"
                                class="text-black">
                                {{
                                  option }} </option>
                                </Field>
                                <ErrorMessage as="p"
                                class="text-sm text-rose-500 mt-1"
                                :name="`Unitplans[${idx}].bedrooms`" />
                              </div>
                            </td>

                            <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                                    <Field v-model="initialData.Unitplans[idx].bathrooms" type="text" :id="`bathrooms_${idx}`" :name="`Unitplans[${idx}].bathrooms`" style="width: auto;"  class="input-primary" placeholder="Bathrooms"/>
                                <ErrorMessage  class="text-sm text-rose-500 mt-1" :name="`Unitplans[${idx}].bathrooms`" />

                            </td>

                            <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">

<div class="col-span-auto">
    <Field
    style="width: auto;"
        v-model="initialData.Unitplans[idx].tour_id"
        as="select" type="text"
        :name="`Unitplans[${idx}].tour_id`"
        :id="`tour_id_${idx}`"
        autocomplete="tour_id"
        class="select-primary"
        :placeholder="`Seclect Tour`">
        <option value="" disabled>Choose</option>
                  <option value="" disabled v-if="!projectStore.virtualtours">
                    No tours found !
                  </option>
                  <option
                    v-else
                    :value="option._id"
                    v-for="(option, index) in projectStore.virtualtours"
                    :key="index"
                    class="text-black"
                  >
                    {{ option.tour_name }}
                  </option>
    </Field>
    <ErrorMessage as="p"
        class="text-sm text-rose-500 mt-1"
        :name="`Unitplans[${idx}].tour_id`" />
</div>

</td>

                            <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                              <div class="col-span-auto w-52">
                                    <Field :id="`style_${idx}`" :name="`Unitplans[${idx}].style`" :model-value="initialData.Unitplans[idx].style" v-slot="{ style }" class="w-52">
                                      <Multiselect
                                      v-bind="style"
                                      v-model="initialData.Unitplans[idx].style"
                                      tag-placeholder="Add this as new style"
                                      placeholder="Search or add Style"
                                      label="name"
                                      track-by="name"
                                      :multiple="false"
                                      :taggable="true"
                                      @tag="addTag"
                                      :options="styleList" maxHeight="250" >
                                      </Multiselect>

                                    </Field>
                                     <ErrorMessage as="p"
                                    class="text-sm text-rose-500 mt-1"
                                    :name="`Unitplans[${idx}].style`" />
                        </div>

                      </td>
                      <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                          <div class="col-span-full mt-2">
                                    <div class="flex justify-start items-start gap-2 w-auto">
                                      <label :for="`is_furnished_${idx}`" class="label-primary">
                                        isFurnished</label
                                      >
                                      <div
                                        class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                                      >
                                        <div class="relative mb-0 p-0">
                                          <Field
                                          :id="`is_furnished_${idx}`"
                                            class="sr-only peer"
                                            :name="`Unitplans[${idx}].is_furnished`"
                                            type="checkbox"
                                            :value="true"
                                          />
                                          <label
                                            :for="`is_furnished_${idx}`"
                                            class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                                          >
                                          </label>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                      </td>

                      <td class="p-3 text-txt-50 dark:text-txt-950  whitespace-nowrap">
                          <div class="col-span-full mt-2">
                                    <div class="flex justify-start items-start gap-2 w-auto">
                                      <label :for="`is_commercial_${idx}`" class="label-primary">
                                        isCommercial</label
                                      >
                                      <div
                                        class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                                      >
                                        <div class="relative mb-0 p-0">
                                          <Field
                                          :id="`is_commercial_${idx}`"
                                            class="sr-only peer"
                                            :name="`Unitplans[${idx}].is_commercial`"
                                            type="checkbox"
                                            :value="true"
                                          />
                                          <label
                                            :for="`is_commercial_${idx}`"
                                            class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                                          >
                                          </label>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                      </td>

                                <button type="button" @click="remove(idx)" class="p-1 bg-gray-200 rounded-lg">
                                      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="close"><rect width="24" height="24" transform="rotate(180 12 12)" opacity="0"/><path d="M13.41 12l4.3-4.29a1 1 0 1 0-1.42-1.42L12 10.59l-4.29-4.3a1 1 0 0 0-1.42 1.42l4.3 4.29-4.3 4.29a1 1 0 0 0 0 1.42 1 1 0 0 0 1.42 0l4.29-4.3 4.29 4.3a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
                                </button>
                                               </tr>
                                <!-- <button type="button" @click="push({ file: '', name: '', type: '', category: '' })">
                                Add Item +
                                </button> -->
                            </FieldArray>

                        </tbody>
                    </table>
                </div>
            </div>
              <div
                        class="mt-4 sm:mt-4 flex justify-center gap-x-3">
                        <button type="button"
                            class="cancel-btn-primary"
                            @click="() => router.push(`/projects/${projectId}/unitplans`)"
                            ref="cancelButtonRef">Cancel</button>
                        <button type="submit"
                            :disabled="loader"
                            class="proceed-btn-primary">Save
                            <Spinner v-if="loader" />
                        </button>
                    </div>
            </Form>
            </div>
            </div>
        </div>
    </Modal>
</template>

<style>

</style>

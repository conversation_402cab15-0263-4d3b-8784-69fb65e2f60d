<script setup>
import { onMounted, ref } from 'vue';
import Modal from '../../common/Modal/Modal.vue';
import { useRoute } from 'vue-router';
import Session from './Session.vue';
import { UserStore } from '../../../store/index';

const route = useRoute();
const projectId = ref(route.params.project_id);
const authStore = UserStore();
if (authStore.auth===null) {
  authStore.GetAuth();
}
const openScheduleSession = ref(false); // Schedule session
const openCreateSession = ref(false); // Create session

/* Hooks */
onMounted(() => {
  if (authStore.auth === null) {
    authStore.GetAuth();
  }

});

</script>

<template>
  <div class="bg-neutral-900 h-full w-full">

    <div class="w-full px-3 py-3 sm:px-6 lg:px-8">
      <div
        class="sm:flex sm:items-center sm:justify-between mb-3">
        <div class="min-w-0 flex-1">
          <h2
            class="text-2xl font-bold -tracking-2 leading-7 text-white sm:truncate sm:text-3xl sm:tracking-tight">
            Sessions</h2>
        </div>
      </div>
    </div>

    <!-- My Projects -->
    <div class="w-[50%] p-4">
      <h2 class="text-white font-semibold mb-3"> Hi PropVr
        , You are now creating the session</h2>
      <div
        class="w-[60%] flex justify-start items-center overflow-hidden gap-x-5 mb-3 bg-gray-200 rounded-md">
        <h4 class="text-black font-medium p-1"> Brigade
          Fairmont </h4>
      </div>

      <div class="mb-3">
        <h6 class="text-white w-full block font-medium mb-2 ">
          About the project </h6>
        <p class="text-white font-normal"> Lorem ipsum
          dolor sit amet consectetur adipisicing elit.
          Dolores sit possimus rerum, voluptates
          laboriosam repudiandae nostrum eius
          doloremque, cupiditate consequatur veritatis
          deserunt tenetur autem voluptatem dolorum
          maxime quos a quidem! </p>
      </div>

      <div class="flex justify-start items-start gap-x-3">
        <button @click="openScheduleSession = true"
          type="button"
          class=" inline-flex justify-center rounded-sm bg-[tranparent] px-3 py-2 text-sm font-semibold text-white shadow-sm ring-1 ring-inset ring-[#1C74D0] sm:col-start-1 sm:mt-0">Create
          a Schedusdfsdfdle </button>
        <button @click="openCreateSession = true"
          type="button"
          class="inline-flex justify-center items-center gap-2 rounded-sm bg-[#1C74D0] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-[#2e6299] focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 ">
          Create Session </button>
      </div>
    </div>

    <!-- Schedule Session -->
    <Modal :open="openScheduleSession">
      <Session @closeModal="() => openScheduleSession = false"
        sessionType="schedule" :projectId="projectId" />
    </Modal>
    <!-- Create Session -->
    <Modal :open="openCreateSession">
      <Session @closeModal="() => openCreateSession = false"
        sessionType="create" :projectId="projectId" />
    </Modal>
  </div>
</template>

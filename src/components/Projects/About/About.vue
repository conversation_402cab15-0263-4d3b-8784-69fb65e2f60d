<script setup>
import TextareaComponent from '../../common/TextareaComponent.vue';
import { ErrorMessage, Field, Form } from 'vee-validate';
import { computed, onMounted, ref, watch } from 'vue';
import NewDropDown from '../../common/NewDropDown.vue';
import { fetchGoogleFonts } from '@/helpers/themehelper';
import { AboutDetailsSchema } from '@/validationSchema/project/settings';
import DnDfileuploader from '../../common/DnDFileUploader.vue';
import { useRoute } from 'vue-router';
import { UpdateProjectSettings, uploadSettingFiles } from '@/api/projects/settings';
import { ProjectStore } from '@/store/project';
import { projectSettingsFormTypes } from '@/enum';
import { processFirebaseFile } from '@/helpers/helpers';
import { uploadFileIcon } from '@/helpers/icons';

const route = useRoute();
const projectStore = ProjectStore();
const project_Id = ref(route.params.project_id); // Project id
const DarkMode = ref(false), LightMode = ref(false), customMode = ref(false);
const showModel = ref(false),  previousData = ref();
const editEnabled = ref(false);
const primarycolor=ref(''), primarytextcolor= ref(''), secondarytextcolor =ref(''), secondarycolor = ref(''), description = ref('');
const updatedLogo = ref(), updatedDarkLogo = ref(), updatedfile = ref(), fontType = ref(''), fontFile=ref(), viewFontFile=ref();
const showPCedit= ref(false), showPTCedit= ref(false), showSCedit= ref(false), showSTCedit= ref(false);
const fontFamilyList = ref([]), newobjt = ref([]), initialData = ref(), updated_at=ref('');
const showLoader = ref(false), showSaved = ref(false), viewupdatedfile=ref(), viewUpdatedLogo=ref(), viewUpdatedDarkLogo=ref(), viewUpdatedImgFile=ref();

const handleTextAreaData = (data) => {
  fontType.value = data;
};

if (project_Id.value){
  projectStore.RefreshSettings(project_Id.value);
}

fetchGoogleFonts().then((res) => {
  fontFamilyList.value = res;
  fontFamilyList.value.unshift('default', 'custom');
  newobjt.value = Object.values(fontFamilyList.value);
}).catch((error) => {
  console.log('error', error);
});

const pendingMode = ref(null);

// Temporary local state for the checkbox to prevent immediate UI change
const createModeComputed = (modeRef, modeName) =>
  computed({
    get: () => modeRef.value,
    set: (value) => {
      if (modeRef.value && !value) {
        showModel.value = true;
        pendingMode.value = modeName;
      } else {
        if (value) {
          DarkMode.value = modeName === "DarkMode";
          LightMode.value = modeName === "LightMode";
          customMode.value = modeName === "CustomMode";
        }
        modeRef.value = value;
      }
    },
  });

const computedCustomMode = createModeComputed(customMode, "CustomMode");
const computedLightMode = createModeComputed(LightMode, "LightMode");
const computedDarkMode = createModeComputed(DarkMode, "DarkMode");

const uiDarkMode = ref(false);
const uiLightMode = ref(false);
const uiCustomMode = ref(false);

const handleModeToggle = (modeRef, uiModeRef, newValue, modeName, event) => {
  if (modeRef.value && !newValue) {
    // When turning OFF, prevent immediate toggle
    showModel.value = true;
    pendingMode.value = modeName;
    event.target.checked = true;
  } else {
    // When turning ON, apply changes
    DarkMode.value = modeName === "DarkMode";
    LightMode.value = modeName === "LightMode";
    customMode.value = modeName === "CustomMode";

    // Sync ui state
    uiDarkMode.value = DarkMode.value;
    uiLightMode.value = LightMode.value;
    uiCustomMode.value = customMode.value;
  }
};

const onModeChange = (modeName, event) => {
  const newValue = event.target.checked;
  if (modeName === "DarkMode") {
    handleModeToggle(DarkMode, uiDarkMode, newValue, "DarkMode", event);
  } else if (modeName === "LightMode") {
    handleModeToggle(LightMode, uiLightMode, newValue, "LightMode", event);
  } else if (modeName === "CustomMode") {
    handleModeToggle(customMode, uiCustomMode, newValue, "CustomMode", event);
  }
};

const resetConfirmation = () => {
  pendingMode.value = null;
  showModel.value = false;
};

const confirmToggle = () => {
  if (pendingMode.value === "DarkMode") {
    DarkMode.value = false;
    uiDarkMode.value = false;
  }
  if (pendingMode.value === "LightMode") {
    LightMode.value = false;
    uiLightMode.value = false;
  }
  if (pendingMode.value === "CustomMode") {
    customMode.value = false;
    uiCustomMode.value = false;
  }
  resetConfirmation();
};

const cancelToggle = () => {
  uiDarkMode.value = DarkMode.value;
  uiLightMode.value = LightMode.value;
  uiCustomMode.value = customMode.value;
  resetConfirmation();
};

const handlefile = (file) => {
  fontFile.value = file;
};

const setupDataCallBack = (values) => {

  if (values) {
    const data = values;

    // Previous Data
    previousData.value = {
      theme: (data?.projectSettings?.theme?.theme ? data?.projectSettings?.theme?.theme : null),
      DarkMode: data.projectSettings.theme.theme === 'dark',
      LightMode: data.projectSettings.theme.theme === 'light',
      customMode: data.projectSettings.theme.theme === 'custom',
      primary: (data?.projectSettings?.theme?.primary ? data?.projectSettings?.theme?.primary : null),
      secondary: (data?.projectSettings?.theme?.secondary ? data?.projectSettings?.theme?.secondary : null),
      primary_text: (data?.projectSettings?.theme?.primary_text ? data?.projectSettings?.theme?.primary_text : null),
      secondary_text: (data?.projectSettings?.theme?.secondary_text ? data?.projectSettings?.theme?.secondary_text : null),
      font_type: (data?.projectSettings?.theme?.font_type ? data?.projectSettings?.theme?.font_type : null),
      description: (data?.description ? data.description : null),
    };

    // Initialize form data for the Form component
    initialData.value = {
      description: data?.description || '',
      dark_mode: data.projectSettings?.theme?.theme === 'dark',
      light_mode: data.projectSettings?.theme?.theme === 'light',
      custom_mode: data.projectSettings?.theme?.theme === 'custom',
      primary: data?.projectSettings?.theme?.primary || '',
      primary_text: data?.projectSettings?.theme?.primary_text || '',
      secondary: data?.projectSettings?.theme?.secondary || '',
      secondary_text: data?.projectSettings?.theme?.secondary_text || '',
      font_type: data?.projectSettings?.theme?.font_type || 'default',
    };

    // Form Initial Values
    if (data?.projectSettings?.general?.updated_at){
      updated_at.value = data.projectSettings.general.updated_at;
    }
    if (data?.projectSettings?.general?.branding_logo){
      processFirebaseFile(data.projectSettings.general.branding_logo).then((result) => {
        if (result) {
          viewUpdatedLogo.value = result;
        }
      });
    }
    if (data?.projectSettings?.general?.branding_logo_dark){
      processFirebaseFile(data.projectSettings.general.branding_logo_dark).then((result) => {
        if (result) {
          viewUpdatedDarkLogo.value = result;
        }
      });
    }

    if (data?.projectSettings?.ale?.welcome_video){
      processFirebaseFile(data.projectSettings.ale.welcome_video).then((result) => {
        if (result) {
          viewupdatedfile.value = result;
        }
      });
    }

    if (data?.projectSettings?.ale?.welcome_thumbnail){
      processFirebaseFile(data.projectSettings.ale.welcome_thumbnail).then((result) => {
        if (result) {
          viewUpdatedImgFile.value = result;
        }
      });
    }

    if (data?.projectSettings?.theme?.primary) {
      primarycolor.value = data.projectSettings.theme.primary;
    }
    if (data?.projectSettings?.theme?.secondary) {
      secondarycolor.value = data.projectSettings.theme.secondary;
    }
    if (data?.projectSettings?.theme?.primary_text) {
      primarytextcolor.value = data.projectSettings.theme.primary_text;
    }
    if (data?.projectSettings?.theme?.secondary_text) {
      secondarytextcolor.value = data.projectSettings.theme.secondary_text;
    }
    if (data?.projectSettings?.theme?.theme) {
      if ( data.projectSettings.theme.theme === 'dark'){
        DarkMode.value = true;
      } else if (data.projectSettings.theme.theme === 'light'){
        LightMode.value = true;
      } else {
        customMode.value=true;
      }
    }
    if (data?.description) {
      description.value = data.description;
    }

    if (data?.projectSettings?.theme?.font_type) {
      fontType.value = data.projectSettings.theme.font_type;
      if ( data?.projectSettings?.theme?.font_type ===  "custom" && data?.projectSettings?.theme?.font_url) {
        processFirebaseFile(data?.projectSettings?.theme?.font_url).then((result) => {
          if (result) {
            viewFontFile.value = result;
          }
        });

      }
    }
  }
};

const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(sourceObj);
  const newObj = {};
  keys.forEach((key) => {
    if (!Array.isArray(sourceObj[key])) {
      if (sourceObj[key] !== compareObj[key]) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

const handleSubmit = async (val) => {
  showLoader.value = true;
  editEnabled.value = !editEnabled.value;
  showPCedit.value = false; showPTCedit.value= false; showSCedit.value = false; showSTCedit.value = false;

  try {
    const prevData = { ...previousData.value }; // prevData track source

    const newCompareObj = Object.fromEntries(
      Object.entries(val).filter(([key]) => key !== 'darkBrandingLogo' && key !== 'lightBrandingLogo' && key !== 'updatefile' && key !== 'font_url'),
    ); // form values

    newCompareObj.theme = newCompareObj.dark_mode ? 'dark'
      : newCompareObj.light_mode ? 'light'
        : newCompareObj.custom_mode ? 'custom'
          : null;

    let formObject = {};

    const parms = frameParms(prevData, newCompareObj);

    // Always proceed with update if there are any changes or if we have form data
    const shouldUpdate = Object.keys(parms).length > 0 ||
                        updatedDarkLogo.value ||
                        updatedLogo.value ||
                        updatedfile.value ||
                        fontFile.value;

    if (shouldUpdate) {
      formObject.project_id = project_Id.value;
      formObject.query = Object.values(projectSettingsFormTypes).reduce((acc, key) => {
        acc[key] = {}; // empty object for each key
        return acc;
      }, {});
      formObject.query.general.updated_at = new Date().toLocaleString();

      // Add all form data to the appropriate sections
      if (parms.theme) {
        formObject.query.theme.theme = parms.theme;
      }
      if (parms.primary) {
        formObject.query.theme.primary = parms.primary;
      }
      if (parms.primary_text) {
        formObject.query.theme.primary_text = parms.primary_text;
      }
      if (parms.secondary) {
        formObject.query.theme.secondary = parms.secondary;
      }
      if (parms.secondary_text) {
        formObject.query.theme.secondary_text = parms.secondary_text;
      }
      if (parms.font_type) {
        formObject.query.theme.font_type = parms.font_type;
      }
      if (parms.description) {
        formObject.description = parms.description;
      }

      // If no changes detected but we have form data, add the current form values
      if (Object.keys(parms).length === 0) {
        console.log('No changes detected, but adding current form values');
        if (newCompareObj.description) {
          formObject.description = newCompareObj.description;
        }
        if (newCompareObj.theme) {
          formObject.query.theme.theme = newCompareObj.theme;
        }
        if (newCompareObj.primary) {
          formObject.query.theme.primary = newCompareObj.primary;
        }
        if (newCompareObj.primary_text) {
          formObject.query.theme.primary_text = newCompareObj.primary_text;
        }
        if (newCompareObj.secondary) {
          formObject.query.theme.secondary = newCompareObj.secondary;
        }
        if (newCompareObj.secondary_text) {
          formObject.query.theme.secondary_text = newCompareObj.secondary_text;
        }
        if (newCompareObj.font_type) {
          formObject.query.theme.font_type = newCompareObj.font_type;
        }
      }
    } else {
      console.log('No changes detected in form data and no files to upload');
      showLoader.value = false;
      return;
    }

    formObject = JSON.parse(
      JSON.stringify(formObject, (key, value) =>
        (value && typeof value === 'object' && Object.keys(value).length === 0 ? undefined : value),
      ),
    );

    if (shouldUpdate) {
      const formData = new FormData();
      updatedDarkLogo.value && updatedDarkLogo.value !== undefined && updatedDarkLogo.value !== null && formData.append('branding_logo_dark', val.darkBrandingLogo);
      updatedLogo.value && updatedLogo.value !== undefined && updatedLogo.value !== null && formData.append('branding_logo', val.lightBrandingLogo);
      updatedfile.value && updatedfile.value !== undefined && updatedfile.value !== null  && updatedfile.value.type.includes('image/') && formData.append('welcome_thumbnail', val.updatedfile );
      updatedfile.value && updatedfile.value !== undefined && updatedfile.value !== null  && updatedfile.value.type.includes('video/') && formData.append('welcome_video', val.updatedfile );
      fontFile.value &&  fontFile.value !== undefined && fontFile.value !== null && formData.append('font_url', val.font_url);

      if (updatedDarkLogo.value || updatedLogo.value || updatedfile.value || fontFile.value) {
        await uploadSettingFiles(formData).then((res) => {
          if (res) {
            if (res.branding_logo) {
              formObject.query.general.branding_logo = res.branding_logo;
            } else if (res.branding_logo_dark) {
              formObject.query.general.branding_logo_dark = res.branding_logo_dark;
            } else if (res.welcome_thumbnail) {
              formObject.query.ale.welcome_thumbnail = res.welcome_thumbnail;
            } else if (res.welcome_video) {
              formObject.query.ale.welcome_video = res.welcome_video;
            } else if  (res.font_url) {
              formObject.query.theme.font_url = res.font_url;
            }
          }
        }).catch((error) => {
          console.log('error', error);
        });
      }
      const result = await UpdateProjectSettings(formObject);
      showLoader.value = false;
      showSaved.value = true;
      setTimeout(() => {
        showSaved.value = false;
      }, 3000);
      if (result) {
        projectStore.settings = result; // Update the store
        setupDataCallBack(result); // Update the data value
        return result;
      }
    }
  } catch (error) {
    console.error('Error in handleSubmit:', error);
    showLoader.value = false;
    throw error;
  }
};

onMounted(() => {
  // Set up watcher for store changes
  watch(() => projectStore.settings, (newVal) => {
    if (newVal){
      setupDataCallBack(projectStore.settings);
    }
  }, { immediate: true }); // Add immediate: true to trigger on mount

  // Watch for description changes
  watch(() => description.value, (newVal) => {
    console.log('About.vue: description changed to:', newVal);
  });

  // Force refresh if no data is available
  if (!projectStore.settings && project_Id.value) {
    projectStore.ForceRefreshSettings(project_Id.value);
  }
});
</script>

<template>
    <div class="w-full h-full flex justify-center items-center relative">
        <Form v-if="projectStore.settings !== null" :validation-schema="AboutDetailsSchema" @submit="handleSubmit"  :initial-values="initialData"  class="w-full h-full flex flex-col gap-5 bg-white">
            <!-- about project -->
            <div class="w-full h-[60px] flex justify-between">
                     <div class="dynamic-heading">
                       <p class="dynamic-topic">Enter Project Details</p>
                       <p class="dynamic-sub-topic">Details provided here will be used to create the project's starting screen.
                         <a href="#" class="font-normal text-xs text-blue-600 dark:text-blue-500 hover:underline">View Sample</a>
                       </p>
                     </div>

                     <div class="flex gap-4 items-center">
                         <p v-if="updated_at" class="text-grey-500 text-sm font-medium"> Modified:{{ updated_at }}</p>
                         <button type="submit" class="py-2.5 px-5 mb-2 text-base font-medium text-white  bg-blue-700 rounded-lg border border-gray-200 flex gap-2 items-center">
                            <svg v-if="showLoader" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                              <path d="M16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0C12.4183 0 16 3.58172 16 8ZM1.45303 8C1.45303 11.6158 4.38421 14.547 8 14.547C11.6158 14.547 14.547 11.6158 14.547 8C14.547 4.38421 11.6158 1.45303 8 1.45303C4.38421 1.45303 1.45303 4.38421 1.45303 8Z" fill="#E5E7EB"/>
                              <path d="M15.0348 6.15202C15.4229 6.05008 15.658 5.65132 15.5213 5.27409C15.2469 4.51711 14.8594 3.80454 14.3707 3.16115C13.7352 2.32454 12.9412 1.62128 12.034 1.09153C11.1268 0.56178 10.1241 0.215909 9.08316 0.0736665C8.28266 -0.0357247 7.47162 -0.0230363 6.67753 0.110065C6.2818 0.176394 6.05008 0.577114 6.15202 0.96519C6.25396 1.35327 6.6511 1.58094 7.04808 1.5226C7.65618 1.43324 8.27506 1.42977 8.88643 1.51332C9.73827 1.62972 10.5588 1.91277 11.3013 2.34631C12.0438 2.77984 12.6935 3.35536 13.2136 4.04002C13.5868 4.5314 13.8879 5.07208 14.109 5.6456C14.2533 6.02 14.6467 6.25396 15.0348 6.15202Z" fill="#3F83F8"/>
                            </svg>

                            <svg v-if="showSaved" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                              <path d="M1 10.1818C1 10.1818 2.5 10.1818 4.5 14C4.5 14 10.0588 4 15 2" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                             Save
                        </button>
                     </div>
            </div>
            <div  class="flex h-full flex-col gap-6 overflow-scroll">
              <div class="w-[50%] flex flex-col relative">
                         <p class="text-gray-900 text-sm font-semibold mb-0.5">About Project </p>
                         <Field name="description" :model-value="description" v-slot="{ field }">
                             <TextareaComponent v-bind="field" disableTextArea="false" @about-text="(data) => { description = data }" :previousData="description" :key="`description-${description}`"/>
                         </Field>

                         <ErrorMessage name="description" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>

              </div>

              <!-- theme selection  -->
              <div class="w-full h-fit flex flex-col gap-5">
                         <div class="flex justify-between w-[50%]">
                            <div>
                             <p class="text-sm font-semibold text-gray-900">Dark Mode </p>
                             <p class="text-xs font-normal text-gray-500">If enabled, this project will be in Dark mode and color can't be customized.</p>
                            </div>
                            <div class="flex justify-center items-end -mb-1">
                                <label class="inline-flex items-center cursor-pointer">
                                     <Field name="dark_mode" :model-value="computedDarkMode" >
                                        <input
                                        v-model="computedDarkMode"
                                          type="checkbox"
                                          @change="onModeChange('DarkMode', $event)"
                                          class="sr-only peer"
                                          :disabled="uiLightMode || uiCustomMode"
                                        />
                                         <div class="relative w-11 h-6 bg-gray-200 rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </Field>

                                     <ErrorMessage name="dark_mode" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>
                                 </label>
                            </div>
                         </div>

                         <div class="flex justify-between w-[50%]">
                            <div>
                             <p class="text-sm font-semibold  text-gray-900">Light Mode </p>
                             <p class="text-xs font-normal  text-gray-500">If enabled, this project will be in Light mode and color can't be customized.</p>
                            </div>
                            <div class="flex justify-center items-end -mb-1 relative">
                                <label class="inline-flex items-center cursor-pointer">
                                     <Field name="light_mode" :model-value="computedLightMode">
                                      <input
                                      v-model="computedLightMode"
                                      type="checkbox"
                                      @change="onModeChange('LightMode', $event)"
                                      class="sr-only peer"
                                      :disabled="uiDarkMode || uiCustomMode"
                                       />

                                      <div class="relative w-11 h-6 bg-gray-200 rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                     </Field>

                                     <ErrorMessage name="light_mode" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>

                                 </label>
                             </div>
                         </div>

                         <div class="flex justify-between w-[50%]">
                            <div>
                             <p class="text-sm font-semibold text-gray-900">Customize Color </p>
                             <p class="text-xs font-normal text-gray-500">If enabled,colors can be customized, but you can't switch between Dark and Light modes</p>
                            </div>
                            <div class="flex justify-center items-end -mb-1 relative">
                                <label class="inline-flex items-center cursor-pointer">
                                     <Field name="custom_mode" :model-value="computedCustomMode" >
                                        <input
                                        v-model="computedCustomMode"
                                          type="checkbox"
                                          @change="onModeChange('CustomMode', $event)"
                                          class="sr-only peer"
                                          :disabled="uiDarkMode || uiLightMode"
                                        />
                                        <div class="relative w-11 h-6 bg-gray-200 rounded-full peer  peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                     </Field>
                                     <ErrorMessage name="custom_mode" as="p"  class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>
                                </label>
                             </div>
                         </div>
              </div>

               <!-- custom theme color selection -->
               <div v-if="customMode"  class="flex flex-col gap-4">
                         <p>Brand Color *</p>
                         <div class="flex gap-5">
                             <div class="flex flex-col gap-1">
                                 <p class="text-xs font-normal text-[#6B7280]">Primary Color</p>
                                 <div class="flex w-[140px] h-[37px] border border-[#D1D5DB] rounded-lg overflow-hidden">
                                     <div class="w-[75%] h-full relative"  :style="{ backgroundColor: primarycolor }">
                                         <Field name="primary" :model-value="primarycolor">
                                          <input v-if="showPCedit" v-model="primarycolor" class="w-full h-full"  type="color" value=""/>
                                         </Field>

                                         <ErrorMessage name="primary" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>

                                     </div>
                                     <div class="w-[25%] flex justify-center items-center" @click="()=>{ showPCedit = true }">
                                         <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                                         <path d="M10.1421 4.48834L11.0765 3.55383C11.5927 3.03772 12.4295 3.03772 12.9456 3.55383C13.4617 4.06995 13.4617 4.90673 12.9456 5.42285L12.0111 6.35735M10.1421 4.48834L4.65284 9.97761C3.95597 10.6745 3.60752 11.0229 3.37026 11.4475C3.133 11.8721 2.89428 12.8747 2.66602 13.8334C3.62474 13.6051 4.62734 13.3664 5.05194 13.1291C5.47654 12.8919 5.82498 12.5435 6.52185 11.8466L12.0111 6.35735M10.1421 4.48834L12.0111 6.35735" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                         <path d="M7.33398 13.8333H11.334" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
                                         </svg>
                                     </div>
                                 </div>
                             </div>

                             <div class="flex flex-col gap-1">
                                 <p class="text-xs font-normal text-[#6B7280]">PrimaryText Color</p>
                                 <div class="flex w-[140px] h-[37px] border border-[#D1D5DB] rounded-lg overflow-hidden">
                                     <div class="w-[75%] h-full relative"  :style="{ backgroundColor: primarytextcolor }">
                                         <Field name="primary_text" :model-value="primarytextcolor">
                                             <input v-if="showPTCedit" v-model="primarytextcolor" class="w-full h-full"  type="color" value=""/>
                                         </Field>
                                         <ErrorMessage name="primary_text" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>

                                     </div>

                                     <div class="w-[25%] flex justify-center items-center" @click="()=>{showPTCedit = true}">
                                             <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                                             <path d="M10.1421 4.48834L11.0765 3.55383C11.5927 3.03772 12.4295 3.03772 12.9456 3.55383C13.4617 4.06995 13.4617 4.90673 12.9456 5.42285L12.0111 6.35735M10.1421 4.48834L4.65284 9.97761C3.95597 10.6745 3.60752 11.0229 3.37026 11.4475C3.133 11.8721 2.89428 12.8747 2.66602 13.8334C3.62474 13.6051 4.62734 13.3664 5.05194 13.1291C5.47654 12.8919 5.82498 12.5435 6.52185 11.8466L12.0111 6.35735M10.1421 4.48834L12.0111 6.35735" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                             <path d="M7.33398 13.8333H11.334" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
                                             </svg>
                                     </div>
                                 </div>
                             </div>

                             <div class="flex flex-col gap-1">
                                 <p class="text-xs font-normal text-[#6B7280]">Secondary Color</p>
                                 <div class="flex w-[140px] h-[37px] border border-[#D1D5DB] rounded-lg overflow-hidden">
                                     <div class="w-[75%] h-full relative"  :style="{ backgroundColor: secondarycolor }">
                                         <Field name="secondary" :model-value="secondarycolor">
                                             <input v-if="showSCedit" v-model="secondarycolor" class="w-full h-full"  type="color" value=""/>
                                         </Field>

                                         <ErrorMessage name="secondary" as="p"  class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>
                                     </div>

                                     <div class="w-[25%] flex justify-center items-center" @click="()=>{showSCedit = true}">
                                         <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                                         <path d="M10.1421 4.48834L11.0765 3.55383C11.5927 3.03772 12.4295 3.03772 12.9456 3.55383C13.4617 4.06995 13.4617 4.90673 12.9456 5.42285L12.0111 6.35735M10.1421 4.48834L4.65284 9.97761C3.95597 10.6745 3.60752 11.0229 3.37026 11.4475C3.133 11.8721 2.89428 12.8747 2.66602 13.8334C3.62474 13.6051 4.62734 13.3664 5.05194 13.1291C5.47654 12.8919 5.82498 12.5435 6.52185 11.8466L12.0111 6.35735M10.1421 4.48834L12.0111 6.35735" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                         <path d="M7.33398 13.8333H11.334" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
                                         </svg>
                                     </div>
                                 </div>
                             </div>

                             <div class="flex flex-col gap-1">
                                 <p class="text-xs font-normal text-[#6B7280]">SecondaryText Color</p>
                                 <div class="flex w-[140px] h-[37px] border border-[#D1D5DB] rounded-lg overflow-hidden">
                                     <div class="w-[75%] h-full relative"  :style="{ backgroundColor: secondarytextcolor }">
                                         <Field name="secondary_text" :model-value="secondarytextcolor">
                                             <input v-if="showSTCedit" v-model="secondarytextcolor" class="w-full h-full" type="color" value=""/>
                                         </Field>

                                         <ErrorMessage name="secondary_text" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>
                                     </div>
                                     <div class="w-[25%] flex justify-center items-center" @click="()=>{showSTCedit = true}">
                                         <svg xmlns="http://www.w3.org/2000/svg" width="16" height="17" viewBox="0 0 16 17" fill="none">
                                         <path d="M10.1421 4.48834L11.0765 3.55383C11.5927 3.03772 12.4295 3.03772 12.9456 3.55383C13.4617 4.06995 13.4617 4.90673 12.9456 5.42285L12.0111 6.35735M10.1421 4.48834L4.65284 9.97761C3.95597 10.6745 3.60752 11.0229 3.37026 11.4475C3.133 11.8721 2.89428 12.8747 2.66602 13.8334C3.62474 13.6051 4.62734 13.3664 5.05194 13.1291C5.47654 12.8919 5.82498 12.5435 6.52185 11.8466L12.0111 6.35735M10.1421 4.48834L12.0111 6.35735" stroke="black" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                         <path d="M7.33398 13.8333H11.334" stroke="black" stroke-width="1.5" stroke-linecap="round"/>
                                         </svg>
                                     </div>
                                 </div>
                             </div>
                         </div>
              </div>

              <!-- input files -->
              <div class="mt-3" v-if="customMode || LightMode || DarkMode">
                         <div  class="flex gap-8">
                             <div v-if="customMode || LightMode" class="flex flex-col gap-2 relative">
                                <p class="text-sm font-semibold text-gray-900">Upload <span v-if="LightMode">Light</span> Project Logo*</p>
                                 <Field name="lightBrandingLogo" id="lightBrandingLogo" :model-value="updatedLogo || viewUpdatedLogo">
                                      <DnDfileuploader inputType="image/*"  class="!w-[240px] !h-[180px]" inputPlaceholder="Click to upload or drag & drop files here" @fileData="(val)=>{ updatedLogo = val}" :previousFileData="viewUpdatedLogo" :key="viewUpdatedLogo"/>
                                 </Field>

                                 <ErrorMessage name="lightBrandingLogo" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px]  text-red-600" />
                            </div>
                            <div v-if="DarkMode" class="flex flex-col gap-2 relative">
                                <p class="text-sm font-semibold text-gray-900">Upload Dark Project Logo*</p>
                                <Field name="darkBrandingLogo"  id="darkBrandingLogo" :model-value="updatedDarkLogo || viewUpdatedDarkLogo">
                                  <DnDfileuploader inputType="image/*" class="!w-[240px] !h-[180px]"  inputPlaceholder="Click to upload or drag & drop files here"
                                  @fileData="(val)=>{ updatedDarkLogo = val}" :previousFileData="viewUpdatedDarkLogo" :key="viewUpdatedDarkLogo"/>
                                </Field>

                                <ErrorMessage name="darkBrandingLogo" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600" />
                            </div>
                             <div class="flex flex-col gap-2 relative">
                                 <p class="text-sm font-semibold text-gray-900">Upload Project Trailer Video/Image*</p>
                                    <Field name="updatedfile" id="updatedfile" :model-value="updatedfile || viewupdatedfile || viewUpdatedImgFile">
                                        <DnDfileuploader inputType="image/*,video/*" class="!w-[240px] !h-[180px]"  inputPlaceholder="Click to upload or drag & drop files here" @fileData="(val)=>{ updatedfile = val}" :previousFileData="viewUpdatedImgFile" :key="`${viewUpdatedImgFile}-${viewupdatedfile}`"/>
                                    </Field>

                                    <ErrorMessage name="updatedfile" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>

                             </div>
                         </div>
              </div>

              <!-- font -->
              <div class="flex flex-row  gap-5 ">
                  <div class="flex flex-col gap-2 mt-2">
                    <p class="text-sm font-semibold text-gray-900">Brand Font *</p>
                    <div class="flex gap-3 relative">
                        <Field name="font_type" :model-value="fontType">
                            <NewDropDown  title="Font" type="edit"  :initialValue="fontType" inputType="radio" :options="newobjt" searchBar=true width="w-52" outerWidth="w-52"
                            @optionSelected="handleTextAreaData" @updatedfontfile="handlefile"  :key="fontType"/>
                        </Field>
                        <ErrorMessage name="font_type" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>
                    </div>
                  </div>

                  <div v-if="fontType === 'custom'" class="relative flex !flex-row items-end">
                    <Field name="font_url" id="font_url" :model-value="fontFile">
                      <span class="absolute top-[3rem] left-4" v-html="uploadFileIcon"></span>
                      <DnDfileuploader inputPlaceholder="Import Font" class="!w-[190px] !h-[40px]" inputType="ttf/*"  @fileData="(val)=>{ fontFile = val}"
                        :previousFileData="viewFontFile" :key="viewFontFile" />
                    </Field>

                   <ErrorMessage name="font_url" as="p" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[30px] text-red-600"/>
                  </div>
              </div>
            </div>
        </Form>

        <div v-if="showModel" class="w-[350px] h-[200px] bg-white border border-gray-600 flex flex-col absolute translate-x-0 translate-y-5 rounded-lg">
            <div class="flex justify-end  pt-3 pr-3 " >
                <div class="cursor-pointer" @click="()=>{ showModel = !showModel}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" viewBox="0 0 12 12" fill="none">
                        <g clip-path="url(#clip0_1249_10451)">
                            <path d="M7.09045 5.99997L11.1665 1.92396C11.24 1.85292 11.2987 1.76795 11.339 1.674C11.3794 1.58004 11.4006 1.47899 11.4015 1.37674C11.4024 1.27449 11.3829 1.17309 11.3442 1.07845C11.3055 0.983811 11.2483 0.89783 11.176 0.825526C11.1037 0.753221 11.0177 0.69604 10.9231 0.65732C10.8284 0.6186 10.727 0.599116 10.6248 0.600005C10.5225 0.600893 10.4215 0.622137 10.3275 0.662496C10.2336 0.702855 10.1486 0.761521 10.0776 0.835071L6.00156 4.91109L1.92555 0.835071C1.78031 0.694795 1.58578 0.617176 1.38387 0.61893C1.18196 0.620685 0.988817 0.701673 0.846038 0.844452C0.70326 0.98723 0.622272 1.18037 0.620517 1.38229C0.618763 1.5842 0.696382 1.77872 0.836658 1.92396L4.91267 5.99997L0.836658 10.076C0.763108 10.147 0.704442 10.232 0.664083 10.326C0.623724 10.4199 0.60248 10.521 0.601592 10.6232C0.600703 10.7255 0.620187 10.8269 0.658907 10.9215C0.697627 11.0161 0.754808 11.1021 0.827113 11.1744C0.899417 11.2467 0.985398 11.3039 1.08004 11.3426C1.17468 11.3813 1.27608 11.4008 1.37833 11.3999C1.48058 11.3991 1.58163 11.3778 1.67558 11.3375C1.76954 11.2971 1.85451 11.2384 1.92555 11.1649L6.00156 7.08886L10.0776 11.1649C10.2228 11.3052 10.4173 11.3828 10.6193 11.381C10.8212 11.3793 11.0143 11.2983 11.1571 11.1555C11.2999 11.0127 11.3809 10.8196 11.3826 10.6177C11.3844 10.4158 11.3067 10.2212 11.1665 10.076L7.09045 5.99997Z" fill="#9CA3AF"/>
                        </g>
                        <defs>
                            <clipPath id="clip0_1249_10451">
                            <rect width="12" height="12" fill="white"/>
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            </div>
            <div class="flex flex-col gap-3 py-3 px-3">
                 <div class="flex justify-center">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <g clip-path="url(#clip0_1249_10453)">
                        <path d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9971 7.34873 18.9426 4.80688 17.0679 2.93215C15.1931 1.05741 12.6513 0.0029116 10 0ZM10 15C9.80222 15 9.60888 14.9413 9.44443 14.8315C9.27998 14.7216 9.15181 14.5654 9.07612 14.3827C9.00044 14.2 8.98063 13.9989 9.01922 13.8049C9.0578 13.6109 9.15304 13.4327 9.2929 13.2929C9.43275 13.153 9.61093 13.0578 9.80491 13.0192C9.99889 12.9806 10.2 13.0004 10.3827 13.0761C10.5654 13.1518 10.7216 13.28 10.8315 13.4444C10.9414 13.6089 11 13.8022 11 14C11 14.2652 10.8946 14.5196 10.7071 14.7071C10.5196 14.8946 10.2652 15 10 15ZM11 11C11 11.2652 10.8946 11.5196 10.7071 11.7071C10.5196 11.8946 10.2652 12 10 12C9.73479 12 9.48043 11.8946 9.2929 11.7071C9.10536 11.5196 9 11.2652 9 11V6C9 5.73478 9.10536 5.48043 9.2929 5.29289C9.48043 5.10536 9.73479 5 10 5C10.2652 5 10.5196 5.10536 10.7071 5.29289C10.8946 5.48043 11 5.73478 11 6V11Z" fill="#9CA3AF"/>
                    </g>
                    <defs>
                        <clipPath id="clip0_1249_10453">
                        <rect width="20" height="20" fill="white"/>
                        </clipPath>
                    </defs>
                    </svg>
                 </div>
                 <p class="text-base font-normal text-gray-500 text-center">You have unsaved changes. Do you want to leave without saving ?</p>
            </div>
            <div class="flex gap-3 justify-center">
                <button  @click="confirmToggle()" type="button" class="py-2.5 px-5 mb-2 text-xs font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 focus:z-10 text-nowrap">Leave</button>
                <button  @click="cancelToggle()" type="button" class="py-2.5 px-5 mb-2 text-xs font-medium text-white focus:outline-none bg-blue-700 rounded-lg border border-gray-200  focus:z-10">Save changes</button>
            </div>
        </div>
    </div>
</template>

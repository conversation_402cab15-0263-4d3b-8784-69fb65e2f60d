<script setup>
import { onMounted, ref, watch } from 'vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { EditProjectSceneSetting } from '../../validationSchema/scene';
import { useRoute } from 'vue-router';
import { Org_Store } from '../../store/organization';
import Modal from '../common/Modal/Modal.vue';
import {updateMasterScene}  from '../../api/masterScene/earth/index';
import router from '../../router';

defineProps({
  scenes: Object,
  sceneId: String,
  sceneData: Object,
});
const organizationStore = Org_Store();
const route = useRoute();

// eslint-disable-next-line vue/no-dupe-keys
const sceneId = ref(route.params.scene_id);
const initialFormData = ref({});

const sample = ()  => {
  if (organizationStore.masterScenes[sceneId.value].sceneData){

    initialFormData.value  = organizationStore.masterScenes[sceneId.value].sceneData;
  }
};

watch(() => organizationStore.masterScenes, () => {

  sample();

});

onMounted(() => {
  if (!organizationStore || organizationStore.length === 0) {
    // OrganizationStore.GetMasterSceneById(sceneId.value);
    router.push(`/masterscenes/earth/${sceneId.value}`);
    alert('Please select');
  }

  if (organizationStore.masterScenes){
    sample();
  }

});

// Sample();

const goback = () => {
  router.go(-1);
};

const handleSubmit = (values) => {

  const detailsFormData = new FormData();
  detailsFormData.append('_id', sceneId.value);
  detailsFormData.append('type', values.type);
  detailsFormData.append('active', values.active);
  detailsFormData.append('info_text', values.info_text);
  detailsFormData.append('parent', values.parent);
  detailsFormData.append('name', values.name);
  detailsFormData.append('highRes', values.high_resolution);
  detailsFormData.append('info_icon', values.info_icon);
  detailsFormData.append('lowRes', values.low_resolution);
  updateMasterScene(detailsFormData).then(() => {
    router.push(`/masterscenes/earth/${sceneId.value}`);
  });

};

</script>

<template>
  <Modal :open="true">
    <div
      class="modal-content-primary"
    >
      <div class="flex justify-center items-center pt-2 sm:hidden">
        <div class="w-16 h-1 opacity-40 bg-neutral-500 rounded-full"></div>
      </div>
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">
            Edit Scene settings.
          </h1>
          <p class="modal-subheading-primary">
            Fill details below to edit Scene settings.
          </p>
        </div>
        <Form
          @submit="handleSubmit"
          :validation-schema="EditProjectSceneSetting"
        >
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">
            <div class="col-span-auto">
              <label
                for="name"
                class="label-primary"
              >
                Name</label
              >
              <Field
                as="input"
                v-model="initialFormData.name"
                type="text"
                name="name"
                id="name"
                autocomplete="off"
                class="input-primary"
                :placeholder="`Enter Name`"
              />
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="name"
              />
            </div>

            <div
              class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
            >
              <label
                for="active"
                class="label-primary"
                >Active</label
              >
              <Field
                v-model="initialFormData.active"
                as="select"
                id="active"
                name="active"
                class="select-primary"
              >
                <option value="true" class="text-black">True</option>
                <option value="false" class="text-black">False</option>
              </Field>
              <ErrorMessage
                name="active"
                class="text-sm text-rose-500 mt-1"
                as="p"
              />
            </div>

            <div
              class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
            >
              <label
                for="type"
                class="label-primary"
                >Type</label
              >
              <Field
                v-model="initialFormData.type"
                as="select"
                id="type"
                name="type"
                class="select-primary"
              >
                <option value="image" class="text-black">Image</option>
                <option value="earth" class="text-black">Earth</option>
              </Field>
              <ErrorMessage
                name="type"
                class="text-sm text-rose-500 mt-1"
                as="p"
              />
            </div>

            <div class="col-span-auto">
              <label
                for="parent"
                class="label-primary"
                >Parent</label
              >
              <Field
                as="input"
                v-model="initialFormData.parent"
                type="text"
                name="parent"
                id="parent"
                class="input-primary"
                :placeholder="`Enter parent Name`"
              />
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="parent"
              />
            </div>

            <div class="col-span-auto">
              <label
                for="info_text"
                class="label-primary"
              >
                Info Text</label
              >
              <Field
                as="input"
                v-model="initialFormData.info_text"
                type="text"
                name="info_text"
                id="info_text"
                class="input-primary"
                :placeholder="`Enter Name`"
              />
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="info_text"
              />
            </div>

            <div class="col-span-auto">
              <label
                for="info_icon"
                class="label-primary"
                >Upload Info icon</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  v-model="initialFormData.info_icon"
                  name="info_icon"
                  id="info_icon"
                  class="input-primary"
                  placeholder="Upload Low Resulation Image"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="info_icon"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label
                for="low_resolution"
                class="label-primary"
                >Upload Low Resolution File</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  v-model="initialFormData.low_resolution"
                  name="low_resolution"
                  id="low_resolution"
                  class="input-primary"
                  placeholder="Upload Low Resulation Image"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="low_resolution"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label
                for="high_resolution"
                class="label-primary"
                >Upload High Resolution File</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  v-model="initialFormData.high_resolution"
                  name="high_resolution"
                  id="high_resolution"
                  class="input-primary"
                  placeholder="Upload High Resulation Image"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="high_resolution"
                />
              </div>
            </div>

            <div
              class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
            >
              <label
                for="parentScene"
                class="label-primary"
                >select Parent Scene</label
              >
              <Field
                as="select"
                id="parentScene"
                name="parentScene"
                class="select-primary"
              >
                <option value="" class="text-gray-700">Choose</option>
                <option
                  value=""
                  v-if="!scenes || Object.keys(scenes).length === 0"
                >
                  No Data found !
                </option>
                <option
                  v-else
                  :value="option.sceneData._id"
                  v-for="(option, index) in scenes"
                  :key="index"
                  class="text-black"
                >
                  {{ option.sceneData.name }}
                </option>
              </Field>
              <ErrorMessage
                name="parentScene"
                class="text-sm text-rose-500 mt-1"
                as="p"
              />
            </div>
          </div>

          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="goback"
              ref="cancelButtonRef"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="proceed-btn-primary"
            >
              Update Changes
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>
<style>
/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>

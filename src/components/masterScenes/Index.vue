<script setup>
import { ref } from 'vue';
import { Org_Store } from '../../store/organization';
import noDataFound from '../../assets/noDataFound.png';
import Button from '../common/Button.vue';
import { useRouter } from 'vue-router';
import { getAllScenes } from '../../api/masterScene/index';

const router = useRouter();

const organizationStore = Org_Store();
const masterScenes = ref(null);

getAllScenes().then((res) => {
  masterScenes.value = res;
  organizationStore.SyncMultipleMasterScenes(res);
});

</script>

<template>
    <div class="">
        <main class="w-full    relative">
            <div
                class="flex items-center justify-between py-3 px-8">
                <div class="min-w-0 flex-1">
                    <h2
                        class="font-bold -tracking-2 leading-7 text-txt-100 dark:text-txt-1000 sm:truncate sm:text-3xl sm:tracking-tight mb-3">
                        Master Scenes
                    </h2>
                    <p class="text-txt-600 dark:text-txt-650">
                        Lorem ipsum dolor sit amet consectetur
                        adipisicing
                        elit.
                    </p>
                </div>
                <Button title="Create New Scene" theme="primary"
                    class="h-10"
                    @click="router.push('/masterscenes/create')">
                    <template v-slot:svg>
                        <svg xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 24 24"
                            class="h-6 w-6 fill-txt-1000 dark:fill-txt-50">
                            <g data-name="Layer 2">
                                <g data-name="plus">
                                    <rect width="24" height="24"
                                        transform="rotate(180 12 12)"
                                        opacity="0" />
                                    <path
                                        d="M19 11h-6V5a1 1 0 0 0-2 0v6H5a1 1 0 0 0 0 2h6v6a1 1 0 0 0 2 0v-6h6a1 1 0 0 0 0-2z" />
                                </g>
                            </g>
                        </svg>
                    </template>
                </Button>
            </div>
            <!-- <BreadCrumb /> -->
            <!-- list of Scene -->
            <div class="mt-3 text-white px-8">
                <div :class="[masterScenes && Object.keys(masterScenes).length !== 0 &&
                    'grid sm:grid-cols-4 gap-x-3 gap-y-3 h-inherit overflow-y-auto grid-container pb-10 px-1',
                    'w-full text-black',
                ]">
                    <div v-if="!masterScenes || Object.keys(masterScenes).length === 0"
                        class="flex w-full m-auto justify-center p-4">
                        <div class="w-full">
                            <img class="w-72 m-auto"
                                :src="noDataFound" alt="" />
                            <p
                                class="text-xs text-center text-neutral-100 mt-2">
                                Oops! No Data Found, Contact
                                admin
                                to add scenes
                            </p>
                        </div>
                    </div>

                    <div v-else
                        v-for="scene, sceneId in masterScenes"
                        :key="sceneId"
                        class="max-sm:rounded-sm rounded-md cursor-pointer max-w-[420px] border-[1px] border-bg-900">
                        <router-link
                            :to="{ path: `/masterscenes/${scene.sceneData.type === 'earth' ? 'earth/' : ''}${scene.sceneData._id}` }">
                            <img :src="scene.sceneData.background ? scene.sceneData.background.low_resolution : scene.sceneData.icon"
                                alt="img"
                                class="rounded-t-md w-full"
                                style="height: 200px; object-fit: cover" />
                            <div
                                class="flex justify-between items-center">
                                <p
                                    class="text-txt-default dark:text-txt-1000 p-2">
                                    {{ scene.sceneData.name }}
                                </p>
                                <button>
                                    <svg xmlns="http://www.w3.org/2000/svg"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        strokeWidth="{1.5}"
                                        stroke="currentColor"
                                        class="w-6 h-6  stroke-bg-default dark:stroke-bg-1000">
                                        <path
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
                                    </svg>
                                </button>
                            </div>
                        </router-link>
                    </div>
                </div>
            </div>
        </main>
    </div>
</template>

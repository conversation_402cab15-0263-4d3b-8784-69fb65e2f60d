<script setup>
// Import projectData from '@/projectData.json';
import { onMounted } from 'vue';
import GlobeComponent from '../../views/globeComponent.vue';
import { Org_Store } from '../../store/organization';

// Const sceneData = ref(projectData['master']['scene'][sceneId.value]);

const newOrganizationStore = Org_Store();
onMounted(() => {
  if (!newOrganizationStore.masterScenes) {
    newOrganizationStore.RefreshMasterScenes();
  }
});

</script>

<template>
    <div class="bg-neutral-900 h-full w-full text-white">

        <GlobeComponent v-if="newOrganizationStore.masterScenes"  :scenes="scenes" ></GlobeComponent>

    </div>
</template>

<script setup>
import { ref } from 'vue';
import Spinner from '../common/Spinner.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { sceneSchema } from '../../validationSchema/scene';
import { getCookie } from '../../helpers/domhelper';
import { Org_Store } from '../../store/organization';
import Modal from '../common/Modal/Modal.vue';
import { useRouter } from 'vue-router';
import { uiOperations } from '../../store/uiOperations';
import { createScene } from '../../api/organization/index';
const uiStore = uiOperations();

const router = useRouter();
const typeList = [{ name: 'earth', value: 'earth' }, { name: 'image', value: 'image' }];
const newOrganizationStore = Org_Store();
const selectedType = ref();
const parent = ref();
const loader = ref(false);

newOrganizationStore.RefreshMasterScenes();

const handleCreateMasterScene = (data) => {
  loader.value = true;
  createScene(data).then(() => {
    router.go(-1);
  }).catch((err) => {
    console.log(err);
    uiStore.handleApiErrorMessage(err.message._message);
  }).finally(() => {
    loader.value = false;
  });
};

const handleSubmitVeeValidate = (values) => {
  // Update the isActive state to false.
  if (values.isActive === undefined || values.isActive === null) {
    values.isActive = false;
  }
  if (values.root === undefined || values.root === null) {
    values.root = false;
  }
  if (values.clouds === undefined || values.clouds === null) {
    values.clouds = false;
  }
  if (values.parent === undefined || values.parent === null || values.parent === '') {
    delete values.parent;
  }

  if (values) {
    const formData = new FormData();
    const organization = getCookie('organization');
    formData.append('organization_id', organization);
    if (values.sceneType !== 'earth') {
      formData.append('lowRes', values.lowResolutionFile);
      formData.append('highRes', values.highResolutionFile);
    }
    formData.append('name', values.sceneName);
    formData.append('type', values.sceneType);
    if (values.parent) {
      formData.append('parent', values.parent);
    }
    formData.append('info_text', values.infoText);
    formData.append('active', values.isActive);
    formData.append('root', values.root);
    formData.append('clouds', values.clouds);
    handleCreateMasterScene(formData);
  }
  console.log(values);
};

</script>

<template>
  <Modal :open="true">
    <div class="modal-content-primary">
      <div class="p-3 sm:p-6">
        <div class="mb-2">
          <h1 class="modal-heading-primary">Create Master Scene</h1>
          <p class="modal-subheading-primary">
            Fill details below to create Master Scene.
          </p>
        </div>
        <Form
          :validation-schema="sceneSchema"
          @submit="handleSubmitVeeValidate"
        >
          <div class="grid grid-cols-2 gap-x-4 gap-y-3 mt-3">
            <div class="col-span-auto">
              <label for="sceneName" class="label-primary"> Name</label>
              <Field
                as="input"
                type="text"
                name="sceneName"
                autocomplete
                id="sceneName"
                class="input-primary"
                :placeholder="`Enter Master Scene Name`"
              />
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="sceneName"
              />
            </div>

            <div class="col-span-auto">
              <label for="sceneType" class="label-primary"> Type</label>
              <Field
                v-model="selectedType"
                as="select"
                type="text"
                name="sceneType"
                id="sceneType"
                autocomplete="sceneType"
                class="select-primary"
                :placeholder="`Enter Master Type`"
              >
                <option value="" disabled>Choose</option>
                <option value="" disabled v-if="!typeList">
                  No Type found !
                </option>
                <option
                  v-else
                  :value="option.value"
                  v-for="(option, index) in typeList"
                  :key="index"
                  class="text-black"
                >
                  {{ option.name }}
                </option>
              </Field>
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="sceneType"
              />
            </div>

            <div v-if="selectedType !== 'earth'" class="col-span-auto">
              <label for="lowResolutionFile" class="label-primary"
                >Upload Low Resolution File</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  name="lowResolutionFile"
                  id="lowResolutionFile"
                  class="input-primary"
                  placeholder="Upload Low Resulation Image"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="lowResolutionFile"
                />
              </div>
            </div>

            <div v-if="selectedType !== 'earth'" class="col-span-auto">
              <label for="highResolutionFile" class="label-primary"
                >Upload High Resolution File</label
              >
              <div class="mt-2">
                <Field
                  type="file"
                  name="highResolutionFile"
                  id="highResolutionFile"
                  autocomplete="highRes"
                  class="input-primary"
                  placeholder="Upload High Resulation Image"
                />
                <ErrorMessage
                  as="p"
                  class="text-sm text-rose-500 mt-1"
                  name="highResolutionFile"
                />
              </div>
            </div>

            <div class="col-span-auto">
              <label for="infoText" class="label-primary"> Info Text</label>
              <Field
                as="input"
                type="text"
                name="infoText"
                class="input-primary"
                :placeholder="`Enter Info Text`"
              />
              <ErrorMessage
                as="p"
                class="text-sm text-rose-500 mt-1"
                name="infoText"
              />
            </div>

            <div
              class="relative w-full h-fit flex-col justify-start items-start inline-flex bg-inherit mb-2"
            >
              <label for="parent" class="label-primary"
                >select Parent Scene</label
              >
              <Field
                v-model="parent"
                as="select"
                id="parent"
                name="parent"
                class="select-primary"
              >
                <option value="" class="text-gray-700">Choose</option>
                <option
                  value=""
                  disabled
                  v-if="
                    !newOrganizationStore.masterScenes ||
                    Object.keys(newOrganizationStore.masterScenes).length === 0
                  "
                >
                  No Data found !
                </option>
                <option
                  :value="option.sceneData._id"
                  v-for="(option, index) in newOrganizationStore.masterScenes"
                  :key="index"
                  class="text-black"
                >
                  {{ option.sceneData.name }}
                </option>
              </Field>
              <ErrorMessage
                name="parent"
                class="text-sm text-rose-500 mt-1"
                as="p"
              />
            </div>

            <div class="col-span-full mt-2">
              <div class="flex justify-start items-start gap-2 w-auto">
                <label for="isActive" class="label-primary"> isActive</label>
                <div
                  class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                >
                  <div class="relative mb-0 p-0">
                    <Field
                      id="isActive"
                      class="sr-only peer"
                      name="isActive"
                      type="checkbox"
                      :value="true"
                    />
                    <label
                      for="isActive"
                      class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                    >
                    </label>
                  </div>
                  <ErrorMessage
                    as="p"
                    class="text-sm text-rose-500 mt-1"
                    name="isActive"
                  />
                </div>
              </div>
            </div>

            <div class="col-span-full mt-2">
              <div class="flex justify-start items-start gap-2 w-auto">
                <label for="clouds" class="label-primary"> isCloude</label>
                <div
                  class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                >
                  <div class="relative mb-0 p-0">
                    <Field
                      id="clouds"
                      class="sr-only peer"
                      name="clouds"
                      type="checkbox"
                      :value="true"
                    />
                    <label
                      for="clouds"
                      class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                    >
                    </label>
                  </div>
                  <ErrorMessage
                    as="p"
                    class="text-sm text-rose-500 mt-1"
                    name="clouds"
                  />
                </div>
              </div>
            </div>

            <div class="col-span-full mt-2">
              <div class="flex justify-start items-start gap-2 w-auto">
                <label for="root" class="label-primary"> isRootScene</label>
                <div
                  class="relative inline-flex flex-col items-start mb-0 cursor-pointer"
                >
                  <div class="relative mb-0 p-0">
                    <Field
                      id="root"
                      class="sr-only peer"
                      name="root"
                      type="checkbox"
                      :value="true"
                    />
                    <label
                      for="root"
                      class="w-9 h-[20px] mb-0 peer-focus:outline-none rounded-full peer bg-gray-500 peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-blue-600 cursor-pointer"
                    >
                    </label>
                  </div>
                  <ErrorMessage
                    as="p"
                    class="text-sm text-rose-500 mt-1"
                    name="root"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="mt-4 sm:mt-4 flex justify-center gap-x-3">
            <button
              type="button"
              class="cancel-btn-primary"
              @click="() => router.go(-1)"
              ref="cancelButtonRef"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="proceed-btn-primary"
            >
              Save
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </div>
  </Modal>
</template>

<style src="vue-multiselect/dist/vue-multiselect.css">
::-webkit-scrollbar {
  width: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
</style>

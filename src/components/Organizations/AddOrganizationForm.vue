<script setup>
import Modal from '@/components/common/Modal/Modal.vue';
import { Form, Field, ErrorMessage } from 'vee-validate';
import { createOrganizationSchema } from '../../validationSchema/organization';
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { CreateOrganization } from '@/api/organization';
import Spinner from '@/components/common/Spinner.vue';
import DnDFileUploader from '@/components/common/DnDFileUploader.vue';
import { UserStore } from '@/store';
import StatusPopup from '../common/StatusPopup.vue';

const router = useRouter();
const Store = UserStore();
const updatedfile = ref();
const openModal = ref(true);
const loader = ref(false);
const showPopup = ref(false);
const isError = ref(false);
const handleButtonClick = () => {
  showPopup.value = false;
  router.push('/projects').then(() => {
    if (isError.value) {
      return;
    }
    window.location.reload();
  });
};
const handleCreateOrganization = (payload) => {
  loader.value = true;
  isError.value = false;
  CreateOrganization(payload)
    .then(() => {
    })
    .catch((err) => {
      isError.value = true;
      console.log('Create organization error: ', err);
    })
    .finally(() => {
      openModal.value = false;
      showPopup.value = true;
      loader.value = false;
    });
};

const handleForm = (values) => {
  console.log("handleForm called with values:", values);
  // Create FormData for file upload
  const formData = new FormData();
  formData.append('name', values.name);
  formData.append('founding_date', values.founding_date);
  formData.append('contact_email', values.contact_email);
  formData.append('phone_number', values.phone_number);
  formData.append('address', values.address);
  formData.append('website', values.website || '');
  formData.append('max_users', values.max_users);
  formData.append('is_public', 'true');

  // Only append thumbnail if file exists
  if (updatedfile.value) {
    formData.append('thumbnail', updatedfile.value);
    console.log("Thumbnail file added:", updatedfile.value.name, updatedfile.value.size);
  } else {
    console.error("No thumbnail file selected!");
    return;
  }
  handleCreateOrganization(formData);
};
</script>

<template>
  <div v-if="!Store.isMobile" class="select-none">
    <Modal :open="openModal">
      <div class="lg:w-[40%] lg:max-h-[98%] overflow-y-auto md:w-[85%] w-[95%] rounded-lg relative bg-white flex flex-col gap-0 max-w-4xl p-3 z-100">
        <div class="">
          <h1 class="text-xl font-bold">Create Organization</h1>
        </div>
        <div class="absolute top-6 right-6 cursor-pointer" @click="router.go(-1)">
          <svg width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="x" clip-path="url(#clip0_707_6588)">
              <path id="Vector" d="M9.0625 1.9375L6.25 5L9.0625 8.0625L8.0625 9.0625L5 6.25L1.9375 9.0625L0.9375 8.0625L3.75 5L0.9375 1.9375L1.9375 0.9375L5 3.75L8.0625 0.9375L9.0625 1.9375Z" fill="#6B7280"/>
            </g>
            <defs>
              <clipPath id="clip0_707_6588">
                <rect width="10" height="10" fill="white"/>
              </clipPath>
            </defs>
          </svg>
        </div>
        <Form :validation-schema="createOrganizationSchema" @submit="handleForm" class="flex flex-col justify-center select-none m-0">
          <!-- Organization Name & Founding Date -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-3 gap-y-2 mb-3 mt-2">
            <div>
              <label for="name" class="label-primary">Organization Name*</label>
              <Field type="text" name="name" id="name" class="input-primary w-full !bg-gray-50 !py-2"
                placeholder="Enter Organization Name" />
              <ErrorMessage as="p" class="text-xs text-rose-500 mt-1 capitalize" name="name" />
            </div>

            <div>
              <label for="founding_date" class="label-primary">Founding Date*</label>
              <Field type="date" name="founding_date" id="founding_date" class="input-primary w-full !bg-gray-50 !py-2" />
              <ErrorMessage as="p" class="text-xs text-rose-500 mt-1 capitalize" name="founding_date" />
            </div>
          </div>

          <!-- Contact Email & Phone Number -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-3 gap-y-2 mb-3">
            <div>
              <label for="contact_email" class="label-primary">Contact Email*</label>
              <Field type="email" name="contact_email" id="contact_email" class="input-primary w-full !bg-gray-50 !py-2"
                placeholder="<EMAIL>" />
              <ErrorMessage as="p" class="text-xs text-rose-500 mt-1 capitalize" name="contact_email" />
            </div>

            <div>
              <label for="phone_number" class="label-primary">Phone Number*</label>
              <Field type="tel" name="phone_number" id="phone_number" class="input-primary w-full !bg-gray-50 !py-2"
                placeholder="1234567890" />
              <ErrorMessage as="p" class="text-xs text-rose-500 mt-1 capitalize" name="phone_number" />
            </div>
          </div>

          <!--Address (Full Width) -->
          <div class="mb-3">
            <label for="address" class="label-primary">Address*</label>
            <Field as="textarea" name="address" id="address" rows="3" class="input-primary w-full !bg-gray-50 !py-2"
              placeholder="Enter organization address" />
            <ErrorMessage as="p" class="text-xs text-rose-500 mt-1 capitalize" name="address" />
          </div>

          <!--Max Users & Website -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-x-3 gap-y-2 mb-3">
            <div>
              <label for="max_users" class="label-primary">Maximum Users*</label>
              <Field type="number" name="max_users" id="max_users" class="input-primary w-full !bg-gray-50 !py-2"
                placeholder="1000" />
              <ErrorMessage as="p" class="text-xs text-rose-500 mt-1 capitalize" name="max_users" />
            </div>

            <div>
              <label for="website" class="label-primary">Website*</label>
              <Field type="url" name="website" id="website" class="input-primary w-full !bg-gray-50 !py-2"
                placeholder="https://www.organization.com" />
              <ErrorMessage as="p" class="text-xs text-rose-500 mt-1 capitalize" name="website" />
            </div>
          </div>

          <!--Thumbnail (Full Width) -->
          <div class="mb-3">
            <label for="organizationLogo" class="text-sm font-medium text-black">Upload Organization Logo*</label>
            <div class="h-[150px] relative flex justify-center items-center bg-gray-50 !border-[1px] border-gray-400 border-opacity-100 rounded-md mt-1">
              <Field name="thumbnail" id="organizationLogo" v-model="updatedfile">
                <DnDFileUploader inputType="image/*" class="!w-full !h-full" inputPlaceholder="Drag and drop a files here, or:" @fileData="(val)=>{updatedfile=null;updatedfile = val}"/>
              </Field>
              <ErrorMessage as="p" class="text-xs text-rose-500 mt-1 absolute bottom-[-20px] capitalize" name="thumbnail" />
            </div>
          </div>

          <!-- Submit Button -->
          <div class="w-full mt-1">
            <button type="submit" :disabled="loader"
              class="bg-[#1c64f2] w-full hover:bg-[#1a56db] active:bg-[#1e429f] text-white font-medium py-2 px-2 rounded-lg flex items-center justify-center gap-2">
              {{ loader ? 'Creating...' : 'Create Organization' }}
              <Spinner v-if="loader" />
            </button>
          </div>
        </Form>
      </div>
    </Modal>
  </div>

  <!-- Mobile Version -->
  <div v-else class="h-full select-none">
    <Modal :open="openModal">
      <div class="modal-content-primary !border-0 !bg-gray-50 h-full w-full flex flex-col gap-4 px-3 py-2 overflow-hidden">
        <div class="w-full h-[6%] flex gap-4 items-center py-4">
          <div class="cursor-pointer" @click="router.go(-1)">
            <svg width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M24 8.5H5.74L13.85 0.65C14.2383 0.283354 14.4581 -0.0132023 14.4581 -0.375C14.4581 -0.736798 14.2383 -1.03335 13.85 -1.4C13.4617 -1.76665 13.1383 -1.97581 12.76 -1.97581C12.3817 -1.97581 12.0583 -1.76665 11.67 -1.4L1.4 8.5C1.03335 8.8883 0.824193 9.21165 0.824193 9.575C0.824193 9.93835 1.03335 10.2617 1.4 10.65L11.67 20.4C12.0583 20.7667 12.3817 20.9758 12.76 20.9758C13.1383 20.9758 13.4617 20.7667 13.85 20.4C14.2383 20.0333 14.4581 19.7098 14.4581 19.348C14.4581 18.9862 14.2383 18.6628 13.85 18.296L5.74 10.5H24C24.3783 10.5 24.7017 10.2908 25.07 9.924C25.4383 9.55735 25.6475 9.23398 25.6475 8.856C25.6475 8.47802 25.4383 8.15465 25.07 7.788C24.7017 7.42135 24.3783 7.212 24 7.212V8.5Z" fill="#6B7280"/>
            </svg>
          </div>
          <p class="text-lg font-bold">Create Organization</p>
        </div>

        <Form :validation-schema="createOrganizationSchema" @submit="handleForm" class="h-[94%] w-[90%] mx-auto flex flex-col justify-center scroller">
          <div class="flex flex-col overflow-y-scroll noScrollApperance">
            <!-- Form Fields - All Full Width -->
            <div class="flex flex-col space-y-4">
          <!-- Organization Name -->
          <div>
            <label for="name_mobile" class="label-primary">Organization Name*</label>
            <Field type="text" name="name" id="name_mobile" class="input-primary !bg-gray-50"
              placeholder="Enter Organization Name" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="name" />
          </div>

          <!-- Founding Date -->
          <div>
            <label for="founding_date_mobile" class="label-primary">Founding Date*</label>
            <Field type="date" name="founding_date" id="founding_date_mobile" class="input-primary !bg-gray-50" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="founding_date" />
          </div>

          <!-- Contact Email -->
          <div>
            <label for="contact_email_mobile" class="label-primary">Contact Email*</label>
            <Field type="email" name="contact_email" id="contact_email_mobile" class="input-primary !bg-gray-50"
              placeholder="<EMAIL>" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="contact_email" />
          </div>

          <!-- Phone Number -->
          <div>
            <label for="phone_number_mobile" class="label-primary">Phone Number*</label>
            <Field type="tel" name="phone_number" id="phone_number_mobile" class="input-primary !bg-gray-50"
              placeholder="1234567890" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="phone_number" />
          </div>

          <!-- Address -->
          <div>
            <label for="address_mobile" class="label-primary">Address*</label>
            <Field as="textarea" name="address" id="address_mobile" rows="3" class="input-primary !bg-gray-50"
              placeholder="Enter organization address" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="address" />
          </div>

          <!-- Max Users -->
          <div>
            <label for="max_users_mobile" class="label-primary">Maximum Users*</label>
            <Field type="number" name="max_users" id="max_users_mobile" class="input-primary !bg-gray-50"
              placeholder="1000" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="max_users" />
          </div>

          <!-- Website -->
          <div>
            <label for="website_mobile" class="label-primary">Website*</label>
            <Field type="url" name="website" id="website_mobile" class="input-primary !bg-gray-50"
              placeholder="https://www.organization.com" />
            <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 capitalize" name="website" />
          </div>

          <!-- Thumbnail (Full Width) -->
          <div>
            <label for="organizationLogo_mobile" class="text-sm font-medium text-black">Organization Logo*</label>
            <div class="h-[200px] relative flex justify-center items-center bg-gray-50 !border-[1px] border-gray-400 border-opacity-100 rounded-md mt-1">
              <Field name="thumbnail" id="organizationLogo_mobile" v-model="updatedfile">
                <DnDFileUploader inputType="image/*" class="!w-full !h-full" inputPlaceholder="Drag and drop a files here, or:" @fileData="(val)=>{updatedfile=null;updatedfile = val}"/>
              </Field>
              <ErrorMessage as="p" class="text-sm text-rose-500 mt-1 absolute bottom-[-20px] capitalize" name="thumbnail" />
            </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-center mt-4 pb-4">
            <button type="submit" :disabled="loader"
              class="w-full bg-[#1c64f2] hover:bg-[#1a56db] active:bg-[#1e429f] text-white font-medium py-2 px-3 rounded-lg flex items-center justify-center gap-2">
              {{ loader ? 'Creating...' : 'Create Organization' }}
              <Spinner v-if="loader" />
            </button>
          </div>
          </div>
        </Form>
      </div>
    </Modal>
  </div>

    <StatusPopup
    :show="showPopup"
    :type="isError ? 'error' : 'success'"
    :title="!isError && 'Organization Created Successfully'"
    :message="isError
      ? 'Whoops! Unable to create organization.'
      : 'Your organization has been created successfully. Click continue to start using the platform.'"
    :buttonText="isError ? 'Try Again' : 'Continue'"
    @buttonClick="handleButtonClick"
  />

</template>

<style scoped>
.label-primary {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #111928;
}

.input-primary {
  margin-top: 0.25rem;
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  color: #374151;
  font-size: 0.875rem;
}

.input-primary:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.scroller {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scroller::-webkit-scrollbar {
  display: none;
}

.noScrollApperance::-webkit-scrollbar {
  display: none;
}
</style>

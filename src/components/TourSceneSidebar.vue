<script setup>
import { onMounted, onUnmounted, ref, watch } from 'vue';
import router from '@/router';
import { OrganizationStore } from '../store/archiveprojects';
// Import Modal from "./Modal/Modal.vue";
// Import UpdateSvgLayers from "./ModalContent/UpdateSvgLayers.vue";
// Import TourSceneModal from "./ModalContent/TourSceneModal.vue";
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router';
import { ListSceneInTour } from '../api/projects/tours';
import { ProjectStore } from '../store/project';
import { uiOperations } from '../store/uiOperations';
const projectStore = ProjectStore();
const route = useRoute();
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);
const organizationStore = OrganizationStore();
const selectedSvg = ref();
const routerPath = useRouter();
const imgList = ref([]);
const uiStore = uiOperations();

onBeforeRouteLeave((to, from, next) => {
  if (to.fullPath.endsWith('/create')) {
    const newPath = to.fullPath.replace('/create', '');
    next(newPath);
  } else {
    next();
  }
});

const handleBackButton = () => {
  const currentPath = routerPath.currentRoute.value.fullPath;
  if (currentPath.includes('/create')) {
    window.history.go(-2);
  }
};
onUnmounted(() => {
  window.removeEventListener('popstate', handleBackButton);
});
onMounted(() => {
  const data = { tour_id: tourId.value, project_id: projectId.value };

  ListSceneInTour(data)
    .then((res) => {
      imgList.value = res;
      projectStore.SyncMultipleVirtualTours({ [res._id]: { ...res } });
    })
    .catch((err) => {
      console.log(err);
      uiStore.handleApiErrorMessage(err.message);
    });
});

watch(
  () => selectedSvg.value,
  () => {
    if (!selectedSvg.value) {
      router.push({ path: route.path, query: {} });
    } else {
      router.push({
        path: route.path,
        query: { svgId: selectedSvg.value._id },
      });
    }
  },
);
watch(
  () => projectStore.virtualtours,
  () => {
    imgList.value = projectStore.virtualtours[tourId.value];
  },
);

const imgHandler = (img) => {
  organizationStore.selectScenePreview(img);
};
</script>

<template>
  <!-- Static sidebar for desktop -->
  <div class="fixed inset-y-0 z-40 flex w-72 flex-col">
    <!-- Sidebar component, swap this element with another sidebar if you like -->
    <div
      class="flex grow flex-col gap-y-4 overflow-y-auto bg-[#262626] px-4 pb-4"
    >
      <div class="flex h-16 shrink-0 items-center">
        <router-link to="/projects" class="p-2">
          <svg
            class="h-7 w-auto"
            viewBox="0 0 202 40"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M32.055 32.8107H26.9987C26.8943 32.6001 26.9316 32.4074 26.9316 32.2132C26.9316 29.7054 26.9405 27.1976 26.9196 24.6898C26.9067 24.5044 26.9454 24.3191 27.0315 24.1545C27.1176 23.9899 27.2477 23.8526 27.4072 23.7578C30.2165 21.7429 33.0123 19.707 35.8111 17.6772C36.4076 17.25 36.2451 17.2948 36.7655 17.6772C39.5494 19.6891 42.3199 21.7175 45.1157 23.7145C45.5273 24.0132 45.6973 24.3015 45.6928 24.8183C45.666 27.8742 45.6794 30.9317 45.6794 33.9892C45.6794 34.1565 45.6794 34.3238 45.6794 34.491C45.6794 34.6987 45.7718 34.7898 45.9776 34.7898C48.6929 34.7898 51.4246 34.963 54.0296 33.9444C59.4468 31.8264 62.7929 27.9325 63.7025 22.1656C64.952 14.2403 59.6973 7.59512 52.6399 5.71314C51.3264 5.36299 49.973 5.18574 48.6139 5.18588C39.0976 5.17692 29.5813 5.17692 20.065 5.18588C16.8436 5.16935 13.7047 6.20538 11.1242 8.13691C8.54368 10.0684 6.66225 12.7901 5.76515 15.8893C4.81947 19.1409 5.03114 22.6207 6.36394 25.7333C7.69674 28.8459 10.0678 31.3978 13.0716 32.9526C15.3773 34.1761 17.9494 34.8088 20.5585 34.7943C24.0641 34.7793 27.5668 34.7943 31.0769 34.7943C31.226 34.7943 31.3617 34.7943 31.5048 34.7943C32.1192 34.8032 32.1326 34.8196 32.061 35.4589C31.9307 36.4839 31.4975 37.4466 30.8171 38.2232C30.1366 38.9998 29.24 39.5549 28.2423 39.8174C27.898 39.9148 27.5427 39.968 27.185 39.9757C24.4667 39.9757 21.744 40.0489 19.0286 39.9413C14.0677 39.7442 9.77179 37.8622 6.17969 34.4388C2.87388 31.2872 0.845966 27.4187 0.215224 22.8944C-0.625766 16.7974 0.995078 11.3934 5.12696 6.81246C8.59083 2.97978 12.9598 0.816998 18.0684 0.15681C18.8249 0.0593485 19.587 0.0119454 20.3498 0.0149129C29.9615 0.0149129 39.5732 -0.029894 49.185 0.035826C55.8726 0.0821288 61.2451 2.9051 65.2383 8.25083C67.6241 11.4472 68.8632 15.0902 69.0273 19.0946C69.2524 24.6301 67.4586 29.4127 63.6906 33.4335C60.3594 36.9944 56.226 39.0526 51.4216 39.7755C50.5489 39.9057 49.6677 39.9696 48.7854 39.9667C46.2505 39.9667 43.729 39.9667 41.2 39.9667C40.3978 39.9667 40.4962 40.0623 40.4962 39.2199C40.4962 35.3021 40.4843 31.3843 40.5067 27.468C40.5235 27.2483 40.4785 27.0282 40.3767 26.8328C40.275 26.6375 40.1206 26.4746 39.9311 26.3627C38.8113 25.5905 37.7183 24.7809 36.6283 23.9729C36.3778 23.7861 36.2078 23.7757 35.9483 23.9729C34.7803 24.8402 33.6018 25.693 32.4129 26.5315C32.1147 26.7435 32.0685 26.9975 32.0699 27.3246C32.0819 28.9676 32.0699 30.621 32.0699 32.2685C32.0819 32.4358 32.0655 32.6016 32.055 32.8107Z"
              fill="white"
            />
            <path
              d="M87.2256 13.5346C87.2824 13.5469 87.3417 13.5386 87.3931 13.5113C87.4444 13.4839 87.4844 13.4393 87.5059 13.3852C88.6988 11.6451 90.43 10.8281 92.4669 10.6429C95.1002 10.4024 97.486 11.0372 99.4468 12.9043C100.708 14.1215 101.601 15.6705 102.022 17.3732C102.861 20.5532 102.86 23.7063 101.575 26.7727C100.55 29.2043 98.8176 30.9444 96.2543 31.7151C94.1474 32.3484 92.0658 32.2304 90.0677 31.2327C89.091 30.7458 88.3126 30.0243 87.6178 29.1416C87.4687 29.3388 87.5223 29.5329 87.5223 29.7062C87.5223 32.4057 87.5223 35.1042 87.5223 37.8017C87.5223 37.969 87.5134 38.1378 87.5223 38.3036C87.5387 38.5739 87.4299 38.6934 87.1511 38.6859C86.7037 38.674 86.2445 38.6859 85.7912 38.6859C84.6952 38.6859 83.5977 38.6859 82.5003 38.6859C81.8934 38.6859 81.8934 38.6859 81.8859 38.0496V37.8346C81.8859 29.2372 81.8859 20.6398 81.8859 12.0424C81.8859 11.0268 81.783 11.1746 82.7597 11.1687C84.0242 11.1687 85.2946 11.1687 86.5516 11.1687C87.3151 11.1687 87.2301 11.1403 87.2331 11.8781C87.2346 12.4233 87.2256 12.9834 87.2256 13.5346ZM96.988 21.3478C96.9209 19.9139 96.7673 18.4486 96.0008 17.1268C94.9108 15.2508 93.1528 14.4815 90.943 14.9819C89.1864 15.3807 88.1963 16.6458 87.7222 18.2978C87.1562 20.2579 87.1474 22.3374 87.6968 24.3022C87.932 25.2591 88.4521 26.1217 89.1879 26.7757C91.5588 28.8175 95.2359 27.8675 96.3945 24.9101C96.8433 23.7899 96.9462 22.6025 96.988 21.3478Z"
              fill="white"
            />
            <path
              d="M150.075 29.0623V37.9957C150.075 38.6978 150.125 38.6738 149.432 38.6738C147.954 38.6738 146.477 38.6738 144.999 38.6738C144.484 38.6738 144.484 38.6649 144.47 38.1765C144.47 38.0809 144.47 37.9853 144.47 37.8897C144.47 29.2475 144.47 20.6048 144.47 11.9616C144.47 11.0386 144.365 11.1626 145.26 11.1581C146.595 11.1511 147.929 11.1511 149.263 11.1581C149.773 11.1581 149.781 11.17 149.787 11.6614C149.787 12.2798 149.787 12.8996 149.787 13.5046C149.917 13.5927 149.981 13.518 150.034 13.4403C151.183 11.7436 152.836 10.8847 154.822 10.6682C157.642 10.3605 160.165 11.0311 162.214 13.1102C163.354 14.2755 164.166 15.7226 164.567 17.3044C165.433 20.5545 165.444 23.7823 164.075 26.904C162.949 29.473 161.049 31.2355 158.259 31.8554C155.629 32.4379 153.19 32.0048 151.111 30.1661C150.777 29.8614 150.494 29.5089 150.075 29.0623ZM149.861 21.4208C149.884 21.7853 149.888 22.2394 149.94 22.6889C150.07 23.7853 150.34 24.8368 150.919 25.8002C152.468 28.3752 156.163 28.308 157.785 26.6964C158.498 25.9854 158.998 25.0887 159.229 24.1079C159.53 22.852 159.615 21.5539 159.481 20.2693C159.412 19.3156 159.174 18.3819 158.778 17.512C157.621 15.0878 154.879 14.108 152.581 15.3208C151.296 15.9989 150.6 17.1356 150.221 18.4978C149.967 19.4268 149.908 20.3798 149.861 21.4208Z"
              fill="white"
            />
            <path
              d="M119.89 21.3104C119.787 19.418 120.187 17.5322 121.049 15.8451C122.574 12.8907 125.075 11.2761 128.304 10.7489C130.07 10.4467 131.881 10.5375 133.608 11.0147C137.097 12.0005 139.388 14.235 140.385 17.7361C141.131 20.3485 141.113 22.9832 140.199 25.5508C138.857 29.3058 136.095 31.3446 132.223 31.9451C130.238 32.2528 128.267 32.1348 126.373 31.4477C122.759 30.1303 120.72 27.4716 120.024 23.7375C119.957 23.4104 119.913 23.079 119.891 22.7458C119.869 22.2663 119.89 21.7958 119.89 21.3104ZM135.265 21.515C135.277 20.6567 135.198 19.7995 135.029 18.9579C134.47 16.3112 132.691 14.5472 129.673 14.8847C127.688 15.1073 126.49 16.3186 125.914 18.1364C125.243 20.2559 125.28 22.4082 125.901 24.5456C125.933 24.6606 125.983 24.7696 126.026 24.8802C126.648 26.4754 127.982 27.8181 130.244 27.8167C132.506 27.8152 134.029 26.7621 134.779 24.6098C135.144 23.5643 135.235 22.4769 135.26 21.515H135.265Z"
              fill="white"
            />
            <path
              d="M166.521 11.1536C168.459 11.1536 170.287 11.1536 172.121 11.1536C172.454 11.1536 172.488 11.3747 172.56 11.6017C173.026 13.1282 173.496 14.6537 173.972 16.1782C174.816 18.9116 175.661 21.6444 176.507 24.3768C176.56 24.5501 176.536 24.7711 176.785 24.8727C177.467 22.6651 178.149 20.46 178.83 18.2574C179.504 16.0717 180.177 13.8855 180.85 11.6988C181.017 11.1566 181.022 11.1521 181.575 11.1506C183.215 11.1506 184.855 11.1506 186.496 11.1506C186.628 11.406 186.485 11.5987 186.422 11.7824C184.682 16.9315 182.942 22.0796 181.204 27.2267C180.776 28.4918 180.34 29.7539 179.932 31.025C179.812 31.3984 179.633 31.5433 179.226 31.5373C177.485 31.5104 175.745 31.5104 174.007 31.5373C173.581 31.5373 173.432 31.373 173.308 31.0115C171.581 25.9332 169.848 20.8548 168.109 15.7764L166.521 11.1536Z"
              fill="white"
            />
            <path
              d="M111.354 14.6577C111.801 13.8616 112.248 13.164 112.867 12.595C114.291 11.2701 115.966 10.583 117.917 10.6025C118.06 10.6019 118.203 10.6119 118.344 10.6323C119.016 10.7414 119.018 10.7443 119.019 11.4493C119.019 12.7617 119.019 14.0742 119.019 15.3866C119.019 15.5733 119.063 15.7689 118.94 15.9945C118.2 15.889 117.452 15.8416 116.704 15.8526C114.169 15.884 112.43 17.2491 111.801 19.7196C111.595 20.5541 111.495 21.4112 111.503 22.2707C111.503 25.0399 111.503 27.8087 111.503 30.5769C111.503 30.7262 111.503 30.8636 111.503 31.0055C111.491 31.5149 111.489 31.5238 110.996 31.5268C110.067 31.5268 109.138 31.5268 108.209 31.5268C107.565 31.5268 106.92 31.5164 106.27 31.5268C105.972 31.5268 105.823 31.4372 105.859 31.1205C105.865 31.0011 105.865 30.8815 105.859 30.7621C105.859 24.4888 105.859 18.2115 105.859 11.9303C105.859 11.0864 105.751 11.1491 106.604 11.1476C107.962 11.1476 109.321 11.1476 110.68 11.1476C111.186 11.1476 111.195 11.1596 111.201 11.657C111.201 12.4217 111.201 13.185 111.201 13.9482C111.23 14.1603 111.166 14.3769 111.354 14.6577Z"
              fill="white"
            />
            <path
              d="M194.307 14.6069C194.54 14.4337 194.597 14.1962 194.72 14.0095C196.176 11.787 198.22 10.6339 200.882 10.604C200.977 10.604 201.074 10.604 201.168 10.604C201.958 10.6757 202 10.7205 202 11.5002C202 12.7668 202 14.0324 202 15.297V15.9871C201.241 15.9213 200.54 15.8377 199.835 15.8482C197.023 15.8661 195.238 17.3582 194.704 20.1439C194.54 21.0356 194.461 21.9407 194.467 22.8473C194.455 25.5463 194.467 28.2453 194.467 30.9444C194.467 31.5194 194.467 31.5254 193.89 31.5269C192.411 31.5269 190.932 31.5269 189.454 31.5269C188.892 31.5269 188.886 31.5179 188.886 30.9294C188.886 24.5566 188.886 18.1797 188.886 11.7989C188.886 11.1626 188.886 11.1567 189.482 11.1552C190.888 11.1552 192.294 11.1552 193.702 11.1552C194.211 11.1552 194.218 11.1671 194.224 11.666C194.224 12.4292 194.224 13.194 194.224 13.9587C194.233 14.1589 194.191 14.371 194.307 14.6069Z"
              fill="white"
            />
          </svg>
        </router-link>
      </div>
      <div class="flex flex-col gap-3">
        <button
          type="button"
          @click="
            () => {
              router.push(`/projects/${projectId}/tours/${tourId}/create`);
            }
          "
          class="w-full createButton inline-flex items-center rounded-md bg-[#1C74D0] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-800"
        >
          <PlusIcon class="h-4 w-4 mr-2 text-white" aria-hidden="true" /> Create
          New Tour
        </button>

        <button
          type="button"
          @click="
            () => {
              router.push(`/projects/${projectId}/tours/${tourId}/customtour/`);
            }
          "
          class="w-full createButton inline-flex items-center rounded-md bg-[#1C74D0] px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-800"
        >
          <PlusIcon class="h-4 w-4 mr-2 text-white" aria-hidden="true" /> Custom Tour
        </button>
      </div>
      <ul class="grid grid-cols-1 gap-y-3" v-if="projectStore.virtualtours">
        <li
          class="text-white w-full h-[200px] flex flex-col cursor-pointer"
          v-for="img in imgList?.scenes"
          :key="img._id"
          @click="imgHandler(img)"
        >
          <img
            class="w-full h-full object-cover rounded-t-md"
            :src="img.panorama"
          />
          <p class="p-2 text-white bg-slate-600">{{ img.title }}</p>
        </li>
      </ul>
    </div>
    <router-view name="modal"></router-view>
  </div>
</template>

<style scoped>
/* width */
::-webkit-scrollbar {
  width: 0px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #404040;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}
</style>


<script setup>
import { searchIcon } from '@/helpers/icons';
import { UserStore } from '../../store/index';
import { onMounted, ref, watch } from 'vue';
import NewDropDown from '../common/NewDropDown.vue';
import { useRouter } from 'vue-router';
import { onClickOutside } from '@vueuse/core';
import ToggleSliderButton from '../common/ToggleSliderButton.vue';
import { Org_Store } from '@/store/organization';
import { ListProjectsFromOrganization } from '@/api/organization';

const Organizationstore = Org_Store();
const emits = defineEmits(['openScheduleModal', 'openStartSessionModal', 'toggleView', 'searchMeetings']);
const Store = UserStore();
const searchText  = ref('');
const showMeetingsRef  = ref();
const showMeetingsType = ref(false);
const router = useRouter();
const userOptions = ref([]);
const filteredUsers = ref([]);
const projects = ref([]);
const projectEntries = ref([]);
const filterOptions = ref({});
function handleFilterAgent (val) {
  console.log("from filter", val);
  console.log("--", filteredUsers.value);

  let userId;
  if (val !== null){
    console.log("2");
    const user = filteredUsers.value.find((item) => item.label === val);
    userId = user ? user.user_id : null;
    router.push({
      query: {
        ...router.currentRoute.value.query,
        user_id: userId,
      },
    });
  } else {
    console.log("clearing");
    router.push({
      query: {
        ...router.currentRoute.value.query,
        user_id: undefined,
      },
    });
  }
}
function handleFilterProject (val){
  console.log("from filter", val);
  let projectId;
  if (val !== null){
    const project = projectEntries.value.find((item) => String(item.name).toLowerCase() === val.toLowerCase());
    console.log("seleee", project);
    projectId = project ? project._id : null;

    router.push({
      query: {
        ...router.currentRoute.value.query,
        project_id: projectId,
      },
    });
  } else {
    router.push({
      query: {
        ...router.currentRoute.value.query,
        project_id: undefined,
      },
    });
  }
}

onClickOutside(showMeetingsRef, () => {
  showMeetingsType.value = false;
});
function handleToggleData (val){
  if (val === 'card'){
    // pageView.value = 'card';
    emits('toggleView', 'card');
  } else {
    // pageView.value = 'list';
    emits('toggleView', 'list');
  }
}
function formatLabel (option) {
  if (!option) {
    return '';
  }
  if (option.first_name) {
    if (option.role === 'admin') {
      return `${option.first_name} (admin)`;
    }
    return `${option.first_name}`;
  } else if (option.email) {
    if (option.role === 'admin') {
      return `${option.email} (admin)`;
    }
    return `${option.email}`;
  }
  return 'Unknown';
}
function getFilteredUsers (users) {
  if (!users) {
    return [{label: 'All' }];
  }
  const allOption = { label: 'All', user_id: null };
  filteredUsers.value = Object.values(users || {});
  console.log("users", filteredUsers.value);
  // Add label property for each user
  userOptions.value = filteredUsers.value.map((user) => {
    user.label = formatLabel(user);
    return user.label;
  });
  console.log("++++", userOptions.value);
  return [allOption, ...filteredUsers.value];
}
// function fetchUsers (users){
//   console.log("users", users);
//   filteredUsers.value = Object.values(users || {});
//   userOptions.value = filteredUsers.value.map((user) => {
//     if (user.label){
//       return user.label;
//     }
//     user.label = formatLabel(user);
//     return user.label;
//   });
// }
async function getProjectsForFilter (){
  await ListProjectsFromOrganization().then((projects_list) => {
    projectEntries.value = Object.values(projects_list || {});
    // Extract project names or structure options as needed
    projects.value = projectEntries.value.map((project) => (project.name));
    console.log("1111", projects.value); // Check the options format
  });
}
watch(async () => {
  // fetchUsers(Organizationstore.users);
  getFilteredUsers(Organizationstore.users);
  await getProjectsForFilter();
  filterOptions.value = {
    project: projects.value,
    agent: userOptions.value,
  };
}, []);
watch(() => searchText.value, () => {
  emits('searchMeetings', searchText.value);
});

onMounted(() => {
  Store.callbackFunctionMonitorChanges();
});
function resetFilters (){
  router.push({
    path: router.currentRoute.value.path,
    query: {},
  });
}
</script>

<template>
        <div v-if="!Store.isMobile" class="dynamic-header">
            <div class="h-full  flex max-xl:flex-col max-xl:!gap-3 md:!gap-3 gap-8 items-start">
                <div class="dynamic-heading">
                    <p class="dynamic-topic">Sessions</p>
                    <p class="dynamic-sub-topic">Create and manage sessions.</p>
                </div>
                <div class="min-w-[220px] w-[220px]">
                  <div  class="flex w-full  h-[37px] px-3 items-center border bg-gray-100 rounded-lg">
                          <span v-html="searchIcon" class="w-[14%]"></span>
                          <input type="search"
                          v-model="searchText"
                          class="w-[86%] bg-transparent text-gray-500 text-sm font-normal placeholder:text-left"
                          :placeholder="'Search for lead name'"/>
                  </div>
                </div>

            </div>

            <div class="lg:flex lg:justify-end lg:gap-7 md:grid md:grid-cols-3 md:w-[60%] md:gap-x-1 md:gap-y-1 h-full w-[70%] flex  justify-end gap-5 items-center relative">
                 <NewDropDown
                    title="Agent"
                    type="select"
                    inputType="radio"
                    :options="userOptions"
                    width="w-[10rem]"
                    @optionSelected="handleFilterAgent"/>
                 <NewDropDown
                    title="Project"
                    type="select"
                    inputType="radio"
                    :options="projects"
                     width="w-[13rem]"
                    @optionSelected="handleFilterProject"/>

                  <ToggleSliderButton :title="['Day','List']" :enableDaySVG="true" @toggleData="handleToggleData"/>

              <button ref="showMeetingsRef" type="button" @click="showMeetingsType = !showMeetingsType"
               :class="{'!bg-[#1E429F]':showMeetingsType}" class=" flex gap-1 items-center w-[100px] h-10 sm:rounded-md bg-[#1c64f2] rounded-lg  active:!bg-[#1e429f]  m-0 px-2 text-xs text-white font-semibold leading-6">
                <svg
                :class="{ 'rotate-45 ': showMeetingsType }"
                  xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                  class="w-6 h-5 fill-white mr-1">
                  <path fill-rule="evenodd"
                    d="M12 5.25a.75.75 0 01.75.75v5.25H18a.75.75 0 010 1.5h-5.25V18a.75.75 0 01-1.5 0v-5.25H6a.75.75 0 010-1.5h5.25V6a.75.75 0 01.75-.75z"
                    clip-rule="evenodd" />
                </svg>
                <p class="text-white text-sm font-medium">Session</p></button>

                <div v-if="showMeetingsType" class="absolute  w-[180px] flex flex-col gap-2 md:top-[110%] lg:top-[115%] xl:top-[115%] z-[50]">
                    <button class="bg-white  hover:!bg-gray-100 px-3 py-[10px] flex items-center gap-4 shadow-xl  rounded-md" @click="()=>emits('openStartSessionModal')">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="videocamera">
                        <g id="Vector">
                        <path d="M6.6 1.5H1.2C0.537258 1.5 0 2.07563 0 2.78571V9.21429C0 9.92437 0.537258 10.5 1.2 10.5H6.6C7.26274 10.5 7.79999 9.92437 7.79999 9.21429V2.78571C7.79999 2.07563 7.26274 1.5 6.6 1.5Z" fill="#1C64F2"/>
                        <path d="M11.7 2.33571C11.6091 2.2787 11.5058 2.24833 11.4005 2.24766C11.2952 2.24698 11.1915 2.27602 11.1 2.33186L8.99999 3.61114V8.45379L11.079 9.86807C11.17 9.92979 11.2747 9.96415 11.3824 9.96754C11.49 9.97093 11.5965 9.94323 11.6907 9.88735C11.7848 9.83146 11.8633 9.74946 11.9177 9.64992C11.9721 9.55039 12.0005 9.437 12 9.32164V2.89307C12.0001 2.78012 11.9724 2.66913 11.9198 2.57129C11.8671 2.47344 11.7913 2.39219 11.7 2.33571Z" fill="#1C64F2"/>
                        </g>
                        </g>
                        </svg>
                    <p class="text-sm">Quick Session</p></button>
                    <button class="bg-white hover:!bg-gray-100 px-3 py-[10px] flex items-center gap-4 shadow-xl  rounded-md" @click="()=>emits('openScheduleModal')">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g id="calendar-day" clip-path="url(#clip0_3240_4699)">
                        <g id="Vector">
                        <path d="M12 2.4C12 2.08174 11.8736 1.77652 11.6485 1.55147C11.4235 1.32643 11.1183 1.2 10.8 1.2H9.6V0.6C9.6 0.44087 9.53679 0.288258 9.42426 0.175736C9.31174 0.0632141 9.15913 0 9 0C8.84087 0 8.68826 0.0632141 8.57574 0.175736C8.46321 0.288258 8.4 0.44087 8.4 0.6V1.2H6.6V0.6C6.6 0.44087 6.53679 0.288258 6.42426 0.175736C6.31174 0.0632141 6.15913 0 6 0C5.84087 0 5.68826 0.0632141 5.57574 0.175736C5.46321 0.288258 5.4 0.44087 5.4 0.6V1.2H3.6V0.6C3.6 0.44087 3.53679 0.288258 3.42426 0.175736C3.31174 0.0632141 3.15913 0 3 0C2.84087 0 2.68826 0.0632141 2.57574 0.175736C2.46321 0.288258 2.4 0.44087 2.4 0.6V1.2H1.2C0.88174 1.2 0.576515 1.32643 0.351472 1.55147C0.126428 1.77652 0 2.08174 0 2.4V3.6H12V2.4Z" fill="#1C64F2"/>
                        <path d="M0 10.8C0 11.1183 0.126428 11.4235 0.351472 11.6485C0.576515 11.8736 0.88174 12 1.2 12H10.8C11.1183 12 11.4235 11.8736 11.6485 11.6485C11.8736 11.4235 12 11.1183 12 10.8V4.8H0V10.8ZM3 6H9C9.15913 6 9.31174 6.06321 9.42426 6.17574C9.53679 6.28826 9.6 6.44087 9.6 6.6C9.6 6.75913 9.53679 6.91174 9.42426 7.02426C9.31174 7.13679 9.15913 7.2 9 7.2H3C2.84087 7.2 2.68826 7.13679 2.57574 7.02426C2.46321 6.91174 2.4 6.75913 2.4 6.6C2.4 6.44087 2.46321 6.28826 2.57574 6.17574C2.68826 6.06321 2.84087 6 3 6Z" fill="#1C64F2"/>
                        </g>
                        </g>
                        <defs>
                        <clipPath id="clip0_3240_4699">
                        <rect width="12" height="12" fill="white"/>
                        </clipPath>
                        </defs>
                        </svg>
                        <p class="text-sm">Schedule Session</p></button>
                </div>
            </div>
        </div>
        <div v-if="Store.isMobile" class="dynamic-header">
            <div class="h-full w-full flex flex-col gap-2  justify-end">
              <div class="w-full flex justify-between">
                <div class="dynamic-heading">
                    <p class="dynamic-topic">Sessions</p>
                    <p class="dynamic-sub-topic">Create and manage sessions.</p>
                </div>
            <div class="flex justify-end gap-2">
              <div>
                <ToggleSliderButton :title="['Day View','List View']" :position="['right-[25px] top-[45px]']" :size="['w-[150px] h-[80px]']" @toggleData="handleToggleData"/>
              </div>
              <div>
                  <NewDropDown
                    :title="['Project','Agent']"
                    type="select"
                    inputType="radio"
                    :options="filterOptions"
                    @projectOptionSelected="handleFilterProject"
                    @agentOptionSelected="handleFilterAgent"
                    @resetFilters="resetFilters"/>
              </div>
            </div>
              </div>

                <div class="min-w-[250px] w-[250px]">
                  <div  class="flex w-full  h-[37px] px-3 items-center border bg-gray-100 rounded-lg">
                          <span v-html="searchIcon" class="w-[14%]"></span>
                          <input type="search"
                          v-model="searchText"
                          class="w-[86%] bg-transparent text-gray-500 text-sm font-normal placeholder:text-left"
                          :placeholder="'Search for lead name'"/>
                  </div>
                </div>

            </div>

        </div>
</template>

<style scoped>
@media screen and (max-width: 625px) {
    input::-webkit-input-placeholder {
        /* position: relative;
        left: -10px; */
        font-size: 14px;
    }
}
@media screen and (min-width: 626px) and (max-width: 1280px) {
    input::-webkit-input-placeholder {
        /* position: relative;
        left: -9px; */
        font-size: 14px;
    }
}
@media screen and (max-width:1281px) and (min-width: 1536px) {
    input::-webkit-input-placeholder {
        /* position: relative;
        left: -8px; */
        font-size: 14px;
    }
}
/* @media screen and (min-width: 768px) {
    input::-webkit-input-placeholder {
        position: relative;
        left: -18px;
        font-size: 14px;
    }
} */
</style>

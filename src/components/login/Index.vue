<script setup>
// Import ForgetModal from "../components/ModalComponents/ForgetModal.vue";
// Import installPrompt from "../components/installPrompt.vue";
import { signInWithEmailAndPassword, setPersistence, indexedDBLocalPersistence, signInWithPopup, onAuthStateChanged } from 'firebase/auth';
import { ref, onMounted, onUnmounted } from 'vue';
import { doc, getDoc, setDoc, serverTimestamp, getFirestore } from "firebase/firestore";
const db = getFirestore();
import { useRouter } from 'vue-router';
// Import DarkLogo from '../../assets/logo/logo_1.png';
// Import LightLogo from '../../assets/logo/logo.png';
// import { uiOperations } from '../../store/uiOperations';
import { Form } from 'vee-validate';
import {signInSchema} from '../../validationSchema/user';
import SignUp from './SignUp.vue';
import AuthInput from '../../components/common/AuthInput.vue';
import { auth, googleProvider, generateToken } from '../../firebaseConfig';
import { UserStore } from '../../store';
import { deleteCookie, setCookie } from '../../helpers/domhelper';
import Button from '../common/Button.vue';
// const uiStore = uiOperations();
const router = useRouter();
const showPassword = ref(false); // default
const store = UserStore();
const loginuserId = ref(null);
const loginPassword = ref(null);
// Const rememberMe = ref(false);
const signUpView = ref(false);
const isSSO = ref(false);
const isVerificationPending = ref(false);
const Errormsg = ref({
  isShow: false,
  message: null,
});

const isloading = ref(false);
// Const SubmitBtn = ref(false);

// Toggle Forms
const handleToggleForms = () => {
  Errormsg.value.isShow = false;
  signUpView.value = !signUpView.value;
  // Reset
  showPassword.value = false;
  if (!signUpView.value){
    if (localStorage.getItem('userName') !== 'null' && localStorage.getItem('userName') ){

      loginuserId.value = localStorage.getItem('userName');
    }

    if (localStorage.getItem('password') !== 'null' && localStorage.getItem('password') ){

      loginPassword.value = localStorage.getItem('password');
    }
  }
};
const handleRedirection = async () => {
  try {
    const user = auth.currentUser;
    if (user) {
      await store
        .GetAuth();
      if (store.user_data.organization_id.length === 0 && !user.emailVerified) {
        signUpView.value = true;
        isVerificationPending.value = true;
        return;
      }
      const urlParams = new URLSearchParams(window.location.search);
      const redirectUri = urlParams.get('redirect_uri');
      if (redirectUri) {
        const decodedRedirectUri = decodeURIComponent(redirectUri);
        if (decodedRedirectUri.startsWith('/')) {
          window.location.href = decodedRedirectUri;
        } else {
          router.push('/projects');
        }
      } else {
        router.push('/projects');
      }
    }
  } catch (error) {
    console.log('Error while redirecting', error);
    Errormsg.value.isShow = true;
    Errormsg.value.message = error.message || "Login failed. Please try again later";
    throw error;
  }
};
// SignIn
const handleSignIn = async (values) => {
  console.log('-------------');
  isloading.value = true;
  deleteCookie('pushToken');
  const pushToken = await generateToken();
  console.log(pushToken);
  if (pushToken){
    setCookie('pushToken', pushToken);
  }
  deleteCookie('organization');
  deleteCookie('accessToken');
  deleteCookie('refreshToken');

  if (values.email && values.password) {
    try {
      // Set persistence (optional, based on your requirements)
      await setPersistence(auth, indexedDBLocalPersistence);

      // Sign in with Firebase
      const userCredential = await signInWithEmailAndPassword(auth, values.email, values.password);
      const user = userCredential.user;

      // Get the ID token
      const accessToken = await user.getIdToken();
      setCookie('accessToken', accessToken);

      // Clear error messages
      Errormsg.value.isShow = false;
      Errormsg.value.message = null;
      await handleRedirection();
    } catch (error) {
      // Handle errors
      Errormsg.value.isShow = true;
      switch (error.code) {
        case 'auth/invalid-email':
          Errormsg.value.message = 'Invalid email!';
          break;
        case 'auth/user-not-found':
          Errormsg.value.message = 'No account with that email was found!';
          break;
        case 'auth/wrong-password':
          Errormsg.value.message = 'Incorrect password';
          break;
        default:
          Errormsg.value.message = 'Login failed. Please try again later';
      }
    } finally {
      isloading.value = false;
    }
  }
};

// google auth sso login

const handleGoogleSignIn = async () => {
  try {
    isloading.value = true;
    isSSO.value = true;
    // Sign in with Google
    const result = await signInWithPopup(auth, googleProvider);
    const user = result?.user;

    if (!user || !user.email) {
      throw new Error("Google authentication failed.");
    }

    console.log("Google User Signed In:", user.email);

    // Get ID Token and save it in cookies
    const accessToken = await user.getIdToken();
    setCookie("accessToken", accessToken);

    // Firestore reference
    const userRef = doc(db, "users", user.uid);
    const userSnap = await getDoc(userRef);

    if (!userSnap.exists()) {
      // Automatically create the user in Firestore
      await setDoc(userRef, {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName || "User",
        firstName: user.displayName?.split(' ')[0] || 'User',
        lastName: user.displayName?.split(' ').slice(1).join(' ') || '',
        role: "user",
        createdAt: serverTimestamp(),
      });
      console.log("New user created in Firestore.");
      const userData = {
        email: user.email,
        first_name: user.displayName?.split(' ')[0] || 'User',
        last_name: user.displayName?.split(' ').slice(1).join(' ') || '',
        uid: user.uid,
        role: "user",
      };
      await store.CreateUser(userData).then(async () => {
        console.log("User created in backend API:", userData);
        await handleRedirection();
      });
    } else {
      console.log("Existing user detected.");
      await handleRedirection();
    }
  } catch (error) {
    console.error(" Google Sign-In error:", error);
    const user = auth.currentUser;
    if (user) {
      user.delete();
    }
    Errormsg.value.isShow = true;
    Errormsg.value.message = error.message || "Google Sign-In failed!";
  } finally {
    isloading.value = false;
    isSSO.value = false;
  }
};

onMounted(() => {

  if (localStorage.getItem('userName') !== 'null' && localStorage.getItem('userName') ){

    loginuserId.value = localStorage.getItem('userName');
  }

  if (localStorage.getItem('password') !== 'null' && localStorage.getItem('password') ){

    loginPassword.value = localStorage.getItem('password');
  }

});
let unsubscribe = () => {};

onMounted(() => {
  unsubscribe = onAuthStateChanged(auth, async (user) => {
    console.log("onAuthStateChanged in index onmounted");
    if (user && !signUpView.value && !isSSO.value) {
      const accessToken = await user.getIdToken();
      setCookie('accessToken', accessToken);
      // console.log("---------------------------")
      // Handle redirect
      await handleRedirection();
    }
  });
});

onUnmounted(() => {
  unsubscribe();
});
</script>
<template>

                        <!-- ThemeSwitcher -->
                        <!-- <ThemeSwitcher position="fixed"/> -->

                        <div class=" h-screen w-full relative bg-bg-1000 dark:bg-bg-default overflow-y-auto flex md:flex-row flex-col ">

                                              <!-- <div class="block lg:hidden w-full pt-3 pl-3"> -->

                                                            <!-- <img v-if="uiStore.toggleTheme.toLowerCase() === 'light'" alt="Logo" class="h-6 lg:h-7 w-auto" :src="DarkLogo" /> -->

                                                            <!-- <img v-else alt="Logo" class="h-6 lg:h-7 w-auto" :src="LightLogo" /> -->

                                              <!-- </div> -->

                                                <!-- Image Col -->
                                                <div class="h-auto md:w-[85vw] w-full relative flex justify-center items-center  ">
                                                        <div class=" w-fit h-[238.27px] md:top-auto top-5 md:left-auto left-5 flex-col justify-center items-center absolute md:m-[72px]">
                                                          <div class="justify-start items-center gap-[10.37px] inline-flex top-5">
                                                             <div class="w-[135.62px] h-[35px] relative">
                                                                <img class=" left-0 top-0 absolute" src="../../assets/logo/logo.png" />
                                                            </div>
                                                          </div>
                                                        <div class="self-stretch h-[126px] flex-col justify-start items-start gap-4 lg:flex hidden">
                                                          <div class="self-stretch h-[62px] text-white text-2xl font-extrabold font-[Inter] leading-[30px] mb-3">
                                                            <span>Discover the Ultimate Platform for Property<br></span>
                                                            <span>Experience Creation & Management.</span>
                                                          </div>
                                                          <div class="self-stretch text-gray-100 text-base font-normal font-['Inter'] leading-normal">Millions of developers and Agents manages their leads & properties with us  - the home to the world’s best developers and Agents professionals.</div>
                                                          <div class="self-stretch justify-start items-center gap-6 inline-flex">
                                                           <div class="flex-col justify-start items-start gap-1 inline-flex">
                                                             <div class="text-white text-sm font-normal font-['Inter'] leading-[21px]">Rated Best By Top Developers</div>
                                                           </div>
                                                <div class="w-[29.27px] h-[0px] rotate-90 border border-gray-200"></div>
                                                  <div class="w-[287px] h-[27px] pl-[3.87px] pr-[1.92px] pt-[3.87px] pb-[3.78px] justify-center items-center flex overflow-hidden">
                                                      <div class="justify-start items-start gap-[20.90px] inline-flex">
                                                          <div class="justify-start items-center gap-[20.90px] flex">
                                                              <div class ="w-full">
                                                                <img src="../../assets/logo/kafd.png" />
                                                              </div>
                                                              <div class ="w-full">
                                                                <img src="../../assets/logo/godrej.png" />
                                                              </div>
                                                              <div class ="w-full">
                                                                <img  src="../../assets/logo/damacLogo.png " />
                                                              </div>
                                                              <div class ="w-full">
                                                                <img  src="../../assets/logo/rustomjee.png" />
                                                              </div>
                                                          </div>
                                                      </div>
                                                  </div>
                                                  </div>
                                                </div>

                                              </div>
                                                   <img class="object-cover h-full w-full" src="../../assets/loginFront.jpg"/>

</div>
                                                <!-- Form -->
                                                <div class="h-auto w-[-webkit-fill-available]  flex justify-center items-center">
                                                            <div class="md:relative absolute md:bottom-auto bottom-0 md:w-fit w-[-webkit-fill-available]">
                                                                <div class="md:w-fit w-[-webkit-fill-available] h-fit md:min-w-[576px] min-w-auto p-6 flex-col justify-center items-center gap-5 inline-flex bg-white rounded-lg"
                                                                 :class="{' shadow-md  border border-gray-200' : !signUpView}">

                            <!-- Headers -->

                                    <!--   <h3 class="text-center text-neutral-800 text-lg font-bold border-b-2 border-[#262626] w-[50%] py-2"> Login as Admin </h3> -->
                                    <div class="self-stretch text-gray-900 text-2xl leading-[30px] font-bold font-sans">{{ !signUpView ? 'Welcome Back' : ''}} </div>
                                    <div class="self-stretch text-gray-900 text-xl leading-[25px] font-bold font-sans">{{ !signUpView ? 'Sign In' : ''}}</div>

                            <!-- SignIn -->
                            <div class="w-full"  v-if="!signUpView">

                                <!-- Form -->
                                <Form  v-slot="{errorBag}" :validation-schema="signInSchema" @submit="handleSignIn" class="w-full flex flex-col gap-2 !m-0 ">

                                        <!-- Email -->
                                        <AuthInput
                                          name="email"
                                          type="email"
                                          label="Email"
                                          placeholder="<EMAIL>"
                                          variant="login"
                                          :has-error="!!(errorBag?.email) || Errormsg.isShow"
                                          v-model="loginuserId"
                                        />

                                        <!-- Password -->
                                        <AuthInput
                                          name="password"
                                          type="password"
                                          label="Password"
                                          placeholder="Password"
                                          variant="login"
                                          :has-error="!!(errorBag?.password) || Errormsg.isShow"
                                          v-model="loginPassword"
                                        />

                                       <!-- Api Error -->
                                      <div class= "flex flex-column justify-space w-full">
                                        <div v-if="Errormsg.isShow" class="min-h-8 flex items-center justify-space w-full">
                                           <p class="text-sm font-normal not-italic text-left text-red-600 font-sans w-max">
                                            <i class="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
                                            {{ Errormsg.message }}
                                           </p>

                                              <!-- <button type="button" @click="handleForgotPassword" class="text-[#1c64f2] dark:text-txt-950 text-sm font-medium cursor-pointer">
                                            Forgot password?
                                          </button> -->
                                        </div>

                                        <!-- Links -->
                                        <div class="flex justify-end items-center w-full">
                                          <!-- <button type="button" @click="handleForgotPassword" class="text-[#1c64f2] dark:text-txt-950 text-sm font-medium cursor-pointer">
                                            Forgot password?
                                          </button> -->

                                          <button
  type="button"
  @click="$router.push('/reset-password')"
  class="text-[#1c64f2] dark:text-txt-950 text-sm font-medium cursor-pointer"
>
  Forgot password?
</button>
                                        </div>
                                      </div>

                                        <!-- <div class="flex justify-between items-center">

                                          <div class="flex items-center h-fit">
                                             <input checked id="default-checkbox" type="checkbox" value="" class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded ">
                                             <label for="default-checkbox" class=" text-sm  m-0 font-bold text-gray-500 dark:text-gray-300">Remember me</label>
                                          </div>

                                      </div> -->

                                        <!-- SignIn Btn -->
                                        <Button title="Submit" type="submit" :loading="isloading && !isSSO" :disabled="isloading" theme="primary" extra-classes="mt-2"></Button>
                                        <Button
                                          title="Sign Up with Google"
                                          :loading="isloading && isSSO"
                                          :disabled="isloading"
                                          theme="secondary"
                                          extra-classes="mt-3 w-full outline outline-1 outline-gray-200 bg-white"
                                          @handleClick="handleGoogleSignIn"
                                        >
                                          <template #svg>
                                            <img
                                              src="https://www.google.com/images/branding/googleg/1x/googleg_standard_color_128dp.png"
                                              alt="Google Logo"
                                              class="w-6 h-6 bg-white rounded-full p-1"
                                            />
                                          </template>
                                        </Button>
                                </Form>

                            </div>

                            <!-- Sign Up -->
                            <div v-else class="w-full m-0">
                              <!-- Form -->
                              <SignUp @handleToggleForms="handleToggleForms" :isVerificationPending="isVerificationPending">
                                <template #googleSignIn>
                                  <Button
                                    title="Sign Up with Google"
                                    :loading="isloading && isSSO"
                                    :disabled="isloading"
                                    theme="secondary"
                                    extra-classes="mt-3 w-full outline outline-1 outline-gray-200 bg-white"
                                    @handleClick="handleGoogleSignIn"
                                  >
                                    <template #svg>
                                      <img
                                        src="https://www.google.com/images/branding/googleg/1x/googleg_standard_color_128dp.png"
                                        alt="Google Logo"
                                        class="w-6 h-6 bg-white rounded-full p-1"
                                      />
                                    </template>
                                  </Button>
                                </template>
                              </SignUp>
                              <div v-if="Errormsg.isShow" class="min-h-8 flex items-center justify-space w-full">
                                  <p class="text-sm font-normal not-italic text-left text-red-600 font-sans w-max">
                                   <i class="fa fa-exclamation-circle mr-1" aria-hidden="true"></i>
                                   {{ Errormsg.message }}
                                  </p>
                              </div>
                            </div>

                            <div class="w-full flex flex-col justify-between items-center">

                                        <!-- signup by google -->

                                        <!-- <Button theme="secondary" title="Sign Up with Google" class=" w-full">
                                                <template v-slot:svg>
                                                    <svg class="h-5 w-[21px]" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                        <path d="M19.3866 8.26138L11.1687 8.26099C10.8058 8.26099 10.5116 8.55294 10.5116 8.91317V11.5192C10.5116 11.8794 10.8058 12.1714 11.1686 12.1714H15.7965C15.2897 13.4769 14.3439 14.5703 13.1372 15.2649L15.1105 18.6559C18.2759 16.8386 20.1473 13.65 20.1473 10.0805C20.1473 9.57228 20.1096 9.20896 20.0341 8.79985C19.9768 8.48903 19.7049 8.26138 19.3866 8.26138Z" fill="#1C74D0"/>
                                                        <path d="M10.0731 16.087C7.80834 16.087 5.83122 14.8586 4.76934 13.0409L1.35349 14.9954C3.09179 17.9861 6.3482 20 10.0731 20C11.9004 20 13.6247 19.5116 15.11 18.6605V18.6558L13.1367 15.2648C12.2341 15.7845 11.1895 16.087 10.0731 16.087Z" fill="#12B347"/>
                                                        <path d="M15.1113 18.6604V18.6558L13.138 15.2648C12.2354 15.7844 11.1909 16.0869 10.0744 16.0869V20C11.9018 20 13.6261 19.5116 15.1113 18.6604Z" fill="#0F993E"/>
                                                        <path d="M3.9419 10.0001C3.9419 8.89195 4.24655 7.8552 4.76995 6.95922L1.35409 5.00476C0.49196 6.47457 0 8.18152 0 10.0001C0 11.8187 0.49196 13.5257 1.35409 14.9955L4.76995 13.041C4.24655 12.145 3.9419 11.1083 3.9419 10.0001Z" fill="#FFD500"/>
                                                        <path d="M10.0731 3.91305C11.55 3.91305 12.9066 4.43399 13.9661 5.30052C14.2275 5.51427 14.6075 5.49884 14.8469 5.26114L16.707 3.41465C16.9787 3.14497 16.9593 2.70352 16.6691 2.4536C14.8938 0.924728 12.584 0 10.0731 0C6.3482 0 3.09179 2.01395 1.35349 5.00466L4.76934 6.95911C5.83122 5.14141 7.80834 3.91305 10.0731 3.91305Z" fill="#FF4B26"/>
                                                        <path d="M13.9675 5.30052C14.2289 5.51427 14.6088 5.49884 14.8483 5.26114L16.7084 3.41465C16.98 3.14497 16.9606 2.70352 16.6705 2.4536C14.8952 0.924689 12.5853 0 10.0744 0V3.91305C11.5513 3.91305 12.9079 4.43399 13.9675 5.30052Z" fill="#D93F21"/>
                                                    </svg>
                                                </template>
                                        </Button> -->

                                        <p v-if="!signUpView" class="w-full text-center">
                                            <span class="text-gray-900 text-sm font-normal font-sans"> Don't have an account yet? </span>
                                            <span class="text-[#1c64f2] dark:text-txt-950 text-sm font-medium cursor-pointer font-sans" @click=" handleToggleForms()"> Sign Up</span>
                                        </p>

                            </div>

                                                                </div>

                                                </div>

                        </div>
                      </div>

</template>

<style scoped>

/* Layout */
.gridLayout{
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
}

/* CheckBox */
input[type=checkbox] {
    position: relative;
    border: 1px solid #737373;
    border-radius: 4px;
    background: none;
    cursor: pointer;
    line-height: 0;
    margin: 0 .3em 0 0;
    outline: 0;
    padding: 0 !important;
    vertical-align: text-top;
    height: 1.25rem;
    width: 1.25rem;
    -webkit-appearance: none;
    /* opacity: .5; */
}

input[type=checkbox]:checked {
    background-color: #2563eb;
    opacity: 1;
}

input[type=checkbox]:checked:before {
    content: '';
    position: absolute;
    right: 50%;
    top: 50%;
    width: 4px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    margin: -1px -1px 0 -1px;
    transform: rotate(45deg) translate(-70%, -40%);
    z-index: 2;
}

input:-webkit-autofill {
  background-color: transparent !important;
  box-shadow: 0 0 0px 1000px white inset !important;
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
}

/* Media Query */

@media only screen and (max-width:992.98px) {
        /* Layout */
        .gridLayout{

            grid-template-columns: repeat(1, 1fr);

        }
}

</style>

<script setup>
import { onMounted, ref } from 'vue';
import { GetAllTranslation, DeleteTranslation, Translate, AddNewLanguage, UpdateTranslation, StoreTranslation, TranslateProjectData, importTranslations, ExportTranslationApi, RemoveLanguage } from '../../api/translate';
import { ErrorMessage, Field, Form } from 'vee-validate';
import Multiselect from 'vue-multiselect';
import Button from '../common/Button.vue';
import LoaderComp from '../common/LoaderComp.vue';
import { translateValidation, TranslateProject } from '@/validationSchema/translate';
import { ListProjectsFromOrganization } from '@/api/organization';

// Flipped Supported Languages mapping (key-value reversed)
const SupportedLanguages = {
  ab: 'Abkhaz',
  ace: 'Acehnese',
  ach: 'Acholi',
  af: 'Afrikaans',
  sq: 'Albanian',
  alz: 'Alur',
  am: 'Amharic',
  ar: 'Arabic',
  hy: 'Armenian',
  as: 'Assamese',
  awa: 'Awadhi',
  ay: 'Aymara',
  az: 'Azerbaijani',
  ban: 'Balinese',
  bm: 'Bambara',
  ba: 'Bashkir',
  eu: 'Basque',
  btx: 'Batak Karo',
  bts: 'Batak Simalungun',
  bbc: 'Batak Toba',
  be: 'Belarusian',
  bem: 'Bemba',
  bn: 'Bengali',
  bew: 'Betawi	',
  bho: 'Bhojpuri',
  bik: 'Bikol',
  bs: 'Bosnian',
  br: 'Breton',
  bg: 'Bulgarian',
  bua: 'Buryat',
  yue: 'Cantonese',
  ca: 'Catalan',
  ceb: 'Cebuano',
  ny: 'Chichewa (Nyanja)',
  'zh-CN': 'Chinese (Simplified)',
  'zh-TW': 'Chinese (Traditional)',
  cv: 'Chuvash',
  co: 'Corsican',
  crh: 'Crimean Tatar',
  hr: 'Croatian',
  cs: 'Czech',
  da: 'Danish',
  din: 'Dinka',
  dv: 'Divehi',
  doi: 'Dogri',
  dov: 'Dombe',
  nl: 'Dutch',
  dz: 'Dzongkha',
  en: 'English',
  eo: 'Esperanto',
  et: 'Estonian',
  ee: 'Ewe',
  fj: 'Fijian',
  fil: 'Filipino (Tagalog)',
  fi: 'Finnish',
  fr: 'French',
  'fr-FR': 'French (French)',
  'fr-CA': 'French (Canadian)',
  fy: 'Frisian',
  ff: 'Fulfulde',
  gaa: 'Ga',
  gl: 'Galician',
  lg: 'Ganda (Luganda)',
  ka: 'Georgian',
  de: 'German',
  el: 'Greek',
  gn: 'Guarani',
  gu: 'Gujarati',
  ht: 'Haitian Creole',
  cnh: 'Hakha Chin',
  ha: 'Hausa',
  haw: 'Hawaiian',
  he: 'Hebrew',
  hil: 'Hiligaynon',
  hi: 'Hindi',
  hmn: 'Hmong',
  hu: 'Hungarian',
  hrx: 'Hunsrik',
  is: 'Icelandic',
  ig: 'Igbo',
  ilo: 'Iloko',
  id: 'Indonesian',
  ga: 'Irish',
  it: 'Italian',
  ja: 'Japanese',
  jw: 'Javanese',
  kn: 'Kannada',
  pam: 'Kapampangan',
  kk: 'Kazakh',
  km: 'Khmer',
  cgg: 'Kiga',
  rw: 'Kinyarwanda',
  ktu: 'Kituba',
  gom: 'Konkani',
  ko: 'Korean',
  kri: 'Krio',
  ku: 'Kurdish (Kurmanji)',
  ckb: 'Kurdish (Sorani)',
  ky: 'Kyrgyz',
  lo: 'Lao',
  ltg: 'Latgalian',
  la: 'Latin',
  lv: 'Latvian',
  lij: 'Ligurian',
  li: 'Limburgan',
  ln: 'Lingala',
  lt: 'Lithuanian',
  lmo: 'Lombard',
  luo: 'Luo',
  lb: 'Luxembourgish',
  mk: 'Macedonian',
  mai: 'Maithili',
  mak: 'Makassar',
  mg: 'Malagasy',
  ms: 'Malay',
  'ms-Arab': 'Malay (Jawi)',
  ml: 'Malayalam',
  mt: 'Maltese',
  mi: 'Maori',
  mr: 'Marathi',
  chm: 'Meadow Mari',
  'mni-Mtei': 'Meiteilon (Manipuri)',
  min: 'Minang',
  lus: 'Mizo',
  mn: 'Mongolian',
  my: 'Myanmar (Burmese)',
  nr: 'Ndebele (South)',
  new: 'Nepalbhasa (Newari)',
  ne: 'Nepali',
  nso: 'Northern Sotho (Sepedi)',
  no: 'Norwegian',
  nus: 'Nuer',
  oc: 'Occitan',
  or: 'Odia (Oriya)',
  om: 'Oromo',
  pag: 'Pangasinan',
  pap: 'Papiamento',
  ps: 'Pashto',
  fa: 'Persian',
  pl: 'Polish',
  pt: 'Portuguese',
  'pt-PT': 'Portuguese (Portugal)',
  'pt-BR': 'Portuguese (Brazil)',
  pa: 'Punjabi',
  'pa-Arab': 'Punjabi (Shahmukhi)',
  qu: 'Quechua',
  rom: 'Romani',
  ro: 'Romanian',
  rn: 'Rundi',
  ru: 'Russian',
  sm: 'Samoan',
  sg: 'Sango',
  sa: 'Sanskrit',
  gd: 'Scots Gaelic',
  sr: 'Serbian',
  st: 'Sesotho',
  crs: 'Seychellois Creole',
  shn: 'Shan',
  sn: 'Shona',
  scn: 'Sicilian',
  szl: 'Silesian',
  sd: 'Sindhi',
  si: 'Sinhala (Sinhalese)',
  sk: 'Slovak',
  sl: 'Slovenian',
  so: 'Somali',
  es: 'Spanish',
  su: 'Sundanese',
  sw: 'Swahili',
  ss: 'Swati',
  sv: 'Swedish',
  tg: 'Tajik',
  ta: 'Tamil',
  tt: 'Tatar',
  te: 'Telugu',
  tet: 'Tetum',
  th: 'Thai',
  ti: 'Tigrinya',
  ts: 'Tsonga',
  tn: 'Tswana',
  tr: 'Turkish',
  tk: 'Turkmen',
  ak: 'Twi (Akan)',
  uk: 'Ukrainian',
  ur: 'Urdu',
  ug: 'Uyghur',
  uz: 'Uzbek',
  vi: 'Vietnamese',
  cy: 'Welsh',
  xh: 'Xhosa',
  yi: 'Yiddish',
  yo: 'Yoruba',
  yua: 'Yucatec Maya',
  zu: 'Zulu',
};

const listOfTranslations = ref([]);
const previousValues = ref([]);
const readableHeaders = ref([]);
const targetLanguage = ref(null);
const showTransLateForm = ref(false);
const translateLoader = ref(false);
const addLanguage = ref(null);
const addLanguageLoader = ref(false);
const removeLanguageLoader = ref({});
const deleteTranslationLoader = ref({});
const enableSave = ref({});
const saveLoader = ref({});
const headerObject = ref({});
const newRows = ref([]);
const disableDeleteButton = ref({});
const loader = ref(false);
const projects = ref(null);
const importCSV = ref();
const processing = ref({import: false, export: false});

const getListOfProjects = () => {
  ListProjectsFromOrganization().then((projects_list) => {
    projects.value = projects_list;
    console.log(projects_list);
  });
};

// Map the headers to more readable format for table
function mapReadableHeaders (headers) {
  const languageMap = SupportedLanguages;
  return headers.map((header, index) => {
    removeLanguageLoader.value[index] = false;
    if (header === 'S.no.') {
      return { name: 'S.NO.', value: 'S.no.' };
    } // Serial number header
    if (header === 'en') {
      return { name: 'English', value: 'en' };
    } // Ensure 'en' (English) is always included
    return {
      name: languageMap[header] || header, // Name of the language
      value: header, // Language code
    };
  });
}

// Get the unique languages for the table Headers
function generateTableHeaders (data) {
  const uniqueKeys = ['S.no.', 'en']; // Explicitly add 'en' (English) here
  previousValues.value = [];
  headerObject.value = {};
  data.forEach((item) => {
    previousValues.value.push({...item});
    enableSave.value[item._id] = false;
    deleteTranslationLoader.value[item._id] = false;
    saveLoader.value[item._id] = false;
    disableDeleteButton.value[item._id] = false;
    Object.keys(item).forEach((key) => {
      if (key !== '_id' && key !== '__v' && uniqueKeys.indexOf(key) === -1) {
        uniqueKeys.push(key);
        headerObject.value[key] = "";
      }
    });
  });
  return uniqueKeys;
}

// Get all the translations
async function fetchTranslations () {
  try {
    const data = await GetAllTranslation();
    const headers = generateTableHeaders(Object.values(data.translations));
    readableHeaders.value = mapReadableHeaders(headers);
    listOfTranslations.value = Object.values(data.translations);
    loader.value = false;
    return;
  } catch (error) {
    console.error("Error fetching translations:", error);
    loader.value = false;
    return;
  }
}

// Delete a translation
async function deleteTranslation (val){
  deleteTranslationLoader.value[val._id] = true;
  disableDeleteButton.value[val._id] = true;
  enableSave.value[val._id] = false;
  try {
    await DeleteTranslation (val._id);
    listOfTranslations.value = listOfTranslations.value.filter((item) => item._id !== val._id);
    deleteTranslationLoader.value[val._id] = false;
    disableDeleteButton.value[val._id] = false;
    setTimeout(() => {
      fetchTranslations();
    }, 1000);
  } catch (error) {
    deleteTranslationLoader.value[val._id] = false;
    console.error("Error deleting", error);
  }
}

// Get translation of text
async function handleTranslate (val){
  translateLoader.value = true;
  const params = {
    sourceLanguageCode: 'en',
    targetLanguageCode: val.targetLanguage.key,
    text: val.text,
  };

  try {
    const response =  await Translate (params);
    console.log (response);
    fetchTranslations();
    showTransLateForm.value = false;
    translateLoader.value = false;
    targetLanguage.value = null;
  } catch (error) {
    console.error("Error deleting", error);
    showTransLateForm.value = false;
    translateLoader.value = false;
    targetLanguage.value = null;
  }
}

// Add a new language to table
async function addNewLanguage () {
  if (!addLanguage.value){
    return;
  }
  addLanguageLoader.value = true;
  console.log(addLanguage.value);
  const params = {
    targetLanguageCode: addLanguage.value.key,
  };
  try {
    await AddNewLanguage (params);
    await fetchTranslations();
    addLanguageLoader.value = false;
    addLanguage.value = null;
  } catch (error) {
    console.error("Error deleting", error);
    fetchTranslations();
    addLanguageLoader.value = false;
    addLanguage.value = null;
  }
}

// Remove language
async function handleRemove (val, index) {
  removeLanguageLoader.value[index] = true;
  try {
    await RemoveLanguage ({targetLanguageCode: val.value});
    readableHeaders.value = readableHeaders.value.filter((item) => item.value !== val.value);
    removeLanguageLoader.value[index] = false;
    fetchTranslations();
  } catch (error){
    removeLanguageLoader.value[index] = false;
    console.log(error);
  }
}

// compares object to check changes and returns value
const frameParms = (sourceObj, compareObj) => {
  const keys = Object.keys(compareObj);
  const newObj = {};
  keys.forEach((key) => {
    if (sourceObj[key] !== undefined) {
      if (JSON.stringify(sourceObj[key]) !== JSON.stringify(compareObj[key])) {
        newObj[key] = compareObj[key];
      }
    } else {
      if (compareObj[key]){
        newObj[key] = compareObj[key];
      }
    }
  });
  return newObj;
};

// to enable and disable save buttons keeping an eye on values changed in a row
function handleChange (obj, index) {
  if (!obj.en){
    enableSave.value[obj._id] = false;
    return;
  }
  if (Object.keys(frameParms(previousValues.value[index], obj)).length > 0) {
    enableSave.value[obj._id] = true;
  } else {
    enableSave.value[obj._id] = false;
  }
}

// Updates the existing row
async function handleUpdate (obj, index) {
  saveLoader.value[obj._id] = true;
  enableSave.value[obj._id] = false;
  const updatedObj = frameParms(previousValues.value[index], obj);
  disableDeleteButton.value[obj._id] = true;

  try {
    // Convert the map to an array of promises
    const updatePromises = Object.entries(updatedObj).map(async ([key, value]) => {
      const payload = {
        translationId: obj._id,
        targetLanguageCode: key,
        translation: value,
      };

      const response = await UpdateTranslation(payload);
      return response;
    });

    await Promise.all(updatePromises);

    previousValues.value = previousValues.value.map((item) => {
      if (item._id === obj._id) {
        return { ...item, ...updatedObj };
      }
      return item;
    });
  } catch (error) {
    console.error(error);
    enableSave.value[obj._id] = false;
    saveLoader.value[obj._id] = false;
    disableDeleteButton.value[obj._id] = false;
  } finally {
    saveLoader.value[obj._id] = false;
    enableSave.value[obj._id] = false;
    disableDeleteButton.value[obj._id] = false;
  }
  fetchTranslations();
}

// add a new row
function addRow () {
  const id = crypto.randomUUID();
  enableSave.value[id] = false;
  disableDeleteButton.value[id] = false;
  console.log(headerObject.value);
  newRows.value.push({...headerObject.value, _id: id});
}

// add a newly added unsaved row
function deleteNewRow (val) {
  enableSave.value[val._id] = false;
  disableDeleteButton.value[val._id] = true;
  deleteTranslationLoader.value[val._id] = true;
  newRows.value = newRows.value.filter((item) => item._id !== val._id);
  deleteTranslationLoader.value[val._id] = false;
}

// to enable and disable save buttons keeping an eye on values changed in newly added unsaved rows
function handleNewRowChange (obj, index) {
  if (!newRows.value[index].en){
    enableSave.value[obj._id] = false;
  } else {
    enableSave.value[obj._id] = true;
  }
}

async function storeTranslations (obj, index) {
  delete obj._id;
  enableSave.value[obj._id] = false;
  saveLoader.value[obj._id] = true;
  disableDeleteButton.value[obj._id] = true;
  const payload = {translations: newRows.value[index]};
  console.log(payload);
  try {
    await StoreTranslation(payload.translations.en, payload);
    await fetchTranslations();
    deleteNewRow(obj);
    delete disableDeleteButton.value[obj._id];
    delete enableSave.value[obj._id] ;
    delete saveLoader.value[obj._id] ;
  } catch (error) {
    console.error(error);
    enableSave.value[obj._id] = false;
    saveLoader.value[obj._id] = false;
    disableDeleteButton.value[obj._id] = false;
  }
}

onMounted(() => {
  loader.value = true;
  fetchTranslations();
});

function handleTranslateProject (val){

  TranslateProjectData(val.project._id);
}

const importExcel = async (event) => {
  processing.value.import = true;
  const file =  event.target.files[0];
  const formData = new FormData();
  formData.append('file', file);
  try {
    await importTranslations(formData);
    fetchTranslations();
    processing.value.import = false;
    if (importCSV.value) {
      importCSV.value.value = ''; // Clear the file input
    }
  } catch (error) {
    console.log('Error uploading translations:', error);
    processing.value.import = false;
    if (importCSV.value) {
      importCSV.value.value = ''; // Clear the file input
    }
  }
};

const exportExcel = async () => {
  processing.value.export = true;
  try {
    const response = await ExportTranslationApi();
    const blob = new Blob([response.data], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');

    link.href = url;
    link.setAttribute('download', 'translations.xlsx');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    processing.value.export = false;

  } catch (error) {
    console.log(error);
    processing.value.export = false;
  }
};

getListOfProjects();
</script>

<template>
  <div>
    <LoaderComp v-if="loader"/>

    <div class="flex items-center justify-between w-full mb-6">
      <p class="text-txt-50 dark:text-txt-1000 text-2xl font-semibold ">
        Translations
      </p>
      <div>
          <Button class="!px-3" @click="()=>exportExcel()" title="Export File" :disabled="processing.export">
            <template v-slot:svg>
              <div v-if="processing.export" class="loader !h-3 !w-3 !border-2"></div>
              <svg v-else width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_306_21007)">
                  <path class="fill-txt-1000 dark:fill-txt-default"
                    d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                  <path class="fill-txt-1000 dark:fill-txt-default"
                    d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                </g>
                <defs>
                  <clipPath id="clip0_306_21007">
                    <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                  </clipPath>
                </defs>
              </svg>
            </template>
          </Button>
      </div>
      <div>
        <input ref="importCSV" class="hidden" type="file" accept=".xlsx" @change="(e)=>importExcel(e,listOfTranslations,readableHeaders)">
        <Button class="!px-3" @click="()=>importCSV.click()" title="Import File" :disabled="processing.import">
          <template v-slot:svg>
            <div v-if="processing.import" class="loader !h-3 !w-3 !border-2"></div>
            <svg v-else width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_306_21007)">
                <path class="fill-txt-1000 dark:fill-txt-default"
                  d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                <path class="fill-txt-1000 dark:fill-txt-default"
                  d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
              </g>
              <defs>
                <clipPath id="clip0_306_21007">
                  <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                </clipPath>
              </defs>
            </svg>
          </template>
        </Button>
      </div>
    </div>

    <div>
        <div v-if="projects">
          <Form :validation-schema="TranslateProject" @submit="handleTranslateProject" class="flex md:gap-3 gap-6 flex-col md:flex-row">
              <div class="relative" :class="projects && 'w-96'">
                <!-- <label for="targetLanguage" class="bg-white  cursor-text text-sm text-bg-50 py-0 mb-2">Select Project<strong>*</strong></label> <br/> -->
                <Field v-slot="{ field }" name="project" type="project" :model-value="targetLanguage">
                  <Multiselect class="mmulti" v-bind="field" v-model="targetLanguage" placeholder="Select Project" :options="Object.values(projects)"
                  :multiple="false" :maxHeight="200" label="name"/>
                </Field>
                <ErrorMessage as="p" class="absolute text-sm text-rose-500 mt-1 capitalize" name="project" />
              </div>
              <div>
                <Button title="Translate project data" type="submit" theme="primary"> </Button>
              </div>
          </Form>
        </div>
      </div>

    <!-- Translate -->
    <div class="mb-6">
      <div class="flex justify-between mb-3 items-center">
        <p class="font-bold text-base">
          Translate
        </p>
        <div>
            <div>
              <Button v-if="!showTransLateForm" type="button" title="Translate" theme="primary"
                @handle-click="() => showTransLateForm = !showTransLateForm">
                <template v-slot:svg>
                  <svg class="w-4 h-4" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M19.5303 4.93757L15.0624 0.46874C14.9139 0.320134 14.7375 0.20225 14.5433 0.121823C14.3492 0.0413957 14.1411 0 13.931 0C13.7208 0 13.5128 0.0413957 13.3186 0.121823C13.1245 0.20225 12.9481 0.320134 12.7995 0.46874L0.46899 12.8003C0.319775 12.9483 0.201474 13.1245 0.120963 13.3187C0.0404513 13.5128 -0.000663414 13.721 8.09464e-06 13.9312V18.4001C8.09464e-06 18.8244 0.168573 19.2313 0.468619 19.5314C0.768666 19.8314 1.17562 20 1.59995 20H6.06878C6.27896 20.0007 6.48718 19.9595 6.68134 19.879C6.87549 19.7985 7.0517 19.6802 7.19973 19.531L19.5303 7.20048C19.6789 7.05191 19.7968 6.87552 19.8772 6.68138C19.9576 6.48724 19.999 6.27916 19.999 6.06903C19.999 5.85889 19.9576 5.65081 19.8772 5.45667C19.7968 5.26253 19.6789 5.08614 19.5303 4.93757ZM6.06878 18.4001H1.59995V13.9312L10.3996 5.13156L14.8684 9.60039L6.06878 18.4001ZM15.9994 8.46843L11.5306 4.00061L13.9305 1.6007L18.3993 6.06853L15.9994 8.46843Z"
                      fill="white" />
                  </svg>
                </template>
              </Button>
              <div v-if="showTransLateForm" class="flex justify-start items-center gap-3">
                <Button title="Reset" type="button" theme="secondary" @handle-click="() => showTransLateForm = !showTransLateForm" :disabled="translateLoader">
                </Button>
                <label for="translate"
                  :class="['bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[9px] p-4 !h-8 m-0 cursor-pointer',(translateLoader && `pointer-events-none opacity-50`)]">
                  <div v-if="translateLoader" class="loader"></div>
                  Save </label>
              </div>
            </div>
        </div>
      </div>

      <!-- Form -->
      <div>
        <Form v-if="showTransLateForm" :validation-schema="translateValidation" @submit="handleTranslate">
          <div class="grid grid-cols-2 lg:grid-cols-3 gap-5">
            <div class="relative">
              <label for="text" class="bg-white  cursor-text text-sm text-bg-50 py-0 mb-2">Enter text <strong>*</strong></label> <br/>
              <Field name="text" as="input" type="text" class="input-primary" />
              <ErrorMessage as="p" class="absolute text-sm text-rose-500 mt-1 capitalize" name="text" />
            </div>
            <div class="relative">
              <label for="targetLanguage" class="bg-white  cursor-text text-sm text-bg-50 py-0 mb-2">Target lanaguage <strong>*</strong></label> <br/>
              <Field v-slot="{ field }" name="targetLanguage" type="targetLanguage" :model-value="targetLanguage">
                <Multiselect class="mmulti" v-bind="field" v-model="targetLanguage" placeholder="Select" :options="Object.entries(SupportedLanguages).map(([key,value])=>{ if(key=='en') return {key, value, $isDisabled:true}; return ({key, value})})"
                :multiple="false" :maxHeight="200" label="value"/>
              </Field>
              <ErrorMessage as="p" class="absolute text-sm text-rose-500 mt-1 capitalize" name="targetLanguage" />
            </div>
          </div>
          <Button id="translate" class="hidden" title="Submit" type="submit" theme="primary"> </Button>
        </Form>
      </div>
    </div>

    <!-- Table -->
    <div class="relative">
      <div
        v-if="readableHeaders.length && listOfTranslations.length"
        class="hidden sm:block overflow-x-auto mb-4 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden p-2"
      >
        <table class="w-full rounded-lg bg-transparent">

          <!-- Headings -->
          <thead>
            <tr>
              <th
                v-for="(item, index) in readableHeaders"
                :key="index"
                class="p-3 text-left text-sm font-semibold text-gray-900 capitalize"
              >
                <div class="flex gap-3 items-center">
                  {{ item.name }}
                  <span v-if="item.value !== 'S.no.' && !removeLanguageLoader[index]" class="hover:cursor-pointer" @click="handleRemove(item,index)">
                    <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M20.25 4.5H16.5V3.75C16.5 3.15326 16.2629 2.58097 15.841 2.15901C15.419 1.73705 14.8467 1.5 14.25 1.5H9.75C9.15326 1.5 8.58097 1.73705 8.15901 2.15901C7.73705 2.58097 7.5 3.15326 7.5 3.75V4.5H3.75C3.55109 4.5 3.36032 4.57902 3.21967 4.71967C3.07902 4.86032 3 5.05109 3 5.25C3 5.44891 3.07902 5.63968 3.21967 5.78033C3.36032 5.92098 3.55109 6 3.75 6H4.5V19.5C4.5 19.8978 4.65804 20.2794 4.93934 20.5607C5.22064 20.842 5.60218 21 6 21H18C18.3978 21 18.7794 20.842 19.0607 20.5607C19.342 20.2794 19.5 19.8978 19.5 19.5V6H20.25C20.4489 6 20.6397 5.92098 20.7803 5.78033C20.921 5.63968 21 5.44891 21 5.25C21 5.05109 20.921 4.86032 20.7803 4.71967C20.6397 4.57902 20.4489 4.5 20.25 4.5ZM9 3.75C9 3.55109 9.07902 3.36032 9.21967 3.21967C9.36032 3.07902 9.55109 3 9.75 3H14.25C14.4489 3 14.6397 3.07902 14.7803 3.21967C14.921 3.36032 15 3.55109 15 3.75V4.5H9V3.75ZM18 19.5H6V6H18V19.5ZM10.5 9.75V15.75C10.5 15.9489 10.421 16.1397 10.2803 16.2803C10.1397 16.421 9.94891 16.5 9.75 16.5C9.55109 16.5 9.36032 16.421 9.21967 16.2803C9.07902 16.1397 9 15.9489 9 15.75V9.75C9 9.55109 9.07902 9.36032 9.21967 9.21967C9.36032 9.07902 9.55109 9 9.75 9C9.94891 9 10.1397 9.07902 10.2803 9.21967C10.421 9.36032 10.5 9.55109 10.5 9.75ZM15 9.75V15.75C15 15.9489 14.921 16.1397 14.7803 16.2803C14.6397 16.421 14.4489 16.5 14.25 16.5C14.0511 16.5 13.8603 16.421 13.7197 16.2803C13.579 16.1397 13.5 15.9489 13.5 15.75V9.75C13.5 9.55109 13.579 9.36032 13.7197 9.21967C13.8603 9.07902 14.0511 9 14.25 9C14.4489 9 14.6397 9.07902 14.7803 9.21967C14.921 9.36032 15 9.55109 15 9.75Z" fill="#5B616E"/>
                    </svg>
                  </span>
                  <div v-if="removeLanguageLoader[index]" class="loader !h-3.5 !w-3.5 !border-2"></div>
                </div>
              </th>
              <th class="w-56 flex justify-between gap-1">
                <div class="w-48">
                  <Multiselect class="mmulti" :show-labels="false" trackBy="value" v-model="addLanguage" tag-placeholder="" placeholder="Add language" :options="Object.entries(SupportedLanguages).map(([key,value])=>({key, value}))" :multiple="false" :taggable="true"  :maxHeight="200" label="value"/>
                </div>
                <Button class="!px-2" @click="addNewLanguage" :disabled="addLanguageLoader">
                  <template v-slot:svg>
                    <svg v-if="!addLanguageLoader" width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0_306_21007)">
                        <path class="fill-txt-1000 dark:fill-txt-default"
                          d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                        <path class="fill-txt-1000 dark:fill-txt-default"
                          d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                      </g>
                      <defs>
                        <clipPath id="clip0_306_21007">
                          <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                        </clipPath>
                      </defs>
                    </svg>
                    <div v-if="addLanguageLoader" class="loader !h-3.5 !w-3.5 !border-2"></div>
                  </template>
                </Button>
              </th>
            </tr>
          </thead>

          <!-- Body or Columns and rows -->
          <tbody>
            <tr v-for="(obj, index) in listOfTranslations" :key="index"
              class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50 capitalize"
            >
              <td class="p-3" v-for="(header,headerIndex) in readableHeaders" :key="headerIndex">
                <div v-if="header.value === 'S.no.'">
                  {{ index + 1 }}
                </div>
                <input
                  type="text"
                  v-if="header.value !== 'S.no.'"
                  v-model="obj[header.value]"
                  class="input-primary min-w-48"
                  :placeholder="header.value === 'en' ? 'Required*' : ''"
                  @input="handleChange(obj,index)"
                />
              </td>
              <td class="p-3 min-w-10 flex">
                <div class="m-auto flex gap-4">
                  <button @click="deleteTranslation(obj)" :disabled="disableDeleteButton[obj._id]" :class="['border-2 h-[2.5rem] w-[2.5rem] border-bg-50 flex gap-2 items-center justify-center p-1 rounded-lg',(disableDeleteButton[obj._id] && `pointer-events-none opacity-50`)]">
                    <div v-if="deleteTranslationLoader[obj._id]" class="loader !h-3.5 !w-3.5 !border-2"></div>
                    <span v-else>
                      <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.25 4.5H16.5V3.75C16.5 3.15326 16.2629 2.58097 15.841 2.15901C15.419 1.73705 14.8467 1.5 14.25 1.5H9.75C9.15326 1.5 8.58097 1.73705 8.15901 2.15901C7.73705 2.58097 7.5 3.15326 7.5 3.75V4.5H3.75C3.55109 4.5 3.36032 4.57902 3.21967 4.71967C3.07902 4.86032 3 5.05109 3 5.25C3 5.44891 3.07902 5.63968 3.21967 5.78033C3.36032 5.92098 3.55109 6 3.75 6H4.5V19.5C4.5 19.8978 4.65804 20.2794 4.93934 20.5607C5.22064 20.842 5.60218 21 6 21H18C18.3978 21 18.7794 20.842 19.0607 20.5607C19.342 20.2794 19.5 19.8978 19.5 19.5V6H20.25C20.4489 6 20.6397 5.92098 20.7803 5.78033C20.921 5.63968 21 5.44891 21 5.25C21 5.05109 20.921 4.86032 20.7803 4.71967C20.6397 4.57902 20.4489 4.5 20.25 4.5ZM9 3.75C9 3.55109 9.07902 3.36032 9.21967 3.21967C9.36032 3.07902 9.55109 3 9.75 3H14.25C14.4489 3 14.6397 3.07902 14.7803 3.21967C14.921 3.36032 15 3.55109 15 3.75V4.5H9V3.75ZM18 19.5H6V6H18V19.5ZM10.5 9.75V15.75C10.5 15.9489 10.421 16.1397 10.2803 16.2803C10.1397 16.421 9.94891 16.5 9.75 16.5C9.55109 16.5 9.36032 16.421 9.21967 16.2803C9.07902 16.1397 9 15.9489 9 15.75V9.75C9 9.55109 9.07902 9.36032 9.21967 9.21967C9.36032 9.07902 9.55109 9 9.75 9C9.94891 9 10.1397 9.07902 10.2803 9.21967C10.421 9.36032 10.5 9.55109 10.5 9.75ZM15 9.75V15.75C15 15.9489 14.921 16.1397 14.7803 16.2803C14.6397 16.421 14.4489 16.5 14.25 16.5C14.0511 16.5 13.8603 16.421 13.7197 16.2803C13.579 16.1397 13.5 15.9489 13.5 15.75V9.75C13.5 9.55109 13.579 9.36032 13.7197 9.21967C13.8603 9.07902 14.0511 9 14.25 9C14.4489 9 14.6397 9.07902 14.7803 9.21967C14.921 9.36032 15 9.55109 15 9.75Z" fill="#5B616E"/>
                      </svg>
                    </span>
                  </button>
                  <button @click="handleUpdate(obj,index)" :disabled="!enableSave[obj._id] && !saveLoader[obj._id]" :class="['border-2 h-[2.5rem] w-[3.5rem] border-bg-50 flex gap-2 justify-center items-center p-1 rounded-lg',(!enableSave[obj._id] && `pointer-events-none opacity-50`)]">
                    <div v-if="saveLoader[obj._id]" class="loader !h-3.5 !w-3.5 !border-2"></div>
                    <span v-else>Save</span>
                  </button>
                </div>
              </td>
            </tr>

            <!-- Newly added rows by the user -->
            <tr v-for="(obj, index) in newRows" :key="index"
              class="bg-bg-800 capitalize"
            >
              <td class="p-3" v-for="(header, headerIndex) in readableHeaders" :key="headerIndex">
                <div v-if="header.value === 'S.no.'">
                  {{ listOfTranslations.length + index + 1 }}
                </div>
                <input
                  type="text"
                  v-if="header.value !== 'S.no.'"
                  v-model="obj[header.value]"
                  class="input-primary min-w-48"
                  :placeholder="header.value === 'en' ? 'Required*' : ''"
                  @input="handleNewRowChange(obj,index)"
                />
              </td>
              <td class="p-3 min-w-10 flex">
                <div class="m-auto flex gap-4">
                  <button @click="deleteNewRow(obj)" :disabled="disableDeleteButton[obj._id]" :class="['border-2 h-[2.5rem] w-[2.5rem] border-bg-50 flex gap-2 items-center justify-center p-1 rounded-lg',(disableDeleteButton[obj._id] && `pointer-events-none opacity-50`)]">
                    <div v-if="deleteTranslationLoader[obj._id]" class="loader !h-3.5 !w-3.5 !border-2"></div>
                    <span v-else>
                      <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M20.25 4.5H16.5V3.75C16.5 3.15326 16.2629 2.58097 15.841 2.15901C15.419 1.73705 14.8467 1.5 14.25 1.5H9.75C9.15326 1.5 8.58097 1.73705 8.15901 2.15901C7.73705 2.58097 7.5 3.15326 7.5 3.75V4.5H3.75C3.55109 4.5 3.36032 4.57902 3.21967 4.71967C3.07902 4.86032 3 5.05109 3 5.25C3 5.44891 3.07902 5.63968 3.21967 5.78033C3.36032 5.92098 3.55109 6 3.75 6H4.5V19.5C4.5 19.8978 4.65804 20.2794 4.93934 20.5607C5.22064 20.842 5.60218 21 6 21H18C18.3978 21 18.7794 20.842 19.0607 20.5607C19.342 20.2794 19.5 19.8978 19.5 19.5V6H20.25C20.4489 6 20.6397 5.92098 20.7803 5.78033C20.921 5.63968 21 5.44891 21 5.25C21 5.05109 20.921 4.86032 20.7803 4.71967C20.6397 4.57902 20.4489 4.5 20.25 4.5ZM9 3.75C9 3.55109 9.07902 3.36032 9.21967 3.21967C9.36032 3.07902 9.55109 3 9.75 3H14.25C14.4489 3 14.6397 3.07902 14.7803 3.21967C14.921 3.36032 15 3.55109 15 3.75V4.5H9V3.75ZM18 19.5H6V6H18V19.5ZM10.5 9.75V15.75C10.5 15.9489 10.421 16.1397 10.2803 16.2803C10.1397 16.421 9.94891 16.5 9.75 16.5C9.55109 16.5 9.36032 16.421 9.21967 16.2803C9.07902 16.1397 9 15.9489 9 15.75V9.75C9 9.55109 9.07902 9.36032 9.21967 9.21967C9.36032 9.07902 9.55109 9 9.75 9C9.94891 9 10.1397 9.07902 10.2803 9.21967C10.421 9.36032 10.5 9.55109 10.5 9.75ZM15 9.75V15.75C15 15.9489 14.921 16.1397 14.7803 16.2803C14.6397 16.421 14.4489 16.5 14.25 16.5C14.0511 16.5 13.8603 16.421 13.7197 16.2803C13.579 16.1397 13.5 15.9489 13.5 15.75V9.75C13.5 9.55109 13.579 9.36032 13.7197 9.21967C13.8603 9.07902 14.0511 9 14.25 9C14.4489 9 14.6397 9.07902 14.7803 9.21967C14.921 9.36032 15 9.55109 15 9.75Z" fill="#5B616E"/>
                      </svg>
                    </span>
                  </button>
                  <button @click="storeTranslations(obj,index)" :disabled="enableSave[obj._id] && saveLoader[obj._id]" :class="['border-2 h-[2.5rem] w-[3.5rem] border-bg-50 flex gap-2 justify-center items-center p-1 rounded-lg',(!enableSave[obj._id] && `pointer-events-none opacity-50`)]">
                    <div v-if="saveLoader[obj._id]" class="loader !h-3.5 !w-3.5 !border-2"></div>
                    <span v-else>Save</span>
                  </button>
                </div>
              </td>
            </tr>
            <tr>
              <Button class="!px-4" @click="()=>{addRow()}" :disabled="addLanguageLoader" title="ADD">
                <template v-slot:svg>
                  <svg v-if="!addLanguageLoader" width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_306_21007)">
                      <path class="fill-txt-1000 dark:fill-txt-default"
                        d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                      <path class="fill-txt-1000 dark:fill-txt-default"
                        d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                    </g>
                    <defs>
                      <clipPath id="clip0_306_21007">
                        <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                      </clipPath>
                    </defs>
                  </svg>
                  <div v-if="addLanguageLoader" class="loader !h-3.5 !w-3.5 !border-2"></div>
                </template>
              </Button>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>
<style>
.loader{
  @apply w-6 h-6 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-4 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>

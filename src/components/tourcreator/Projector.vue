<script setup>
import 'aframe';
import { defineProps } from 'vue';

const props = defineProps({
  url: String,
  id: String,
  activateOrientation: Boolean,
});

console.log(props.url);

function cdn (path, bucketURL, replaceURL) {
  if (path !== undefined && path.includes('?alt')) {
    path = path.substring(0, path.lastIndexOf('?alt'));
    path = path.replace('firebasestorage.googleapis.com/v0/b/' + bucketURL + '/o', replaceURL);
    return path;
  }
  return path;
}

// Function recordRotation()
// {
//   Const tourId = ref(route.params.tour_id);
//   Const projectId = ref(route.params.project_id);

//   If(props.activateOrientation && props.activateOrientation === true)
//   {

//    Const rotationQuadients = document.getElementById('camera').getAttribute('rotation');
//    Console.log(rotationQuadients);

//     Const rotationData = {
//       Tour_id: tourId.value,
//       Image_id: props.id,
//       Rotation: {
//         X: rotationQuadients.x,
//         Y: rotationQuadients.y,
//         Z: rotationQuadients.z,
//       }
//     };

//     Console.log(rotationData);

//     UpdateImage(rotationData)
//         .then((res) => {
//         })
//         .catch((err) => {
//           Console.log(err);
//         });
//   }
// }

</script>

<template>
    <div class=" h-full projector">
        <a-scene @mouseup="recordRotation" id="scene" loading-screen="enabled:false" vr-mode-ui="enabled: false" style="position:absolute;" renderer="colorManagement:true;sortObjects: true;maxCanvasWidth: 1920;maxCanvasHeight: 1920;">
          <a-entity id="mouseCursor" cursor="rayOrigin: mouse;fuse:false;"  raycaster="objects:.boxes,.emb,.arrows" ></a-entity>
            <a-assets id="assetsAdd" ref="assetsAdd">
                <img
                    id="scene1"
                    src="https://cdn.propvr.tech/creationtool/v2/images/vrinitial.jpg"
                  />
            </a-assets>

            <a-assets id="vidassets">

              </a-assets>

              <a-assets>

                <img id="hotspotimg" src="https://storagecdn.propvr.tech/assets%2Fhotspot.svg" defscale="0.1 0.1" crossorigin="anonymous">

                <img id="hotspotimgedit" src="https://storagecdn.propvr.tech/assets%2Fhotspotedit.svg" crossorigin="anonymous">

                <img id="infoimg" src="https://storagecdn.propvr.tech/assets%2Finfohotspot.svg" defscale="0.2 0.2" crossorigin="anonymous">

                <img id="infoimgedit" src="https://storagecdn.propvr.tech/assets%2Finfohotspotselect.svg" crossorigin="anonymous">

                <img id="inventory_icon" defscale="0.05 0.05" src="https://storagecdn.propvr.tech/iHwPWJvWDQYW6ilIAfNfgupytcb2%2Ficons%2F1628861761370-inv3.png?alt=media&token=bbcf5c21-ec82-4e93-a0dd-b369f1bf2c60">

                <img id="navarrowimg" src="https://cdn.propvr.tech/images/navarrow.svg">
              </a-assets>

              <a-entity position="0 0 0">
                    <a-sky  src="https://storagecdn.propvr.ai/propvr%2FHomePage%2FMetaverse.webp?alt=media"></a-sky>
                    <a-sky v-if="url" id="vrImage" radius="20" :src="cdn(url, 'propvr-in-31420.appspot.com', 'storagecdn.propvr.ai')"  material="shader:flat" crossorigin="anonymous"></a-sky>
              </a-entity>

              <a-entity rotation="0 0 0" id="stereo_sky" overunder=""></a-entity>

              <a-entity position="0 0 0" rotation="0 90 0" id="camera" camera
              cursor-visible="false" stereocam="eye:left"  look-controls compass cursor="rayOrigin: mouse">
                    <a-entity id="marker" position="0 0 -1"></a-entity>

                    <a-entity id="navarrow" position="0 -4 -7.5" scale="0.75 0.75 0.75" counter-rot visible="false">
                  <a-image id="uparr" class="arrows" position="-2 0 0" rotation="-90 90 0" src="#navarrowimg" material="side:front;opacity:0.3" data-toggle="modal" data-target="#hotspot_destination_modal" onclick="$('#hotspot_destination_modal')[0].dataset.for='uparr'" arrow-listener></a-image>
                  <a-image id="downarr" class="arrows" position="2 0 0" rotation="-90 -90 0" src="#navarrowimg" material="side:front;opacity:0.3" data-toggle="modal" data-target="#hotspot_destination_modal" onclick="$('#hotspot_destination_modal')[0].dataset.for='downarr'" arrow-listener></a-image>
                  <a-image id="leftarr" class="arrows" position="0 0 2" rotation="-90 180 0" src="#navarrowimg" material="side:front;opacity:0.3" data-toggle="modal" data-target="#hotspot_destination_modal" onclick="$('#hotspot_destination_modal')[0].dataset.for='leftarr'" arrow-listener></a-image>
                  <a-image id="rightarr" class="arrows" position="0 0 -2" rotation="-90 0 0" src="#navarrowimg" material="side:front;opacity:0.3" data-toggle="modal" data-target="#hotspot_destination_modal" onclick="$('#hotspot_destination_modal')[0].dataset.for='rightarr'" arrow-listener></a-image>
                  </a-entity>
              </a-entity>
        </a-scene>
   </div>
</template>

<style scoped>

.projector {
  width: 100%;
}
</style>

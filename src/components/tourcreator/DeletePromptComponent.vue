<script setup>
import { ref, defineEmits } from 'vue';
import { useRoute } from 'vue-router';
import { deleteImage } from '../../api/projects/customtour/index.ts';
import { ProjectStore } from '../../store/project';

const route = useRoute();
const emits = defineEmits(['ifDeleteClicked', 'ifCancelClicked']);
const tourId = ref(route.params.tour_id);
// Const imageId = ref(route.params.image_id);
const projectStore = ProjectStore();

const props = defineProps({
  imageId: String,
});

const tourImageId = ref(props.imageId);

const closeDeletePromptComponent = () => {
  emits('ifCancelClicked', false);
};

const deleteImg = () => {
  console.log(tourImageId);
  emits('ifDeleteClicked', false);
  const deleteTourProject =
    {
      image_id: tourImageId.value,
      tour_id: tourId.value,

    };

  deleteImage(deleteTourProject)
    .then(() => {
      const deleteImg = Object.keys(projectStore.tourImages).filter((x) => x === tourImageId.value)[0];
      delete projectStore.tourImages[deleteImg];
    })
    .catch((err) => {
      console.log(err);
    });

};
</script>

<template>
    <div class="flex justify-center items-center absolute w-full h-full backdrop-blur-[9px] bg-[#00000059] z-10">
        <div
            class="absolute z-3  min-w-[37.5rem] h-[11.56rem] flex flex-col p-6 gap-5 bg-bg-1000 rounded w-auto shadow z-10">
            <p class="text-txt-defalut text-lg font-bold leading-5">Are you sure you want to delete <spam
                    class="text-red-600">
                    {{ projectStore.tourImages[imageId].name }}</spam> image?</p>
            <p class="text-txt-650 text-sm/[15px] font-bold leading-5">This action will permanently delete this image
                from
                your tour.</p>
            <div class="flex  justify-end gap-3 mt-[1.25rem]">
                <button @click="closeDeletePromptComponent()"
                    class="w-32 h-10 bg-bg-850 rounded text-sm font-bold text-txt-default btn-shadow cursor-pointer">CANCEL</button>
                <button @click="deleteImg(name)"
                    class="w-32 h-10 bg-[#ff3d71] rounded text-sm font-bold text-txt-1000 btn-shadow cursor-pointer">DELETE</button>
            </div>
        </div>
    </div>
</template>

<style scoped>
.shadow {
    box-shadow: 0 27px 24px 0 rgba(0, 0, 0, .2), 0 40px 77px 0 rgba(0, 0, 0, .22);
}

.btn-shadow {
    box-shadow: 0 2px 2px 0 rgba(153, 153, 153, .14), 0 3px 1px -2px rgba(153, 153, 153, .2), 0 1px 5px 0 rgba(153, 153, 153, .12);
}
</style>

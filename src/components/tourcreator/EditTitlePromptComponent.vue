<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { updateImage } from '../../api/projects/customtour/index.ts';
import { ProjectStore } from '../../store/project';

const router = useRouter();
const route = useRoute();

const tourId = ref(route.params.tour_id);
const projectId = ref(route.params.project_id);
const imageId = ref(route.params.image_id);
const projectStore = ProjectStore();

const newTitle = ref(null);

const saveTitle = () => {

  const editTitle =
    {
      name: newTitle.value,
      image_id: imageId.value,
      tour_id: tourId.value,
    };

  updateImage(editTitle)
    .then(() => {

      const id = Object.keys(projectStore.tourImages).filter((x) => x === imageId.value)[0];
      projectStore.tourImages[id].name = editTitle.name;

    })
    .catch((err) => {
      console.log(err);
    });
  router.push(`/projects/${route.params.project_id}/tours/${route.params.tour_id}/customtour/`);
};
</script>

<template>
    <div class="flex justify-center items-center absolute w-full h-full backdrop-blur-[9px] bg-[#00000059] z-10 ">

        <div class="absolute z-3  w-[37.5rem] h-[8rem] flex flex-col p-6 gap-5 bg-bg-1000 rounded shadow ">
            <p class="text-txt-defalut text-lg font-bold leading-5 ">Edit Image Title <spam
                    class="text-red-600"></spam></p>

            <div class="flex justify-start gap-3">
                <input type="text" class="bg-gray-300 rounded placeholder:text-left h-10 w-[25em] px-[1em] "  v-model="newTitle"  placeholder="Enter the Title">
                <button @click="() => {
                    router.push(`/projects/${projectId}/tours/${tourId}/customtour/`);
                }"
                    class="w-32 h-10 bg-bg-850 rounded text-sm font-bold text-txt-default btn-shadow cursor-pointer">CANCEL</button>
                <button @click="saveTitle()"
                    class="w-32 h-10 bg-[#016315fc] rounded text-sm font-bold text-txt-1000 btn-shadow cursor-pointer">SAVE</button>
            </div>
        </div>
    </div>
</template>

<style scoped>
.shadow {
    box-shadow: 0 27px 24px 0 rgba(0, 0, 0, .2), 0 40px 77px 0 rgba(0, 0, 0, .22);
}

.btn-shadow {
    box-shadow: 0 2px 2px 0 rgba(153, 153, 153, .14), 0 3px 1px -2px rgba(153, 153, 153, .2), 0 1px 5px 0 rgba(153, 153, 153, .12);
}
</style>

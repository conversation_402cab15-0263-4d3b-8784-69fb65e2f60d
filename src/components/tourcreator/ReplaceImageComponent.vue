<script setup>
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { replaceImage} from '../../api/projects/customtour/index.ts';
import { ProjectStore } from '../../store/project';

const projectStore = ProjectStore();

const router = useRouter();
const route = useRoute();
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);
const imageId = ref(route.params.image_id);
const uploadedData = ref('');
const selectedImg = ref({});

selectedImg.value = Object.keys(projectStore.tourImages).filter((x) => x === imageId.value)[0];

const resizeWithCanvas = (img, type) => {
  if (type === 'file') {
    const MAX_WIDTH = 4096;
    const MAX_HEIGHT = 4096;
    const OUTPUT_QUALITY = 0.9;

    const canvas = document.createElement('canvas');
    let ctx = canvas.getContext('2d');
    let width = 4096;
    let height = 4096;

    ctx.drawImage(img, 0, 0);

    if (width > height) {
      if (width > MAX_WIDTH) {
        height *= MAX_WIDTH / width;
        width = MAX_WIDTH;
      }
    } else {
      if (height > MAX_HEIGHT) {
        width *= MAX_HEIGHT / height;
        height = MAX_HEIGHT;
      }
    }
    canvas.width = width;
    canvas.height = height;
    ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0, width, height);

    if (navigator.userAgent.toLowerCase().indexOf('chrome') > -1) {
      return canvas.toDataURL('image/jpeg', OUTPUT_QUALITY);
    }
    return canvas.toDataURL('image/jpeg');

  }
  if (type === 'thumb') {
    const MAX_WIDTH = 1024;
    const MAX_HEIGHT = 1024;
    const OUTPUT_QUALITY = 0.5;

    const canvas = document.createElement('canvas');
    let ctx = canvas.getContext('2d');
    let width = 1024;
    let height = 1024;

    ctx.drawImage(img, 0, 0);

    if (width > height) {
      if (width > MAX_WIDTH) {
        height *= MAX_WIDTH / width;
        width = MAX_WIDTH;
      }
    } else {
      if (height > MAX_HEIGHT) {
        width *= MAX_HEIGHT / height;
        height = MAX_HEIGHT;
      }
    }
    canvas.width = width;
    canvas.height = height;
    ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0, width, height);

    if (navigator.userAgent.toLowerCase().indexOf('chrome') > -1) {
      return canvas.toDataURL('image/jpeg', OUTPUT_QUALITY);
    }
    return canvas.toDataURL('image/jpeg');
  }
  return null;
};

function dataURLtoBlob (dataUrl) {
  const parts = dataUrl.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  const uInt8Array = new Uint8Array(rawLength);
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  return new Blob([uInt8Array], { type: contentType });
}

function handleFileUpload (file, type) {
  console.log('handleFileUpload called');

  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = function (event) {
      const blob = new Blob([event.target.result]);
      const blobURL = URL.createObjectURL(blob);

      const image = new Image();
      image.src = blobURL;

      image.onload = function () {
        const resized = resizeWithCanvas(image, 'thumb');
        const highres = resizeWithCanvas(image, type);

        const thumbnailBlob = dataURLtoBlob(resized);
        const highresBlob = dataURLtoBlob(highres);

        const extension = file.name.split('.').pop();
        const filename = file.name.replace(/\.[^/.]+$/, '');
        console.log('🚀 ~ handleFileUpload ~ filename:', filename);

        const tourImageObj = {
          project_id: projectId.value,
          tour_id: tourId.value,
          image_id: selectedImg,
          thumbnail: thumbnailBlob, // Assign thumbnail Image object
          thumbnailName: filename + '_lowres.' + extension,
          url: highresBlob, // Assign high-res Image object
          urlName: filename + '_highres.' + extension,
          thumbnailUrl: resized,
        };

        resolve(tourImageObj);
      };
    };

    reader.onerror = function (err) {
      console.error('FileReader error:', err);
      reject(err);
    };

    reader.readAsArrayBuffer(file);
  });
}

const openStorage = () => {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = 'image/*'; // Allow only image files
  fileInput.onchange = (event) => {
    const file = event.target.files[0];
    handleFileUpload(file, fileInput.type)
      .then((result) => {
        console.log(result);
        uploadedData.value = result;
        console.log(uploadedData.value);
      })
      .catch((err) => {
        console.log(err);
      });
  };
  fileInput.click();
};

const addTourImages = (data) => {
  console.log(data);

  replaceImage(data)
    .then((res) => {
      console.log(res.res._id);
      projectStore.tourImages[selectedImg].thumbnail= res.res.thumbnail;
      projectStore.tourImages[selectedImg].url= res.res.url;
    })
    .catch((err) => {
      console.log(err);
    });

  router.push(`/projects/${route.params.project_id}/tours/${route.params.tour_id}/customtour/`);

};

</script>

<template>
    <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-3/12 h-2/3 z-10 !bg-bg-1000 dark:bg-bg-default" >
        <div class="h-full !bg-bg-1000 p-6 flex flex-col ">
            <!-- header -->
            <div class="flex justify-between items-center mt-4 mb-2 p-1">
                    <p class="text-txt-default text-lg relative font-bold pb-2 dark:text-txt-1000 flex justify-start  items-center gap-1">Replace Image</p>
                    <div >
                    <p class="text-txt-default text-sm relative font-semibold pb-2 dark:text-txt-1000 flex justify-center  items-center gap-1"
                        @click="openStorage"> + Upload Image </p>
                    <input ref="fileInput" type="file" style="display: none" @change="onFileChange" />
                    </div>

            </div>

            <hr class="w-full bg-bg-50">

            <div class="flex justify-around mt-12 mb-16">

                <!-- to display the selected image -->
                <!-- <div class="flex flex-col justify-center items-center gap-4">
                    <div class="flex justify-center items-end">
                        <p class="text-txt-default text-lg relative font-bold pb-2 dark:text-txt-1000 flex justify-start  items-center gap-1">Current Image</p>
                    </div>

                    <div class="flex flex-col w-[11.875rem] h-[6rem] gap-3 items-center">
                        <p class="text-txt-default text-base relative font-medium whitespace-nowrap dark:text-txt-1000  mr-3">{{  projectStore.tourImages[selectedImg].name }}</p>
                        <img class="object-fill w-full h-full" :src="projectStore.tourImages[selectedImg].thumbnail"/>
                    </div>
                </div> -->

                <div class="flex flex-col justify-center items-center gap-4">
                    <div class="flex justify-center items-end">
                        <p class="text-txt-default text-lg relative font-bold pb-2 dark:text-txt-1000 flex justify-start  items-center gap-1">New Image</p>
                    </div>

                    <div class=" w-[11.875rem] h-[6rem] border border-black custom-img">
                        <div v-if="uploadedData" class="flex flex-col  w-[11.875rem] h-[6rem] gap-3 items-center">
                            <!-- <p class="text-txt-default text-base relative font-medium whitespace-nowrap dark:text-txt-1000  mr-3">{{ uploadedData.name  }}</p> -->
                            <img class="object-fill w-full h-full" :src="uploadedData.thumbnailUrl" alt="New Image"/>
                        </div>
                    </div>
                </div>

            </div>

            <div class=" flex items-center justify-end pt-[20px] ">
                <div class="flex gap-2">
                    <button type="button"
                    @click="
                () => {
                router.push(`/projects/${projectId}/tours/${tourId}/customtour/`);
                }"
                    class=" flex justify-between items-center w-fit h-10 m-0 px-3 text-base text-txt-500 font-semibold leading-6  sm:rounded-md">
                    CANCEL</button>

                    <button type="button"
                    @click="addTourImages(uploadedData)"
                    class="bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 text-base rounded-lg flex flex-row justify-center items-center gap-[0.56rem] px-3 h-10 disabled:opacity-80">
                    Add To Tour</button>
                </div>
            </div>

        </div>
    </div>
</template>

<style scoped>
.custom-img {
  border: 2px solid white;
  float: right;
  border-radius: 5px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>

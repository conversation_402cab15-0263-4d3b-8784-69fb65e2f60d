<script setup>
import { ref, onMounted } from 'vue';
import AddMediaImageCard from './AddMediaImageCard.vue';
import { ProjectStore } from '../../store/project';
import { useRoute, useRouter } from 'vue-router';
import {createImage} from '../../api/projects/customtour/index.ts';

const router = useRouter();
const route = useRoute();
const Store = ProjectStore();

const data=ref([]);
const selectedImgData=ref([]);
const fileInput = ref(null);
const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);

const resizeWithCanvas = (img, type) => {
  if (type === 'file') {
    const MAX_WIDTH = 4096;
    const MAX_HEIGHT = 4096;
    const OUTPUT_QUALITY = 0.9;

    const canvas = document.createElement('canvas');
    let ctx = canvas.getContext('2d');
    let width = 4096;
    let height = 4096;

    ctx.drawImage(img, 0, 0);

    if (width > height) {
      if (width > MAX_WIDTH) {
        height *= MAX_WIDTH / width;
        width = MAX_WIDTH;
      }
    } else {
      if (height > MAX_HEIGHT) {
        width *= MAX_HEIGHT / height;
        height = MAX_HEIGHT;
      }
    }
    canvas.width = width;
    canvas.height = height;
    ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0, width, height);

    if (navigator.userAgent.toLowerCase().indexOf('chrome') > -1) {
      return canvas.toDataURL('image/jpeg', OUTPUT_QUALITY);
    }
    return canvas.toDataURL('image/jpeg');

  }
  if (type === 'thumb') {
    const MAX_WIDTH = 1024;
    const MAX_HEIGHT = 1024;
    const OUTPUT_QUALITY = 0.5;

    const canvas = document.createElement('canvas');
    let ctx = canvas.getContext('2d');
    let width = 1024;
    let height = 1024;

    ctx.drawImage(img, 0, 0);

    if (width > height) {
      if (width > MAX_WIDTH) {
        height *= MAX_WIDTH / width;
        width = MAX_WIDTH;
      }
    } else {
      if (height > MAX_HEIGHT) {
        width *= MAX_HEIGHT / height;
        height = MAX_HEIGHT;
      }
    }
    canvas.width = width;
    canvas.height = height;
    ctx = canvas.getContext('2d');
    ctx.drawImage(img, 0, 0, width, height);

    if (navigator.userAgent.toLowerCase().indexOf('chrome') > -1) {
      return canvas.toDataURL('image/jpeg', OUTPUT_QUALITY);
    }
    return canvas.toDataURL('image/jpeg');

  }
  return null;
};

function dataURLtoBlob (dataUrl) {
  const parts = dataUrl.split(';base64,');
  const contentType = parts[0].split(':')[1];
  const raw = window.atob(parts[1]);
  const rawLength = raw.length;
  const uInt8Array = new Uint8Array(rawLength);
  for (let i = 0; i < rawLength; ++i) {
    uInt8Array[i] = raw.charCodeAt(i);
  }
  return new Blob([uInt8Array], { type: contentType });
}

async function handleFileUpload (files, type) {
  console.log('handleFileUpload called');
  const filesArray = Array.from(files);
  const promises = [];

  for (let i = 0; i < filesArray.length; i++) {
    const file = filesArray[i];

    promises.push(new Promise((resolve, reject) => {
      const reader = new FileReader();

      reader.onload = function (event) {
        const blob = new Blob([event.target.result]);
        const blobURL = URL.createObjectURL(blob);

        const image = new Image();
        image.src = blobURL;

        image.onload = function () {
          const resized = resizeWithCanvas(image, 'thumb');
          const highres = resizeWithCanvas(image, type);
          const name = file.name;

          const thumbnailBlob = dataURLtoBlob(resized);
          const highresBlob = dataURLtoBlob(highres);

          const extension = file.name.split('.').pop();
          const filename = file.name.replace(/\.[^/.]+$/, '');
          console.log('🚀 ~ handleFileUpload ~ filename:', filename);

          const tourImageObj = {
            project_id: projectId.value,
            tour_id: tourId.value,
            name: name,
            thumbnail: thumbnailBlob, // Assign thumbnail Image object
            thumbnailName: filename + '_lowres.' + extension,
            urlName: filename + '_highres.' + extension,
            url: highresBlob, // Assign high-res Image object
            thumbnailUrl: resized,

          };
          console.log(tourImageObj.thumbnailName, tourImageObj.urlName);
          resolve(tourImageObj);
        };
      };

      reader.onerror = function (err) {
        reject(err);
      };

      reader.readAsArrayBuffer(file);
    }));
  }
  return Promise.allSettled(promises);
}

const openStorage = () => {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = 'image/*';
  fileInput.multiple = true;
  fileInput.onchange = (event) => {
    handleFileUpload(event.target.files, fileInput.type).then((results) => {
      console.log(results);
      const datas=[];
      results.forEach((resultItem) => {
        datas.push(resultItem.value);
      });
      data.value = [...datas];
      console.log(data.value );
    }).catch( (err) => {
      console.log(err);
    });
  };
  fileInput.click();
};

const select = (data) => {
  selectedImgData.value.push(data);
  console.log(selectedImgData.value);
};
const deSelect = () => {
  console.log(selectedImgData.value);
};

const addTourImages = () => {

  for (let i=0;i < selectedImgData.value.length;i++){
    createImage(selectedImgData.value[i])
      .then((res) => {
        Store.$patch({tourImages: {...Store.$state.tourImages, [res.data._id]: res.data}});
      })
      .catch((err) => {
        console.log(err);
      });
  }

  router.push(`/projects/${route.params.project_id}/tours/${route.params.tour_id}/customtour/`);
};

onMounted(() => {
  fileInput.value = document.createElement('input');
  fileInput.value.type = 'file';
  fileInput.value.multiple = true;
  fileInput.value.style.display = 'none';
  fileInput.value.addEventListener('change', handleFileUpload);
  document.body.appendChild(fileInput.value);
});

</script>

<template>

  <div class="w-full h-full flex  top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 justify-center items-center absolute backdrop-blur-[9px] bg-[#00000059] z-10 ">
    <div class="  bg-bg-1000 dark:bg-bg-default min-w-[40em] w-fit " >
        <!-- header -->
       <div class="h-[11.25rem] flex flex-col gap-3 p-6" >
           <p class="text-txt-default text-lg relative font-bold pb-2 dark:text-txt-1000 flex justify-start  items-center gap-1">Add Media</p>
           <div class="flex justify-between">
               <div class="flex flex-col gap-2">
                    <div>
                    <p class="text-txt-500 text-base relative font-semibold pb-2 dark:text-txt-1000">Browse or Apply Media to tour</p>
                    </div>
                    <!-- <div class="flex gap-2 justify-center items-center">
                        <input class="w-4 h-4" type="checkbox" name="" id="">
                        <p class="text-sm font-semibold">Disabling the image compression</p>
                    </div> -->
               </div>
                <div>
                   <p class="text-txt-default text-sm relative font-semibold pb-2 dark:text-txt-1000 flex justify-center  items-center gap-1"
                    @click="openStorage"> + Upload Files </p>
                   <input ref="fileInput" type="file" style="display: none" @change="onFileChange" />
                </div>
           </div>
           <hr>
           <div class="flex justify-between">
               <div class="flex gap-6">
                    <p class="text-txt-default text-base relative font-semibold pb-2 dark:text-txt-1000  mr-3"> Current Projects</p>
                    <!-- //text-txt-650 -->
                    <!-- <input class="text-txt-1000 text-base font-bold" type="radio" name="" id="" > Other Projects -->
               </div>
                <!-- <div>
                   <p @click="openDeletePromptComponent()"
                    class="text-txt-default text-sm relative font-semibold pb-2 dark:text-txt-1000 flex justify-center items-center gap-1">
                    <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M11.9904 3.50077C11.1614 3.50077 10.4894 4.17267 10.4894 5.00149L10.4894 5.00812H13.4914L13.4914 5.00149C13.4914 4.17267 12.8194 3.50077 11.9904 3.50077ZM8.98836 5.00149L8.98837 5.00812H3.75369C3.33744 5.00812 3 5.34556 3 5.76181C3 6.17806 3.33744 6.5155 3.75369 6.5155H5.00399V19.0001C5.00399 20.6569 6.34713 22.0001 8.00399 22.0001H16.0079C17.6648 22.0001 19.0079 20.6569 19.0079 19.0001V6.5155H20.2363C20.6526 6.5155 20.99 6.17806 20.99 5.76181C20.99 5.34556 20.6526 5.00812 20.2363 5.00812H14.9924L14.9925 5.00149C14.9925 3.34381 13.6484 2 11.9904 2C10.3324 2 8.98836 3.34381 8.98836 5.00149ZM13.4912 9.75192C13.4912 9.3371 13.828 9.00083 14.2428 9.00083C14.6576 9.00083 14.9944 9.33711 14.9944 9.75192V17.2979C14.9944 17.7127 14.6576 18.049 14.2428 18.049C13.828 18.049 13.4912 17.7127 13.4912 17.2979V9.75192ZM9.00057 9.75194C9.00057 9.33712 9.33737 9.00085 9.75218 9.00085C10.167 9.00085 10.5038 9.33713 10.5038 9.75194V17.298C10.5038 17.7128 10.167 18.049 9.75218 18.049C9.33737 18.049 9.00057 17.7128 9.00057 17.298V9.75194Z" fill="#FF1E46"/>
                    </svg>
                     Delete Media</p>
                </div>  -->
           </div>
       </div>

       <!-- body -->
       <div class="min-h-[18.75rem] max-h-[21.87rem] overflow-y-scroll flex flex-wrap p-6 pt-0 gap-4">
            <div  class="" v-for="(key, index) in data" :key="index">
                <AddMediaImageCard :data="data[index]"  @checked="select(data[index])" @uncheck = "deSelect(data[index])"/>
            </div>
       </div>

       <!-- footer -->
       <div class="h-[3.62rem] flex items-center justify-end px-4 pb-8 pt-4 ">
            <div class="flex gap-2">
                <button type="button"
                @click="
            () => {
              router.push(`/projects/${projectId}/tours/${tourId}/customtour/`);
            }"
                class=" flex justify-between items-center w-fit h-10 m-0 px-3 text-base text-txt-500 font-semibold leading-6  sm:rounded-md">
                CANCEL</button>

                <button type="button"
                @click="addTourImages()"
                class="bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[0.56rem] px-3 h-10 disabled:opacity-80">
                Add To Tour</button>
            </div>

       </div>

    </div>

</div>

</template>

<style scoped>
input[type=radio]{
    display: none;
}

</style>

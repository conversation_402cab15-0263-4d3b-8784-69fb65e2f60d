<script setup>
import { ref,  defineEmits } from 'vue';
import ImageCard from './ImageCard.vue';
import { useRoute} from 'vue-router';
import router from '@/router';
import { ProjectStore } from '../../store/project';
import DeletePromptComponent from './DeletePromptComponent.vue';

const projectStore = ProjectStore();
const route = useRoute();
const showAddMediaComponent = ref(false);
const tourImageId = ref(null);

function deleteImages (imageId){
  console.log('imageId'+ imageId);
  tourImageId.value =imageId;
  showAddMediaComponent.value = !showAddMediaComponent.value;
}

function closePrompt (){
  showAddMediaComponent.value = !showAddMediaComponent.value;
}

const emits = defineEmits(['selectedImage', 'changeOrientationId']);

const projectId = ref(route.params.project_id);
const tourId = ref(route.params.tour_id);

const open = ref(true);

const toggle = () => {
  open.value = !open.value;
};

const selectedImageId = (id) => {
  const selectedUrl = projectStore.tourImages[id].url;
  emits('selectedImage', selectedUrl);

};

const changeOrientation = (id, activateOrientation) => {
  emits('changeOrientationId', id, activateOrientation);
};

function checkWindowWidth () {
  var windowWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;

  if (windowWidth < 1080) {
    open.value=false;
  } else {
    open.value=true;
  }
}

window.addEventListener('resize', checkWindowWidth);
checkWindowWidth();
</script>

<template>
  <div  class="flex flex-rows z-10 ">
    <div :class="[open ? 'dopen' : 'dclose']"
     class="overflow-hidden top-0 left-0 flex flex-col justify-center items-center max-h-full bg-bg-1000">
        <div class="my-2 ">
          <button type="button absolute"
          @click="
            () => {
              router.push(`/projects/${projectId}/tours/${tourId}/customtour/addtour`);
            }
          "
              class="bg-bg-50 dark:bg-bg-1000 text-txt-1000 text-sm font-medium dark:text-txt-150 rounded-lg flex flex-row justify-center items-center gap-[0.56rem] !p-4 h-10 disabled:opacity-80">
              <svg  class="w-5 h-5" enable-background="new 0 0 50 50" height="50px" id="Layer_1" version="1.1" viewBox="0 0 50 50" width="50px" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect fill="none" height="50" width="50"/><line fill="none" stroke="#ffffff" stroke-miterlimit="10" stroke-width="4" x1="9" x2="41" y1="25" y2="25"/><line fill="none" stroke="#ffffff" stroke-miterlimit="10" stroke-width="4" x1="25" x2="25" y1="9" y2="41"/></svg>
              Add images
          </button>
        </div>

        <div class=" flex grow flex-col gap-y-3 overflow-y-auto max-h-screen no-scrollbar">

          <div class="flex flex-col  " v-for="(data, index) in projectStore.tourImages" :key="index">
            <ImageCard :data="data" :index="index" @deleteButton="deleteImages(data._id)" @orientation="changeOrientation" @selectedImageId="selectedImageId"/>
            <hr>
          </div>
        </div>
   </div>

    <div @click="toggle"
      class=" w-10 h-10 cursor-pointer flex items-center justify-center bg-white">
      <svg :class="[open ? 'arrow-open' : 'arrow-close']" class="rotate-90" xmlns="http://www.w3.org/2000/svg" height="24" viewBox="0 -960 960 960" width="24">
        <path
          d="M480-200 240-440l56-56 184 183 184-183 56 56-240 240Zm0-240L240-680l56-56 184 183 184-183 56 56-240 240Z" />
      </svg>
    </div>

  </div>
   <div v-if="showAddMediaComponent" class="absolute w-full h-full" >
    <DeletePromptComponent :imageId="tourImageId"  @ifDeleteClicked="closePrompt()" @ifCancelClicked="closePrompt()" />
    </div>
</template>

<style scoped>
.no-scrollbar {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
    display: none;
}

@keyframes side-bar {
  0% {
    width:0;
  }
  80%
  {
    width:340px
  }
  100% {
    width:300px;
  }
}

@keyframes side-bar-close {
  0% {
    width:300px;
  }
  80%
  {
    width:40px
  }
  100% {
    width:0px

  }
}

.dopen {
  animation: side-bar 0.5s cubic-bezier(0.455, 0.030, 0.515, 0.955) both;
}

.dclose {
  animation: side-bar-close 0.5s cubic-bezier(0.455, 0.030, 0.515, 0.955) both;
}
.arrow-open {
  transform: rotate(90deg);
  transition-duration: 0.5s;
}

.arrow-close {
  transform: rotate(270deg);
  transition-duration: 0.5s;
}

.deleteAnimation
{
  animation: slide-bottom 0.3s cubic-bezier(0.250, 0.460, 0.450, 0.940) both;
}
@keyframes slide-bottom {
  0% {
            transform: translateY(-100px);
  }
  100% {
            transform: translateY(0);
  }
}

.skeleton-loader {
        background-color: #ffffff;
        /* background: linear-gradient(
          100deg,
          rgba(255, 255, 255, 0) 40%,
          rgba(255, 255, 255, .5) 50%,
          rgba(255, 255, 255, 0) 60%
        ) #262626; */
        background: linear-gradient(90deg, rgba(255,255,255,1) 90%, rgba(1,7,8,1) 150%);
        background-size: 200% 100%;
        background-position-x: 180%;
        animation:  1s loading ease-in-out infinite;
    }

    @keyframes loading {
        to {
          background-position-x: -20%;
        }
    }

</style>

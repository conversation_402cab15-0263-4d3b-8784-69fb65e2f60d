<script setup>

import { defineProps, ref, defineEmits } from 'vue';

defineProps({
  type: Array,
  label: String,
});

const emits = defineEmits(['selected']);

const selectedOption = ref(null);
const selectedType = (optiontype) => {
  selectedOption .value = optiontype;
  // Console.log(selectedOption );
  emits('selected', selectedOption.value );
};

</script>

<template>

    <div class="w-fit flex flex-wrap gap-3">
      <label v-for="(typeItem, index) in type"
       :key="index" :class="['radio-label', {'checked': selectedOption === typeItem}]"
       @click="selectedType(typeItem)"
       class=" w-auto h-[2.3rem] bg-bg-900 flex justify-center items-center rounded-[3.75rem] px-4 py-[0.56rem] cursor-pointer">
        <input type="radio" :value="typeItem" v-model="selectedOption"/>
        <span class="text-txt-default text-base leading-normal font-normal not-italic whitespace-nowrap">{{ typeItem[label] }}</span>
      </label>
    </div>

</template>

<style scoped>

input[type='radio']{
  display: none;
}

.radio-label.checked {
  background-color: #000000;
}

.radio-label.checked span {
  color: white;
}
</style>

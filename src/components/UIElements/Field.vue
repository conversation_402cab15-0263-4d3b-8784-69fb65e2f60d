<script setup>
import { defineProps } from 'vue';

defineProps(['holder']);

</script>

<template>

  <div>
    <div class="field ">
      <input type="text" placeholder=""
            class="flex w-full relative rounded-lg h-10 transition-all duration-[0.3s] ease-in-out px-3 py-0 border-[1px] border-bg-700 focus:border-bg-default"
            autocomplete="off" required />
     <label for="email"
      class="bg-white absolute cursor-text z-10 text-sm font-normal text-bg-550 transition-all duration-[0.3s] ease-in-out px-2.5 py-0 left-2.5 top-2">{{ holder }} <strong>*</strong></label>
    </div>
  </div>

</template>

<style scoped>
.field input:focus+label,
.field select:focus+label,
.field input:valid+label,
.field select:valid+label {
  @apply text-xs -top-2;
}

.field input:focus+label,
.field select:focus+label {
  @apply text-bg-50;
}

/* .field{
   /* margin:20px 10px;
   position:relative;
  display:inline-block;
}

/* label {
padding:10px;
pointer-events: none;
 position:absolute;
 left:0;
 top:0;
 transition: 0.2s;
 transition-timing-function: ease;
 transition-timing-function: cubic-bezier(0.25, 0.1, 0.25, 1);
 opacity:0.5;
 color: black;
background-color:white;
} */

/* input {
 /* display: none;
 border: 1px solid black;
} */

/* input:focus + label, input:not(:placeholder-shown) + label {
    opacity:1;
    transform: scale(0.75) translateY(-70%) translateX(-14px);
    font-size: 12px;
    margin-left: 10px;
} */

/* #5B616E */
/* ::placeholder{
 color: black;
font-size: 14px;
font-style: normal;
font-weight: 400;
line-height: normal;
}         */

</style>

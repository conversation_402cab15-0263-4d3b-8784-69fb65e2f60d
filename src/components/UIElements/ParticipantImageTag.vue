<script setup>
import { defineProps, defineEmits} from 'vue';

const props = defineProps({
  data: Object,
});

const emit = defineEmits(['delete']);

console.log(props.data);

// To remove the selected item
const handleDelete = (name) => {
  emit('delete', name);
};
</script>

<template>
    <div class="w-full h-[40px] flex justify-between">
        <div class="flex gap-[7px] justify-center items-center">
            <div class="w-[32px] h-[32px] rounded-full ">
                <img class=" object-fill rounded-full w-[100%] h-[100%]" :src="data.url"/>
            </div>
            <div class=" flex justify-center items-center">
                <p class="text-txt-100 text-sm font-medium non-italic">{{ data.name }} </p>
            </div>
        </div>
        <div class="w-[42px] h-[40px]" @click="handleDelete(data.name)">
            <svg width="42" height="40" viewBox="0 0 42 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect width="42" height="40" rx="8" fill="#F9FBFC"/>
            <g clip-path="url(#clip0_172_18814)">
            <path d="M26.3033 25.3033C26.1861 25.4205 26.0271 25.4864 25.8614 25.4864C25.6956 25.4864 25.5366 25.4205 25.4194 25.3033L21 20.8839L16.5806 25.3033C16.4634 25.4205 16.3044 25.4864 16.1386 25.4864C15.9729 25.4864 15.8139 25.4205 15.6967 25.3033C15.5795 25.1861 15.5136 25.0272 15.5136 24.8614C15.5136 24.6956 15.5795 24.5367 15.6967 24.4195L20.1161 20L15.6967 15.5806C15.5795 15.4634 15.5136 15.3044 15.5136 15.1387C15.5136 14.9729 15.5795 14.8139 15.6967 14.6967C15.8139 14.5795 15.9729 14.5137 16.1386 14.5137C16.3044 14.5137 16.4634 14.5795 16.5806 14.6967L21 19.1162L25.4194 14.6967C25.5366 14.5795 25.6956 14.5137 25.8614 14.5137C26.0271 14.5137 26.1861 14.5795 26.3033 14.6967C26.4205 14.8139 26.4864 14.9729 26.4864 15.1387C26.4864 15.3044 26.4205 15.4634 26.3033 15.5806L21.8839 20L26.3033 24.4195C26.4205 24.5367 26.4864 24.6956 26.4864 24.8614C26.4864 25.0272 26.4205 25.1861 26.3033 25.3033Z" fill="#757575"/>
            </g>
            <defs>
            <clipPath id="clip0_172_18814">
            <rect width="20" height="20" fill="white" transform="translate(11 10)"/>
            </clipPath>
            </defs>
            </svg>
        </div>
    </div>
</template>

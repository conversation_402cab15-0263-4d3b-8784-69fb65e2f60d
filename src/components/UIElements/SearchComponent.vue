<script setup>
import { ref, computed, onMounted, defineEmits } from 'vue';

const showLeadList = ref(false);
const showLeadSearch = ref(false);
const lead =ref('');
const searchInput= ref('');
const list = ref();

const leadList = () => {
  console.log('jijjiji<PERSON><PERSON>ji');
  showLeadList.value = !showLeadList.value;
  console.log(showLeadList.value);
};

defineEmits(['emitLead']);

const leadNames = ref(['Aathis', 'adarsh', 'ahalya', 'buvanesh', 'bala', 'cynthia', 'charan', 'deandra', 'dinesh', 'diya', 'Krithic', 'eshan']);

const toggleLeadNamesSearch = () => {
  showLeadSearch.value = !showLeadSearch.value;
};

const filteredNames = computed(() => {
  const input = lead.value.trim().toLowerCase();

  console.log(input);
  if (!input) {
    return leadNames.value;
  }
  return leadNames.value.filter((item) => item.toLowerCase().includes(input));

});

const searchFilteredNames = computed(() => {
  const input = searchInput.value.trim().toLowerCase();

  console.log(input);
  if (!input) {
    return leadNames.value;
  }
  return leadNames.value.filter((item) => item.toLowerCase().includes(input));

});

function outsideClickHandler (event) {
  const div = list.value;
  if (div && !div.contains(event.target)) {
    // SearchClicked.value = false;
    showLeadList.value=false;
  }
}

onMounted(() => {
  document.addEventListener('click', outsideClickHandler);
});

const emitLead = (selectedTopic) => {
  lead.value = selectedTopic;
  showLeadList.value = false; // Close the dropdown after selecting a lead
  showLeadSearch.value=false;
};

</script>

<template>
   <div class="flex relative justify-between items-center gap-[24px] w-[480px]" >

            <div  class="field  h-10 w-[414px] " @click="leadList()" >
                <input type="text" placeholder=""
                v-model="lead"
                class="block w-full rounded h-10 transition-all duration-[0.3s] ease-in-out px-3 py-0 border-[1px] border-bg-700 focus:border-bg-default"
                autocomplete="off" required
                v-on:change="leadList()"/>
                <label for="email"
                class="bg-white absolute cursor-text z-10 text-sm font-normal text-bg-550 transition-all duration-[0.3s] ease-in-out px-2.5 py-0 left-2.5 top-2" >Lead Name <strong>*</strong></label>

                <div  v-if="showLeadList"  >
                    <div class=" absolute top-[3rem] -left-[0.25rem] w-[480px] h-[243px] rounded-[16px] bg-bg-950 flex flex-col gap-4 p-[15px] border border-red-600 " style="z-index:8">
                            <div class=" flex flex-col max-h-[195px]" style="scroll-behavior: smooth;scrollbar-width: none;overflow: scroll;">
                                <div v-for="(item, index) in filteredNames"
                                :key="index"
                                @click="emitLead(item)"
                                class="h-[19px] mb-[20px] capitalize">
                                    {{ item }}
                                </div>
                            </div>

                            <div class="w-[544px] h-[19px] ">
                                    <p class="text-base leading-normal font-normal not-italic text-[#5B616E] ">Lead Not found ?  <span class="text-cyan-400">Add New Lead</span></p>
                            </div>
                    </div>
                </div>

            <div  class="relative w-[42px] h-[40px] flex justify-center items-center border !border-[#5B616E] rounded-lg" @click="toggleLeadNamesSearch()" >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.1281 15.457C8.28356 15.457 5 16.0396 5 18.3662C5 20.6916 8.26322 21.2909 12.1281 21.2909C15.9727 21.2909 19.2551 20.7143 19.2551 18.3865C19.2551 16.0587 15.993 15.457 12.1281 15.457Z" stroke="#8E95A2" stroke-width="1.79429" stroke-linecap="round" stroke-linejoin="round"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M12.1279 12.1365C14.6507 12.1365 16.6962 10.0922 16.6962 7.56827C16.6962 5.04549 14.6507 3 12.1279 3C9.60513 3 7.5596 5.04549 7.5596 7.56827C7.55007 10.0827 9.58001 12.1282 12.0944 12.1365H12.1279Z" stroke="#8E95A2" stroke-width="1.79429" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>

                <div v-if="showLeadSearch">
            <!-- h-[300px] -->
                <div class="absolute top-[3.5rem] -left-[0.25rem] w-[490px] h-auto rounded-[16px] bg-bg-950 flex flex-col gap-[0.75rem] p-[15px] border border-red-100" style="z-index:11">
                        <p class="text-base non-italic font-semibold leading-5 align-left"> Select Lead</p>
                        <div class=" w-full h-[40px] py-[10px] px-[10px] flex justify-between items-center bg-bg-1000 rounded-[16px]">
                            <input v-model="searchInput" v-on:change="showList()" type="text" placeholder="" class="w-full h-full text-txt-default text-xs font-normal ">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.3453 11.7425C9.02362 12.7107 7.38492 13.1444 5.75708 12.9567C4.12923 12.769 2.6323 11.9739 1.56576 10.7303C0.499223 9.48666 -0.0582671 7.88635 0.00482557 6.2495C0.0679182 4.61265 0.74694 3.05997 1.90604 1.90209C3.06515 0.744221 4.61885 0.0665459 6.25631 0.00464636C7.89378 -0.0572531 9.49424 0.501188 10.7375 1.56825C11.9808 2.63531 12.7752 4.13229 12.9617 5.7597C13.1483 7.38711 12.7133 9.02494 11.7438 10.3455V10.3445C11.7838 10.3745 11.8218 10.4065 11.8588 10.4425L15.71 14.2924C15.8977 14.4799 16.0032 14.7342 16.0033 14.9995C16.0034 15.2647 15.8981 15.5192 15.7105 15.7068C15.5229 15.8944 15.2685 15.9999 15.0031 16C14.7378 16.0001 14.4833 15.8948 14.2956 15.7073L10.4444 11.8575C10.4082 11.8217 10.3747 11.7833 10.3443 11.7425H10.3453ZM6.50016 12.0004C7.22265 12.0004 7.93807 11.8582 8.60557 11.5818C9.27306 11.3054 9.87957 10.9003 10.3904 10.3896C10.9013 9.87889 11.3066 9.2726 11.5831 8.60533C11.8596 7.93807 12.0019 7.2229 12.0019 6.50065C12.0019 5.77841 11.8596 5.06324 11.5831 4.39597C11.3066 3.72871 10.9013 3.12242 10.3904 2.61171C9.87957 2.10101 9.27306 1.6959 8.60557 1.41951C7.93807 1.14312 7.22265 1.00086 6.50016 1.00086C5.04101 1.00086 3.64164 1.5803 2.60987 2.61171C1.5781 3.64312 0.998456 5.04202 0.998456 6.50065C0.998456 7.95929 1.5781 9.35818 2.60987 10.3896C3.64164 11.421 5.04101 12.0004 6.50016 12.0004Z" fill="#5B616E"/>
                            </svg>
                        </div>

                        <div class=" flex flex-col max-h-[195px]" style="scroll-behavior: smooth;scrollbar-width: none;overflow: scroll;">

                            <div v-for="(item, index) in searchFilteredNames"
                            :key="index"
                            @click="emitLead(item)"
                            class="h-[19px] mb-[20px] capitalize">
                                {{ item }}
                            </div>

                        </div>
                        <div class="w-[544px] h-[19px] ">
                                <p class="text-base leading-normal font-normal not-italic text-[#5B616E] ">Lead Not found ?  <span class="text-cyan-400">Add New Lead</span></p>
                        </div>
                  </div>
                </div>

            </div>
  </div>
</div>

</template>

<style scoped>

.field input:focus+label,
.field select:focus+label,
.field input:valid+label,
.field select:valid+label {
  @apply text-xs -top-2;
}

.field input:focus+label,
.field select:focus+label {
  @apply text-bg-50;
}
</style>

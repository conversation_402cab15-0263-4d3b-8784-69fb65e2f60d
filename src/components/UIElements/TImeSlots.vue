<script setup>

import { defineProps, ref, defineEmits } from 'vue';

defineProps({
  slots: Array,
});

const emits = defineEmits(['selectTime']);

const selectedOption = ref(null);
const selectedType = (optiontype) => {
  if (optiontype.availableSessions !== 0) {
    selectedOption.value = optiontype;
    emits('selectTime', selectedOption.value);
  }
};

function meridiem (time) {
  return new Date(time).toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
  // Const [hours, minutes] = time.split(':');
  // Const suffix = parseInt(hours) >= 12 ? 'PM' : 'AM';
  // Return `${time} ${suffix}`;
}

</script>

<template>

    <div class="w-fit flex flex-wrap gap-3">
      <span v-for="(slot, index) in slots"
       :key="index" :class="['radio-label', {'checked': selectedOption === slot}, {'select-none opacity-50 hover:cursor-not-allowed':slot.availableSessions <= 0}, {'hover:cursor-pointer':slot.availableSessions > 0 || slot.availableSessions === undefined}]"
       @click="selectedType(slot)"
       class=" w-auto h-[2.3rem] bg-bg-900 flex justify-center items-center rounded-[3.75rem] px-4 py-[0.56rem]">
        <input type="radio" :value="slot" v-model="selectedOption"/>
        <span class="text-txt-default text-base leading-normal font-normal not-italic whitespace-nowrap">{{ meridiem(slot.slot) }}{{ slot.availableSessions ? ` (${slot.availableSessions})` : ``}}</span>
    </span>
    </div>

</template>

<style scoped>

input[type='radio']{
  display: none;
}

.radio-label.checked {
  background-color: #000000;
}

.radio-label.checked span {
  color: white;
}
</style>

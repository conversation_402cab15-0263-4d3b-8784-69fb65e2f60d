<script setup>
import { ref, defineEmits, defineProps } from 'vue';

const emit = defineEmits(['handleSchedule']);
defineProps({
  data: Object,
  showButtons: Boolean,
});

const showCard = ref(true);

const handleClose = () => {
  showCard.value = false;
};
</script>

<template>
    <div v-if="showCard" class="w-[350px]  h-auto rounded-2xl p-[20px] bg-bg-1000 flex flex-col justify-between items-center md:w-[600px] md:min-h-[423px] md:h-auto">
        <div class="flex flex-col justify-center items-center mt-[35px] mb-[40px]">
            <div v-html="data.svg"></div>
            <p class=" text-base font-semibold text-txt-100">{{ data.text }}</p>
        </div>
        <div v-if="showButtons"
         class="w-full h-[100px] sm:mt-4 flex flex-col justify-center items-center gap-[20px]">
         <button type="submit"
            class="inline-flex justify-center items-center gap-2 rounded bg-bg-100 w-full h-10  text-base font-medium  text-txt-1000 shadow-sm hover:bg-bg-150" @click="handleClose()">
            Okay
          </button>
          <button type="button"
            class="inline-flex justify-center items-center gap-2 rounded bg-bg-850 w-full h-10 text-base font-medium text-txt-100 shadow-sm hover:bg-bg-900" @click="emit(handleSchedule)">
           Go To Schedule
          </button>

        </div>
    </div>

</template>

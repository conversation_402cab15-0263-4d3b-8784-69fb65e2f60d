<script setup>
import { ref } from 'vue';
import RadioBtnPill from './RadioBtnPill.vue';

const slots = ref(['10:15 AM', ' 11:00', '10:25 PM', '9:00 AM', '10:20 AM', '10:55', '10:50 PM', '01:15 AM']);

// Emits the selected option
const handleSelectedSlot= (selectedOption) => {
  console.log(selectedOption.value);
};

</script>

<template>
    <div class="flex flex-wrap justify-between items-center gap-[24px] w-[480px] h-[auto] ">
        <RadioBtnPill :type="slots" @selected="handleSelectedSlot"/>
    </div>

</template>

<style>

</style>

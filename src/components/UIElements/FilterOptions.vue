<script setup>
import { ref, watch } from 'vue';
import Multiselect from 'vue-multiselect';

const props = defineProps({
  label: {
    type: String,
    required: true,
  },
  options: {
    type: Array,
    required: true,
  },
  initialValue: {
    type: [Object, String],
    default: null,
  },
  customLabel: {
    type: Function,
    default: (option) => option.name || option.value || option,
  },
  placeholder: {
    type: String,
    default: '',
  },
  trackBy: {
    type: String,
    default: 'id',
  },
});

const emit = defineEmits(['selection']);

const selectedValue = ref(props.initialValue);

const handleSelection = (value) => {
  emit('selection', { type: props.label.toLowerCase(), value });
};

watch(() => props.initialValue, (newValue) => {
  selectedValue.value = newValue ? newValue : props.initialValue ;
});
</script>

<template>
    <div class="filter-option">
        <div class="flex label relative">
            <div
                class="flex whitespace-nowrap w-full px-4 py-1 bg-stone-50 dark:bg-bg-default rounded-tl-lg rounded-bl-lg border-solid border-t border-l border-b border-gray-300 justify-center items-center gap-2.5 ">
                {{ label }}
            </div>
            <Multiselect :class="['filter-select', `filterBy${label}`]" v-model="selectedValue" :options="options"
                :searchable="false" :show-labels="false" :custom-label="customLabel" :placeholder="placeholder"
                :track-by="trackBy" :max-height="250" @select="handleSelection" />
        </div>
    </div>
</template>

<style scoped>
.filter-option {
    @apply mb-4;
}

.filter-select {
    @apply -ml-2;
}

/* Add any additional styles here */
</style>

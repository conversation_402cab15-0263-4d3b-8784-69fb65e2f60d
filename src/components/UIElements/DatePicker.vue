<script setup>
import { ref, onMounted} from 'vue';
import Datepicker from '@vuepic/vue-datepicker';
import '@vuepic/vue-datepicker/dist/main.css';

/* Props, State and Emits */
const emit = defineEmits(['selectDate']);
const { initalValue, label, required, css_id, allowPastDates, showLabelHigh } = defineProps({
  initalValue: String,
  label: String,
  required: Boolean,
  css_id: String,
  allowPastDates: Boolean,
  showLabelHigh: Boolean,
});
const showCalenderIcon=ref(true);
const date = ref(null); // Date val
const dp = ref(); // Reference

/* Methods */
const format = (date) => {
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}/${month}/${year}`; // Display format
};

const handleChange = (e) => {
  e.target.value = e.target.value.replace(/[^0-9/]/g, '');
  // Avoid alp & special characters
  if (!e.target.value){
    dp.value.clearValue(); // Clear
    date.value = null; // Reset to null
  }
};

const handleFocus = ()  => {
  if (css_id && showLabelHigh){
    document.querySelector(`[for="${css_id}"]`).classList.add('labelHigh');
    document.querySelector(`[for="${css_id}"]`).classList.remove('labellow');
  }
};

const handleBlur = ()  => {
  if (css_id && showLabelHigh){
    if (date.value === null) {
      document.querySelector(`[for="${css_id}"]`).classList.remove('labelHigh');
      document.querySelector(`[for="${css_id}"]`).classList.add('labellow');
    }
  }
};

// Iniial Values
if (initalValue){
  date.value = new Date(initalValue);
}

onMounted(() => {
  if (initalValue && css_id && showLabelHigh){
    document.querySelector(`[for="${css_id}"]`).classList.remove('labellow');
    document.querySelector(`[for="${css_id}"]`).classList.add('labelHigh');
  }
});

</script>
<template>
   <div class="flex justify-start items-center w-auto gap-6">
         <div v-if="showCalenderIcon" class="datePickerField w-full flex justify-center items-center rounded-lg">
                 <Datepicker :class="showLabelHigh ?'':'!bg-gray-50 !rounded-lg'" :uid="css_id" @focus="handleFocus" @blur="handleBlur" @input="(e) => handleChange(e)" v-model="date" ref="dp"  :min-date=" allowPastDates ? false : new Date()" :enable-time-picker="false" :teleport="true" :input-attr="{ readonly: true }"  @update:model-value="(val) => val ? emit('selectDate',val.toISOString()) : emit('selectDate',val)" :format="format" position="left">
                    <template #action-buttons>
                        <div class="custom-select bg-repeat-x" @click="() => dp.selectDate()">Confirm</div>
                    </template>
                </Datepicker>
                <label :for="css_id" class="absolute cursor-text font-normal text-bg-550 transition-all duration-[0.3s] ease-in-out px-2.5 py-0 left-3.5 datelabel" :class="[showLabelHigh?(date ? 'labelHigh' : 'labellow'):(date ?'customlabelHigh':'customlabellow')]"> {{ label }} <strong v-if="required">{{ showLabelHigh ? '*':'' }}</strong></label>
          </div>
   </div>
</template>
<style>

.datePickerField{
  border: 1px solid rgb(179, 179, 179);
  height: 100%;
}

.dp__menu {
  font-size: 14px;
  height: fit-content;
  top: -10px;
  border-radius: 16px;
  background: var(--White, #FFF);
  box-shadow: 0px 0px 22.9px 0px rgba(0, 0, 0, 0.11);
}
/*
.dp--menu-wrapper{
    top:30px !important;
} */

.dp__theme_light {
  --dp-primary-color: black;
  --dp-range-between-dates-background-color: black;
  --dp-range-between-dates-text-color: #eee;
}

.dp__selection_preview {
  display: none;
}

.dp__input {
  border: none;
  background: transparent;
  color: black;
  font-weight: 400;
  font-size: 16px;
  position: relative;
  z-index: 1;
}

.dp__action_row {
  width: 100%;
}

.dp__arrow_top {
  display: none;
}

.dp__action_button {
  padding: 12px;
  margin-left: 10px;
}

.dp__calendar_header_item {
  color: var(--dp-secondary-color);
  font-size: 10px;
  text-transform: Uppercase;
}

.dp__range_between,
.dp__range_end,
.dp__range_start {
  border-radius: 100%;
}

.dp__range_between {
  background-color: var(--dp-secondary-color);
  color: black;
}

.dp__date_hover_end:hover {
  border-radius: 100%;
}

.dp__date_hover_start:hover {
  border-radius: 100%;
}
.dp__month_year_select {
  font-weight: 600;
    font-size: 16px;
}
.dp__calender_item {
  width: 36px;
  height: 36px;
}
.dp__clear_icon{
    display:none;
}
.custom-select {
  cursor: pointer;
  color: white;

  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  /* width: 200px; */
  height: 40px;
  padding: 10px 127px;
  border-radius: 8px;
  box-sizing: border-box;
  background: black;
}

.labellow{
    @apply top-3 left-10 bg-white ;
    z-index: 0;
}

.labelHigh{
    @apply text-xs -top-2 z-10 left-2.5 bg-white;
}
.customlabelHigh{
    @apply hidden;
}
.customlabellow{
    @apply top-[2.5rem] left-8 bg-transparent;
    z-index: 0;
}

</style>

<script setup>
import { computed, ref } from 'vue';
import CalendarSvg from '../../assets/svgs/CalendaerSvg.vue';
import TimeSvg from '../../assets/svgs/TimeSvg.vue';
import { format, isToday, isTomorrow } from 'date-fns';

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  fields: {
    type: Array,
    required: true,
  },
  actions: {
    type: Array,
    default: () => [],
  },
  highlight: {
    type: Object,
    default: () => ({}),
  },
  organizationStore: {
    type: Object,
    required: true,
  },
  isMobile: {
    type: Boolean,
    default: false,
  },
  sessionType: {
    type: String,
    default: 'upcoming',
  },
  sceneType: {
    type: String,
    default: 'upcoming',
  },

});

const emit = defineEmits(['action', 'field-click', 'participantslist']);

const handleParticipantClick = () => {
  if (props.isMobile) {
    emit('field-click', {
      field: props.fields.find((f) => f.key === 'leads'),
      value: props.item.leads,
      item: props.item,
    });
  }
};

const formatDuration = (minutes) => {
  if (!minutes) {
    return 'N/A';
  }
  const hrs = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  const secs = Math.round((minutes % 1) * 60);

  let result = '';
  if (hrs > 0) {
    result += `${hrs}h `;
  }
  if (mins > 0 || hrs > 0) {
    result += `${mins}m `;
  }
  result += `${secs}s`;

  return result.trim();
};

const menuActions = computed(() => {
  return props.actions.filter((action) => action.key === 'cancel' || action.key === 'end');
});

const showMenuButton = computed(() => {
  return props.sessionType === 'upcoming' && props.item.status !== 'cancelled' && props.item.status !== 'ended';
});

const highlightClass = computed(() => {
  if (props.highlight.tag && props.item[props.highlight.tag]) {
    return 'highlight-tag';
  }
  return '';
});

const showMenu = ref(false);

const formatValue = (field, value) => {
  if (!field) {
    return 'N/A';
  }
  if (field.format) {
    return field.format(value, props.item);
  }
  if (field.key === 'project_id') {
    return props.organizationStore.projects && props.organizationStore.projects[value]
      ? props.organizationStore.projects[value].name
      : value || 'N/A';
  }
  if (field.key === 'user_id') {
    return props.organizationStore.users && props.organizationStore.users[value]
      ? props.organizationStore.users[value].name || props.organizationStore.users[value].email
      : value || 'N/A';
  }
  if (field.key === 'duration_minutes') {
    return formatDuration(value);
  }
  if (Array.isArray(value)) {
    return value.join(', ');
  }
  return value || 'N/A';
};

const getFieldLabel = (key) => {
  const field = props.fields.find((f) => f.key === key);
  return field ? field.label : key;
};

const getFieldValue = (key) => {
  const field = props.fields.find((f) => f.key === key);
  return formatValue(field, props.item[key]);
};

const actionInProgress = ref(false);

const handleAction = (action, event) => {
  if (event) {
    event.stopPropagation();
  }
  if (action && action.action && !actionInProgress.value) {
    actionInProgress.value = true;
    try {
      action.action(props.item);
    } finally {
      // Reset the flag after a short delay
      setTimeout(() => {
        actionInProgress.value = false;
      }, 1000); // Adjust this delay as needed
    }
  }
  emit('action', { action, item: props.item });
};

const formatDateDisplay = (timestamp) => {
  if (!timestamp) {
    return 'N/A';
  }
  const date = new Date(timestamp);
  if (isToday(date)) {
    return 'Today';
  } else if (isTomorrow(date)) {
    return 'Tomorrow';
  }
  return format(date, 'dd MMM yyyy');

};

const formatTimeDisplay = (timestamp) => {
  if (!timestamp) {
    return 'N/A';
  }
  const date = new Date(timestamp);
  return format(date, 'hh:mm a');
};

const toggleMenu = () => {
  showMenu.value = !showMenu.value;
};

const closeMenu = () => {
  showMenu.value = false;
};

const mobileActions = computed(() => {
  return props.actions.filter((action) => {
    if (action.key === 'copy') {
      return props.item.status !== 'cancelled';
    }
    return action.key !== 'menu' && action.key !== 'cancel' && action.key !== 'end';
  });
});

const getParticipants = (data) => {
  let result = '';

  if (data.length > 1) {
    const firstName = data[0].name;
    const remainingCount = data.length - 1;
    result = `${firstName}+${remainingCount}`;
  } else if (data.length === 1) {
    const firstName = data[0].name;
    result = `${firstName}`;
  }

  return result;
};

const participantslist = (val) => {
  emit('participantslist', val);
};

</script>

<template>
    <div class="bg-white dark:bg-bg-150 rounded-2xl shadow-[0px 4px 16px 0px #0000001A] border border-gray-300 dark:border-bg-200 shadow-sm max-sm:p-3 relative"
        :class="[highlightClass, { 'mb-4': isMobile }]">
        <template v-if="isMobile">
            <!-- Mobile view content -->
            <div class="flex justify-between items-start mb-3" :class="sceneType === 'analytics' ? 'gap-[15px]' : ''">
                <div class="min-w-0 w-[35%]" v-if="sceneType === 'schedule'" @click="handleParticipantClick">
                    <h3 class="font-roboto text-xs font-normal text-[#5B616E] mb-2 capitalize">
                        {{ getFieldLabel('leads') }}
                    </h3>
                    <p
                        class="font-roboto text-black text-sm font-normal leading-[16.41px] capitalize cursor-pointer truncate">
                        {{ getFieldValue('leads') || '-' }}
                    </p>
                </div>
                <div v-if="sceneType === 'analytics'">
                    <h3 class="text-xs font-normal text-gray-500 capitalize">{{ getFieldLabel('participants') }}</h3>
                    <p v-if="item.participants.length > 1" @click="participantslist(item.participants)"
                        class="text-base font-medium underline">{{ getParticipants(item.participants) }}</p>
                    <p v-else-if="item.participants.length === 1" class="text-base font-medium">{{
                        getParticipants(item.participants) }}</p>
                    <p v-else class="text-[13px] font-normal ">N/A</p>

                </div>
                <div class="flex items-center gap-1 ml-auto flex-shrink-0">
                    <div class="flex items-center">
                        <TimeSvg class="w-4 h-4 flex-shrink-0 text-[#5B616E]" />
                        <span
                            class="font-roboto text-black text-sm font-light leading-[16.41px] ml-1 whitespace-nowrap">
                            {{ formatTimeDisplay(item.schedule_time) }}
                        </span>
                    </div>
                    <div class="flex items-center ml-2">
                        <CalendarSvg class="w-4 h-4 flex-shrink-0 text-[#5B616E]" />
                        <span
                            class="font-roboto text-black text-sm font-light leading-[16.41px] ml-1 whitespace-nowrap">
                            {{ formatDateDisplay(item.schedule_time) }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="flex justify-between mb-2">
                <div>
                    <h4 class="font-roboto text-xs font-normal text-[#5B616E] mb-2 capitalize">
                        {{ getFieldLabel('project_id') }}
                    </h4>
                    <p class="font-roboto text-black text-sm font-light leading-[16.41px]">
                        {{ getFieldValue('project_id') }}
                    </p>
                </div>
                <svg width="1" height="22" viewBox="0 0 1 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <line x1="0.5" y1="0.5" x2="0.5" y2="21.5" stroke="#D8DBDF" />
                </svg>
                <div>
                    <h4 class="font-roboto text-xs font-normal text-[#5B616E] mb-2 capitalize">
                        {{ getFieldLabel('status') }}
                    </h4>
                    <p class="font-roboto text-black text-sm font-light leading-[16.41px] capitalize">
                        {{ getFieldValue('status') }}
                    </p>
                </div>
                <svg v-if="item.tag" width="1" height="22" viewBox="0 0 1 22" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <line x1="0.5" y1="0.5" x2="0.5" y2="21.5" stroke="#D8DBDF" />
                </svg>
                <div v-if="item.tag" class="min-w-0">
                    <h4 class="font-roboto text-xs font-light text-[#5B616E] mb-1 capitalize">
                        {{ getFieldLabel('tag') }}
                    </h4>
                    <p
                        class="text-txt-50 border-[1px] px-2 py-1 ml-[-4px] capitalize text-xs  max-w-[60px] bg-green-100 border-lime-600  rounded-[60px] inline-block">
                        {{ item.tag || '-' }}
                    </p>
                </div>

            </div>
            <div class="flex justify-between items-center mt-3 gap-2">
                <template v-for="action in mobileActions" :key="action.key">
                    <button v-if="action.key === 'join' || action.key === 'rejoin'"
                        @click="(event) => handleAction(action, event)" :class="['flex-grow py-2 px-3 text-sm font-medium rounded-lg flex items-center justify-center',
                            action.key === 'join' || action.key === 'rejoin' ?
                                'bg-gray-900 text-white' :
                                'border border-bg-900 bg-bg-1000 dark:bg-bg-50 dark:text-txt-950']"
                        :disabled="action.disabled">
                        <span v-if="action.icon" v-html="action.icon"></span>
                        <span v-else>{{ action.label }}</span>
                    </button>
                    <button v-else @click="(event) => handleAction(action, event)" :class="['p-2  rounded-lg  border border-bg-900 bg-bg-1000 dark:bg-bg-50 dark:text-txt-950',
                        action.class]" :disabled="action.disabled">
                        <span v-if="action.icon" v-html="action.icon"></span>
                        <span v-else>{{ action.label }}</span>
                    </button>
                </template>
                <div v-if="showMenuButton" class="relative">
                    <button @click="toggleMenu" class="p-2 border rounded-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z">
                            </path>
                        </svg>
                    </button>
                    <div v-if="showMenu"
                        class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-10">
                        <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                            <button v-for="action in menuActions" :key="action.key"
                                @click="handleAction(action); closeMenu()"
                                class="block  w-full text-left px-4 py-2 text-base text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                role="menuitem">
                                <div class="flex items-center gap-2">
                                    <svg v-if="action.key === 'cancel'" class="w-3 h-3" viewBox="0 0 14 14" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path d="M13 1L1 13M1 1L13 13" stroke="#5B616E" stroke-width="1.5"
                                            stroke-linecap="round" stroke-linejoin="round" />
                                    </svg>
                                    <svg v-if="action.key === 'end'" class="w-4 h-4" viewBox="0 0 16 18" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <path
                                            d="M6.33333 1L5.74436 1.20786C3.45215 2.01689 2.30603 2.4214 1.65301 3.34437C1 4.26734 1 5.48276 1 7.9136V10.0864C1 12.5172 1 13.7326 1.65301 14.6556C2.30603 15.5786 3.45215 15.9831 5.74436 16.7922L6.33333 17"
                                            stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" />
                                        <path
                                            d="M15.2219 9.00005H6.33301M15.2219 9.00005C15.2219 8.37765 13.4492 7.21475 12.9997 6.77783M15.2219 9.00005C15.2219 9.62245 13.4492 10.7854 12.9997 11.2223"
                                            stroke="#5B616E" stroke-width="1.5" stroke-linecap="round"
                                            stroke-linejoin="round" />
                                    </svg>

                                    <span>{{ action.label }}</span>
                                </div>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="sceneType === 'analytics' && item.duration_minutes">
                <h4 class="text-xs font-normal  text-gray-500 capitalize">{{ getFieldLabel('Duration') }}</h4>
                <p class="text-sm text-black">{{ formatDuration(item.duration_minutes) }}</p>
            </div>
        </template>

        <template v-else>
            <!-- Desktop view content -->
            <div class="flex flex-col h-full justify-between gap-3">
                <div class="p-3">
                    <div class="mb-4 flex items-center justify-between">
                        <div>
                            <p class="font-roboto text-xs font-normal text-[#5B616E] mb-2 capitalize">
                                {{ getFieldLabel('project_id') }}
                            </p>
                            <h3 class="font-roboto text-[#0F0F0F] text-sm font-normal truncate leading-[16.41px]">
                                {{ getFieldValue('project_id') }}
                            </h3>
                        </div>
                        <div v-if="item.tag" class="flex items-center gap-2">
                            <p
                                class="font-roboto text-[#0F0F0F] text-base font-normal capitalize leading-[16.41px] bg-[#E5FFDE] px-2 py-1 rounded-2xl inline-block whitespace-nowrap">
                                {{ item.tag || '-' }}
                            </p>
                            <div v-show="showMenuButton" class="relative w-[36px]">
                                <button @click="toggleMenu" v-show="showMenuButton" class="p-2 rounded-lg  w-[36px]">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
                                    </svg>
                                </button>
                                <!-- Updated Menu items with consistent SVGs -->
                                <div v-if="showMenu"
                                    class="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                                    <div class="py-1" role="menu">
                                        <button v-for="action in menuActions" :key="action.key"
                                            @click="handleAction(action); closeMenu()"
                                            class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                                            role="menuitem">
                                            <div class="flex items-center gap-2">
                                                <svg v-if="action.key === 'cancel'" class="w-3 h-3" viewBox="0 0 14 14"
                                                    fill="none">
                                                    <path d="M13 1L1 13M1 1L13 13" stroke="#5B616E" stroke-width="1.5"
                                                        stroke-linecap="round" stroke-linejoin="round" />
                                                </svg>
                                                <svg v-if="action.key === 'end'" class="w-4 h-4" viewBox="0 0 16 18"
                                                    fill="none">
                                                    <path
                                                        d="M6.33333 1L5.74436 1.20786C3.45215 2.01689 2.30603 2.4214 1.65301 3.34437C1 4.26734 1 5.48276 1 7.9136V10.0864C1 12.5172 1 13.7326 1.65301 14.6556C2.30603 15.5786 3.45215 15.9831 5.74436 16.7922L6.33333 17"
                                                        stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" />
                                                    <path
                                                        d="M15.2219 9.00005H6.33301M15.2219 9.00005C15.2219 8.37765 13.4492 7.21475 12.9997 6.77783M15.2219 9.00005C15.2219 9.62245 13.4492 10.7854 12.9997 11.2223"
                                                        stroke="#5B616E" stroke-width="1.5" stroke-linecap="round"
                                                        stroke-linejoin="round" />
                                                </svg>
                                                <span>{{ action.label }}</span>
                                            </div>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between mb-3">
                        <div class="w-1/3">
                            <h4 class="font-roboto text-xs font-normal  text-[#5B616E] mb-2 capitalize">
                                {{ getFieldLabel('status') }}
                            </h4>
                            <p
                                class="font-roboto text-[#0F0F0F] text-sm font-normal leading-[16.41px] truncate pr-2 capitalize">
                                {{ getFieldValue('status') }}
                            </p>
                        </div>
                        <svg width="1" height="22" viewBox="0 0 1 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="0.5" y1="0.5" x2="0.5" y2="21.5" stroke="#D8DBDF" />
                        </svg>
                        <div class="w-1/3">
                            <h4 class="text-xs font-normal  text-[#5B616E] mb-2 pl-2 capitalize font-roboto">
                                {{ getFieldLabel('leads') }}
                            </h4>
                            <p @click="emit('field-click', {
                                field: fields.find(f => f.key === 'leads'),
                                value: item.leads,
                                item: item
                            })"
                                class="font-roboto text-[#0F0F0F] text-sm font-normal leading-[16.41px] truncate pl-2 pr-2 cursor-pointer">
                                {{ getFieldValue('leads') }}
                            </p>
                        </div>
                        <svg width="1" height="22" viewBox="0 0 1 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="0.5" y1="0.5" x2="0.5" y2="21.5" stroke="#D8DBDF" />
                        </svg>
                        <div class="w-1/3">
                            <h4 class="text-xs font-normal  text-[#5B616E] mb-2 pl-2 font-roboto">
                                {{ getFieldLabel('user_id') }}
                            </h4>
                            <p class="font-roboto text-[#0F0F0F] text-sm font-normal leading-[16.41px] truncate pl-2">
                                {{ getFieldValue('user_id') }}
                            </p>
                        </div>
                    </div>
                </div>
                <!-- Bottom section - Always at bottom -->
                <div
                    class="px-3 py-3 bg-[#F9FAFB] dark:bg-bg-100  items-center justify-between  rounded-b-2xl min-h-[3rem]">
                    <div class="flex flex-wrap items-center justify-between ">
                        <!-- Date/Time info -->
                        <div class="flex items-center gap-2 shrink-0 ">
                            <div class="flex items-center gap-1">
                                <TimeSvg class="w-4 h-4 text-[#5B616E]" />
                                <span class="font-roboto text-[#0F0F0F] dark:text-white text-sm font-medium">
                                    {{ formatTimeDisplay(item.schedule_time) }}
                                </span>
                            </div>
                            <div class="flex items-center gap-1">
                                <CalendarSvg class="w-4 h-4 text-[#5B616E]" />
                                <span class="font-roboto text-[#0F0F0F] dark:text-white text-sm font-medium">
                                    {{ formatDateDisplay(item.schedule_time) }}
                                </span>
                            </div>
                        </div>

                        <!-- Action buttons with fixed dimensions -->
                        <div class="flex items-center gap-2 shrink-0 h-10  align-middle w-max">
                            <template v-for="action in mobileActions" :key="action.key">
                                <button v-show="action.key === 'join' || action.key === 'rejoin'"
                                    @click="(event) => handleAction(action, event)"
                                    class="border border-bg-900 w-full sm:w-28 py-2 text-sm font-medium bg-bg-1000 rounded-lg dark:bg-bg-50 dark:text-txt-950 flex justify-center items-center"
                                    :disabled="action.disabled">
                                    <span v-if="action.icon" v-html="action.icon"></span>
                                    <span v-else>{{ action.label }}</span>
                                </button>
                                <button v-show="action.key === 'copy'" @click="(event) => handleAction(action, event)"
                                    class="border border-bg-900 bg-bg-1000 rounded-lg dark:bg-bg-50 p-2 w-full sm:w-8 flex items-center justify-center"
                                    :disabled="action.disabled">
                                    <span v-if="action.icon" v-html="action.icon"></span>
                                    <span v-else>{{ action.label }}</span>
                                </button>
                            </template>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>

<style scoped>
.text-green-600 {
    color: #059669;
}

@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@500&display=swap');

.font-roboto {
    font-family: 'Roboto', sans-serif;
}

/* Add specific card styles */
:deep(.card-menu-button) {
    @apply p-2 border rounded-lg hover:bg-gray-50 transition-colors duration-200;
}

:deep(.card-menu-items) {
    @apply absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50;
}

:deep(.menu-item) {
    @apply block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900;
}

/* Ensure the bottom section stays at bottom */
.flex-col {
    min-height: 100%;
}

.mt-auto {
    margin-top: auto;
}
</style>

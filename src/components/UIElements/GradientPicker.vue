<script setup>
import { ref, watch, computed, defineEmits, defineProps } from 'vue';
const props = defineProps(['initialGradient']);
const emit = defineEmits(['toggleModal', 'gradientSelect']);
const colors = ref([
  { value: '#58bad9', stop: 0 },
  { value: '#e4f1e6', stop: 100 },
]);
const gradientType = ref('linear');
const transparent = ref(1);
const customGradient = ref('');
const updateGradient = () => {
  let gradientString = '';
  if (gradientType.value === 'linear') {
    gradientString = `linear-gradient(to right, ${colors.value.map((color) => color.value + ' ' + color.stop + '%').join(', ')}, rgba(255, 255, 255, ${1 - transparent.value}))`;
  } else if (gradientType.value === 'radial'){
    gradientString = `radial-gradient(circle, ${colors.value.map((color) => color.value + ' ' + color.stop + '%').join(', ')}, rgba(255, 255, 255, ${1 - transparent.value}))`;
  } else {
    gradientString = customGradient.value;
    const rgbRegex = /rgba?\((\s*\d+%?,?){3}\s*(\d*\.?\d*)\s*\)/gi;
    const matches = [...gradientString.matchAll(rgbRegex)];
    if (matches.length > 0) {
      const lastMatch = matches[matches.length - 1];
      const alpha = parseFloat(lastMatch[2]);
      transparent.value = alpha; // Set transparency based on the alpha value
    }
  }
  return gradientString;
};

const updateColor = (index, value) => {
  colors.value[index].value = value;
  updateGradient();
};

const addColor = () => {
  colors.value.push({ value: '#000000', stop: 50 });
  updateGradient();
};

const removeColor = (index) => {
  colors.value.splice(index, 1);
  updateGradient();
};

const emitGradient = () => {
  const linearGradient = updateGradient();
  console.log(linearGradient);
  emit('gradientSelect', linearGradient);
};

const gradientStyle = computed(() => updateGradient());
// Parse initial gradient to extract colors and stops
const parseGradient = (gradientCss) => {
  let gradientString = gradientCss;
  // If initialGradient is NULL set default linear gradient
  if (props.initialGradient===null){
    gradientString = 'linear-gradient(to right, rgba(255, 255, 255, 1), #e4d5b4 29%, #e0ba67 100%)';
  }
  // Example parsing logic, adjust as needed
  const colorStopRegex = /#[0-9a-fA-F]{6}\s\d{1,3}%/g;
  const matches = gradientString.match(colorStopRegex);
  if (matches) {
    colors.value = matches.map((match) => {
      const [value, stop] = match.split(/\s/);
      return { value, stop: parseInt(stop) };
    });
  }
  // Check if the initial gradient is linear or radial
  if (gradientString.includes('linear-gradient')) {
    gradientType.value = 'linear';
  } else if (gradientString.includes('radial-gradient')) {
    gradientType.value = 'radial';
  }
  // Extract alpha value of the first color stop
  if (colors.value.length > 0) {
    let gradientString = props.initialGradient;
    // If initialGradient is NULL set default linear gradient
    if (props.initialGradient===null){
      gradientString = 'linear-gradient(to right, rgba(255, 255, 255, 1), #e4d5b4 29%, #e0ba67 100%)';
    }
    const rgbRegex = /rgba?\((\s*\d+%?,?){3}\s*(\d*\.?\d*)\s*\)/gi;
    const matches = [...gradientString.matchAll(rgbRegex)];
    if (matches.length > 0) {
      const lastMatch = matches[matches.length - 1];
      const alpha = parseFloat(lastMatch[2]);
      transparent.value = 1 - alpha; // Set transparency based on the alpha value
    }
  }
  updateGradient(); // Call updateGradient after setting transparency value
};

parseGradient(props.initialGradient); // Call the parseGradient function when the component is initialized
watch([colors, gradientType, transparent], () => {
  updateGradient(); // Call updateGradient whenever there's a change in relevant properties
});
</script>
<template>
  <div>
    <!-- Modal -->
    <div class="fixed inset-0 overflow-y-auto flex justify-center items-center bg-gray-800 bg-opacity-75 z-50">
      <div class="bg-white rounded p-4 w-full md:max-w-lg mx-auto">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">Gradient Editor</h2>
          <button @click="emit('toggleModal')" class="text-gray-500">&times;</button>
        </div>
        <div class="relative mt-4 mb-3 h-32">
          <div class="bg-transparent rounded-xl"></div>
          <div class="gradient-box absolute top-0 left-0 w-full h-full rounded-xl" :style="{ background: gradientStyle, opacity: transparent }"></div>

        </div>
        <div>
          <select v-model="gradientType" @change="updateGradient" class="p-2 border rounded mb-2">
            <option value="linear">Linear Gradient</option>
            <option value="radial">Radial Gradient</option>
            <option value="custom">Custom Gradient</option>
          </select>
          <div v-if="gradientType !== 'custom'">
          <div  v-for="(color, index) in colors" :key="index" class="flex items-center mb-2">
            <div class="color-stopper mr-2" :style="{ backgroundColor: color.value }"></div>
            <input type="range" min="0" max="100" v-model="color.stop" @input="updateGradient" class="range-slider mr-2" />
            <input type="color" :value="color.value" @input="updateColor(index, $event.target.value)" class="mr-2" />
            <button @click="removeColor(index)" v-if="colors.length > 1" class="px-2 py-1 bg-red-500 text-white rounded">Remove</button>
          </div>

          <button @click="addColor" v-if="colors.length < 3" class="px-3 py-2 bg-blue-500 text-white rounded">Add Color</button>
          <div class="mt-2">
            <label class="block">Transparent:</label>
            <input type="range" min="0" max="1" step="0.01" v-model="transparent" @input="updateGradient" class="range-slider" />

          </div>
        </div>

        <div class="mt-4" v-else>
          <label class="block">Custom Gradient:</label>
          <input type="text" @input="updateGradient" v-model="customGradient" class="p-2 border rounded mb-2" placeholder="Enter custom gradient CSS">
        </div>
        <div class="mt-4">
          <button @click="emitGradient" class="px-4 py-2 bg-green-500 text-white rounded">OK</button>
        </div>
      </div>
    </div>
  </div>
  </div>
</template>

<style scoped>
.gradient-preview {
  width: 200px;
  height: 200px;
}

.transparent-box {
  width: 100%;
  height: 20px;
}

.color-stop {
  width: 100%;
}

.gradient-box {
  width: 200px;
  height: 50px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  height: 100%;
  width: 100%;
}
/* Add this to your CSS */
.transparency-adjuster {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.transparency-bar {
  flex: 1;
  position: relative;
}

.transparency-bar input[type="range"] {
  -webkit-appearance: none;
  width: 100%;
  background: transparent;
}

.transparency-bar input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 15px;
  height: 15px;
  background: white;
  border-radius: 50%;
  position: relative;
  z-index: 1;
}

.transparency-value {
  width: 30px;
  margin-left: 10px;
}

.blur-box-container {
  display: flex;
}

.blur-box {
  width: 20px;
  height: 20px;
  background: linear-gradient(45deg, #ccc 25%, transparent 25%),linear-gradient(-45deg, #ccc 25%, transparent 25%),linear-gradient(45deg, transparent 75%, #ccc 75%),linear-gradient(-45deg, transparent 75%, #ccc 75%); /* Adjust the color as needed */
  margin: 0 5px;
}
.bg-transparent {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  z-index: 1;
}
</style>

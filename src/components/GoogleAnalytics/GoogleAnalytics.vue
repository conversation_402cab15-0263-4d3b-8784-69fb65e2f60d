<script setup>
import { computed, watch, ref, onMounted, nextTick } from 'vue';
import Chart from 'chart.js/auto';
import EventTable from './EventTable.vue';
import { getEventNames } from '../../config/eventConfig';

const props = defineProps({
  data: {
    type: Array,
    required: true,
  },
  eventData: {
    type: Object,
    required: true,
  },
  isLoading: {
    type: Boolean,
    required: true,
  },
  error: {
    type: String,
    default: null,
  },
  startDate: {
    type: String,
    required: true,
  },
  endDate: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['update:startDate', 'update:endDate', 'apply']);

const aggregatedData = computed(() => {
  const aggregated = props.data.reduce((acc, item) => {
    if (!acc[item.country]) {
      acc[item.country] = {
        country: item.country,
        region: item.region,
        city: item.city,
        browser: item.browser,
        operatingSystem: item.operatingSystem,
        deviceCategory: item.deviceCategory,
        sessionSource: item.sessionSource,
        eventName: item.eventName,
        activeUsers: 0,
        sessions: 0,
        totalUsers: 0,
        newUsers: 0,
        engagementRate: 0,
        userEngagementDuration: 0,
        screenPageViews: 0,
        keyEvents: 0,
        newUserPercentage: 0,
        pageviewsPerUser: 0,
      };
    }
    acc[item.country].activeUsers += parseInt(item.activeUsers);
    acc[item.country].sessions += parseInt(item.sessions);
    acc[item.country].totalUsers += parseInt(item.totalUsers);
    acc[item.country].newUsers += parseInt(item.newUsers);
    acc[item.country].engagementRate += parseFloat(item.engagementRate);
    acc[item.country].userEngagementDuration += parseInt(item.userEngagementDuration);
    acc[item.country].screenPageViews += parseInt(item.screenPageViews);
    acc[item.country].keyEvents += parseInt(item.keyEvents);
    acc[item.country].newUserPercentage += parseFloat(item.newUserPercentage);
    acc[item.country].pageviewsPerUser += parseFloat(item.pageviewsPerUser);
    return acc;
  }, {});
  return Object.values(aggregated);
});

const totalActiveUsers = computed(() =>
  aggregatedData.value.reduce((sum, item) => sum + item.activeUsers, 0),
);

const totalSessions = computed(() =>
  aggregatedData.value.reduce((sum, item) => sum + item.sessions, 0),
);

const totalUsers = computed(() =>
  aggregatedData.value.reduce((sum, item) => sum + item.totalUsers, 0),
);

const totalNewUsers = computed(() =>
  aggregatedData.value.reduce((sum, item) => sum + item.newUsers, 0),
);

const totalPageViews = computed(() =>
  aggregatedData.value.reduce((sum, item) => sum + item.screenPageViews, 0),
);

const userEngagementDuration = computed(() =>
  aggregatedData.value.reduce((sum, item) => sum + item.userEngagementDuration, 0),
);

const browserData = computed(() => {
  const browsers = {};
  aggregatedData.value.forEach((item) => {
    if (!browsers[item.browser]) {
      browsers[item.browser] = 0;
    }
    browsers[item.browser] += item.sessions;
  });
  return Object.entries(browsers).sort((a, b) => b[1] - a[1]).slice(0, 5);
});

const osData = computed(() => {
  const os = {};
  aggregatedData.value.forEach((item) => {
    if (!os[item.operatingSystem]) {
      os[item.operatingSystem] = 0;
    }
    os[item.operatingSystem] += item.sessions;
  });
  return Object.entries(os).sort((a, b) => b[1] - a[1]).slice(0, 5);
});

const deviceData = computed(() => {
  const devices = {};
  aggregatedData.value.forEach((item) => {
    if (!devices[item.deviceCategory]) {
      devices[item.deviceCategory] = 0;
    }
    devices[item.deviceCategory] += item.sessions;
  });
  return Object.entries(devices);
});

const chartRefs = ref({
  browserChart: null,
  osChart: null,
  deviceChart: null,
});

const drawCharts = async () => {
  if (!aggregatedData.value.length) {
    return;
  }

  await nextTick();

  // GeoChart
  if (typeof google !== 'undefined' && google.charts) {
    google.charts.load('current', { 'packages': ['geochart'] });
    google.charts.setOnLoadCallback(() => {
      const data = new google.visualization.DataTable();
      data.addColumn('string', 'Country');
      data.addColumn('number', 'Active Users');

      aggregatedData.value.forEach((item) => {
        data.addRow([item.country, item.activeUsers]);
      });

      const options = {
        colorAxis: { colors: ['#e7711c', '#4374e0'] },
        backgroundColor: '#f7f7f7',
        datalessRegionColor: '#f8bbd0',
        defaultColor: '#f5f5f5',
      };

      const chartElement = document.getElementById('regions_div');
      if (chartElement) {
        const chart = new google.visualization.GeoChart(chartElement);
        chart.draw(data, options);
      }
    });
  }

  // Browser Chart
  const browserCtx = document.getElementById('browser-chart');
  if (browserCtx) {
    if (chartRefs.value.browserChart) {
      chartRefs.value.browserChart.destroy();
    }
    chartRefs.value.browserChart = new Chart(browserCtx, {
      type: 'doughnut',
      data: {
        labels: browserData.value.map((item) => item[0]),
        datasets: [{
          data: browserData.value.map((item) => item[1]),
          backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF'],
        }],
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'right',
          },
          title: {
            display: true,
            text: 'Top Browsers',
          },
        },
      },
    });
  }

  // OS Chart
  const osCtx = document.getElementById('os-chart');
  if (osCtx) {
    if (chartRefs.value.osChart) {
      chartRefs.value.osChart.destroy();
    }
    chartRefs.value.osChart = new Chart(osCtx, {
      type: 'bar',
      data: {
        labels: osData.value.map((item) => item[0]),
        datasets: [{
          label: 'Sessions',
          data: osData.value.map((item) => item[1]),
          backgroundColor: '#36A2EB',
        }],
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: false,
          },
          title: {
            display: true,
            text: 'Top Operating Systems',
          },
        },
        scales: {
          y: {
            beginAtZero: true,
          },
        },
      },
    });
  }

  // Device Category Chart
  const deviceCtx = document.getElementById('device-chart');
  if (deviceCtx) {
    if (chartRefs.value.deviceChart) {
      chartRefs.value.deviceChart.destroy();
    }
    chartRefs.value.deviceChart = new Chart(deviceCtx, {
      type: 'pie',
      data: {
        labels: deviceData.value.map((item) => item[0]),
        datasets: [{
          data: deviceData.value.map((item) => item[1]),
          backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
        }],
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'bottom',
          },
          title: {
            display: true,
            text: 'Device Categories',
          },
        },
      },
    });
  }
};

const handleApply = () => {
  emit('apply');
};

watch(() => props.data, async (newData) => {
  if (newData.length > 0) {
    await nextTick();
    drawCharts();
  }
}, { immediate: true });

onMounted(async () => {
  if (props.data.length > 0) {
    await nextTick();
    drawCharts();
  }
});

const formatDate = (dateString) => {
  if (dateString === '7daysAgo') {
    return new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)).toISOString().split('T')[0];
  }
  if (dateString === 'today') {
    return new Date().toISOString().split('T')[0];
  }
  return dateString;
};

const startDateFormatted = ref(formatDate(props.startDate));
const endDateFormatted = ref(formatDate(props.endDate));

const eventNames = getEventNames();

const isEventLoading = (eventName) => {
  return !props.eventData[eventName] || props.isLoading;
};
</script>

<template>
  <div class="p-4 min-h-screen">
    <div class="max-w-7xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-bold text-gray-800">Analytics Dashboard</h2>
        <div class="flex space-x-4 items-center">
          <input type="date" v-model="startDateFormatted" @input="$emit('update:startDate', $event.target.value)"
            class="border rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <input type="date" v-model="endDateFormatted" @input="$emit('update:endDate', $event.target.value)"
            class="border rounded p-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
          <button @click="handleApply"
            class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
            Apply
          </button>
        </div>
      </div>

      <div v-if="isLoading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500"></div>
      </div>
      <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
        role="alert">
        <strong class="font-bold">Error:</strong>
        <span class="block sm:inline">{{ error }}</span>
      </div>
      <div v-else-if="data.length === 0" class="text-center text-gray-600">No data available</div>
      <div v-else class="space-y-6">
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <div class="p-4 bg-white rounded-lg shadow">
            <h3 class="text-sm font-semibold mb-2 text-gray-700">Active Users</h3>
            <p class="text-2xl font-bold text-blue-600">{{ totalActiveUsers.toLocaleString() }}</p>
          </div>
          <div class="p-4 bg-white rounded-lg shadow">
            <h3 class="text-sm font-semibold mb-2 text-gray-700">Sessions</h3>
            <p class="text-2xl font-bold text-green-600">{{ totalSessions.toLocaleString() }}</p>
          </div>
          <div class="p-4 bg-white rounded-lg shadow">
            <h3 class="text-sm font-semibold mb-2 text-gray-700">Total Users</h3>
            <p class="text-2xl font-bold text-purple-600">{{ totalUsers.toLocaleString() }}</p>
          </div>
          <div class="p-4 bg-white rounded-lg shadow">
            <h3 class="text-sm font-semibold mb-2 text-gray-700">New Users</h3>
            <p class="text-2xl font-bold text-orange-600">{{ totalNewUsers.toLocaleString() }}</p>
          </div>
          <div class="p-4 bg-white rounded-lg shadow">
            <h3 class="text-sm font-semibold mb-2 text-gray-700">Page Views</h3>
            <p class="text-2xl font-bold text-teal-600">{{ totalPageViews.toLocaleString() }}</p>
          </div>
          <div class="p-4 bg-white rounded-lg shadow">
            <h3 class="text-sm font-semibold mb-2 text-gray-700">Engagement Duration</h3>
            <p class="text-2xl font-bold text-indigo-600">{{ userEngagementDuration.toLocaleString() }}</p>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-white rounded-lg shadow p-4">
            <div id="regions_div" style="width: 100%; height: 300px;"></div>
          </div>
          <div class="bg-white rounded-lg shadow p-4">
            <canvas id="os-chart" style="width: 100%; height: 300px;"></canvas>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="bg-white rounded-lg shadow p-4">
            <canvas id="browser-chart" style="width: 100%; height: 300px;"></canvas>
          </div>
          <div class="bg-white rounded-lg shadow p-4">
            <canvas id="device-chart" style="width: 100%; height: 300px;"></canvas>
          </div>
        </div>

        <div class="bg-white rounded-lg shadow overflow-hidden">
          <h3 class="text-xl font-semibold p-4 bg-gray-50 text-gray-800">Detailed Analytics</h3>
          <div class="overflow-x-auto">
            <table class="w-full text-sm text-left text-gray-500">
              <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3">Country</th>
                  <th scope="col" class="px-6 py-3">Region</th>
                  <th scope="col" class="px-6 py-3">City</th>
                  <th scope="col" class="px-6 py-3">Browser</th>
                  <th scope="col" class="px-6 py-3">OS</th>
                  <th scope="col" class="px-6 py-3">Device</th>
                  <th scope="col" class="px-6 py-3">Source</th>
                  <th scope="col" class="px-6 py-3">Event</th>
                  <th scope="col" class="px-6 py-3">Active Users</th>
                  <th scope="col" class="px-6 py-3">Sessions</th>
                  <th scope="col" class="px-6 py-3">Total Users</th>
                  <th scope="col" class="px-6 py-3">New Users</th>
                  <th scope="col" class="px-6 py-3">Engagement Rate</th>
                  <th scope="col" class="px-6 py-3">Engagement Duration</th>
                  <th scope="col" class="px-6 py-3">Page Views</th>
                  <th scope="col" class="px-6 py-3">Key Events</th>
                  <th scope="col" class="px-6 py-3">New User %</th>
                  <th scope="col" class="px-6 py-3">Pages/User</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in aggregatedData" :key="item.country" class="bg-white border-b">
                  <td class="px-6 py-4">{{ item.country }}</td>
                  <td class="px-6 py-4">{{ item.region }}</td>
                  <td class="px-6 py-4">{{ item.city }}</td>
                  <td class="px-6 py-4">{{ item.browser }}</td>
                  <td class="px-6 py-4">{{ item.operatingSystem }}</td>
                  <td class="px-6 py-4">{{ item.deviceCategory }}</td>
                  <td class="px-6 py-4">{{ item.sessionSource }}</td>
                  <td class="px-6 py-4">{{ item.eventName }}</td>
                  <td class="px-6 py-4">{{ item.activeUsers.toLocaleString() }}</td>
                  <td class="px-6 py-4">{{ item.sessions.toLocaleString() }}</td>
                  <td class="px-6 py-4">{{ item.totalUsers.toLocaleString() }}</td>
                  <td class="px-6 py-4">{{ item.newUsers.toLocaleString() }}</td>
                  <td class="px-6 py-4">{{ item.engagementRate.toFixed(2) }}%</td>
                  <td class="px-6 py-4">{{ item.userEngagementDuration.toLocaleString() }}</td>
                  <td class="px-6 py-4">{{ item.screenPageViews.toLocaleString() }}</td>
                  <td class="px-6 py-4">{{ item.keyEvents.toLocaleString() }}</td>
                  <td class="px-6 py-4">{{ item.newUserPercentage.toFixed(2) }}%</td>
                  <td class="px-6 py-4">{{ item.pageviewsPerUser.toFixed(2) }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="bg-white rounded-lg shadow overflow-hidden mt-6">
          <h3 class="text-xl font-semibold p-4 bg-gray-50 text-gray-800">Event Details</h3>
          <div class="grid grid-cols-2 sm:grid-cols-2 gap-4 p-4">
            <div v-for="eventName in eventNames" :key="eventName" class="bg-white rounded-lg shadow overflow-hidden">
              <h4 class="text-lg font-semibold p-2 bg-gray-100 text-gray-800">{{ eventName }}</h4>
              <div v-if="isEventLoading(eventName)" class="flex justify-center items-center h-32">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              </div>
              <EventTable v-else :eventData="eventData[eventName] || []" :eventName="eventName" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

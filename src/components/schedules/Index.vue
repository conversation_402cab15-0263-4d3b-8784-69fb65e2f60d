<script setup>
import { ref, watch, onUnmounted, onMounted, computed } from 'vue';
import Button from '../../components/common/Button.vue';
import ActionButtons from '../../components/UIElements/ActionButton.vue';
import { formatTime, formatDate } from '../../helpers/domhelper';
import { GetSessions, RejoinSession, CancelSession, EndSession } from '../../api/sessions/index';
import { Org_Store } from '../../store/organization';
import { useRoute } from 'vue-router';
import router from '@/router';
import { UserStore } from '../../store';
import { uiOperations } from '../../store/uiOperations';
import LoaderComp from '../common/LoaderComp.vue';
import Noschedule from '../../assets/svgs/Noschedule.vue';
import CalendaerSvg from '../../assets/svgs/CalendaerSvg.vue';
import TimeSvg from '../../assets/svgs/TimeSvg.vue';
import DropdownButtonComp from '../common/DropdownButtonComp.vue';
import CreateScheduleIcon from '../../assets/svgs/scheduleSvgs.vue/CreateScheduleIcon.vue';
import CreateSessionIcon from '../../assets/svgs/scheduleSvgs.vue/CreateSessionIcon.vue';
import BottomSheet from '../common/BottomSheet.vue';
import ToggleButton from '../common/ToggleButton.vue';
import BreadCrumb from '../UIElements/BreadCrumb.vue';
import LabelDropdown from '../common/LabelDropdown.vue';
import CardComponent from '../UIElements/CardComponent.vue';
const uistore = uiOperations();
const use_router = useRoute();
const userStore = UserStore();
const Organizationstore = Org_Store();
const session_fields = ref(['Lead Name', 'Date', 'Time', 'Project', 'Duration', 'Tag', 'Status', '']);
const type = ref(use_router.query.type || 'upcoming');
const sessions = ref(null);
const userselected = ref();
const tagOptions = ref([]);
const openMenus = ref({});
const showParticipantsSheet = ref(false);
const selectedParticipants = ref([]);
const isLoading = ref({
  cancel: false,
  end: false,
});
const showModal = ref(false);
const modalContent = ref(null);
const selectedTag = ref([{ value: use_router.query.tag ? use_router.query.tag : 'All' }]);
Organizationstore.RefreshProjects();
Organizationstore.RefreshUsers();
const uniqueTags = new Set();
const selectedUserId = ref(use_router.query.user_id ? use_router.query.user_id : null);
const loader = ref(false);
const showFilterBottomSheet = ref(false);
const viewType = ref('table');
const selectedFilterValues = ref({
  users: [{
    id: 'all',
    label: 'All',
    value: { first_name: 'All', label: 'All', user_id: null },
  }],
  tags: [{
    id: 'all',
    label: 'All',
    value: { value: 'All' },
  }],
});

const tempFilterValues = ref({
  users: [{
    id: 'all',
    label: 'All',
    value: { first_name: 'All', label: 'All', user_id: null },
  }],
  tags: [{
    id: 'all',
    label: 'All',
    value: { value: 'All' },
  }],
});

if (Organizationstore.projects) {
  Object.values(Organizationstore.projects).forEach((project) => {
    if (project && project.projectSettings && project.projectSettings.salestool && project.projectSettings.salestool.tags.length) {
      console.log(project.projectSettings.salestool.tags);
      project.projectSettings.salestool.tags.forEach((tag) => {
        uniqueTags.add(tag);
      });
    }
  });
}
tagOptions.value = [{ value: 'All' }, ...Array.from(uniqueTags).map((tag) => ({ value: tag }))];
watch(() => Organizationstore.projects, () => {
  const uniqueTags = new Set();
  Object.values(Organizationstore.projects).forEach((project) => {
    if (project && project.projectSettings && project.projectSettings.salestool && project.projectSettings.salestool.tags.length) {
      console.log(project.projectSettings.salestool.tags);
      project.projectSettings.salestool.tags.forEach((tag) => {
        uniqueTags.add(tag);
      });
    }
  });
  tagOptions.value = [{ value: 'All' }, ...Array.from(uniqueTags).map((tag) => ({ value: tag }))];
  console.log(tagOptions.value);
});

watch(() => Organizationstore.users, () => {
  userselected.value = !use_router.query.user_id ? { first_name: 'All' } : Organizationstore.users[use_router.query.user_id];
});
// watch(() => use_router.query, (newQuery) => {
//   // Initialize users filter
//   if (newQuery.user_id && Organizationstore.users) {
//     const user = Organizationstore.users[newQuery.user_id];
//     if (user) {
//       selectedFilterValues.value.users = [user];
//     }
//   }

//   // Initialize tags filter
//   if (newQuery.tag) {
//     selectedFilterValues.value.tags = [{ value: newQuery.tag }];
//   }
// }, { immediate: true });

function openFilterBottomSheet () {
  tempFilterValues.value = JSON.parse(JSON.stringify(selectedFilterValues.value));
  showFilterBottomSheet.value = true;
}

function formatLabel (option) {
  if (!option) {
    return '';
  }
  if (option.first_name) {
    if (option.role === 'admin') {
      return `${option.first_name} (admin)`;
    }
    return `${option.first_name}`;
  } else if (option.email) {
    if (option.role === 'admin') {
      return `${option.email} (admin)`;
    }
    return `${option.email}`;
  }
  return 'Unknown';
}

function getFilteredUsers (users) {
  if (!users) {
    return [{ first_name: 'All', label: 'All' }];
  }
  const allOption = { first_name: 'All', label: 'All', user_id: null };
  const filteredUsers = Object.values(users || {});
  console.log("users", filteredUsers);
  // Add label property for each user
  filteredUsers.forEach((user) => {
    user.label = formatLabel(user);
  });
  return [allOption, ...filteredUsers];
}

const filterTabs = computed(() => {
  const tabs = [];

  if (userStore?.user_role?.userrole?.role !== 'reader') {
    tabs.push({
      id: 'users',
      label: 'By user',
      icon: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M8 8C10.21 8 12 6.21 12 4C12 1.79 10.21 0 8 0C5.79 0 4 1.79 4 4C4 6.21 5.79 8 8 8ZM8 10C5.33 10 0 11.34 0 14V16H16V14C16 11.34 10.67 10 8 10Z" fill="currentColor"/>
      </svg>`,
    });
  }

  tabs.push({
    id: 'tags',
    label: 'By Tag',
    icon: `<svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15 8H6M12.1875 13H8.8125" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
  },
  );

  // function formatLabel (option) {
  //   if (!option) {
  //     return '';
  //   }

  //   if (option.first_name) {
  //     if (option.role === 'admin') {
  //       return `${option.first_name} (admin)`;
  //     }
  //     return `${option.first_name}`;
  //   } else if (option.email) {
  //     if (option.role === 'admin') {
  //       return `${option.email} (admin)`;
  //     }
  //     return `${option.email}`;
  //   }
  //   return 'Unknown';
  // }

  // function getFilteredUsers (users) {
  //   if (!users) {
  //     return [{ first_name: 'All', label: 'All' }];
  //   }

  //   const allOption = { first_name: 'All', label: 'All', user_id: null };
  //   const filteredUsers = Object.values(users || {});

  //   // Add label property for each user
  //   filteredUsers.forEach((user) => {
  //     user.label = formatLabel(user);
  //   });

  //   return [allOption, ...filteredUsers];
  // }

  return tabs;
});
const filterOptions = computed(() => ({
  users: Organizationstore.users ? getFilteredUsers(Organizationstore.users).map((user) => ({
    id: user.user_id || user.email || 'all',
    label: user.first_name === 'All' ? 'All' : formatLabel(user),
    value: user,
  })) : [],
  tags: tagOptions.value ? tagOptions.value.map((tag) => ({
    id: tag.value,
    label: tag.value,
    value: tag,
  })) : [],
}));

const persistentFilterValues = ref({
  users: [{
    id: 'all',
    label: 'All',
    value: { first_name: 'All', label: 'All', user_id: null },
  }],
  tags: [{
    id: 'all',
    label: 'All',
    value: { value: 'All' },
  }],
});

watch(() => use_router.query, (newQuery) => {
  if (newQuery.user_id && Organizationstore.users) {
    const user = Organizationstore.users[newQuery.user_id];
    if (user) {
      const userSelection = {
        id: user.user_id,
        label: formatLabel(user),
        value: user,
      };
      persistentFilterValues.value.users = [userSelection];
      selectedFilterValues.value.users = [userSelection];
    }
  }

  if (newQuery.tag) {
    const tagSelection = {
      id: newQuery.tag,
      label: newQuery.tag,
      value: { value: newQuery.tag },
    };
    persistentFilterValues.value.tags = [tagSelection];
    selectedFilterValues.value.tags = [tagSelection];
  }
}, { immediate: true });

// Filter count computed property
const filterCount = computed(() => {
  let count = 0;

  // Count users filter
  if (selectedFilterValues.value.users?.length &&
    !selectedFilterValues.value.users.some((u) => u.id === 'all' || u.value?.first_name === 'All')) {
    count++;
  }

  // Count tags filter
  if (selectedFilterValues.value.tags?.length &&
    !selectedFilterValues.value.tags.some((t) => t.value?.value === 'All')) {
    count++;
  }

  return count;
});

function fetchSessionData () {
  loader.value = true;
  if (Organizationstore.createSession === true) {
    Organizationstore.createSession = false;
  }
  const params = {
    type: type.value,
    ...use_router.query,
  };
  if (userStore.user_data._id) {

    if (userStore.user_role.userrole.role === 'admin') {
      session_fields.value = ['Lead Name', 'Date', 'Time', 'Project', 'Duration', 'Tag', 'Status', 'Agent', ''];
    }

    GetSessions(params).then((sessions_data) => {
      console.log(sessions_data);
      sessions.value = sessions_data;
      loader.value = false;
    }).catch(() => {
      sessions.value = null;
      loader.value = false;
    });
  } else {
    loader.value = false;
  }
}

function handleFilterApply (selections) {
  const query = { ...use_router.query };

  // Handle users selection and sync with desktop dropdown
  if (selections?.users?.length) {
    const selectedUser = selections.users[0].value;
    if (selectedUser && selectedUser.first_name !== 'All') {
      query.user_id = selectedUser.user_id;
      userselected.value = selectedUser;
    } else {
      delete query.user_id;
      userselected.value = { first_name: 'All' };
    }
  }

  // Handle tags selection and sync with desktop dropdown
  if (selections?.tags?.length) {
    const selectedTagValue = selections.tags[0].value;
    if (selectedTagValue && selectedTagValue.value !== 'All') {
      query.tag = selectedTagValue.value;
      selectedTag.value = [selectedTagValue];
    } else {
      delete query.tag;
      selectedTag.value = [{ value: 'All' }];
    }
  }

  // Update persistent and actual filter values only when applied
  selectedFilterValues.value = JSON.parse(JSON.stringify(selections));
  persistentFilterValues.value = JSON.parse(JSON.stringify(selections));

  // Update route and fetch data
  router.push({ query }).then(() => {
    fetchSessionData();
  });

  showFilterBottomSheet.value = false;
}

async function handleButtonClick (e) {
  type.value = e;
  await router.push({ path: '/schedules', query: { ...use_router.query, type: e } });
  sessions.value = null;
  fetchSessionData();
}

router.afterEach(async (to, from) => {
  console.log(to);
  if (from.path === '/schedules/create' || from.path === '/schedules/createSession') {
    console.log('From path', from.path);
    console.log(Organizationstore.createSession);
    if (Organizationstore.createSession === true) {
      fetchSessionData();
    }
  }
});

function openModal (leads) {
  modalContent.value = leads;
  showModal.value = true;
}

function closeModal () {
  showModal.value = false;
  modalContent.value = null;
}

function closeFilterBottomSheet () {
  tempFilterValues.value = JSON.parse(JSON.stringify(selectedFilterValues.value));
  showFilterBottomSheet.value = false;
}
async function copyurl (id) {
  const url = window.location.origin + '/salestool/joinroom/' + id;

  try {
    if (navigator.clipboard && window.isSecureContext) {
      // For modern browsers
      await navigator.clipboard.writeText(url);
      uistore.showToast('URL copied to clipboard', 'success');
    } else {
      // Fallback for older browsers and iOS
      const textArea = document.createElement('textarea');
      textArea.value = url;
      textArea.style.position = 'fixed';
      textArea.style.top = '-999999px';
      textArea.style.left = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();

      try {
        document.execCommand('copy');
        textArea.remove();
        uistore.showToast('URL copied to clipboard', 'success');
      } catch (err) {
        console.error('Failed to copy text: ', err);
        uistore.showToast('Failed to copy URL', 'error');
      } finally {
        textArea.remove();
      }
    }
  } catch (err) {
    console.error('Failed to copy: ', err);
    uistore.showToast('Failed to copy URL', 'error');
  }
}
function toggleViewType () {
  viewType.value = viewType.value === 'table' ? 'grid' : 'table';
}

function redirectToSalestool (id) {
  window.open('/salestool/preview/' + id, '_blank');
  fetchSessionData();
}

async function rejoinSession (sessionId) {
  try {
    const response = await RejoinSession({ session_id: sessionId });
    if (response.status === "on-going") {
      redirectToSalestool(sessionId);
      openMenus.value[sessionId] = false;
    } else {
      console.error('Failed to rejoin session:', response.error);
    }
  } catch (error) {
    console.error('Error rejoining session:', error);
  } finally {
    loader.value = false;
  }
}

function handleRejoin (sessionId) {
  if (!loader.value) {
    loader.value = true;
    rejoinSession(sessionId);
  }
}

async function HandleEndSession (sessionId) {
  try {
    isLoading.value.end = true;
    await EndSession({ session_id: sessionId });
    fetchSessionData();
    openMenus.value[sessionId] = false;
    isLoading.value.end = false;
  } catch (error) {
    isLoading.value.end = false;
    console.log("ended session error: " + error);
  }
}

async function HandleCancelSession (sessionId) {
  try {
    isLoading.value.cancel = true;
    await CancelSession({ session_id: sessionId });
    fetchSessionData();
    openMenus.value[sessionId] = false;
    isLoading.value.cancel = false;
  } catch (error) {
    isLoading.value.cancel = false;
    console.log("Cancelled session error: " + error);
  }
}

function toggleMenu (itemId) {
  // Close all other menus
  Object.keys(openMenus.value).forEach((key) => {
    if (key !== itemId) {
      openMenus.value[key] = false;
    }
  });

  // Toggle the clicked menu
  openMenus.value = {
    ...openMenus.value,
    [itemId]: !openMenus.value[itemId],
  };
}

function closeMenu (itemId) {
  if (itemId) {
    openMenus.value[itemId] = false;
  } else {
    openMenus.value = {};
  }
}

function canRejoinSession (session) {
  const now = new Date();
  const endTime = new Date(session.scheduled_end_time);
  console.log(session.status === 'ended' && now < endTime);
  return session.status === 'ended' && now < endTime;
}

const formatDuration = (minutes) => {
  const hrs = Math.floor(minutes / 60);
  const mins = Math.floor(minutes % 60);
  const secs = Math.round((minutes % 1) * 60);

  let result = '';
  if (hrs > 0) {
    result += `${hrs}h `;
  }
  if (mins > 0 || hrs > 0) {
    result += `${mins}m `;
  }
  result += `${secs}s`;

  return result.trim();
};

const sessionFields = computed(() => [
  { key: 'project_id', label: 'Project' },
  {
    key: 'leads',
    label: 'Lead Name',
    format: (leads) => (leads && leads.length > 0 ? leads[0].name : '-'),
    clickable: true,
    class: 'text-blue-500 hover:text-blue-700',
  },
  {
    key: 'schedule_time',
    label: 'Date & Time',
    format: (value) => (value ? `${formatDate(value)} ${formatTime(value)}` : '-'),
  },
  {
    key: 'duration_minutes',
    label: 'Duration',
    format: (minutes) => (minutes ? formatDuration(minutes) : '-'),
  },
  { key: 'status', label: 'Status' },
  {
    key: 'user_id',
    label: 'Agent',
    format: (userId) => (Organizationstore.users && Organizationstore.users[userId]
      ? Organizationstore.users[userId].name || Organizationstore.users[userId].email
      : userId || '-'),
  },
]);

const sessionActions = computed(() => [
  {
    key: 'rejoin',
    label: 'Rejoin Now',
    class: 'border border-bg-900 bg-bg-1000 dark:bg-bg-50 dark:text-txt-950',
    condition: (item) => canRejoinSession(item),
    action: (item) => handleRejoin(item._id),
  },
  {
    key: 'join',
    label: 'Join Now',
    class: 'bg-gray-900 text-white px-4 py-2 rounded-lg text-sm font-medium flex-grow',
    condition: (item) => item.status !== 'ended' && item.status !== 'cancelled' && item.user_id === userStore.user_data._id,
    action: (item) => redirectToSalestool(item._id),
  },
  {
    key: 'copy',
    icon: `<svg class="h-5 w-5" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path class="fill-bg-default dark:fill-bg-950" d="M8.72151 11.8873C8.82142 11.9873 8.87755 12.1228 8.87755 12.2641C8.87755 12.4054 8.82142 12.541 8.72151 12.6409L8.05904 13.3073C7.35845 14.0073 6.40842 14.4004 5.41796 14.4001C4.42749 14.3998 3.47771 14.0061 2.77757 13.3056C2.07742 12.6052 1.68426 11.6553 1.68457 10.665C1.68488 9.67475 2.07864 8.72515 2.77923 8.02513L4.38677 6.41789C5.0597 5.74454 5.96455 5.35377 6.91618 5.32552C7.86781 5.29728 8.79426 5.63371 9.50595 6.26596C9.55846 6.31251 9.60129 6.36895 9.63199 6.43205C9.66269 6.49516 9.68065 6.56368 9.68486 6.63373C9.68907 6.70377 9.67944 6.77396 9.65652 6.84028C9.63359 6.90661 9.59783 6.96777 9.55127 7.02027C9.50471 7.07277 9.44826 7.11559 9.38515 7.14629C9.32203 7.17698 9.25349 7.19494 9.18343 7.19915C9.11337 7.20336 9.04318 7.19373 8.97684 7.17081C8.91051 7.14789 8.84934 7.11213 8.79682 7.06558C8.28851 6.61426 7.62696 6.37415 6.94745 6.39434C6.26794 6.41453 5.62182 6.6935 5.14121 7.1742L3.53368 8.77944C3.03363 9.2794 2.7527 9.9575 2.7527 10.6645C2.7527 11.3716 3.03363 12.0497 3.53368 12.5497C4.03373 13.0496 4.71195 13.3305 5.41913 13.3305C6.12632 13.3305 6.80453 13.0496 7.30459 12.5497L7.96706 11.8873C8.01658 11.8377 8.07538 11.7984 8.14011 11.7716C8.20484 11.7448 8.27422 11.731 8.34429 11.731C8.41435 11.731 8.48374 11.7448 8.54846 11.7716C8.61319 11.7984 8.67199 11.8377 8.72151 11.8873ZM13.3922 2.69165C12.6915 1.99266 11.7421 1.6001 10.7523 1.6001C9.76244 1.6001 8.81304 1.99266 8.11235 2.69165L7.44988 3.35467C7.34992 3.45469 7.2938 3.59033 7.29386 3.73172C7.29393 3.87312 7.35017 4.0087 7.45021 4.10864C7.55026 4.20858 7.68591 4.26469 7.82734 4.26463C7.96876 4.26457 8.10437 4.20834 8.20433 4.10831L8.8668 3.44596C9.36686 2.94599 10.0451 2.66512 10.7523 2.66512C11.4594 2.66512 12.1377 2.94599 12.6377 3.44596C13.1378 3.94592 13.4187 4.62401 13.4187 5.33107C13.4187 6.03812 13.1378 6.71622 12.6377 7.21618L11.0302 8.82475C10.5493 9.30517 9.90304 9.58378 9.22354 9.60359C8.54403 9.62341 7.88263 9.38295 7.37457 8.93137C7.32205 8.88481 7.26088 8.84906 7.19455 8.82614C7.12821 8.80322 7.05801 8.79359 6.98796 8.7978C6.84647 8.8063 6.71416 8.87064 6.62012 8.97668C6.57356 9.02918 6.53779 9.09034 6.51487 9.15666C6.49195 9.22299 6.48232 9.29317 6.48653 9.36322C6.49503 9.50468 6.55938 9.63697 6.66544 9.73099C7.37696 10.3634 8.30332 10.7001 9.25494 10.6721C10.2066 10.6441 11.1115 10.2536 11.7846 9.58039L13.3922 7.97315C14.0917 7.27254 14.4846 6.32303 14.4846 5.33307C14.4846 4.34311 14.0917 3.3936 13.3922 2.69298V2.69165Z" />
    </svg>`,
    class: 'border border-gray-300 rounded-lg p-2',
    condition: (item) => {
      const join = item.status !== 'cancelled' && item.status !== 'ended' && item.user_id === userStore.user_data._id;
      const rejoin = canRejoinSession(item);
      return join || rejoin;
    },
    action: (item) => copyurl(item._id),
  },
  {
    key: 'menu',
    icon: `<svg class="w-5 h-5 text-gray-500" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
    </svg>`,
    class: 'border border-gray-300 rounded-lg p-2 ml-2',
    action: (item) => toggleMenu(item._id),
  },
  {
    key: 'cancel',
    label: 'Cancel Session',
    class: 'bg-red-400 hover:bg-red-500 text-white',
    condition: (item) => type.value === 'upcoming' && item.status === 'scheduled',
    action: (item) => HandleCancelSession(item._id),
  },
  {
    key: 'end',
    label: 'End Session',
    class: 'bg-red-400 hover:bg-red-500 text-white',
    condition: (item) => type.value === 'upcoming' && (item.status === 'on-going'  || item.status === 'active' || item.status === 'on-hold'),
    action: (item) => HandleEndSession(item._id),
  },
]);

const handleFieldClick = ({ field, item }) => {
  if (field.key === 'leads' && item.leads.length>0) {
    if (window.innerWidth < 640) { // sm breakpoint
      selectedParticipants.value = item.leads;
      showParticipantsSheet.value = true;
    } else {
      openModal(item.leads);
    }
  }
};

const filteredActions = (item) => {
  return sessionActions.value.filter((action) => {
    return !action.condition || action.condition(item);
  });
};

const handleCardAction = ({ action, item }) => {
  if (action.action) {
    if (action.key === 'join' || action.key === 'rejoin') {
      if (action.key === 'join') {
        redirectToSalestool(item._id);
      } else if (action.key === 'rejoin') {
        handleRejoin(item._id);
      }
    } else {
      action.action(item);
    }
  }
};

function handleOutsideClick (event) {
  const openMenuIds = Object.keys(openMenus.value).filter((key) => openMenus.value[key]);
  openMenuIds.forEach((id) => {
    const menu = document.querySelector(`[data-menu-id="${id}"]`);
    const button = document.querySelector(`[data-button-id="${id}"]`);
    if (menu && !menu.contains(event.target) && !button.contains(event.target)) {
      closeMenu(id);
    }
  });
}

onMounted(() => {
  document.addEventListener('click', handleOutsideClick);
});

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
});

async function handleProjectSelection (val) {
  const query = { ...use_router.query };

  if (val.user_id) {
    selectedUserId.value = val;
    userselected.value = val;
    query.user_id = val.user_id;

    // Sync with mobile filters
    const userSelection = {
      id: val.user_id,
      label: formatLabel(val),
      value: val,
    };
    persistentFilterValues.value.users = [userSelection];
    selectedFilterValues.value.users = [userSelection];
  } else if (val.first_name === 'All') {
    userselected.value = { first_name: 'All' };
    selectedUserId.value = null;
    delete query.user_id;

    // Sync with mobile filters
    const allUserSelection = {
      id: 'all',
      label: 'All',
      value: { first_name: 'All', label: 'All', user_id: null },
    };
    persistentFilterValues.value.users = [allUserSelection];
    selectedFilterValues.value.users = [allUserSelection];
  }

  await router.push({ query });
  fetchSessionData();
}

function handleFilterClear () {
  // Reset to default selections
  const defaultValues = {
    users: [{
      id: 'all',
      label: 'All',
      value: { first_name: 'All', label: 'All', user_id: null },
    }],
    tags: [{
      id: 'all',
      label: 'All',
      value: { value: 'All' },
    }],
  };

  persistentFilterValues.value = JSON.parse(JSON.stringify(defaultValues));
  selectedFilterValues.value = JSON.parse(JSON.stringify(defaultValues));

  // Clear URL parameters
  const query = { ...use_router.query };
  delete query.user_id;
  delete query.tag;

  // Update route and fetch data
  router.push({ query }).then(() => {
    fetchSessionData();
  });

  showFilterBottomSheet.value = false;
}

async function handleTagSelection (tag) {
  selectedTag.value = tag;
  const query = { ...use_router.query };

  if (tag.value !== 'All') {
    query.tag = tag.value;

    // Sync with mobile filters
    const tagSelection = {
      id: tag.value,
      label: tag.value,
      value: tag,
    };
    persistentFilterValues.value.tags = [tagSelection];
    selectedFilterValues.value.tags = [tagSelection];
  } else {
    delete query.tag;

    // Sync with mobile filters
    const allTagSelection = {
      id: 'all',
      label: 'All',
      value: { value: 'All' },
    };
    persistentFilterValues.value.tags = [allTagSelection];
    selectedFilterValues.value.tags = [allTagSelection];
  }

  await router.push({ query });
  fetchSessionData();
}

fetchSessionData();
</script>

<template>
  <div
    class="relative bg-transparent flex flex-col justify-start items-start gap-1 overflow-hidden sm:overflow-y-auto h-full  sm:pb-0 w-full">
    <LoaderComp v-if="loader"/>

    <!-- Sticky header for mobile -->
    <div class="sm:hidden block w-full dark:bg-bg-150 z-[8] flex-grow-0 flex-shrink-0 ">
      <div class="px-4 pt-2 pb-2">
        <div class="flex justify-between items-center mb-2 gap-2">
          <div>
            <h3 class="text-txt-50 dark:text-txt-1000 text-lg font-semibold">Schedule</h3>
            <p class="text-gray-600 dark:text-txt-650 text-xs font-normal">Manage your upcoming and previous schedule
            </p>
          </div>
          <div class="flex items-center gap-2.5">
            <Button title="" @click="openFilterBottomSheet" theme="secondary"
              class="w-full text-xs sm:text-sm flex items-center justify-center !bg-transparent border-[1px] border-[#E6E6E6] !pr-[2px] !pl-2 !h-10">
              <template #svg>
                <svg class="w-5 h-5 sm:w-6 sm:h-6" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path class="fill-txt-1000 dark:fill-txt-default" d="M15 2.5H1L6.5 8.5V13.5L9.5 15.5V8.5L15 2.5Z"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span v-if="filterCount > 0"
                  class="absolute top-1 right-[65px] flex items-center justify-center w-4 h-4 text-xs font-medium text-white bg-black rounded-full">
                  {{ filterCount }}
                </span>
              </template>
            </Button>
            <DropdownButtonComp :options="[
              {
                title: 'Create',
                action: () => router.push('/schedules/createSession'),
                iconComp: CreateSessionIcon
              },
              {
                title: 'Schedule',
                action: () => router.push('/schedules/create'),
                iconComp: CreateScheduleIcon
              }
            ]" @select="option => option.action()" />
          </div>
        </div>
        <ActionButtons :active="type" :array="['upcoming', 'previous']" @click="(e) => handleButtonClick(e)"
          class="w-full p-0.5" />
      </div>
    </div>

    <!-- Desktop header (hidden on mobile) -->
    <div class="hidden sm:block w-full">
      <div class="mb-4 px-4 pt-6 flex flex-col sm:flex-row justify-between items-start sm:items-center">
        <div class="flex flex-col justify-start items-start gap-3 sm:mb-0">
          <h3 class="text-txt-50 dark:text-txt-1000 text-2xl font-semibold">Schedule</h3>
          <p class="text-gray-600 dark:text-txt-650 text-base font-normal mb-0">Manage your upcoming and previous
            schedule</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-4 sm:gap-6 items-stretch sm:items-center w-full sm:w-auto">
          <router-link to="/schedules/createSession" class="w-full sm:w-auto">
            <Button title="Create Session" theme="primary">
              <template v-slot:svg>
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_306_21007)">
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_306_21007">
                      <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                    </clipPath>
                  </defs>
                </svg>
              </template>
            </Button>
          </router-link>
          <router-link to="/schedules/create" class="w-full sm:w-auto">
            <Button title="Create Schedule" theme="primary" class="w-full sm:w-auto">
              <template v-slot:svg>
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_306_21007)">
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_306_21007">
                      <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                    </clipPath>
                  </defs>
                </svg>
              </template>
            </Button>
          </router-link>
        </div>
      </div>
    </div>

    <div class="hidden sm:block w-full">
      <hr />
      <div class="my-2 px-4 ">
        <BreadCrumb :list="[{ name: 'Schedule', route: '/schedules' }]" :active="0" />
      </div>
      <hr />
    </div>

    <div class="hidden sm:block border-b border-gray-200 mb-4 w-full">
      <nav class="-mb-px flex">
        <ActionButtons :active="type" :array="['previous', 'upcoming']" @click="(e) => handleButtonClick(e)"
          class="w-full sm:w-auto" />
      </nav>
    </div>
    <div class="sm:px-4 w-full h-full overflow-hidden overflow-x-hidden sm:overflow-visible flex-1">
      <!-- Tabs -->
      <div class="hidden sm:flex flex-col sm:flex-row justify-between items-center gap-8">
        <p class="text-2xl font-medium" style="text-wrap: nowrap;">Created Schedules</p>
        <div class="flex flex-col sm:flex-row gap-x-7 gap-y-5 w-fit items-center flex-wrap justify-end">
          <div class="w-fit"
            v-if="Organizationstore.users && (userStore.user_role && userStore.user_role.userrole.role == 'admin')">
            <LabelDropdown :options="getFilteredUsers(Organizationstore.users)" @handleSelect="handleProjectSelection"
              label="Users" value="label" width="263px" trackBy="user_id"
              :selectedOption="getFilteredUsers(Organizationstore.users).find(item => item.user_id === selectedUserId)" />
          </div>
          <div class="w-fit">
            <LabelDropdown :options="tagOptions" @handleSelect="handleTagSelection" label="Tags" value="value"
              width="263px" :selectedOption="selectedTag[0]"/>
          </div>
          <ToggleButton v-model="viewType" @toggle="toggleViewType" />
        </div>
      </div>
      <!-- table content -->
      <div v-if="sessions !== null && Object.keys(sessions).length !== 0"
        class="py-2.5 sm:py-4 h-full sm:h-auto flex-1 relative overflow-hidden overflow-x-hidden  sm:overflow-visible w-full ">
        <!-- desktop view -->
        <div v-if="viewType === 'table'"
          class="hidden sm:block overflow-x-auto mb-4 w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-y-hidden">
          <table class="w-full rounded-lg bg-transparent">
            <thead class="bg-gray-50 dark:bg-bg-150">
              <tr>
                <th v-for="(item, index) in session_fields" :key="index"
                  class="p-3 text-left text-xs font-semibold text-gray-900">{{ item }}</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in sessions" :key="index"
                class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">
                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap cursor-pointer"
                  @click="openModal(item.leads)">
                  <div class="flex items-center">
                    <div class="w-28 whitespace-nowrap overflow-hidden text-ellipsis capitalize">
                      {{ item.leads && item.leads.length > 0 ? item.leads[0].name : '-' }}
                      <div v-if="item.leads && item.leads.length > 1"
                        class="ml-3 mt-1 inline-flex items-center justify-center bg-black text-white text-xs font-medium rounded-full px-[5px] py-1">
                        +{{ item.leads.length - 1 }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                  <span class="flex gap-1 items-center">
                    <CalendaerSvg />
                    <p class="whitespace-nowrap">{{ formatDate(item.schedule_time) }}</p>
                  </span>
                </td>
                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                  <div class="flex gap-1 items-center">
                    <TimeSvg />
                    <p class="whitespace-nowrap">{{ formatTime(item.schedule_time) }}</p>
                  </div>
                </td>
                <td v-if="Organizationstore.projects" class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                  {{ Organizationstore.projects[item.project_id] ? Organizationstore.projects[item.project_id].name :
                    item.project_id }}
                </td>
                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                  {{ item.duration_minutes > 0 ? formatDuration(item.duration_minutes) : '-' }}
                </td>
                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                  {{ item.tag }}
                </td>
                <td class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                  {{ item.status }}
                </td>
                <td v-if="Organizationstore.users && userStore.user_role.userrole.role === 'admin'"
                  class="p-3 text-txt-50 dark:text-txt-950 whitespace-nowrap">
                  {{ Organizationstore.users[item.user_id] ? Organizationstore.users[item.user_id].name ?
                    Organizationstore.users[item.user_id].name : Organizationstore.users[item.user_id].email :
                    item.user_id }}
                </td>
                <td class="p-3 flex flex-col sm:flex-row gap-2">
                  <!-- Actions -->
                  <div v-if="canRejoinSession(item)" class="flex flex-col sm:flex-row gap-2">
                    <button @click="handleRejoin(item._id)"
                      class="border border-bg-900 w-full sm:w-28 py-2 text-sm font-medium bg-bg-1000 rounded-lg dark:bg-bg-50 dark:text-txt-950 flex justify-center items-center"
                      :disabled="loader">
                      Rejoin Now
                    </button>
                    <button @click="copyurl(item._id)"
                      class="border border-bg-900 bg-bg-1000 rounded-lg dark:bg-bg-50 p-2 w-full sm:w-8 flex items-center justify-center">
                      <svg class="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="fill-bg-default dark:fill-bg-950"
                          d="M8.72151 11.8873C8.82142 11.9873 8.87755 12.1228 8.87755 12.2641C8.87755 12.4054 8.82142 12.541 8.72151 12.6409L8.05904 13.3073C7.35845 14.0073 6.40842 14.4004 5.41796 14.4001C4.42749 14.3998 3.47771 14.0061 2.77757 13.3056C2.07742 12.6052 1.68426 11.6553 1.68457 10.665C1.68488 9.67475 2.07864 8.72515 2.77923 8.02513L4.38677 6.41789C5.0597 5.74454 5.96455 5.35377 6.91618 5.32552C7.86781 5.29728 8.79426 5.63371 9.50595 6.26596C9.55846 6.31251 9.60129 6.36895 9.63199 6.43205C9.66269 6.49516 9.68065 6.56368 9.68486 6.63373C9.68907 6.70377 9.67944 6.77396 9.65652 6.84028C9.63359 6.90661 9.59783 6.96777 9.55127 7.02027C9.50471 7.07277 9.44826 7.11559 9.38515 7.14629C9.32203 7.17698 9.25349 7.19494 9.18343 7.19915C9.11337 7.20336 9.04318 7.19373 8.97684 7.17081C8.91051 7.14789 8.84934 7.11213 8.79682 7.06558C8.28851 6.61426 7.62696 6.37415 6.94745 6.39434C6.26794 6.41453 5.62182 6.6935 5.14121 7.1742L3.53368 8.77944C3.03363 9.2794 2.7527 9.9575 2.7527 10.6645C2.7527 11.3716 3.03363 12.0497 3.53368 12.5497C4.03373 13.0496 4.71195 13.3305 5.41913 13.3305C6.12632 13.3305 6.80453 13.0496 7.30459 12.5497L7.96706 11.8873C8.01658 11.8377 8.07538 11.7984 8.14011 11.7716C8.20484 11.7448 8.27422 11.731 8.34429 11.731C8.41435 11.731 8.48374 11.7448 8.54846 11.7716C8.61319 11.7984 8.67199 11.8377 8.72151 11.8873ZM13.3922 2.69165C12.6915 1.99266 11.7421 1.6001 10.7523 1.6001C9.76244 1.6001 8.81304 1.99266 8.11235 2.69165L7.44988 3.35467C7.34992 3.45469 7.2938 3.59033 7.29386 3.73172C7.29393 3.87312 7.35017 4.0087 7.45021 4.10864C7.55026 4.20858 7.68591 4.26469 7.82734 4.26463C7.96876 4.26457 8.10437 4.20834 8.20433 4.10831L8.8668 3.44596C9.36686 2.94599 10.0451 2.66512 10.7523 2.66512C11.4594 2.66512 12.1377 2.94599 12.6377 3.44596C13.1378 3.94592 13.4187 4.62401 13.4187 5.33107C13.4187 6.03812 13.1378 6.71622 12.6377 7.21618L11.0302 8.82475C10.5493 9.30517 9.90304 9.58378 9.22354 9.60359C8.54403 9.62341 7.88263 9.38295 7.37457 8.93137C7.32205 8.88481 7.26088 8.84906 7.19455 8.82614C7.12821 8.80322 7.05801 8.79359 6.98796 8.7978C6.84647 8.8063 6.71416 8.87064 6.62012 8.97668C6.57356 9.02918 6.53779 9.09034 6.51487 9.15666C6.49195 9.22299 6.48232 9.29317 6.48653 9.36322C6.49503 9.50468 6.55938 9.63697 6.66544 9.73099C7.37696 10.3634 8.30332 10.7001 9.25494 10.6721C10.2066 10.6441 11.1115 10.2536 11.7846 9.58039L13.3922 7.97315C14.0917 7.27254 14.4846 6.32303 14.4846 5.33307C14.4846 4.34311 14.0917 3.3936 13.3922 2.69298V2.69165Z" />
                      </svg>
                    </button>
                  </div>
                  <!-- for joining session -->
                  <div
                    v-if="(item.status != 'ended' && item.status != 'cancelled') && item.user_id == userStore.user_data._id"
                    class="flex gap-3">

                    <button @click="redirectToSalestool(item._id)"
                      class="border border-bg-900 w-28 py-2 text-sm font-medium bg-bg-1000 rounded-lg dark:bg-bg-50 dark:text-txt-950 flex justify-center items-center">
                      Join Now
                    </button>
                    <button @click="copyurl(item._id)"
                      class="border border-bg-900 bg-bg-1000 rounded-lg dark:bg-bg-50 p-2 w-8 flex items-center justify-center">
                      <svg class="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="fill-bg-default dark:fill-bg-950"
                          d="M8.72151 11.8873C8.82142 11.9873 8.87755 12.1228 8.87755 12.2641C8.87755 12.4054 8.82142 12.541 8.72151 12.6409L8.05904 13.3073C7.35845 14.0073 6.40842 14.4004 5.41796 14.4001C4.42749 14.3998 3.47771 14.0061 2.77757 13.3056C2.07742 12.6052 1.68426 11.6553 1.68457 10.665C1.68488 9.67475 2.07864 8.72515 2.77923 8.02513L4.38677 6.41789C5.0597 5.74454 5.96455 5.35377 6.91618 5.32552C7.86781 5.29728 8.79426 5.63371 9.50595 6.26596C9.55846 6.31251 9.60129 6.36895 9.63199 6.43205C9.66269 6.49516 9.68065 6.56368 9.68486 6.63373C9.68907 6.70377 9.67944 6.77396 9.65652 6.84028C9.63359 6.90661 9.59783 6.96777 9.55127 7.02027C9.50471 7.07277 9.44826 7.11559 9.38515 7.14629C9.32203 7.17698 9.25349 7.19494 9.18343 7.19915C9.11337 7.20336 9.04318 7.19373 8.97684 7.17081C8.91051 7.14789 8.84934 7.11213 8.79682 7.06558C8.28851 6.61426 7.62696 6.37415 6.94745 6.39434C6.26794 6.41453 5.62182 6.6935 5.14121 7.1742L3.53368 8.77944C3.03363 9.2794 2.7527 9.9575 2.7527 10.6645C2.7527 11.3716 3.03363 12.0497 3.53368 12.5497C4.03373 13.0496 4.71195 13.3305 5.41913 13.3305C6.12632 13.3305 6.80453 13.0496 7.30459 12.5497L7.96706 11.8873C8.01658 11.8377 8.07538 11.7984 8.14011 11.7716C8.20484 11.7448 8.27422 11.731 8.34429 11.731C8.41435 11.731 8.48374 11.7448 8.54846 11.7716C8.61319 11.7984 8.67199 11.8377 8.72151 11.8873ZM13.3922 2.69165C12.6915 1.99266 11.7421 1.6001 10.7523 1.6001C9.76244 1.6001 8.81304 1.99266 8.11235 2.69165L7.44988 3.35467C7.34992 3.45469 7.2938 3.59033 7.29386 3.73172C7.29393 3.87312 7.35017 4.0087 7.45021 4.10864C7.55026 4.20858 7.68591 4.26469 7.82734 4.26463C7.96876 4.26457 8.10437 4.20834 8.20433 4.10831L8.8668 3.44596C9.36686 2.94599 10.0451 2.66512 10.7523 2.66512C11.4594 2.66512 12.1377 2.94599 12.6377 3.44596C13.1378 3.94592 13.4187 4.62401 13.4187 5.33107C13.4187 6.03812 13.1378 6.71622 12.6377 7.21618L11.0302 8.82475C10.5493 9.30517 9.90304 9.58378 9.22354 9.60359C8.54403 9.62341 7.88263 9.38295 7.37457 8.93137C7.32205 8.88481 7.26088 8.84906 7.19455 8.82614C7.12821 8.80322 7.05801 8.79359 6.98796 8.7978C6.84647 8.8063 6.71416 8.87064 6.62012 8.97668C6.57356 9.02918 6.53779 9.09034 6.51487 9.15666C6.49195 9.22299 6.48232 9.29317 6.48653 9.36322C6.49503 9.50468 6.55938 9.63697 6.66544 9.73099C7.37696 10.3634 8.30332 10.7001 9.25494 10.6721C10.2066 10.6441 11.1115 10.2536 11.7846 9.58039L13.3922 7.97315C14.0917 7.27254 14.4846 6.32303 14.4846 5.33307C14.4846 4.34311 14.0917 3.3936 13.3922 2.69298V2.69165Z" />
                      </svg>
                    </button>
                  </div>
                  <div v-if="type === 'upcoming' && item.status !== 'cancelled' && item.status != 'ended'">
                    <button @click="toggleMenu(index)" :data-button-id="index"
                      class="border border-bg-900 bg-bg-1000 rounded-lg dark:bg-bg-50 p-2 w-8 flex items-center justify-center">
                      <svg class="h-4 w-4" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <circle cx="8" cy="3" r="1.5" class="fill-bg-default dark:fill-bg-950" />
                        <circle cx="8" cy="8" r="1.5" class="fill-bg-default dark:fill-bg-950" />
                        <circle cx="8" cy="13" r="1.5" class="fill-bg-default dark:fill-bg-950" />
                      </svg>
                    </button>

                    <!-- Popup menu -->
                    <div v-if="openMenus[index]" :data-menu-id="index"
                      class="absolute right-0 mt-2 w-40 rounded-md shadow-lg bg-white dark:bg-bg-800 ring-1 ring-black ring-opacity-5 z-100">
                      <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                        <button v-if="item.status == 'scheduled'"
                          class="w-full flex justify-between text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-bg-700 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                          role="menuitem" :disabled="isLoading.cancel" @click="HandleCancelSession(item._id)">
                          <template v-if="isLoading.cancel">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700 dark:text-gray-200 inline-block"
                              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                              </circle>
                              <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                              </path>
                            </svg>
                            Cancelling...
                          </template>
                          <template v-else>
                            <svg width="14" height="18" viewBox="0 0 16 18" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path
                                d="M6.33333 1L5.74436 1.20786C3.45215 2.01689 2.30603 2.4214 1.65301 3.34437C1 4.26734 1 5.48276 1 7.9136V10.0864C1 12.5172 1 13.7326 1.65301 14.6556C2.30603 15.5786 3.45215 15.9831 5.74436 16.7922L6.33333 17"
                                stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" />
                              <path
                                d="M15.2219 9.00005H6.33301M15.2219 9.00005C15.2219 8.37765 13.4492 7.21475 12.9997 6.77783M15.2219 9.00005C15.2219 9.62245 13.4492 10.7854 12.9997 11.2223"
                                stroke="#5B616E" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>

                            Cancel Session
                          </template>
                        </button>
                        <button v-if="item.status == 'on-going' || item.status == 'active' || item.status == 'on-hold'"
                          class="w-full flex justify-around text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-bg-700 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
                          role="menuitem" :disabled="isLoading.end" @click="HandleEndSession(item._id)">
                          <template v-if="isLoading.end">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-700 dark:text-gray-200 inline-block"
                              xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                              </circle>
                              <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                              </path>
                            </svg>
                            Ending...
                          </template>
                          <template v-else>
                            <svg width="14" height="18" viewBox="0 0 14 14" fill="none"
                              xmlns="http://www.w3.org/2000/svg">
                              <path d="M13 1L1 13M1 1L13 13" stroke="#5B616E" stroke-width="1.5" stroke-linecap="round"
                                stroke-linejoin="round" />
                            </svg>
                            End Session
                          </template>
                        </button>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Grid view -->
        <div v-else class="grid-container ">
          <div class="hidden sm:grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            <CardComponent v-for="scheduleItem in sessions" :key="scheduleItem._id" :item="scheduleItem"
              :fields="sessionFields" :actions="filteredActions(scheduleItem)" :highlight="highlight"
              :organization-store="Organizationstore" :is-mobile="false" :session-type="type" @action="handleCardAction"
              @field-click="handleFieldClick" sceneType="schedule" />
          </div>
        </div>

        <!-- Mobile view -->
        <div class="sm:hidden space-y-4 overflow-y-auto !h-full pl-4 pr-[21px]">
          <CardComponent v-for="scheduleItem in sessions" :key="scheduleItem._id" :item="scheduleItem"
            :fields="sessionFields" :actions="filteredActions(scheduleItem)" :highlight="highlight"
            :organization-store="Organizationstore" :is-mobile="true" :session-type="type" @action="handleCardAction"
            @field-click="handleFieldClick" sceneType="schedule" />
        </div>
      </div>

      <!-- No Schedule -->
      <div v-else class="w-full" :class="loader && 'hidden'">
        <div class="w-fit m-auto">
          <Noschedule />
        </div>
        <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">UH-OH, THERE ARE NO SESSIONS BOOKED
        </div>
        <router-link to="/schedules/create">
          <Button class="mt-8 mx-auto" title="Create Schedule" theme="primary">
            <template v-slot:svg>
              <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_306_21007)">
                  <path class="fill-txt-1000 dark:fill-txt-default"
                    d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                  <path class="fill-txt-1000 dark:fill-txt-default"
                    d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                </g>
                <defs>
                  <clipPath id="clip0_306_21007">
                    <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                  </clipPath>
                </defs>
              </svg>
            </template>
          </Button>
        </router-link>
      </div>
    </div>
  </div>
  <!-- Filter Bottom Sheet -->
  <BottomSheet v-model="showFilterBottomSheet" title="Filter Schedule" :show-search="true"
    search-placeholder="Search Users" :tabs="filterTabs" :options="filterOptions" type="filter"
    :selectedValues="tempFilterValues" @update:selectedValues="(val) => tempFilterValues = val"
    @apply="handleFilterApply" @clear="handleFilterClear" @close="closeFilterBottomSheet">
    <template #title>
      <div class="flex items-center gap-2">
        <h3 class="text-base font-semibold text-txt-50 dark:text-txt-950">Filter Schedule</h3>
        <span v-if="filterCount > 0"
          class="inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-full">
          {{ filterCount }}
        </span>
      </div>
    </template>

    <template #close-icon>
      <svg width="18" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path class="fill-txt-1000 dark:fill-txt-default" d="M12 4L4 12M4 4L12 12" stroke="currentColor"
          stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </template>

    <template #search-icon>
      <svg class="w-4 h-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
      </svg>
    </template>
  </BottomSheet>
  <!-- participants in mobile view-->
  <BottomSheet v-model="showParticipantsSheet" type="list" title="Participants">
    <template #close-icon>
      <svg width="18" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path class="fill-txt-1000 dark:fill-txt-default" d="M12 4L4 12M4 4L12 12" stroke="currentColor"
          stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
      </svg>
    </template>

    <template #content>
      <div class="divide-y divide-gray-200 dark:divide-gray-700">
        <div v-for="participant in selectedParticipants" :key="participant.email" class="px-4 py-4">
          <!-- Participant Name -->
          <div class="flex flex-col gap-1">
            <p class="text-base font-medium text-gray-900 dark:text-white capitalize">
              {{ participant.name }}
            </p>
            <!-- Email -->
            <p class="text-sm text-gray-600 dark:text-gray-400">
              {{ participant.email }}
            </p>
            <!-- Phone -->
            <p v-if="participant.phone_number" class="text-sm text-gray-600 dark:text-gray-400">
              {{ participant.phone_number }}
            </p>
          </div>
        </div>
      </div>
    </template>
  </BottomSheet>

  <!-- lead modal -->
  <div v-if="showModal" class="fixed inset-0 overflow-y-auto z-50 flex items-center justify-center px-4 sm:px-0">
    <div class="fixed inset-0 transition-opacity" aria-hidden="true" @click="closeModal">
      <div class="absolute inset-0 bg-black opacity-75"></div>
    </div>
    <div
      class="relative z-50 bg-white dark:bg-bg-1000 rounded-lg w-full max-w-lg sm:max-w-3xl mx-auto overflow-hidden shadow-xl"
      style="max-height: 90vh; display: flex; flex-direction: column;">
      <div class="p-4 flex-shrink-0">
        <div class="flex justify-between items-center">
          <p class="font-bold text-lg text-txt-50 dark:text-txt-950">Participants</p>
          <button @click="closeModal"
            class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>
      <div class="overflow-y-auto flex-grow">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-bg-200">
          <thead class="bg-gray-50 dark:bg-bg-700 sticky top-0">
            <tr>
              <th scope="col"
                class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Participant Name</th>
              <th scope="col"
                class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Email</th>
              <th scope="col"
                class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                Phone</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200 dark:bg-bg-800 dark:divide-bg-200">
            <tr v-for="(participant, index) in modalContent" :key="index"
              class="hover:bg-gray-100 dark:hover:bg-bg-700">
              <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-200 capitalize">
                {{ participant.name }}</td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ participant.email }}</td>
              <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                {{ participant.phone_number }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<style scoped>
.multiselect__single {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  projects: { type: Array, default: () => [] },
});

const emit = defineEmits(['selectProject']);
const searchQuery = ref('');

// Use computed property for filtered projects based on search
const filteredProjects = computed(() => {
  const query = searchQuery.value.toLowerCase().trim();
  if (!query) {
    return props.projects;
  }

  return props.projects.filter((project) =>
    project.name.toLowerCase().includes(query),
  );
});

function handleProjectSelect (project) {
  console.log(project);
  emit('selectProject', project);
}
</script>

<template>
  <div class="project-selection flex flex-col sm:max-h-[85vh] h-full">
    <!-- Search Bar -->
    <div  v-if="projects.length">
      <div class="mb-4 relative">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search Project"
          class="w-full px-3 py-1.5 bg-bg-950 rounded-[18px] placeholder:text-left placeholder:text-sm text-sm leading-snug  outline-none transition-all"
        >
        <div
          class="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer text-gray-500 hover:text-gray-700 z-10"
        >
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M10.3453 11.7425C9.02362 12.7107 7.38492 13.1444 5.75708 12.9567C4.12923 12.769 2.6323 11.9739 1.56576 10.7303C0.499223 9.48666 -0.0582671 7.88635 0.00482557 6.2495C0.0679182 4.61265 0.74694 3.05997 1.90604 1.90209C3.06515 0.744221 4.61885 0.0665459 6.25631 0.00464636C7.89378 -0.0572531 9.49424 0.501188 10.7375 1.56825C11.9808 2.63531 12.7752 4.13229 12.9617 5.7597C13.1483 7.38711 12.7133 9.02494 11.7438 10.3455V10.3445C11.7838 10.3745 11.8218 10.4065 11.8588 10.4425L15.71 14.2924C15.8977 14.4799 16.0032 14.7342 16.0033 14.9995C16.0034 15.2647 15.8981 15.5192 15.7105 15.7068C15.5229 15.8944 15.2685 15.9999 15.0031 16C14.7378 16.0001 14.4833 15.8948 14.2956 15.7073L10.4444 11.8575C10.4082 11.8217 10.3747 11.7833 10.3443 11.7425H10.3453ZM6.50016 12.0004C7.22265 12.0004 7.93807 11.8582 8.60557 11.5818C9.27306 11.3054 9.87957 10.9003 10.3904 10.3896C10.9013 9.87889 11.3066 9.2726 11.5831 8.60533C11.8596 7.93807 12.0019 7.2229 12.0019 6.50065C12.0019 5.77841 11.8596 5.06324 11.5831 4.39597C11.3066 3.72871 10.9013 3.12242 10.3904 2.61171C9.87957 2.10101 9.27306 1.6959 8.60557 1.41951C7.93807 1.14312 7.22265 1.00086 6.50016 1.00086C5.04101 1.00086 3.64164 1.5803 2.60987 2.61171C1.5781 3.64312 0.998456 5.04202 0.998456 6.50065C0.998456 7.95929 1.5781 9.35818 2.60987 10.3896C3.64164 11.421 5.04101 12.0004 6.50016 12.0004Z" fill="#5B616E"/>
        </svg>
        </div>
      </div>
      <!-- No Results Message -->
      <div
        v-if="filteredProjects.length === 0"
        class="text-center py-8 text-gray-500"
      >
        No projects found matching "{{ searchQuery }}"
      </div>
    </div>

    <!-- Projects Grid -->
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 overflow-y-auto h-full sm:max-h-[55vh] px-1 py-2">
      <div
        v-for="project in filteredProjects"
        :key="project._id"
        @click="handleProjectSelect(project)"
        class="cursor-pointer hover:shadow-lg border-4 hover:border-4 hover:border-blue-500 rounded-lg overflow-hidden h-40 relative group"
      >
        <div class="relative w-full h-full">
          <img
            :src="project.project_thumbnail || 'https://firebasestorage.googleapis.com/v0/b/realvr-eb62c.appspot.com/o/CreationtoolAssets%2Fsiteassets%2Fplaceholder.jpg?alt=media'"
            :alt="project.name"
            class="object-cover w-full h-full rounded"
          >
          <div class="absolute inset-0 bg-black bg-opacity-50 flex flex-col justify-center items-center transition-all duration-300">
            <p class="text-white text-md font-semibold text-center px-2 leading-tight">
              {{ project.name }}
            </p>
            <p
              v-if="project.projectSettings?.salestool?.default_experience?.toLowerCase() === 'pixel_streaming'"
              class="text-white text-xs font-medium mt-1"
            >
              Metaverse
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.project-selection {
  max-height: 85vh;
}
</style>

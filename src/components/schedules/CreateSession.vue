<script setup>
import { Form, Field, ErrorMessage } from 'vee-validate';
import { ref, onMounted, watch, computed  } from 'vue';
import Modal from '../common/Modal/Modal.vue';
import { Org_Store } from '../../store/organization';
import { createSession } from '../../api/sessions';
import Multiselect from 'vue-multiselect';
import { getListofUnits } from '../../api/projects/units';
import FormField from '../common/FormField.vue';
import Button from '../common/Button.vue';
import { createSessionSchema, createSessionSchemaTesting } from '../../validationSchema/session';
import { useRouter } from 'vue-router';
import { CreateLead } from '../../api/leads';
import ProjectSelection from './ProjectSelection.vue';
import Navbar from '../common/Navbar.vue';

const loader = ref(false);
const selectedProject = ref(null);
const selectedUnit = ref(null);
const experinceType = ref(null);
const listOfProjects = ref(null);
const listofUnits = ref([]);
const router = useRouter();
const projectTag = ref();
const organizationstore = Org_Store();
organizationstore.RefreshProjects();

if (organizationstore.projects) {
  listOfProjects.value = Object.values(organizationstore.projects);
  console.log('typeof: ', Array.isArray(listOfProjects.value));
}

function getUnits (id) {
  getListofUnits(id).then((res) => {
    listofUnits.value = Object.values(res);
  });
}
const isTestingSession = computed(() => {
  return projectTag.value?.toLowerCase() === 'testing';
});
const dynamicValidationSchema = computed(() => {
  if (!isTestingSession.value) {
    // console.log("Testing session nhii")
    return createSessionSchema;
  }
  // console.log("Testing hai")
  return createSessionSchemaTesting;

});
const showProjectSelection = ref(true);
const filteredProjects = ref([]);
console.log('List of projects', listOfProjects);

// Function selectProject (val) {
//   If (val) {
//     If (
//       Object.prototype.hasOwnProperty.call(val, 'projectSettings') &&
//       Object.prototype.hasOwnProperty.call(val.projectSettings, 'salestool') &&
//       Object.prototype.hasOwnProperty.call(val.projectSettings.salestool, 'default_experience')
//     ) {
//       If (
//         Val.projectSettings.salestool.default_experience.toLowerCase() === 'ale'
//       ) {
//         ExperinceType.value = 'ale';
//       } else if (
//         Val.projectSettings.salestool.default_experience.toLowerCase() ===
//         'pixel_streaming'
//       ) {
//         ExperinceType.value = 'pixel_streaming';
//       }
//       GetUnits(val._id);
//     }
//   } else {
//     SelectedProject.value = null;
//   }
// }

function selectProject (project) {
  selectedProject.value = project;
  showProjectSelection.value = false;
  if (project) {
    if (
      project.projectSettings?.salestool?.default_experience
    ) {
      experinceType.value = project.projectSettings.salestool.default_experience.toLowerCase();
    }
    getUnits(project._id);
  }
}

function backToProjectSelection () {
  if (showProjectSelection.value && !selectedProject.value){
    router.go(-1);
  }
  showProjectSelection.value = true;
  selectedProject.value = null;
  projectTag.value = null;
  experinceType.value = null;
  selectedUnit.value = null;
}
// Function performSearch() {
//   If (!searchQuery.value) {
//     FilteredProjects.value = listOfProjects.value;
//   } else {
//     FilteredProjects.value = listOfProjects.value.filter(project =>
//       Project.name.toLowerCase().includes(searchQuery.value.toLowerCase())
//     );
//   }
// }
// Function clearSearch() {
//   SearchQuery.value = '';
//   FilteredProjects.value = listOfProjects.value;
// }
// const addTag = (newTag) => { // To add new category if it is not in the
//   const tag = newTag;
//   // Adding to list
//   selectedProject.value.projectSettings.salestool.tags.push(newTag);
//   projectTag.value = tag; // Selecting same new tag
// };

const scheduleSession = (val) => {
  loader.value = true;
  const sessionObject = {
    project_id: val.project._id,
    type: experinceType.value,
    source: 'dashboard',
    is_scheduled: false,
    schedule_time: new Date().toISOString(),
    description: val.description ? val.description : '',
    ...(val.unit && { 'unit': val.unit }),
    ...(val.tag && { 'tag': val.tag }),
  };

  if (sessionObject) {
    createSession(sessionObject)
      .then((sessionResponse) => {
        const previewUrl = '/salestool/preview/' + sessionResponse._id;

        const openNewTab = (url) => {
          const newWindow = window.open(url, '_blank');

          if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
            window.location.href = url;

            const link = document.createElement('a');
            link.href = url;
            link.target = '_blank';
            link.rel = 'noopener,noreferrer';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
          }
        };

        if (val.name && val.email) {
          const leadObject = {
            ...(val.name && { 'name': val.name }),
            ...(val.email && { 'email': val.email }),
            ...(val["phone number"] && { 'phone_number': val["phone number"] }),
            type: val.unit ? 'unit' : 'project',
            project_id: val.project._id,
            session_id: sessionResponse._id,
            source: 'sales_session',
            ...(val.unit && { 'unit_id': val.unit._id }),
          };

          if (!isTestingSession.value) {
            CreateLead(leadObject)
              .then(() => {
                loader.value = false;
                organizationstore.createSession = true;
                openNewTab(previewUrl);
                router.go(-1);
              })
              .catch((err) => {
                console.log('Error creating lead: ', err);
                loader.value = false;
              });
          } else {
            loader.value = false;
            organizationstore.createSession = true;
            router.go(-1);
          }
        } else {
          router.push('/schedules');
          openNewTab(previewUrl);
        }
      })
      .catch((err) => {
        console.log('Error creating session: ', err);
        loader.value = false;
      });
  }
};

const loadProjects = async () => {
  await organizationstore.RefreshProjects();
  if (organizationstore.projects) {
    listOfProjects.value = Object.values(organizationstore.projects);
    filteredProjects.value = listOfProjects.value;
  }
};

watch(() => organizationstore.projects, (newProjects) => {
  if (newProjects) {
    listOfProjects.value = Object.values(newProjects);
    filteredProjects.value = listOfProjects.value;
  }
});

onMounted(() => {
  loadProjects();
});
</script>

<template>
  <Modal :open="true" :preventOverflow="true">
    <div
    class="relative transform rounded-b-none sm:rounded-t-lg sm:rounded-b-lg bg-bg-1000 text-left shadow-xl transition-all sm:my-8 w-full sm:max-w-3xl h-full sm:h-fit sm:max-h-[80vh] flex flex-col sm:px-2 sm:py-2">
    <div class="sm:hidden block">
      <Navbar/>
    </div>
      <div class="p-2 sm:h-full overflow-y-scroll overflow-x-hidden flex flex-col">
        <div class="mb-1 mr-0">
          <div class="flex justify-between items-center">
            <div class="flex gap-3 items-center mb-0.5">
              <button class="block sm:hidden"
                @click="backToProjectSelection">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12.0054C5 12.2216 5.07447 12.4054 5.23404 12.5568L13.7447 20.7838C13.883 20.9243 14.0638 21 14.266 21C14.6809 21 15 20.6865 15 20.2541C15 20.0486 14.9149 19.8649 14.7979 19.7351L6.79787 12.0054L14.7979 4.27568C14.9149 4.13514 15 3.95135 15 3.74595C15 3.32432 14.6809 3 14.266 3C14.0638 3 13.883 3.07568 13.7447 3.21622L5.23404 11.4541C5.07447 11.6054 5 11.7892 5 12.0054Z" fill="black"/>
                </svg>
              </button>
              <h1 class="font-bold mt-1">{{ showProjectSelection ? 'Select Project' : 'Create a Session' }}</h1> <!-- Made heading bold -->
            </div>
            <button class="p-1 rounded cursor-pointer hover:bg-bg-900 hidden sm:block" @click="backToProjectSelection">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="{1.5}"
                stroke="currentColor" class="w-6 h-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <p v-if="!showProjectSelection" class="text-sm mt-1 mb-3 sm:mb-2">Leave your client's contact information to create a session</p>
          <p v-else class="text-sm mt-1 mb-3">Select a project to create an instant session ({{ filteredProjects?.length }} projects)</p>
          <div v-if="selectedProject" class="sm:hidden block mb-2">
            <p class="text-xs text-gray-600 mb-2">Selected project</p>
            <p class="">{{selectedProject.name}}</p>
          </div>
        </div>

        <div :class="showProjectSelection && 'overflow-y-hidden'" class="h-full sm:h-auto">
          <ProjectSelection
            v-if="showProjectSelection && filteredProjects.length > 0"
            :projects="filteredProjects"
            @select-project="selectProject"
          />
          <Form v-else class="form flex flex-col" @submit="scheduleSession" :validation-schema="dynamicValidationSchema">
            <div v-if="listOfProjects" class="form-item relative sm:mb-6">
              <!-- select tags -->
            <div v-if="selectedProject" class="form-item relative mb-4">
            <Field name="tag" v-slot="{ field }" :model-value="projectTag">
              <Multiselect
                class="mmulti"
                v-model="projectTag"
                v-bind="field"
                placeholder="Select a tag"
                :options="selectedProject ? selectedProject.projectSettings.salestool.tags : []"
                :multiple="false"
                :taggable="false"
                :maxHeight="150"
              >
              </Multiselect>
            </Field>
            <ErrorMessage name="tag" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
              </svg>
              <span class="text-xs font-normal text-red-600 capitalize">{{ message }}</span>
            </ErrorMessage>
            </div>
              <FormField class="mb-4" label="Client Name" name="name" :required="!isTestingSession" v-if="!isTestingSession"/>
              <FormField class="mb-4" label="Client Email" type="email" name="email" :required="!isTestingSession" v-if="!isTestingSession"/>
              <FormField class="mb-4" label="Phone number" type="number" name="phone number" :required="!isTestingSession" v-if="!isTestingSession"/>
              <div class="hidden sm:block">
                <Field name="project" :model-value="selectedProject" v-slot="{ field }">
                  <Multiselect v-bind="field" :allow-empty="false" v-model="selectedProject" :searchable="true"
                    :show-labels="false" :custom-label="(val) => val.name" placeholder="Interested project *"
                    @select="selectProject" :options="listOfProjects" track-by="_id" :maxHeight="200" disabled>
                  </Multiselect>
                </Field>
              </div>

              <ErrorMessage name="project" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-1 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class="text-xs font-normal text-red-600 capitalize">
                  {{ message }}</span>
              </ErrorMessage>
            </div>

            <!-- Select untis -->

            <div v-if="listofUnits && listofUnits.length !== 0" class="form-item relative mb-4">
              <Field name="unit" :model-value="selectedUnit" v-slot="{ field }">
                <Multiselect class="mmulti" :allow-empty="false" v-bind="field" v-model="selectedUnit"
                  :show-labels="false" :searchable="true" track-by="_id" :custom-label="(val) => val.name"
                  placeholder="Interested unit" :options="listofUnits" :maxHeight="200">
                </Multiselect>
              </Field>

              <ErrorMessage name="unit" as="p" v-slot="{ message }"
                class="flex justify-start items-center gap-2 ml-1 mb-2 absolute -bottom-[27px]">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z"
                    fill="#B3261E" />
                </svg>
                <span class="text-xs font-normal text-red-600 capitalize">
                  {{ message }}</span>
              </ErrorMessage>
            </div>

            <FormField class="mb-7" label="Description" name="description" />

            <!-- submit and cancel Buttons -->
            <div class="flex gap-4 justify-center">
              <Button :disabled="loader" title="Back" type="button" class="w-full h-10 !bg-bg-1000 hidden sm:block border border-black !text-sm" theme="secondary" @handle-click="backToProjectSelection" />
              <Button :disabled="loader" :title="loader ? '' : 'Start Session'" type="submit" class="w-full !h-10 !text-sm !font-medium"
                theme="primary">
                <template v-if="loader" v-slot:svg>
                  <div class="loader"></div>
                </template>
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.loader {
  @apply w-6 h-6 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-4 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.project-selection {
  max-height: 85vh;
  overflow-y: auto;
}

.aspect-w-16,
.aspect-w-16>img {
  position: static;
  padding-bottom: 0;
  height: auto;
}
</style>

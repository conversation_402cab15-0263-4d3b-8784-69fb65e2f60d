<script setup>

import { Form, Field, ErrorMessage } from 'vee-validate';
import { ref, onMounted, watch, computed } from 'vue';
import DatePicker from '../UIElements/DatePicker.vue';
import Modal from '../common/Modal/Modal.vue';
import { Org_Store } from '../../store/organization';
import { GetAvailableSlots, createSession } from '../../api/sessions';
import Multiselect from 'vue-multiselect';
import { getListofUnits } from '../../api/projects/units/index';
import FormField from '../common/FormField.vue';
import Button from '../common/Button.vue';
import { scheduleSessionSchema, scheduleSessionSchemaTesting } from '../../validationSchema/session';
import { CreateLead } from '../../api/leads';
import { useRouter } from 'vue-router';
import Spinner from '../common/Spinner.vue';
import {formatTime } from '../../helpers/domhelper';
import ProjectSelection from './ProjectSelection.vue'; // Adjust path as needed
import Navbar from '../common/Navbar.vue';

const loader = ref(false);
const selectedProject = ref(null);
const selectedUnit = ref(null);
const date = ref(null);
const time = ref(null);
const experinceType = ref(null);
const slots = ref(null);
const listOfProjects = ref(null);
const listofUnits = ref([]);
const timezone = ref(null);
const router = useRouter();
const projectTag = ref();

const organizationstore = Org_Store();
organizationstore.RefreshProjects();

if (organizationstore.projects){
  listOfProjects.value = Object.values(organizationstore.projects);
}

async function getSlots (date, id) {
  const obj = {
    project_id: id,
    date: date,
  };

  try {
    const res = await GetAvailableSlots(obj);
    if (slots.value !== null) {
      slots.value = [];
    }

    const currentTime = new Date();
    const currentDateString = currentTime.toISOString().split('T')[0]; // Get current date in YYYY-MM-DD format
    const inputDateString = new Date(date).toISOString().split('T')[0]; // Get input date in YYYY-MM-DD format

    slots.value = res.filter((item) => {
      const slotTime = new Date(item.slot);
      const isToday = inputDateString === currentDateString;

      const isSlotTimePast = isToday && (
        slotTime.getHours() < currentTime.getHours() ||
        (slotTime.getHours() === currentTime.getHours() && slotTime.getMinutes() < currentTime.getMinutes())
      );

      return !(item.availableSessions === 0 || isSlotTimePast);
    }).sort((a, b) => {
      const aTime = new Date(a.slot);
      const bTime = new Date(b.slot);

      const aTotalMinutes = (aTime.getHours() * 60) + aTime.getMinutes();
      const bTotalMinutes = (bTime.getHours() * 60) + bTime.getMinutes();

      return aTotalMinutes - bTotalMinutes;
    });

  } catch (error) {
    slots.value = [];
  }
}

function getUnits (id){
  if (selectedUnit.value !== null || selectedUnit.value !== undefined){
    selectedUnit.value = null;
  }
  getListofUnits(id).then((res) => {
    listofUnits.value = Object.values(res);
  });
}

const showProjectSelection = ref(true);
// const searchQuery = ref('');
const filteredProjects = ref([]);

// Function selectProject (val) {
//   If (val){
//     If (slots.value !== null){
//       Slots.value = null;
//     }
//     If (Object.prototype.hasOwnProperty.call(val, 'projectSettings') &&
//         Object.prototype.hasOwnProperty.call(val.projectSettings, 'salestool') &&
//         Object.prototype.hasOwnProperty.call(val.projectSettings.salestool, 'default_experience')){
//       If (val.projectSettings.salestool.default_experience.toLowerCase() === 'ale'){
//         ExperinceType.value = 'ale';
//         Timezone.value = val.projectSettings.general.timezone || 'Asia/Kolkata';
//         Slots.value = val.projectSettings.general.slots.map((item) => {
//           Const obj = {
//             Slot: item,
//           };
//           Return obj;
//         });
//       } else if (date.value && val.projectSettings.salestool.default_experience.toLowerCase() === 'pixel_streaming'){
//         ExperinceType.value = 'pixel_streaming';
//         GetSlots(date.value, selectedProject.value._id);
//       }
//       GetUnits(val._id);
//     }
//   } else {
//     SelectedProject.value = null;
//   }
// }

function selectProject (project) {
  selectedProject.value = project;
  showProjectSelection.value = false;
  if (project) {
    if (slots.value !== null) {
      slots.value = null;
    }
    if (Object.prototype.hasOwnProperty.call(project, 'projectSettings') &&
        Object.prototype.hasOwnProperty.call(project.projectSettings, 'salestool') &&
        Object.prototype.hasOwnProperty.call(project.projectSettings.salestool, 'default_experience')) {
      if (project.projectSettings.salestool.default_experience.toLowerCase() === 'ale') {
        experinceType.value = 'ale';
        timezone.value = project.projectSettings.general.timezone || 'Asia/Kolkata';
        slots.value = project.projectSettings.general.slots.map((item) => {
          const obj = {
            slot: item,
          };
          return obj;
        });
      } else if (project.projectSettings.salestool.default_experience.toLowerCase() === 'pixel_streaming') {
        experinceType.value = 'pixel_streaming';
        // if (date.value) {
        //   getSlots(date.value, project._id);
        // }
      } else {
        experinceType.value = 'default';
      }
      getUnits(project._id);
    } else {
      experinceType.value = 'default';
    }
  } else {
    selectedProject.value = null;
    experinceType.value = null;
  }
}

// Function performSearch() {
//   If (!searchQuery.value) {
//     FilteredProjects.value = listOfProjects.value;
//   } else {
//     FilteredProjects.value = listOfProjects.value.filter(project =>
//       Project.name.toLowerCase().includes(searchQuery.value.toLowerCase())
//     );
//   }
// }

// Function clearSearch() {
//   SearchQuery.value = '';
//   FilteredProjects.value = listOfProjects.value;
// }

const isTestingSession = computed(() => projectTag.value?.toLowerCase() === 'testing');

const dynamicValidationSchema = computed(() => {
  if (isTestingSession.value) {
    return scheduleSessionSchemaTesting;
  }
  return scheduleSessionSchema;

});

function backToProjectSelection () {
  if (showProjectSelection.value && !selectedProject.value){
    router.go(-1);
  }
  showProjectSelection.value = true;
  selectedProject.value = null;
  experinceType.value = null;
  slots.value = null;
  timezone.value = null;
  listofUnits.value = [];
  projectTag.value = null;
}

function selectDate (selectedDate) {
  if (selectedDate){
    date.value = selectedDate;
    if (date.value && selectedProject.value && (selectedProject.value?.projectSettings?.salestool?.default_experience?.toLowerCase() === 'pixel_streaming')){
      slots.value = null;
      getSlots(date.value, selectedProject.value._id);
    }
  }
}

// const addTag = (newTag) => { // To add new category if it is not in the
//   const tag = newTag;
//   // Adding to list
//   selectedProject.value.projectSettings.salestool.tags.push(newTag);
//   projectTag.value = tag; // Selecting same new tag
// };

// Function meridiem (time) {
//   Return new Date(time).toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
// }

function combineDateAndTime (dateString, timeString) {
  // Split time into hours, minutes, and AM/PM
  const time = new Date(timeString).toLocaleString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true });
  const date  = dateString.slice(0, 10);
  const [timePart, period] = time.split(' ');
  const [hours, minutes] = timePart.split(':').map(Number);
  let isoDate = null;
  // Split date into day, month, and year
  const [year, month, day] = date.split('-').map(Number);
  if (period === 'AM' && hours === 12) {
    // If time is 12 AM, set hours to 0
    isoDate = new Date(year, month - 1, day, 0, minutes).toISOString();
  } else if (period === 'PM' && hours !== 12) {
    // If time is PM (not 12 PM), add 12 hours to hours
    isoDate = new Date(year, month - 1, day, hours + 12, minutes).toISOString();
  } else {
    // Otherwise, use the given hours
    isoDate = new Date(year, month - 1, day, hours, minutes).toISOString();
  }
  return isoDate;
  // Console.log(dateString);
  // Let date = `${dateString.slice(0,10)}T${timeString.slice(11,25)}`
  // // let [hours, minutes] = timeString.split(':').map(Number);
  // // date.setHours(hours);
  // // date.setMinutes(minutes);
  // Return date;
}

const scheduleSession = (val) => {
  loader.value = true;
  const scheduleTime = combineDateAndTime(val.sessionDate, val.time.slot);
  const sessionObject = {
    project_id: val.project._id,
    type: experinceType.value,
    source: 'dashboard',
    is_scheduled: true,
    schedule_time: scheduleTime,
    description: val.description ? val.description : '',
    ...(val.tag && {'tag': val.tag}),
  };

  if (sessionObject){
    createSession(sessionObject)
      .then((sessionResponse) => {
        const leadObject = {
          name: val.name,
          email: val.email,
          phone_number: val['phone number'] ? val['phone number'] : '',
          type: val.unit ? 'unit' : 'project',
          project_id: val.project._id,
          session_id: sessionResponse._id,
          source: 'sales_session',
          ...(val.unit && {'unit_id': val.unit._id}),
        };
        if (!isTestingSession.value){
          CreateLead(leadObject)
            .then(() => {
              loader.value = false;
              organizationstore.createSession = true;
              console.log(organizationstore.createSession);
              router.go(-1);
            })
            .catch((err) => {
              console.log('Error creating lead: ', err);
              loader.value = false;
            });
        } else {
          loader.value = false;
          organizationstore.createSession = true;
          console.log(organizationstore.createSession);
          router.go(-1);
        }
        // Redirect back in Vue router (if used)
      })
      .catch((err) => {
        console.log('Error creating session: ', err);
        loader.value = false;
      });
  }
};
const loadProjects = async () => {
  await organizationstore.RefreshProjects();
  if (organizationstore.projects) {
    listOfProjects.value = Object.values(organizationstore.projects);
    filteredProjects.value = listOfProjects.value;
  }
};

watch(() => organizationstore.projects, (newProjects) => {
  if (newProjects) {
    listOfProjects.value = Object.values(newProjects);
    filteredProjects.value = listOfProjects.value;
  }
});
onMounted(() => {
  loadProjects();
});
</script>

<template>
  <Modal :open="true" :preventOverflow = true>
    <div
      class="relative transform rounded-b-none sm:rounded-t-lg sm:rounded-b-lg bg-bg-1000 text-left shadow-xl transition-all sm:my-8 w-full sm:max-w-3xl h-full sm:h-fit sm:max-h-[80vh] flex flex-col sm:px-1.5 sm:py-2">
      <div class="sm:hidden block">
        <Navbar/>
      </div>
      <div class="p-2 sm:p-6 h-fit max-h-full overflow-y-scroll overflow-x-hidden flex flex-col">
        <div class="mb-1 mr-0">
          <div class="flex justify-between items-center mb-1">
            <div class="flex gap-3 items-center mb-0.5">
              <button class="cursor-pointer block sm:hidden" @click="backToProjectSelection">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M5 12.0054C5 12.2216 5.07447 12.4054 5.23404 12.5568L13.7447 20.7838C13.883 20.9243 14.0638 21 14.266 21C14.6809 21 15 20.6865 15 20.2541C15 20.0486 14.9149 19.8649 14.7979 19.7351L6.79787 12.0054L14.7979 4.27568C14.9149 4.13514 15 3.95135 15 3.74595C15 3.32432 14.6809 3 14.266 3C14.0638 3 13.883 3.07568 13.7447 3.21622L5.23404 11.4541C5.07447 11.6054 5 11.7892 5 12.0054Z" fill="black"/>
                </svg>
              </button>
              <h1 class="font-bold">{{ showProjectSelection ? 'Select Project' : 'Schedule a Session' }}</h1>
            </div>
            <button class="p-1 rounded cursor-pointer hover:bg-bg-900 sm:block hidden" @click="backToProjectSelection">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth="{1.5}"
                stroke="currentColor" class="w-6 h-6">
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <h1 v-if="showProjectSelection" class="text-sm text-gray-600 mb-3 sm:mb-2">Select a project to schedule and plan a session ({{ filteredProjects?.length }} projects)</h1>
          <h1 v-else class="text-sm text-gray-600 mb-3">Leave your client's contact information to schedule a session</h1>
          <div v-if="selectedProject" class="sm:hidden block mb-2">
            <p class="text-xs text-gray-600 mb-2">Selected project</p>
            <p class="">{{selectedProject.name}}</p>
          </div>
        </div>
        <div :class="showProjectSelection && 'overflow-y-hidden'" class="h-full sm:h-auto">
          <ProjectSelection
            v-if="showProjectSelection"
            :projects="filteredProjects"
            @select-project="selectProject"
          />
        <Form v-else class="form flex flex-col" @submit="scheduleSession" :validation-schema="dynamicValidationSchema">

          <!-- select tags -->
          <div v-if="selectedProject" class="form-item relative mb-4">
          <Field name="tag" v-slot="{ field }" :model-value="projectTag">
            <Multiselect
              class="mmulti"
              v-model="projectTag"
              v-bind="field"
              placeholder="Select a tag"
              :options="selectedProject ? selectedProject.projectSettings.salestool.tags : []"
              :multiple="false"
              :taggable="false"
              :maxHeight="200"
            >
            </Multiselect>
          </Field>
          <ErrorMessage name="tag" as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-3 mb-2 absolute -bottom-[27px]">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
            </svg>
            <span class="text-xs font-normal text-red-600 capitalize">{{ message }}</span>
          </ErrorMessage>
          </div>
            <FormField class="mb-7" label="Client Name*" name="name" v-if="!isTestingSession"/>

            <FormField class="mb-7" label="Enter email Id*" type="email" name="email" v-if="!isTestingSession"/>

            <FormField class="mb-7" label="Phone number" type="number" v-if="!isTestingSession" name="phone number" />

          <div v-if="listOfProjects" class="form-item relative mb-4 hidden sm:block">

            <Field name="project" :model-value="selectedProject" v-slot="{ field }">
              <Multiselect v-bind="field" :allow-empty="false" v-model="selectedProject" :searchable="true" :show-labels="false" :custom-label="(val) => val.name" placeholder="Interested project *" @select="selectProject" @remove="slots=null" :options="listOfProjects" track-by="_id" :maxHeight="250" disabled >
              </Multiselect>
            </Field>

              <ErrorMessage name="project"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-1 mb-2 absolute -bottom-[27px]">
                                                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                          <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                          </svg>
                                                          <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
              </ErrorMessage>

          </div>

          <!-- Select untis -->

          <div v-if="listofUnits && listofUnits.length !== 0" class="form-item relative mb-4">

            <Field name="unit" :model-value="selectedUnit" v-slot="{ field }">
              <Multiselect class="mmulti" :allow-empty="false" v-bind="field" v-model="selectedUnit" :searchable="true" :show-labels="false" track-by="_id" :custom-label="(val) => val.name" placeholder="Interested unit" :options="listofUnits" :maxHeight="250"  >
              </Multiselect>
            </Field>

            <ErrorMessage name="unit"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-1 mb-2 absolute -bottom-[27px]">
                                                                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                                </svg>
                                                                <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
            </ErrorMessage>

          </div>

        <FormField class="mb-7" label="Description" name="description"/>
        <div class="mb-7 relative">
                <Field name="sessionDate" v-model="date" v-slot="{ field }">
                  <DatePicker :showLabelHigh="true" v-bind="field" @select-date="selectDate"  label="Enter session Date - dd/mm/yy" css_id="picker" :required="true"  />
                </Field>
                <ErrorMessage name="sessionDate"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-1 mb-2 absolute -bottom-[27px]">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                  </svg>
                  <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
                </ErrorMessage>
          </div>
        <!-- Slots -->
        <div v-if="selectedProject" class="form-item relative mb-4">
          <div class="flex flex-col gap-[13px]">
            <p class="text-sm not-italic font-medium text-txt-default">Time Slot<span style="color:red">*</span></p>
            <div v-if="slots==null && date && selectedProject" class="w-full flex">
              <Spinner class="m-auto"/>
            </div>
            <div v-if=" slots != null && slots.length" class="relative">
              <Field name="time" :model-value="time" v-slot="{ field }">
                <!-- DO NOT CHANGE -->
                <multiselect
                  class="mmulti"
                  v-model="time"
                  v-bind="field"
                  :allow-empty="false"
                  :options="slots"
                  :searchable="true"
                  :show-labels="false"
                  placeholder="Select Time"
                  :custom-label="val => formatTime(val.slot, timezone)"
                  :maxHeight="250"
                >
                  <template v-slot:option="{ option }">
                    <div class="flex items-center justify-between">
                      <span>{{ formatTime(option.slot, timezone) }}</span>
                      <span v-if="option.availableSessions || option.availableSessions >= 0" class="w-[8.125rem] font-medium text-txt-700 text-sm">
                        Available Slot: {{ option.availableSessions }}
                      </span>
                    </div>
                  </template>
                </multiselect>
              </Field>
              <ErrorMessage name="time"  as="p" v-slot="{ message }" class="flex justify-start items-center gap-2 ml-1 mb-2 absolute -bottom-[27px]">
                                                          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                          <path fill-rule="evenodd" clip-rule="evenodd" d="M8 0C3.584 0 0 3.584 0 8C0 12.416 3.584 16 8 16C12.416 16 16 12.416 16 8C16 3.584 12.416 0 8 0ZM7.2 12V10.4H8.8V12H7.2ZM7.2 4V8.8H8.8V4H7.2Z" fill="#B3261E"/>
                                                          </svg>
                                                          <span  class=" text-xs font-normal  text-red-600 capitalize "> {{ message }}</span>
              </ErrorMessage>
            </div>
            <div v-if="slots != null && slots.length == 0 " class="text-bg-550 text-sm">
              * No slots available for the selected date or experience. *
            </div>
          </div>
        </div>

          <!-- submit and cancel Buttons -->
          <div class="flex justify-center gap-3">
            <Button :disabled="loader" title="Back" type="button" class="w-full h-10 !bg-bg-1000 hidden sm:block border border-black !text-sm" theme="secondary" @handle-click="backToProjectSelection" />
            <Button :disabled="loader" :title="loader ? '' : 'Schedule session'" type="submit" class="w-full !h-10 !text-sm !font-medium" theme="primary">
              <template v-if="loader" v-slot:svg>
                <div class="loader"></div>
              </template>
            </Button>
          </div>
        </Form>
        </div>
    </div>
  </div>
  </Modal>
</template>

<style scoped>
.project-selection {
  max-height: 85vh;
  overflow-y: auto;
}

.aspect-w-16,
.aspect-w-16 > img {
  position: static;
  padding-bottom: 0;
  height: auto;
}
.loader{
  @apply w-6 h-6 animate-[spin_2s_linear_infinite] rounded-[50%] border-t-[white] border-4 border-solid border-[#4e4c4c];
  -webkit-animation: spin 2s linear infinite;
  /* Safari */
  }

  @-webkit-keyframes spin {
    0% { -webkit-transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>

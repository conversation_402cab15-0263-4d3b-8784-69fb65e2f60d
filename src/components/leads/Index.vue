<script setup>
import Button from '../../components/common/Button.vue';
import { ref, computed, watch } from 'vue';
import { Org_Store } from '../../store/organization';
import { getListofUnits } from '../../api/projects/units';
import { GetLeads, DeleteLead, CreateLead } from '../../api/leads/index.ts';
import { tableViewStatusTypes } from '../../enum';
import { UserStore } from '../../store';
import EditDeleteButton from '../UIElements/EditDeleteButton.vue';
import { useRouter } from 'vue-router';
import DeleteModalContent from '../common/ModalContent/DeleteModalContent.vue';
import Modal from '../common/Modal/Modal.vue';
import { uiOperations } from '../../store/uiOperations';
// import BreadCrumb from '../UIElements/BreadCrumb.vue';
import ToggleSliderButton from '../common/ToggleSliderButton.vue';
import NewDropDown from '../common/NewDropDown.vue';
import Noschedule from '../../assets/svgs/Noschedule.vue';
import LeadsCard from './LeadsCard.vue';
import LoaderComp from '../common/LoaderComp.vue';

const OrganizationStore = Org_Store();
const userStore = UserStore();
const uiStore = uiOperations();
const leads = ref(null);
const router = useRouter();
const columnData = ref(['S.no', 'name', 'email', 'phone number', 'status', 'units', 'project', 'Agent', '', '  ']);
OrganizationStore.RefreshProjects();
const selectedLead = ref();
const openDeleteModal = ref(false);
const deleteLoader = ref(false);
const showFilterBottomSheet = ref(false);
const loader = ref(false);
const activeFilter = ref('users');

const selectedUsers = ref([]);
const selectedStatuses = ref([]);
const selectedProjects = ref([]);

const selectedIndex = ref(null); // Tracks the open dropdown
const isDropdownOpen = ref(false);
const units = ref({});

const getListOfAllUnits = (projectId) => {
  getListofUnits(projectId).then((res) => {
    units.value = { ...units.value, ...res };
  });
};

const toggleDropdown = (index) => {
  isDropdownOpen.value = !isDropdownOpen.value;
  // Toggle dropdown visibility: If the dropdown is already open, close it; otherwise, open it
  selectedIndex.value = selectedIndex.value === index ? null : index;
};

const connect = (method, phoneNumber, email) => {
  if (method === 'WhatsApp') {

    const formattedPhone = `+${phoneNumber}`;

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    if (isMobile) {

      window.location.href = `https://wa.me/${formattedPhone}`;
    } else {
      // For desktop, use the WhatsApp Web link
      window.open(`https://web.whatsapp.com/send?phone=${formattedPhone}`, '_blank');
    }

  } else if (method === 'Call') {
    const formattedPhone = `tel:${phoneNumber}`;
    window.location.href = formattedPhone;
  } else if (method === 'Email') {
    // Ensure the email is encoded correctly
    const encodedEmail = encodeURIComponent(email);

    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    if (isMobile) {
      // For mobile, open the default email app with the email address
      const mailToLink = `mailto:${email}`;
      window.location.href = mailToLink;
    } else {
      // For desktop, open Gmail's compose window in a new tab
      const subject = "Subject"; // You can customize this or make it dynamic
      const body = "Body content here"; // You can customize this or make it dynamic
      const emailLink = `https://mail.google.com/mail/?view=cm&fs=1&to=${encodedEmail}&su=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
      window.open(emailLink, '_blank');
    }
  }

  dropdownOpen.value = false; // Close the dropdown after selection
};

async function importLeadsFromCSV (event) {
  const file = event.target?.files?.[0];
  if (!file) {
    return;
  }

  const reader = new FileReader();
  reader.onload = async function (e) {
    const text = e.target?.result;
    const lines = text.split('\n').filter(Boolean);
    const headers = lines[0].split(',');

    const importedLeads = lines.slice(1).map((line) => {
      const values = line.split(',');
      const lead = {};
      headers.forEach((header, index) => {
        lead[header.trim()] = values[index]?.replace(/^"|"$/g, '') || '';
      });
      return lead;
    });

    // Upload each lead
    for (const lead of importedLeads) {
      try {
        await CreateLead(lead);
      } catch (error) {
        console.error('Failed to create lead:', error);
      }
    }

    try {
      const allLeads = await GetLeads(); // make sure this function exists
      leads.value = allLeads;
    } catch (error) {
      console.error('Failed to fetch updated leads:', error);
    }
  };

  reader.readAsText(file);
}

function exportLeadsToCSV () {
  const rows = Object.values(leads.value); // leads is an object keyed by lead ID

  if (!rows.length) {
    alert("No data to export.");
    return;
  }

  const headers = Object.keys(rows[0]);
  const csvContent = [
    headers.join(","), // Header row
    ...rows.map((row) =>
      headers.map((h) => `"${(row[h] ?? '').toString().replace(/"/g, '""')}"`).join(","),
    ),
  ].join("\n");

  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", "leads_export.csv");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

// Status options for filtering
const statusOptions = computed(() => {
  const options = [...tableViewStatusTypes.map((status) =>
    (status === 'not_interested' ? 'Not interested' : status.charAt(0).toUpperCase() + status.slice(1)),
  )];
  return options;
});

const viewType = ref('list'); // we can set as list or card to set it as default view

// const filterTabs = ref([
//   {
//     id: 'users',
//     label: 'Users',
//     icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//       <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
//         stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
//       <path d="M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22"
//         stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
//     </svg>`,
//   },
//   {
//     id: 'status',
//     label: 'Status',
//     icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
//       <path d="M9 14L12 17L20 9" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
//       <path d="M20 12V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V6C4 4.89543 4.89543 4 6 4H15"
//         stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
//     </svg>`,
//   },
// ]);

const persistentFilterValues = ref({
  users: [{
    id: 'all',
    label: 'All',
    value: { first_name: 'All', label: 'All', user_id: null },
  }],
  status: [{
    id: 'all',
    label: 'All',
    value: 'All',
  }],
});

const selectedFilterValues = ref({
  users: [{
    id: 'all',
    label: 'All',
    value: { first_name: 'All', label: 'All', user_id: null },
  }],
  status: [{
    id: 'all',
    label: 'All',
    value: 'All',
  }],
});

const tempFilterValues = ref({
  users: [{
    id: 'all',
    label: 'All',
    value: { first_name: 'All', label: 'All', user_id: null },
  }],
  status: [{
    id: 'all',
    label: 'All',
    value: 'All',
  }],
});

function formatLabel (option) {
  if (option.first_name) {
    if (option.role === 'admin') {
      return `${option.first_name} (admin)`;
    }
    return `${option.first_name}`;
  }
  if (option.role === 'admin') {
    return `${option.email} (admin)`;
  }
  return `${option.email}`;
}

function getFilteredUsers (users) {
  const filteredUsers = Object.values(users);
  filteredUsers.forEach((user) => {
    user.label = formatLabel(user);
    // user.user_id = user._id; // Add user_id for filtering
  });
  return [...filteredUsers];
}

watch(showFilterBottomSheet, (isOpen) => {
  if (isOpen) {
    // Use the persistent values when opening
    selectedFilterValues.value = JSON.parse(JSON.stringify(persistentFilterValues.value));
  }
});

// const filterOptions = computed(() => ({
//   users: OrganizationStore.users ? getFilteredUsers(OrganizationStore.users).map((user) => ({
//     id: user.user_id || user.email || 'all',
//     label: user.first_name === 'All' ? 'All' : formatLabel(user),
//     value: user,
//   })) : [],
//   status: statusOptions.value.map((status) => ({
//     id: status.toLowerCase().replace(/\s+/g, '_'),
//     label: status,
//     value: status === 'All' ? 'All' : status,
//   })),
// }));

const handleCardAction = ({ action, item }) => {
  if (action === 'edit') {
    router.push(`/leads/${item._id}/edit`);
  } else if (action === 'delete') {
    selectedLead.value = item._id;
    openDeleteModal.value = true;
  }
};

function toggleViewType (val) {
  if (val === 'card') {
    viewType.value = 'card';
  } else {
    viewType.value = 'list';
  }
}

function applyCombinedFilters () {

  console.log('Filters applied:', {
    users: selectedUsers.value,
    statuses: selectedStatuses.value,
    projects: selectedProjects.value,
  });
  loader.value = true;

  const queryParams = new URLSearchParams();

  // Handle users
  const usersList = getFilteredUsers(OrganizationStore.users);
  const matchedUserIds = usersList
    .filter((user) => selectedUsers.value.includes(user.label))
    .map((user) => user.user_id);
  matchedUserIds.forEach((id) => queryParams.append('user_id[]', id));

  // Handle statuses
  const statusMap = {
    'Hot': 'hot',
    'Warm': 'warm',
    'Cold': 'cold',
    'All': 'all',
  };
  const filteredStatuses = selectedStatuses.value
    .filter((status) => status !== 'All')
    .map((status) => statusMap[status] || status.toLowerCase());
  filteredStatuses.forEach((status) => queryParams.append('status[]', status));

  // Handle projects
  const projectIdMap = OrganizationStore.projects || {};
  const matchedProjectIds = Object.entries(projectIdMap)
    .filter(([, project]) => selectedProjects.value.includes(project.name))
    .map(([id]) => id);
  matchedProjectIds.forEach((pid) => queryParams.append('project_id[]', pid));

  const finalQuery = queryParams.toString() ? `?${queryParams.toString()}` : '';

  GetLeads(finalQuery)
    .then((leads_data) => {
      leads.value = leads_data;
      OrganizationStore.SyncMultiplelLeads(leads_data);
      loader.value = false;
    })
    .catch((err) => {
      loader.value = false;
      console.error(err);
      uiStore.handleApiErrorMessage(err.message);
    });
}

// Handle filter changes for users
function handleFilterUsers (selectedUserLabels) {
  selectedUsers.value = selectedUserLabels;
  applyCombinedFilters();  // Apply combined filters
}

// Handle filter changes for status
function handleFilterStatus (selectedStatusesLabels) {
  selectedStatuses.value = selectedStatusesLabels;
  applyCombinedFilters();  // Apply combined filters
}

// Handle filter changes for projects
function handleFilterProjects (selectedProjectNames) {
  selectedProjects.value = selectedProjectNames;
  applyCombinedFilters();  // Apply combined filters
}

const getProjectOptions = computed(() => {
  const projectsObj = OrganizationStore.projects || {};
  return Object.values(projectsObj)
    .map((project) => project?.name)
    .filter(Boolean);
});

const filterCount = computed(() => {
  let count = 0;

  // Count user filters
  const appliedUserSelections = persistentFilterValues.value?.users || [];
  if (!appliedUserSelections.some((item) =>
    item.id === 'all' ||
    item.value?.first_name === 'All',
  )) {
    count += appliedUserSelections.length;
  }

  // Count status filters
  const appliedStatusSelections = persistentFilterValues.value?.status || [];
  if (!appliedStatusSelections.some((item) =>
    item.id === 'all' ||
    item.value === 'All',
  )) {
    count += appliedStatusSelections.length;
  }

  // Count project filters if any exist
  if (OrganizationStore.activeProjectFilters && OrganizationStore.activeProjectFilters.length > 0) {
    count += OrganizationStore.activeProjectFilters.length;
  }

  return count;
});

function handleFilterApply () {
  applyCombinedFilters();
  showFilterBottomSheet.value = false;
}

function openFilterBottomSheet () {
  tempFilterValues.value = JSON.parse(JSON.stringify(persistentFilterValues.value));
  selectedFilterValues.value = JSON.parse(JSON.stringify(tempFilterValues.value));
  showFilterBottomSheet.value = true;
}

const handleGetLead = () => {
  if (userStore.user_role?.userrole?.role) {
    if (userStore.user_role.userrole.role !== 'admin') {
      loader.value = true;
      GetLeads(`?user_id=${userStore.user_data._id}`).then((leads_data) => {
        leads.value = leads_data;
        loader.value = false;
        OrganizationStore.SyncMultiplelLeads(leads_data);
        const project_id = {};
        for (const key in leads_data) {
          if (leads_data[key]) {
            if (leads_data[key]?.interested_in.length) {
              leads_data[key].interested_in.forEach((item) => {
                if (item.project_id) {
                  if (!project_id[item.project_id]) {
                    project_id[item.project_id] = true;
                    getListOfAllUnits(item.project_id);
                  }
                }
              });
            }
          }
        }
      }).catch((err) => {
        loader.value = false;
        console.error(err);
        uiStore.handleApiErrorMessage(err.message);
      });
    } else {
      loader.value = true;
      OrganizationStore.RefreshUsers();
      // columnData.value.push('Agent');
      GetLeads().then((leads_data) => {
        leads.value = leads_data;
        loader.value = false;
        OrganizationStore.SyncMultiplelLeads(leads_data);
        const project_id = {};
        for (const key in leads_data) {
          if (leads_data[key]) {
            if (leads_data[key]?.interested_in.length) {
              leads_data[key].interested_in.forEach((item) => {
                if (item.project_id) {
                  if (!project_id[item.project_id]) {
                    project_id[item.project_id] = true;
                    getListOfAllUnits(item.project_id);
                  }
                }
              });
            }
          }
        }
      }).catch((err) => {
        loader.value = false;
        console.error(err);
        uiStore.handleApiErrorMessage(err.message);
      });
    }
  }
};

handleGetLead();

document.addEventListener('refreshLeads', () => {
  handleGetLead();
});

const handleDelete = () => {
  deleteLoader.value = true;
  console.log(selectedLead.value);

  if (selectedLead.value) {
    DeleteLead(selectedLead.value).then(() => {
      deleteLoader.value = false;
      document.dispatchEvent(new Event('refreshLeads'));
      openDeleteModal.value = false;
    }).catch((err) => {
      deleteLoader.value = false;
      console.log(err);
      uiStore.handleApiErrorMessage(err.message);
    });
  } else {
    deleteLoader.value = false;
    uiStore.handleApiErrorMessage("Select lead first");
  }

  selectedLead.value = null;
};

const handleMenuSelected = (lead_id) => {
  if (selectedLead.value === lead_id) {
    selectedLead.value = null;
  } else {
    selectedLead.value = lead_id;
  }
};

const handleEdit = () => {
  router.push(`/leads/${selectedLead.value}/edit`);
  selectedLead.value = null;
};
// watch(() => item.interested_in, (newVal) => {
// console.log(newVal);
//   // if (newVal && newVal.length) {
//   //   getListOfAllUnits(item.project_id); // replace with the actual projectId source
//   // }
// });
</script>

<template>
  <div
    class="relative bg-transparent h-full flex flex-col justify-start items-start gap-1 sm:overflow-y-auto overflow-x-hidden">
    <LoaderComp v-if="loader" />
    <!-- Desktop Header -->
    <div class="w-full hidden sm:block">
      <!-- Title for desktop header -->
      <div class=" flex justify-between">
        <div class="dynamic-header">
          <div class="dynamic-heading">
            <p class="dynamic-topic">Leads</p>
            <p class="dynamic-sub-topic">Create and manage leads.</p>
          </div>
        </div>
        <div class="lg:flex lg:justify-end lg:gap-7 md:grid md:grid-cols-3 md:w-[60%] md:gap-x-1 md:gap-y-1 h-fit">

          <div class="hidden sm:block">
            <NewDropDown title="Users" type="select" inputType="checkbox" width="w-[13rem]"
              :options="getFilteredUsers(OrganizationStore.users).map(user => user.label)"
              @optionSelected="handleFilterUsers" />
          </div>

          <!-- Status Dropdown -->
          <div class="hidden sm:block ">
            <NewDropDown title="Status" type="select" inputType="checkbox" :options="statusOptions"
              @optionSelected="handleFilterStatus" />
          </div>

          <!-- Projects Dropdown -->
          <div class="hidden sm:block">
            <NewDropDown title="Projects" type="select" inputType="checkbox" :options="getProjectOptions"
              @optionSelected="handleFilterProjects" />
          </div>

          <div class="hidden sm:block">
            <ToggleSliderButton :title="['card', 'list']" v-model="viewType" @toggleData="toggleViewType" />
          </div>
          <div class="hidden sm:block  border rounded-lg rounded-bl-lg border-gray-200 hover:border-gray-300 h-fit">
            <input type="file" accept=".csv" ref="fileInput" class="hidden" @change="importLeadsFromCSV" />
            <Button title="Import" class="bg-white !text-black" @click="() => $refs.fileInput.click()">
              <template v-slot:svg>
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M0.5 10.2831L0.516602 10.4295C0.536642 10.5249 0.57375 10.6146 0.625977 10.6932L0.714844 10.8016C0.813348 10.9008 0.934674 10.9648 1.0625 10.9891L1.19141 10.9998H5.28027L4.75391 10.451V10.45C4.65306 10.3468 4.57334 10.2254 4.51855 10.0926C4.47655 9.99076 4.44983 9.88336 4.43848 9.77426L4.43164 9.66489C4.43072 9.55494 4.44545 9.44525 4.47559 9.33969L4.51074 9.23618C4.55107 9.1335 4.60658 9.03678 4.6748 8.95004L4.74707 8.86703C4.82372 8.78721 4.91191 8.71942 5.00879 8.66586L5.1084 8.61703C5.21105 8.57329 5.31986 8.54547 5.43066 8.535L5.54199 8.53012C5.65361 8.53118 5.7638 8.5501 5.86914 8.58481L5.97266 8.62485C6.01674 8.64457 6.05879 8.66801 6.09961 8.69321V5.87485C6.09961 5.58295 6.21117 5.29914 6.41504 5.08676C6.61961 4.87367 6.90172 4.74985 7.2002 4.74985C7.4986 4.7499 7.78083 4.87372 7.98535 5.08676C8.1891 5.29912 8.2998 5.58304 8.2998 5.87485V8.70004C8.46836 8.59949 8.66206 8.54392 8.86328 8.54575C9.12151 8.54811 9.36685 8.64258 9.55957 8.80746L9.63867 8.88266C9.81497 9.06644 9.92172 9.30376 9.94727 9.55356L9.95312 9.66098C9.95522 9.91338 9.87437 10.1604 9.72168 10.3612L9.65137 10.4452L9.64551 10.451L9.11816 10.9998H10.7998C10.934 10.9998 11.0667 10.958 11.1807 10.8788L11.2881 10.787C11.4215 10.6478 11.5 10.455 11.5 10.2498V2.71664C11.4974 2.56671 11.4523 2.42427 11.374 2.30649L11.2852 2.19809C11.1867 2.09892 11.0653 2.03487 10.9375 2.01059L10.8086 1.99985L4.7002 1.99985V4.62485C4.7002 5.02532 4.56664 5.41459 4.32129 5.72641L4.20898 5.85532C3.89191 6.18545 3.45748 6.37485 3 6.37485H0.5L0.5 10.2831ZM2.5 4.12485V2.48422L0.923828 4.12485H2.5Z"
                    fill="#1F2A37" stroke="#1F2A37" />
                </svg>

              </template>
            </Button>
          </div>

          <div class="hidden sm:block border rounded-lg rounded-bl-lg border-gray-200 hover:border-gray-300 h-fit">
            <Button title="Export" @click="exportLeadsToCSV" class="bg-white !text-black">
              <template v-slot:svg>
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M0.5 2.7168L0.516602 2.57031C0.536642 2.47494 0.57375 2.38522 0.625977 2.30664L0.714844 2.19824C0.813348 2.09907 0.934674 2.03503 1.0625 2.01074L1.19141 2L5.28027 2L4.75391 2.54883V2.5498C4.65306 2.65303 4.57334 2.77445 4.51855 2.90723C4.47655 3.00908 4.44983 3.11649 4.43848 3.22559L4.43164 3.33496C4.43072 3.44491 4.44545 3.5546 4.47559 3.66016L4.51074 3.76367C4.55107 3.86634 4.60658 3.96307 4.6748 4.0498L4.74707 4.13281C4.82372 4.21264 4.91191 4.28043 5.00879 4.33398L5.1084 4.38281C5.21105 4.42656 5.31986 4.45438 5.43066 4.46484L5.54199 4.46973C5.65361 4.46867 5.7638 4.44975 5.86914 4.41504L5.97266 4.375C6.01674 4.35528 6.05879 4.33184 6.09961 4.30664L6.09961 7.125C6.09961 7.4169 6.21117 7.7007 6.41504 7.91309C6.61961 8.12617 6.90172 8.25 7.2002 8.25C7.4986 8.24995 7.78083 8.12613 7.98535 7.91309C8.1891 7.70072 8.2998 7.41681 8.2998 7.125V4.2998C8.46836 4.40036 8.66206 4.45592 8.86328 4.4541C9.12151 4.45174 9.36685 4.35727 9.55957 4.19238L9.63867 4.11719C9.81497 3.93341 9.92172 3.69608 9.94727 3.44629L9.95313 3.33887C9.95522 3.08647 9.87437 2.83943 9.72168 2.63867L9.65137 2.55469L9.64551 2.54883L9.11816 2L10.7998 2C10.934 2 11.0667 2.04184 11.1807 2.12109L11.2881 2.21289C11.4215 2.35207 11.5 2.54486 11.5 2.75L11.5 10.2832C11.4974 10.4331 11.4523 10.5756 11.374 10.6934L11.2852 10.8018C11.1867 10.9009 11.0653 10.965 10.9375 10.9893L10.8086 11L4.7002 11L4.7002 8.375C4.7002 7.97453 4.56664 7.58526 4.32129 7.27344L4.20898 7.14453C3.89191 6.8144 3.45748 6.625 3 6.625L0.5 6.625L0.5 2.7168ZM2.5 8.875V10.5156L0.923828 8.875H2.5Z"
                    fill="#1F2A37" stroke="#1F2A37" />
                </svg>

              </template>
            </Button>
          </div>

          <div class="hidden sm:block">
            <Button title="Add Lead" theme="primary" @click="() => $router.push(`/leads/add`)">
              <template v-slot:svg>
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_306_21007)">
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_306_21007">
                      <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                    </clipPath>
                  </defs>
                </svg>
              </template>
            </Button>
          </div>
        </div>
      </div>

    </div>
    <!-- Sticky header for mobile -->
    <div class="sm:hidden block w-full dark:bg-bg-15 flex-0">
      <div class="px-4 pt-2 pb-2">
        <div class="flex justify-between items-center gap-2">
          <div>
            <h3 class="text-txt-50 dark:text-txt-1000 text-lg font-semibold">Leads</h3>
            <p class="text-gray-600 dark:text-txt-650 text-sm font-normal">Add and manage your leads
            </p>
          </div>
          <div class="flex items-center gap-2.5">
            <Button title="" @click="openFilterBottomSheet" theme="secondary"
              class="w-full text-xs sm:text-sm flex items-center justify-center !bg-transparent border-[1px] border-[#E6E6E6] !pr-[2px] !pl-2 !h-10">
              <template #svg>
                <svg class="w-5 h-5 sm:w-6 sm:h-6" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path class="fill-txt-1000 dark:fill-txt-default" d="M15 2.5H1L6.5 8.5V13.5L9.5 15.5V8.5L15 2.5Z"
                    stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <span v-if="filterCount > 0"
                  class="absolute top-1 right-[65px] flex items-center justify-center w-4 h-4 text-xs font-medium text-white bg-black rounded-full">
                  {{ filterCount }}
                </span>
              </template>
            </Button>
            <Button class="h-fit !p-3 !gap-0" theme="primary" @click="() => $router.push(`/leads/add`)">
              <template v-slot:svg>
                <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_306_21007)">
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z" />
                    <path class="fill-txt-1000 dark:fill-txt-default"
                      d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z" />
                  </g>
                  <defs>
                    <clipPath id="clip0_306_21007">
                      <rect width="12" height="12" fill="white" transform="translate(0.00390625 0.5)" />
                    </clipPath>
                  </defs>
                </svg>
              </template>
            </Button>

          </div>
        </div>
      </div>
    </div>

    <!-- <div class="hidden sm:block mb-4 w-full">
      <hr />
      <div class="my-2 px-4 ">
        <BreadCrumb :list="[{ name: 'Leads', route: '/leads' }]" :active="0" />
      </div>
    </div> -->

    <!-- <div class="hidden sm:flex flex-col sm:flex-row justify-between items-center gap-8 mb-4 px-4 w-full">
      <p class="text-2xl font-medium" style="text-wrap: nowrap;">Added Leads</p>
      <div class="flex flex-col sm:flex-row gap-x-7 gap-y-5 w-fit items-center flex-wrap justify-end">
        <LabelDropdown :options="getFilteredUsers(OrganizationStore.users)" @handleSelect="handleProjectSelection"
          label="Users" value="label" width="263px" :selectedOption="{first_name: 'All', label: 'All'}"/>
        <ToggleButton v-model="viewType" @toggle="toggleViewType" />
      </div>
    </div> -->
    <!-- Table contents -->
    <div v-if="leads !== null && Object.keys(leads).length !== 0"
      class="sm:mb-4 py-2.5 sm:px-0 sm:p-4 h-full flex-1 relative overflow-auto w-full z-0">

      <div v-if="viewType === 'list' && userStore.user_role?.userrole?.role"
        class="hidden sm:block w-full bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-x-auto h-[-webkit-fill-available]">

        <table class="w-full table-auto min-w-[1000px] rounded-2xl bg-transparent">
          <thead class="sticky top-0 z-10 bg-gray-50 dark:bg-bg-150">
            <tr>
              <th v-bind:key="item" v-for="item in columnData"
                :class="[(item === 'Agents' && (userStore.user_role?.userrole?.role === 'admin' ? 'block' : 'hidden')), 'p-3 text-left text-sm font-semibold text-gray-900 bg-transparent capitalize whitespace-nowrap']">
                {{ item }}
              </th>
              <th class="sr-only w-7"></th>
            </tr>
          </thead>

          <tbody class="text-sm">
            <tr :key="id" v-for="(item, id, index) in leads"
              class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">

              <td class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize whitespace-nowrap">
                {{ index + 1 }}
              </td>

              <td
                class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                :title="item.name">
                {{ item.name }}
              </td>

              <td
                class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 break-words max-w-[160px] overflow-hidden text-ellipsis whitespace-nowrap"
                :title="item.email">
                <span v-if="item.email">
                  <span class="inline-block overflow-hidden text-ellipsis whitespace-nowrap max-w-full"
                    :title="item.email">
                    {{ item.email }}
                  </span>
                </span>
                <span v-else>-</span>
              </td>

              <td
                class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize break-words max-w-[160px] overflow-hidden text-ellipsis whitespace-nowrap"
                :title="item.phone_number">
                <span v-if="item?.phone_number" class="flex justify-start items-center gap-1">
                  {{ item.phone_number }}
                </span>
                <span v-else>-</span>
              </td>

              <td class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize">
                <p v-if="item.lead_status" :class="[item.lead_status === tableViewStatusTypes[0] && 'bg-green-100',
                item.lead_status === tableViewStatusTypes[1] && 'bg-orange-100',
                item.lead_status === tableViewStatusTypes[2] && 'bg-cyan-50 ',
                item.lead_status === tableViewStatusTypes[3] && 'bg-orange-100',
                item.lead_status === tableViewStatusTypes[4] && 'bg-slate-200 ',
                  'rounded-md text-txt-50 border-[1px] px-3 w-fit']">
                  {{ item.lead_status === 'not_interested' ? 'Not interested' : item.lead_status }}
                </p>
                <p v-else>-</p>
              </td>

              <td class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize whitespace-nowrap">
                <span v-if="item.interested_in && item.interested_in.length">
                  <span v-for="(unit) in item.interested_in" :key="unit.unit_id" class="block">
                    {{ units?.[unit.unit_id]?.name || unit.unit_id }}
                  </span>
                </span>
                <span v-else>-</span>
              </td>

              <!--
              <td class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize whitespace-nowrap">
                <span v-if="item?.joining_time" class="flex justify-start items-center gap-1">
                  <CalendaerSvg />
                  {{ formatTheDateFromISOString(new Date(item.joining_time).toISOString()) }}
                </span>
                <span v-else>-</span>
              </td>

              <td class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize whitespace-nowrap">
                <span v-if="item?.lead_creation_time" class="flex justify-start items-center gap-1">
                  <TimeSvg />
                  {{ extractTheTimeStringFromDateTime(new Date(item.lead_creation_time)) }}
                </span>
                <span v-else>-</span>
              </td> -->

              <td
                class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                :title="OrganizationStore.projects?.[item.interested_in?.[0]?.project_id]?.name || item.interested_in?.[0]?.project_id">
                {{
                  OrganizationStore.projects?.[item.interested_in?.[0]?.project_id]?.name ||
                  item.interested_in?.[0]?.project_id
                }}
              </td>

              <td v-if="userStore.user_role?.userrole?.role === 'admin' && OrganizationStore.users"
                class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 whitespace-nowrap">
                <span v-for="(userItem, index) in OrganizationStore.users" :key="index"
                  :class="userItem.user_id === item.user_id ? 'block' : 'hidden'">
                  {{ userItem.first_name && userItem.last_name ? `${userItem.first_name} ${userItem.last_name}` :
                    userItem.email }}
                </span>
              </td>
              <td>
                <div class="relative">

                  <button @click="toggleDropdown(index)" :class="[
                    'border-2 px-4 py-2 rounded-lg flex items-center transition-colors',
                    selectedIndex === index
                      ? 'bg-blue-700 text-white border-blue-700'
                      : 'bg-white text-blue-700 border-blue-700'
                  ]">
                    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g clip-path="url(#clip0)">
                        <path
                          d="M6.68646 9.86067C7.01188 10.185 7.45264 10.3672 7.91214 10.3672C8.37164 10.3672 8.8124 10.185 9.13782 9.86067L9.75066 9.24793C10.0761 8.92357 10.5168 8.74143 10.9763 8.74143C11.4358 8.74143 11.8766 8.92357 12.202 9.24793L13.4277 10.4734C13.6069 10.6234 13.751 10.8109 13.8499 11.0227C13.9488 11.2344 14 11.4652 14 11.6989C14 11.9326 13.9488 12.1634 13.8499 12.3752C13.751 12.5869 13.6069 12.7744 13.4277 12.9244C10.2182 16.1343 6.52362 14.6095 3.30447 11.3908C0.0853122 8.17212 -1.4319 4.48164 1.7785 1.27173C1.92863 1.0927 2.11617 0.948738 2.32792 0.849961C2.53968 0.751185 2.77051 0.699997 3.00418 0.699997C3.23784 0.699997 3.46867 0.751185 3.68043 0.849961C3.89218 0.948738 4.07972 1.0927 4.22985 1.27173L5.45553 2.49722C5.78015 2.82248 5.96246 3.26321 5.96246 3.72271C5.96246 4.18221 5.78015 4.62294 5.45553 4.9482L4.84269 5.56095C4.51807 5.88621 4.33576 6.32694 4.33576 6.78644C4.33576 7.24594 4.51807 7.68667 4.84269 8.01193L6.68646 9.86067Z"
                          :class="selectedIndex === index ? 'fill-white' : 'fill-blue-700'" />
                      </g>
                      <defs>
                        <clipPath id="clip0">
                          <rect width="14" height="14" fill="white" transform="translate(0 0.7)" />
                        </clipPath>
                      </defs>
                    </svg>
                    <span class="ml-2">Connect</span>
                  </button>

                  <div v-if="selectedIndex === index" :style="dropdownStyle"
                    class="absolute bg-white border border-gray-300 -mt-20 !mr-44 right-0 p-2 rounded-md z-50 drop-shadow-lg">
                    <button @click="connect('Email', item.phone_number, item.email)"
                      class="w-full text-left py-2 pr-4 pl-2 hover:bg-gray-100 rounded-md gap-2 flex items-center">
                      <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M7 8.1398L13.4925 2.5804C13.2432 2.36859 12.9271 2.25158 12.6 2.25H1.4C1.07287 2.25158 0.756802 2.36859 0.5075 2.5804L7 8.1398Z"
                          fill="black" />
                        <path
                          d="M7.875 9.2339C7.62023 9.42877 7.30884 9.53519 6.9881 9.537C6.6892 9.53761 6.39824 9.44079 6.1593 9.2612L0 3.9888L0 11.35C0 11.7213 0.1475 12.0774 0.41005 12.3399C0.672601 12.6025 1.0287 12.75 1.4 12.75H12.6C12.9713 12.75 13.3274 12.6025 13.5899 12.3399C13.8525 12.0774 14 11.7213 14 11.35V3.9888L7.875 9.2339Z"
                          fill="black" />
                      </svg>

                      <span>Email</span></button>
                    <button @click="connect('WhatsApp', item.phone_number, index)"
                      class="w-full text-left py-2 pr-4 pl-2 hover:bg-gray-100 rounded-md  gap-2 flex items-center">

                      <svg width="14" height="17" viewBox="0 0 14 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M6.9834 0.5C10.8402 0.500012 13.9668 3.62659 13.9668 7.4834C13.9668 11.3402 10.8402 14.4668 6.9834 14.4668C3.12659 14.4668 1.1597e-05 11.3402 0 7.4834C0 3.62659 3.12659 0.5 6.9834 0.5ZM5.27539 4.49023C5.17556 4.49023 5.07681 4.51252 4.98633 4.55469C4.89579 4.59692 4.81517 4.65881 4.75098 4.73535C3.37875 6.10768 4.02708 7.68548 5.40332 9.06152C6.77967 10.4377 8.35921 11.0892 9.73145 9.7168C9.808 9.65271 9.86984 9.57286 9.91211 9.48242C9.95435 9.39197 9.97653 9.29318 9.97656 9.19336C9.97656 9.09352 9.95432 8.99477 9.91211 8.9043C9.86983 8.81377 9.80806 8.73309 9.73145 8.66895L9.20801 8.14551C9.06887 8.00683 8.88005 7.92871 8.68359 7.92871C8.48717 7.92874 8.29829 8.00685 8.15918 8.14551L7.89746 8.40723C7.75844 8.54573 7.57027 8.62391 7.37402 8.62402C7.17759 8.62402 6.98874 8.54587 6.84961 8.40723L6.06152 7.61719C5.92273 7.47813 5.84473 7.28923 5.84473 7.09277C5.84473 6.89631 5.92273 6.70743 6.06152 6.56836L6.32324 6.30664C6.4619 6.16767 6.53995 5.9795 6.54004 5.7832C6.54004 5.58674 6.46203 5.39785 6.32324 5.25879L5.79883 4.73535C5.73468 4.65885 5.65493 4.59692 5.56445 4.55469C5.47398 4.51249 5.37522 4.49027 5.27539 4.49023Z"
                          fill="black" />
                        <path d="M3.56871 13.4654L4.4724 13.9939L3.44805 14.7105L3.56871 13.4654Z" fill="black"
                          stroke="black" />
                      </svg>

                      WhatsApp</button>
                    <button @click="connect('Call', item.phone_number, index)"
                      class="w-full text-left py-2 pr-4 pl-2 hover:bg-gray-100 rounded-md  gap-2 flex items-center">

                      <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clip-path="url(#clip0_6461_21963)">
                          <path
                            d="M6.68646 9.66067C7.01188 9.98503 7.45264 10.1672 7.91214 10.1672C8.37164 10.1672 8.8124 9.98503 9.13782 9.66067L9.75066 9.04793C10.0761 8.72357 10.5168 8.54144 10.9763 8.54144C11.4358 8.54144 11.8766 8.72357 12.202 9.04793L13.4277 10.2734C13.6069 10.4234 13.751 10.6109 13.8499 10.8227C13.9488 11.0344 14 11.2652 14 11.4989C14 11.7326 13.9488 11.9634 13.8499 12.1752C13.751 12.3869 13.6069 12.5744 13.4277 12.7244C10.2182 15.9343 6.52362 14.4095 3.30447 11.1908C0.0853122 7.97212 -1.4319 4.28164 1.7785 1.07173C1.92863 0.892706 2.11617 0.748741 2.32792 0.649964C2.53968 0.551188 2.77051 0.5 3.00418 0.5C3.23784 0.5 3.46867 0.551188 3.68043 0.649964C3.89218 0.748741 4.07972 0.892706 4.22985 1.07173L5.45553 2.29722C5.78015 2.62248 5.96246 3.06322 5.96246 3.52272C5.96246 3.98221 5.78015 4.42295 5.45553 4.74821L4.84269 5.36095C4.51807 5.68621 4.33576 6.12694 4.33576 6.58644C4.33576 7.04594 4.51807 7.48668 4.84269 7.81193L6.68646 9.66067Z"
                            fill="black" />
                        </g>
                        <defs>
                          <clipPath id="clip0_6461_21963">
                            <rect width="14" height="14" fill="white" transform="translate(0 0.5)" />
                          </clipPath>
                        </defs>
                      </svg>

                      Call</button>
                  </div>
                </div>
              </td>

              <td class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 whitespace-nowrap relative">
                <button @click="handleMenuSelected(item._id)">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                    class="w-6 h-6 fill-black">
                    <path fill-rule="evenodd"
                      d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
                <div v-if="selectedLead === item._id" class="absolute z-50 right-[15rem] bottom-full mb-2">
                  <EditDeleteButton @handleDelete="openDeleteModal = true" @handleEdit="handleEdit" />
                </div>

              </td>

            </tr>
          </tbody>
        </table>
      </div>

      <!-- Card/Grid/Mobile views -->
      <div v-else class="hidden sm:grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <LeadsCard v-for="lead in leads" :key="lead._id" :item="lead" :organization-store="OrganizationStore"
          :units="units" :is-mobile="isMobile" @action="handleCardAction" />
      </div>

      <!-- card view for mobile -->

      <div class=" sm:hidden grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <LeadsCard v-for="lead in leads" :key="lead._id" :item="lead" :organization-store="OrganizationStore"
          :is-mobile="isMobile" @action="handleCardAction" />
      </div>

      <!-- table View for mobile -->

      <!-- <div v-if="viewType === 'list' && userStore.user_role?.userrole?.role"
        class="block sm:hidden w-full p-4 bg-bg-1000 dark:bg-bg-150 rounded-2xl border-[1px] border-gray-300 dark:border-bg-200 overflow-x-auto h-[-webkit-fill-available]">

        <table class="w-full table-auto p-6 rounded-2xl bg-transparent">
          <thead>
            <tr class="table-header">
              <th class="p-3 text-left text-sm font-semibold text-gray-900 bg-transparent capitalize whitespace-nowrap">
                S.no</th>
              <th class="p-3 text-left text-sm font-semibold text-gray-900 bg-transparent capitalize whitespace-nowrap">
                Name</th>
            </tr>
          </thead>

          <tbody class="text-sm">
            <tr :key="id" v-for="(item, id, index) in leads"
              class="even:bg-stone-50 odd:bg-bg-1000 even:dark:bg-bg-200 dark:odd:bg-bg-50">

              <td class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize whitespace-nowrap">
                {{ index + 1 }}
              </td>

              <td
                class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 capitalize max-w-[200px] overflow-hidden text-ellipsis whitespace-nowrap"
                :title="item.name">
                {{ item.name }}
              </td>

              <td class="p-3 align-top text-sm font-normal text-txt-50 dark:text-txt-950 whitespace-nowrap">
                <button @click="handleMenuSelected(item._id)">
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"
                    class="w-6 h-6 fill-black">
                    <path fill-rule="evenodd"
                      d="M10.5 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0zm0 6a1.5 1.5 0 113 0 1.5 1.5 0 01-3 0z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
                <EditDeleteButton v-if="selectedLead === item._id" class="absolute right-10 mb-32"
                  @handleDelete="openDeleteModal = true" @handleEdit="handleEdit" />
              </td>
            </tr>
          </tbody>
        </table>
      </div> -->
    </div>

    <div v-else class="w-full">
      <div class="w-fit m-auto">
        <Noschedule />
      </div>
      <div class="text-txt-default dark:text-txt-950 font-medium m-auto w-fit">No Leads Added</div>
      <Button title="Add Leads" theme="primary" class="mt-8 mx-auto">
        <template v-slot:svg>
          <svg class="w-3 h-3" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clip-path="url(#clip0_300_3957)">
              <path class="fill-white dark:fill-black"
                d="M6.00391 12.5C5.84851 12.5 5.69947 12.4383 5.58959 12.3284C5.4797 12.2185 5.41797 12.0695 5.41797 11.9141V1.08594C5.41797 0.930537 5.4797 0.781502 5.58959 0.671617C5.69947 0.561733 5.84851 0.5 6.00391 0.5C6.15931 0.5 6.30834 0.561733 6.41823 0.671617C6.52811 0.781502 6.58984 0.930537 6.58984 1.08594V11.9141C6.58984 12.0695 6.52811 12.2185 6.41823 12.3284C6.30834 12.4383 6.15931 12.5 6.00391 12.5Z"
                fill="" />
              <path class="fill-white dark:fill-black"
                d="M11.418 7.08594H0.589844C0.434443 7.08594 0.285408 7.02421 0.175523 6.91432C0.0656388 6.80444 0.00390625 6.6554 0.00390625 6.5C0.00390625 6.3446 0.0656388 6.19556 0.175523 6.08568C0.285408 5.97579 0.434443 5.91406 0.589844 5.91406H11.418C11.5734 5.91406 11.7224 5.97579 11.8323 6.08568C11.9422 6.19556 12.0039 6.3446 12.0039 6.5C12.0039 6.6554 11.9422 6.80444 11.8323 6.91432C11.7224 7.02421 11.5734 7.08594 11.418 7.08594Z"
                fill="" />
            </g>
            <defs>
              <clipPath id="clip0_300_3957" class="fill-white dark:fill-black">
                <rect width="12" height="12" fill="" transform="translate(0.00390625 0.5)" />
              </clipPath>
            </defs>
          </svg>

        </template>
      </Button>
    </div>

    <transition name="slide-up">
  <div
    v-if="showFilterBottomSheet"
    class="fixed inset-x-0 bottom-0 z-40 bg-white rounded-t-2xl shadow-lg min-h-[60vh] max-h-[60vh] sm:hidden flex flex-col"
  >
    <!-- Header -->
    <div class="flex justify-between items-center p-4 border-b">
      <h2 class="text-lg font-semibold">Filter Leads</h2>
      <button @click="showFilterBottomSheet = false">
        <svg width="18" height="18" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- Main Scrollable Content -->
    <div class="flex flex-1  space-x-4">
      <!-- Left Tabs -->
      <div class="flex flex-col pt-2  w-1/3 shrink-0 bg-gray-100">
        <button
          class="text-center text-sm "
          :class="{ 'bg-blue-100': activeFilter === 'users' }"
          @click="activeFilter = 'users'"
        >
          Users
        </button>
        <button
          class="text-center text-sm "
          :class="{ 'bg-blue-100': activeFilter === 'status' }"
          @click="activeFilter = 'status'"
        >
          Status
        </button>
        <button
          class="text-center text-sm"
          :class="{ 'bg-blue-100': activeFilter === 'projects' }"
          @click="activeFilter = 'projects'"
        >
          Projects
        </button>
      </div>

      <!-- Right Panel Content -->
      <div class="w-2/3  mt-2" >
        <div v-show="activeFilter === 'users'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Users</label>
          <div class="space-y-2 h-40 overflow-y-auto ">
            <div
              v-for="user in getFilteredUsers(OrganizationStore.users)"
              :key="user.label"
            >
              <label class="flex items-center space-x-2">
                <input type="checkbox" :value="user.label" v-model="selectedUsers" />
                <span>{{ user.label }}</span>
              </label>
            </div>
          </div>
        </div>

        <div v-show="activeFilter === 'status'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
          <div class="space-y-2 h-40 overflow-y-auto ">
            <div v-for="status in statusOptions" :key="status">
              <label class="flex items-center space-x-2">
                <input type="checkbox" :value="status" v-model="selectedStatuses" />
                <span>{{ status }}</span>
              </label>
            </div>
          </div>
        </div>

        <div v-show="activeFilter === 'projects'">
          <label class="block text-sm font-medium text-gray-700 mb-2">Projects</label>
          <div class="space-y-2 h-40 overflow-y-auto ">
            <div v-for="project in getProjectOptions" :key="project">
              <label class="flex items-center space-x-2">
                <input type="checkbox" :value="project" v-model="selectedProjects" />
                <span>{{ project }}</span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sticky Buttons -->
    <div class="border-t p-4 flex justify-between">
      <button @click="handleFilterClear" class="text-gray-600">Clear</button>
      <button @click="handleFilterApply" class="bg-blue-600 text-white px-4 py-2 rounded-lg">Apply</button>
    </div>
  </div>

</transition>

    <Modal :open="openDeleteModal">
      <DeleteModalContent @closeModal="(e) => openDeleteModal = false" :loader="deleteLoader"
        @handleDelete="handleDelete" :dataName="'Lead'" />
    </Modal>
  </div>
</template>

<style></style>

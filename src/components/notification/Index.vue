<script setup>
import { ref, computed, watch } from 'vue';
import NotificationCard from '../common/NotificationCard.vue';
import { UserStore } from '@/store';
import { Org_Store } from '@/store/organization';
import { UpdateNotifications } from '@/api/organization';

const store = UserStore();
const orgStore = Org_Store();
const notifications = ref([]);

if (store.user_data){
  orgStore.FetchNotifications(store.user_data._id);
}

function Update (val) {
  if (val.length){
    Promise.all(
      val.map((notification) =>
        UpdateNotifications(notification._id, { viewed: true, user_id: store.user_data._id }),
      ),
    )
      .then((results) => {
        orgStore.newNotifications = [];
        console.log('All notifications updated successfully', results);
      })
      .catch((error) => {
        console.error('Error updating notifications', error);
      });
  }
}

if (orgStore.newNotifications){
  Update(orgStore.newNotifications);
}

watch(() => orgStore.notifications, (val) => {
  notifications.value = val;
});

watch(() => orgStore.newNotifications, (val) => {
  Update(val);
});

notifications.value = orgStore.notifications;

// Function to format dates
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
};

// Get today and yesterday dates for comparison
const today = new Date();
today.setHours(0, 0, 0, 0);
const yesterday = new Date(today);
yesterday.setDate(yesterday.getDate() - 1);

// Group notifications by date and sort groups
const groupedNotifications = computed(() => {
  // Sort notifications by timestamp (newest first)
  const sorted = [...notifications.value].sort((a, b) =>
    new Date(b.timestamp) - new Date(a.timestamp),
  );

  // Group by date
  const groups = {};

  sorted.forEach((notification) => {
    const notifDate = new Date(notification.timestamp);
    notifDate.setHours(0, 0, 0, 0);

    let dateKey;
    if (notifDate.getTime() === today.getTime()) {
      dateKey = 'Today';
    } else if (notifDate.getTime() === yesterday.getTime()) {
      dateKey = 'Yesterday';
    } else {
      dateKey = formatDate(notification.timestamp);
    }

    if (!groups[dateKey]) {
      groups[dateKey] = [];
    }

    groups[dateKey].push(notification);
  });

  // Convert to array for v-for
  return Object.entries(groups);
});

function handleNotificationClick (url) {
  if (typeof window !== 'undefined' && url) {
    window.open(url, '_blank');
  }
}

</script>

<template>
  <div class="dynamic-header">
            <div class="dynamic-heading ">
                <p class="dynamic-topic">Notifications</p>
            </div>
            </div>

    <div class="w-full">

        <!-- Loop through date groups -->
         <div class="mx-auto w-fit">
          <template v-if="notifications && notifications.length > 0">
              <div v-for="(group, groupIndex) in groupedNotifications" :key="groupIndex" class="mb-6 w-fit">
                  <!-- Date header -->
                  <div class="text-gray-500 text-sm font-medium leading-[14px] pb-4">{{ group[0] }}</div>

                  <!-- Grid layout for notifications on this date -->
                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      <div v-for="(item, index) in group[1]" :key="index">
                          <NotificationCard
                              @handle-click="(url)=>{ handleNotificationClick(url) }"
                              :status="item.status"
                              :timeStamp="item.timestamp"
                              :url="item.url"
                          />
                      </div>
                  </div>
              </div>
          </template>

          <!-- Show when no notifications -->
          <div v-if="notifications && notifications.length === 0" class="text-gray-500 text-sm">
              No notifications
          </div>
         </div>
    </div>
</template>

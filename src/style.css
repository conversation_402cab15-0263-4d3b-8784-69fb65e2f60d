@tailwind base;
@tailwind components;
@tailwind utilities;




@layer base {
  @font-face {
    font-family: 'Inter';
    src: url('../src/assets/fonts/Inter-Regular.woff2') format('woff2'), url('../src/assets/fonts/Inter-Regular.woff2')
        format('woff');
    font-weight: 400;
    font-style: normal;
  }
}

/* width */
::-webkit-scrollbar {
  width: 0.25rem;
  height: 0.25rem;
}

/* Track */
::-webkit-scrollbar-track {
  background: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #737373;
  border-radius: 5px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #939393;
}

html, body {
  height: 100%;
  overflow: hidden;
  width: 100%;
}

/* NoScroll */

.noScrollApperance::-webkit-scrollbar {
  display: none;
}

.modal-content-primary {
  @apply relative transform overflow-hidden rounded-t-2xl rounded-b-none sm:rounded-t-lg sm:rounded-b-lg bg-bg-1000 text-left shadow-xl transition-all sm:my-8 w-full sm:max-w-3xl;
}

.modal-heading-primary {
  @apply mb-1 flex justify-between items-center;
}

.modal-subheading-primary {
  @apply text-sm;
}

.input-primary {
  @apply flex rounded-lg h-10 transition-all duration-[0.3s] ease-in-out px-3 py-0 border-[1px] border-bg-700 focus:border-bg-default placeholder:text-start placeholder:text-sm;
}

.label-primary {
  @apply block text-sm font-normal leading-6 text-black mb-1;
}

.select-primary {
  @apply block p-2 w-full rounded-md bg-[transparent] py-1.5 text-black shadow-sm border-[1px] border-bg-700 ring-0 ring-bg-700 focus:ring-1 focus:ring-inset focus:ring-indigo-500 sm:text-sm sm:leading-6 placeholder:text-start placeholder:text-sm placeholder:text-gray-500;
}

.select-primary1 {
  @apply block p-2 w-full rounded-md bg-[transparent] py-1.5 text-black border-[1px] ring-0 sm:text-sm sm:leading-6 placeholder:text-start placeholder:text-sm placeholder:text-gray-500 border-none;
}

.proceed-btn-primary {
  @apply bg-bg-50 dark:bg-bg-1000 text-txt-1000 dark:text-txt-150 rounded-md flex flex-row justify-center items-center px-6 h-10 disabled:opacity-80;
}

.cancel-btn-primary {
  @apply bg-gray-200 text-txt-150 rounded-lg flex flex-row justify-center items-center px-6 disabled:opacity-80 h-10;
}

.textarea-primary {
  @apply block p-2 w-full text-sm text-black bg-inherit rounded-lg border-[1px] border-bg-700 placeholder:text-start placeholder:text-sm placeholder:text-gray-500;
}

.dropdown-btn {
  @apply inline-flex w-full h-10 justify-between items-center rounded-md p-2 text-black text-sm shadow-sm;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.dynamic-viewbox{
  @apply h-screen w-full flex justify-start items-start overflow-hidden bg-[#f3f4f6];
}
.dynamic-container{
  @apply bg-white h-full overflow-y-auto w-full rounded-lg p-4;
}
.dynamic-header{
 @apply flex justify-between items-center mb-3
}
.dynamic-heading{
  @apply flex flex-col gap-1;
}
.dynamic-topic{
  @apply text-lg;
}
.dynamic-sub-topic{
 @apply text-xs text-gray-500;
}

.multiselect__content-wrapper {
  position: static !important;
  }
.w-fit{
  width: fit-content;
}
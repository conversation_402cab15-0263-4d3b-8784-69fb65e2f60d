<script setup lang="ts">
// Import dotenv from "dotenv";
import { initializeApp } from 'firebase/app';
import Toaster from './components/common/Toaster.vue';
import { generateToken } from './firebaseConfig';
import { Org_Store } from './store/organization';
import { UserStore } from './store';
import { onMounted } from 'vue';

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  databaseURL: import.meta.env.VITE_FIREBASE_DATABASE_URL,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
};
initializeApp(firebaseConfig);
document.addEventListener('contextmenu', function (e) {
  e.preventDefault();
});
generateToken();
const store = Org_Store();
const userStore = UserStore();
const checkCachedNotifications = async () => {
  try {
    if ('caches' in window) {
      const cache = await caches.open('notifications-cache');
      const indexResponse = await cache.match('/notifications/index');

      if (indexResponse) {
        const notificationIds = await indexResponse.json();
        console.log('Found cached notifications:', notificationIds);

        if (notificationIds.length > 0 && userStore?.user_data?._id) {
          // Fetch notifications data
          store.FetchNotifications(userStore.user_data._id);
          store.FetchNewNotifications(userStore.user_data._id, false);

          // Clear notifications
          await cache.put('/notifications/index', new Response('[]'));
          for (const id of notificationIds) {
            await cache.delete(`/notifications/${id}`);
          }
        }
      }
    }
  } catch (error) {
    console.error('Error checking cached notifications:', error);
  }
};

onMounted(() => {
  document.addEventListener('visibilitychange', async () => {
    if (document.visibilityState === 'visible') {
      await checkCachedNotifications();
    }
  });
});
</script>
<template>

      <!-- Layouts -->
      <router-view class="fixed inset-0 w-full h-full overflow-hidden"> </router-view>

      <!-- Toaster -->
      <Toaster/>

</template>

<style>
body,
#app {
  margin: 0px;
  width: 100vw;
  height: 100%;
  overflow: hidden;
  font-family: 'Inter', sans-serif;
}

.InventoryCls {
  cursor: pointer;
}
.InventoryCls:hover,
polygon:hover {
  fill: red;
  opacity: 0.75;
}
</style>

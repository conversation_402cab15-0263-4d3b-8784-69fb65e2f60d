export enum CommunityType {
    VILLA = 'villa',
    TOWER = 'tower'
}
export type community = {
    _id: string,
    project_id: string,
    name: string,
    total_towers: number,
    total_units: number,
    total_amenities: number,
    thumbnail: string,
    category: CommunityType
}

export type createCommunity = {
    project_id: string,
    name: string,
    total_towers?: number,
    total_units?: number,
    total_amenities?: number,
    thumbnail: string,
    category: CommunityType
}

export type updateCommunity = {
    project_id: string,
    community_id:string,
    name?: string,
    total_towers?: number,
    total_units?: number,
    total_amenities?: number,
    thumbnail?: string,
    category?: CommunityType
}

export type transformedCommunity = {
    [key:string]:community
}

export type moveCommunityToTrash = {
    community_id : string[],
    timetimeStamp: string
  }

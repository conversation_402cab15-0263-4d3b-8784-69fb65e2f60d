export type position = {
    x: number,
    y: number,
    z: number
}
export type hotspot = {
    _id: string,
    position: position,
    destination: string
}
export type customTourImage = {
    _id:string,
    name:string,
    rotation?:object,
    thumbnail:string,
    url:string,
    group_id?:string,
    group_name?:string,
    hotspots?:Array<hotspot>
}
export type deleteImage = {
    tour_id:string,
    image_id:string
}
export type createCustomTourImage = {
    name:string,
    thumbnail:File,
    url:string
}
type rotation={
    x:number,
    y:number,
    z:number
}
export type updateCustomTourImage = {
    image_id:string,
    name?:string,
    rotation?:rotation,
    group_id?:string,
    group_name?:string,
    thumbnail?:string,
    url:string
}

export type updateHotspot = {
    position?: position,
    destination?: string
}

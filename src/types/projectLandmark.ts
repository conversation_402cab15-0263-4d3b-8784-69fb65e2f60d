
export enum landmarkCategory {
    HEALTH = 'health',
    EDUCATION = 'education',
    ENTERTAINMENT = 'entertainment',
    TRANSIT = 'transit',
    COMMERCIAL = 'commercial',
    RELIGIOUS = 'religious',
    GOVERNMENTAL = 'governmental',
    RECREATIONAL = 'recreational',
    LANDMARK = 'landmark',
    OTHER = 'other',
    OTHERPROJECTS = 'other projects',
    BANKS = 'banks',
    METROSTATIONS = 'metro stations',
    HOTELS = 'hotels',
    WORKSPACES = 'workspaces',
    RESTAURANTS = 'restaurants',
    SHOPS = 'shops',
    MALLS = 'malls',
    PARKS = 'parks'
}
export enum routeCategory {
  DRIVING = 'driving',
  TRANSIT = 'transit',
  WALKING = 'walking'
}
export type projectLandmark = {
    _id : string,
    name : string,
    thumbnail : string,
    icon : string,
    distance : number,
    category : landmarkCategory,
    walk_timing : number,
    transit_timing : number,
    car_timing : number,
    lat:number,
    long:number,
    description:string
}

export type createProjectLandmark = {
  project_id : string,
    name : string,
    thumbnail : File | Blob,
    icon? : File | Blob,
    distance : number,
    category : landmarkCategory,
    walk_timing : number,
    transit_timing : number,
    car_timing : number,
    lat?:number,
    long?:number,
    description:string
}

export type updateProjectLandmark = {
    landmark_id:string,
    project_id:string,
    name? : string,
    distance? : number,
    category? : landmarkCategory,
    walk_timing? : number,
    transit_timing? : number,
    car_timing? : number,
    lat?:number,
    long?:number,
    description?:string,
    walking?: string,
    driving?: string,
    transit?: string
}
export type updateProjectLandmarkFiles = {
    landmark_id:string
    project_id:string
    icon:File | Blob,
    thumbnail : File | Blob
}

export type projectLandmarkTrashType = {
    landmark_id : string[],
    timetimeStamp: string
}

export type saveRoutes = {
  project_id: string,
  landmark_id: string,
  mode: routeCategory,
  json: string,
}
export type getAllRoutesInput = {
  project_id : string,
  lat:number,
  lng:number
}
export type getRoutesInput = {
  project_id : string,
  projLat:number,
  projLong:number,
  landmarkId: string
}
export type Photos = {
    height: string,
    width: string,
    photo_reference: string,
    html_attributions: string
  }
export type Geometry = {
    location: {
      lat: number,
      lng: number
    },
    viewport: {
      northeast: {
        lat: string,
        lng: string
      },
       southwest: {
        lat: string,
        lng: string
       }
    }
  }

export const gmap_types = [
  'hospital',
  'doctor',
  'dentist',
  'school',
  'secondary_school',
  'university',
  'amusement_park',
  'movie_theater',
  'zoo',
  'transit_station',
  'train_station',
  'taxi_stand',
  'bus_station',
  'convenience_store',
  'clothing_store',
  'supermarket',
  'department_store',
  'mosque',
  'church',
  'hindu_temple',
  'synagogue',
  'local_government_office',
  'police',
  'embassy',
  'park',
  'shopping_mall',
  'cafe',
  'restaurant',
  'tourist_attraction',
  'gym',
  'night_club',
  'hardware_store',
  'library',
];

export const categoryMap: { [key: string]: landmarkCategory } = {
  'hospital': landmarkCategory.HEALTH,
  'doctor': landmarkCategory.HEALTH,
  'dentist': landmarkCategory.HEALTH,
  'school': landmarkCategory.EDUCATION,
  'secondary_school': landmarkCategory.EDUCATION,
  'university': landmarkCategory.EDUCATION,
  'amusement_park': landmarkCategory.ENTERTAINMENT,
  'movie_theater': landmarkCategory.ENTERTAINMENT,
  'zoo': landmarkCategory.ENTERTAINMENT,
  'transit_station': landmarkCategory.TRANSIT,
  'train_station': landmarkCategory.TRANSIT,
  'taxi_stand': landmarkCategory.TRANSIT,
  'bus_station': landmarkCategory.TRANSIT,
  'convenience_store': landmarkCategory.COMMERCIAL,
  'clothing_store': landmarkCategory.COMMERCIAL,
  'supermarket': landmarkCategory.COMMERCIAL,
  'department_store': landmarkCategory.COMMERCIAL,
  'mosque': landmarkCategory.RELIGIOUS,
  'church': landmarkCategory.RELIGIOUS,
  'hindu_temple': landmarkCategory.RELIGIOUS,
  'synagogue': landmarkCategory.RELIGIOUS,
  'local_government_office': landmarkCategory.GOVERNMENTAL,
  'police': landmarkCategory.GOVERNMENTAL,
  'embassy': landmarkCategory.GOVERNMENTAL,
  'park': landmarkCategory.RECREATIONAL,
  'shopping_mall': landmarkCategory.RECREATIONAL,
  'cafe': landmarkCategory.RECREATIONAL,
  'restaurant': landmarkCategory.RECREATIONAL,
  'tourist_attraction': landmarkCategory.LANDMARK,
  'gym': landmarkCategory.OTHER,
  'night_club': landmarkCategory.OTHER,
  'hardware_store': landmarkCategory.OTHER,
  'library': landmarkCategory.OTHER,
};

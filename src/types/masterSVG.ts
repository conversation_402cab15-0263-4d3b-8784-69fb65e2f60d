export enum masterSVGType {
    LANDMARK = 'landmark',
    PROJECT = 'project',
    RADIUS = 'radius',
    ROUTE = 'route',
    PIN = 'pin',
    IMAGE = 'image',
    UNAVAILABLE = 'unavailable'
}
export type masterSVG={
    _id:string,
    scene_id:string,
    svg_url:string,
    layers:object,
    type:masterSVGType
}
export type transformedSVG={
    [key:string]:masterSVG
}
export type updatePayload={
    [key: string]: string | { x: number; y: number } ; // Allow specific types
}

export enum masterLandmarkCategory {
    HEALTH = 'health',
    EDUCATION = 'education',
    ENTERTAINMENT = 'entertainment',
    TRANSIT = 'transit',
    COMMERCIAL = 'commercial',
    RELIGIOUS = 'religious',
    GOVERNMENTAL = 'governmental',
    RECREATIONAL = 'recreational',
    OTHER = 'other'
}
export type masterLandmark = {
    _id : string,
    name : string,
    thumbnail : string,
    distance : number,
    category : masterLandmarkCategory,
    walk_timing : number,
    transit_timing : number,
    car_timing : number
}

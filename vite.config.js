import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import { readFileSync, writeFileSync } from 'node:fs';
import { resolve } from 'node:path';
import path from "path";
import vueDevtools from "vite-plugin-vue-devtools";
import { createHtmlPlugin } from "vite-plugin-html";
export default ({ mode }) => {
process.env = {...process.env, ...loadEnv(mode, process.cwd())};
return defineConfig({
  plugins: [
    vue(), vueDevtools(), createHtmlPlugin(),
    // Custom plugin to handle service worker
    {
      name: 'firebase-service-worker',
      // Use writeBundle instead of closeBundle
      writeBundle() {
        // Read the service worker template
        const swTemplate = readFileSync(
          resolve(__dirname, 'public/firebase-messaging-sw.js'),
          'utf-8'
        );
        
        // Replace placeholders with environment variables
        const processedSW = swTemplate
          .replace('__FIREBASE_API_KEY__', process.env.VITE_FIREBASE_API_KEY || '')
          .replace('__FIREBASE_AUTH_DOMAIN__', process.env.VITE_FIREBASE_AUTH_DOMAIN || '')
          .replace('__FIREBASE_DATABASE_URL__', process.env.VITE_FIREBASE_DATABASE_URL || '')
          .replace('__FIREBASE_PROJECT_ID__', process.env.VITE_FIREBASE_PROJECT_ID || '')
          .replace('__FIREBASE_STORAGE_BUCKET__', process.env.VITE_FIREBASE_STORAGE_BUCKET || '')
          .replace('__FIREBASE_APP_ID__', process.env.VITE_FIREBASE_APP_ID || '')
          .replace('__FIREBASE_MESSAGING_SENDER_ID__', process.env.VITE_FIREBASE_MESSAGING_SENDER_ID || '');
        
        // Write the processed service worker
        try {
          writeFileSync(resolve(__dirname, 'dist/firebase-messaging-sw.js'), processedSW);
          console.log('Firebase service worker processed and written successfully');
        } catch (error) {
          console.error('Failed to write Firebase service worker:', error);
        }
      }
    }
  ],
  esbuild: {
    drop: ['console', 'debugger'],
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
});
};